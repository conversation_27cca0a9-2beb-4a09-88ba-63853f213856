package com.ideal.qc.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ideal.qc.service.QcDataAnnotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcDataAnnotation;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 质检标注Controller
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
@RestController
@RequestMapping("/annotation")
public class QcDataAnnotationController extends BaseController
{
    @Autowired
    private QcDataAnnotationService qcDataAnnotationService;

    /**
     * 查询质检标注列表
     */
    @RequiresPermissions("qc:annotation:list")
    @GetMapping("/list")
    public TableDataInfo list(QcDataAnnotation qcDataAnnotation)
    {
        startPage();
        List<QcDataAnnotation> list = qcDataAnnotationService.selectQcDataAnnotationList(qcDataAnnotation);
        return getDataTable(list);
    }
    /**
     * 查询质检标注列表
     */
    @RequiresPermissions("qc:annotation:list")
    @GetMapping("/listAll")
    public AjaxResult listAll(QcDataAnnotation qcDataAnnotation)
    {
        List<QcDataAnnotation> list = qcDataAnnotationService.selectQcDataAnnotationList(qcDataAnnotation);
        return success(list);
    }
    /**
     * 导出质检标注列表
     */
    @RequiresPermissions("qc:annotation:export")
    @Log(title = "质检标注", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QcDataAnnotation qcDataAnnotation)
    {
        List<QcDataAnnotation> list = qcDataAnnotationService.selectQcDataAnnotationList(qcDataAnnotation);
        ExcelUtil<QcDataAnnotation> util = new ExcelUtil<QcDataAnnotation>(QcDataAnnotation.class);
        util.exportExcel(response, list, "质检标注数据");
    }

    /**
     * 获取质检标注详细信息
     */
    @RequiresPermissions("qc:annotation:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(qcDataAnnotationService.selectQcDataAnnotationById(id));
    }

    /**
     * 新增质检标注
     */
    @RequiresPermissions("qc:annotation:add")
    @Log(title = "质检标注", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QcDataAnnotation qcDataAnnotation)
    {
        qcDataAnnotationService.insertQcDataAnnotation(qcDataAnnotation);
        return success(qcDataAnnotation);
    }

    /**
     * 修改质检标注
     */
    @RequiresPermissions("qc:annotation:edit")
    @Log(title = "质检标注", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QcDataAnnotation qcDataAnnotation)
    {
        return toAjax(qcDataAnnotationService.updateQcDataAnnotation(qcDataAnnotation));
    }

    /**
     * 删除质检标注
     */
    @RequiresPermissions("qc:annotation:remove")
    @Log(title = "质检标注", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(qcDataAnnotationService.deleteQcDataAnnotationByIds(ids));
    }
}
