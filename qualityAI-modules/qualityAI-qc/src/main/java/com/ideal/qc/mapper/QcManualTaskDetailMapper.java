package com.ideal.qc.mapper;

import com.ideal.qc.domain.QcManualTaskDetail;
import com.ideal.qc.domain.QcManualTaskDetailVO;

import java.util.List;
import java.util.Map;

public interface QcManualTaskDetailMapper {

    /**
     * 查询人工质检任务详情
     *
     * @param id 人工质检任务详情主键
     * @return 人工质检任务详情
     */
    public QcManualTaskDetail selectQcManualTaskDetailById(Long id);

    /**
     * 查询人工质检任务详情列表
     *
     * @param qcManualTaskDetail 人工质检任务详情
     * @return 人工质检任务详情集合
     */
    public List<QcManualTaskDetail> selectQcManualTaskDetailList(QcManualTaskDetail qcManualTaskDetail);


    /**
     * 根据任务id和相关条件，查询该任务下所有明细(查询所有)
     */
    public List<QcManualTaskDetailVO> selectQcManualTaskDetailsByTaskId(Map<String,Object> map);

    /**
     * 根据任务id和相关条件，查询该任务下所有明细(质检组长或组员查询列表)
     */
    public List<QcManualTaskDetailVO> selectQcManualTaskDetailsByTaskIdForRole(Map<String,Object> map);

    /**
     * 根据任务id获取当前用户下某条任务下的已完成数量及会话数(不同角色的用户看到的数量不同)
     */
    Map<String,Object> selectCountByTaskId(Map<String,Object> map);


    /**
     * 查询某任务id下的未完成明细总数量
     */

    int getNotFinishCount(Long id);


    /**
     * 回收某任务id下的任务明细
     */
    int recycleDetail(Long id);


    /**
     * 新增人工质检任务详情
     *
     * @param qcManualTaskDetail 人工质检任务详情
     * @return 结果
     */
    public int insertQcManualTaskDetail(QcManualTaskDetail qcManualTaskDetail);


    public int batchInsert(List<QcManualTaskDetail> list);

    /**
     * 修改人工质检任务详情
     *
     * @param qcManualTaskDetail 人工质检任务详情
     * @return 结果
     */
    public int updateQcManualTaskDetail(QcManualTaskDetail qcManualTaskDetail);

    /**
     * 删除人工质检任务详情
     *
     * @param id 人工质检任务详情主键
     * @return 结果
     */
    public int deleteQcManualTaskDetailById(Long id);

    /**
     * 批量删除人工质检任务详情
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQcManualTaskDetailByIds(Long[] ids);


}
