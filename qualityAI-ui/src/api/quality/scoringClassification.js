import request from '@/utils/request'

// 查询评分模板分类列表
export function getQcScoringClassificationList(scoringTemplateId) {
  return request({
    url: '/qc/scoring/classification/list/' + scoringTemplateId,
    method: 'post'
  })
}

// 新增评分模板分类
export function addQcScoringClassification(data) {
  return request({
    url: '/qc/scoring/classification/add',
    method: 'post',
    data: data
  })
}

// 修改评分模板分类
export function updateQcScoringClassification(data) {
  return request({
    url: '/qc/scoring/classification/update',
    method: 'post',
    data: data
  })
}

// 删除评分模板分类
export function removeQcScoringClassification(id) {
  return request({
    url: '/qc/scoring/classification/remove/' + id,
    method: 'post'
  })
}
