import request from '@/utils/request'

// 查询评分模板列表
export function getQcScoringTemplateList(params, data) {
  return request({
    url: '/qc/scoring/template/list',
    method: 'post',
    params,
    data
  })
}

// 新增评分模板
export function addQcScoringTemplate(data) {
  return request({
    url: '/qc/scoring/template/add',
    method: 'post',
    data: data
  })
}

// 修改评分模板
export function updateQcScoringTemplate(data) {
  return request({
    url: '/qc/scoring/template/update',
    method: 'post',
    data: data
  })
}

export function getTemplate(id) {
  return request({
    url: '/qc/scoring/template/'+id,
    method: 'post'
  })
}
// 删除评分模板
export function removeQcScoringTemplate(id) {
  return request({
    url: '/qc/scoring/template/remove/' + id,
    method: 'post'
  })
}
