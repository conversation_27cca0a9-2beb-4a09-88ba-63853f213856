package com.ideal.qc.service.impl;

import java.util.List;
import com.ideal.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.QcSyncPlanMapper;
import com.ideal.qc.domain.QcSyncPlan;
import com.ideal.qc.service.IQcSyncPlanService;

import javax.annotation.Resource;

/**
 * 数据同步计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class QcSyncPlanServiceImpl implements IQcSyncPlanService 
{
    @Resource
    private QcSyncPlanMapper qcSyncPlanMapper;

    /**
     * 查询数据同步计划
     * 
     * @param id 数据同步计划主键
     * @return 数据同步计划
     */
    @Override
    public QcSyncPlan selectQcSyncPlanById(Long id)
    {
        return qcSyncPlanMapper.selectQcSyncPlanById(id);
    }

    /**
     * 查询数据同步计划列表
     * 
     * @param qcSyncPlan 数据同步计划
     * @return 数据同步计划
     */
    @Override
    public List<QcSyncPlan> selectQcSyncPlanList(QcSyncPlan qcSyncPlan)
    {
        return qcSyncPlanMapper.selectQcSyncPlanList(qcSyncPlan);
    }

    /**
     * 新增数据同步计划
     * 
     * @param qcSyncPlan 数据同步计划
     * @return 结果
     */
    @Override
    public int insertQcSyncPlan(QcSyncPlan qcSyncPlan)
    {
        qcSyncPlan.setCreateTime(DateUtils.getNowDate());
        return qcSyncPlanMapper.insertQcSyncPlan(qcSyncPlan);
    }

    /**
     * 修改数据同步计划
     * 
     * @param qcSyncPlan 数据同步计划
     * @return 结果
     */
    @Override
    public int updateQcSyncPlan(QcSyncPlan qcSyncPlan)
    {
        return qcSyncPlanMapper.updateQcSyncPlan(qcSyncPlan);
    }

    /**
     * 批量删除数据同步计划
     * 
     * @param ids 需要删除的数据同步计划主键
     * @return 结果
     */
    @Override
    public int deleteQcSyncPlanByIds(Long[] ids)
    {
        return qcSyncPlanMapper.deleteQcSyncPlanByIds(ids);
    }

    /**
     * 删除数据同步计划信息
     * 
     * @param id 数据同步计划主键
     * @return 结果
     */
    @Override
    public int deleteQcSyncPlanById(Long id)
    {
        return qcSyncPlanMapper.deleteQcSyncPlanById(id);
    }
}
