package com.ideal.qc.service;

import java.util.List;
import com.ideal.qc.domain.QcHotWord;

/**
 * ASR热词配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IQcHotWordService 
{
    /**
     * 查询ASR热词配置
     * 
     * @param id ASR热词配置主键
     * @return ASR热词配置
     */
    public QcHotWord selectQcHotWordById(Long id);

    /**
     * 查询ASR热词配置列表
     * 
     * @param qcHotWord ASR热词配置
     * @return ASR热词配置集合
     */
    public List<QcHotWord> selectQcHotWordList(QcHotWord qcHotWord);

    /**
     * 查询ASR热词配置树结构
     * 
     * @param qcHotWord ASR热词配置
     * @return ASR热词配置树结构
     */
    public List<QcHotWord> selectQcHotWordTree(QcHotWord qcHotWord);

    /**
     * 新增ASR热词配置
     * 
     * @param qcHotWord ASR热词配置
     * @return 结果
     */
    public int insertQcHotWord(QcHotWord qcHotWord);

    /**
     * 修改ASR热词配置
     * 
     * @param qcHotWord ASR热词配置
     * @return 结果
     */
    public int updateQcHotWord(QcHotWord qcHotWord);

    /**
     * 批量删除ASR热词配置
     * 
     * @param ids 需要删除的ASR热词配置主键集合
     * @return 结果
     */
    public int deleteQcHotWordByIds(Long[] ids);

    /**
     * 删除ASR热词配置信息
     * 
     * @param id ASR热词配置主键
     * @return 结果
     */
    public int deleteQcHotWordById(Long id);

    /**
     * 获取启用的热词字符串（用于ASR调用）
     * 
     * @return 热词字符串，以分号分隔
     */
    public String getEnabledHotWordsString();

    /**
     * 校验热词名称是否唯一
     * 
     * @param qcHotWord 热词信息
     * @return 结果
     */
    public String checkWordNameUnique(QcHotWord qcHotWord);

    /**
     * 构建树结构
     * 
     * @param hotWords 热词列表
     * @return 树结构
     */
    public List<QcHotWord> buildTree(List<QcHotWord> hotWords);
}
