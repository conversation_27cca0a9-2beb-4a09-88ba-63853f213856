package com.ideal.qc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.ZjTask;
import com.ideal.qc.service.IZjTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 质检任务Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/task")
public class ZjTaskController extends BaseController
{
    @Autowired
    private IZjTaskService iZjTaskService;

    /**
     * 查询质检任务列表
     */
    @RequiresPermissions("quality:task:list")
    @GetMapping("/list")
    public TableDataInfo list(ZjTask zjTask)
    {
        startPage();
        List<ZjTask> list = iZjTaskService.selectZjTaskList(zjTask);
        return getDataTable(list);
    }

    /**
     * 导出质检任务列表
     */
    @RequiresPermissions("quality:task:export")
    @Log(title = "质检任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZjTask zjTask)
    {
        List<ZjTask> list = iZjTaskService.selectZjTaskList(zjTask);
        ExcelUtil<ZjTask> util = new ExcelUtil<ZjTask>(ZjTask.class);
        util.exportExcel(response, list, "质检任务数据");
    }

    /**
     * 获取质检任务详细信息
     */
    @RequiresPermissions("quality:task:query")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") Long taskId)
    {
        return success(iZjTaskService.selectZjTaskByTaskId(taskId));
    }

    /**
     * 新增质检任务
     */
    @RequiresPermissions("quality:task:add")
    @Log(title = "质检任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZjTask zjTask)
    {
        int taskId=iZjTaskService.insertZjTask(zjTask);
        if(taskId>0){
            iZjTaskService.insertZjDataTemplate(zjTask);
        }
        return toAjax(taskId);
    }

    /**
     * 修改质检任务
     */
    @RequiresPermissions("quality:task:edit")
    @Log(title = "质检任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZjTask zjTask)
    {
        return toAjax(iZjTaskService.updateZjTask(zjTask));
    }

    /**
     * 删除质检任务
     */
    @RequiresPermissions("quality:task:remove")
    @Log(title = "质检任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable Long[] taskIds)
    {
        return toAjax(iZjTaskService.deleteZjTaskByTaskIds(taskIds));
    }


    /**
     * 启动任务
     */
    @Log(title = "启动任务")
    @PostMapping("/start")
    public void start(@RequestBody ZjTask zjTask)
    {
        try {
            iZjTaskService.startTask(zjTask);
            //已完成更新状态
            ZjTask zjTaskUp=new ZjTask();
            zjTaskUp.setTaskId(zjTask.getTaskId());
            zjTaskUp.setExecuteStatus("2");
            iZjTaskService.updateZjTask(zjTaskUp);
        }catch (Exception e){
            logger.info(e.getMessage());
        }

    }
}
