package com.ideal.qc.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.QcBizAnswerMapper;
import com.ideal.qc.domain.QcBizAnswer;
import com.ideal.qc.service.IQcBizAnswerService;

import javax.annotation.Resource;

/**
 * 问卷答案(EAV)Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@Service
public class QcBizAnswerServiceImpl implements IQcBizAnswerService 
{
    @Resource
    private QcBizAnswerMapper qcBizAnswerMapper;

    /**
     * 查询问卷答案(EAV)
     * 
     * @param id 问卷答案(EAV)主键
     * @return 问卷答案(EAV)
     */
    @Override
    public QcBizAnswer selectQcBizAnswerById(Long id)
    {
        return qcBizAnswerMapper.selectQcBizAnswerById(id);
    }

    /**
     * 查询问卷答案(EAV)列表
     * 
     * @param qcBizAnswer 问卷答案(EAV)
     * @return 问卷答案(EAV)
     */
    @Override
    public List<QcBizAnswer> selectQcBizAnswerList(QcBizAnswer qcBizAnswer)
    {
        return qcBizAnswerMapper.selectQcBizAnswerList(qcBizAnswer);
    }

    /**
     * 新增问卷答案(EAV)
     * 
     * @param qcBizAnswer 问卷答案(EAV)
     * @return 结果
     */
    @Override
    public int insertQcBizAnswer(QcBizAnswer qcBizAnswer)
    {
        return qcBizAnswerMapper.insertQcBizAnswer(qcBizAnswer);
    }

    /**
     * 修改问卷答案(EAV)
     * 
     * @param qcBizAnswer 问卷答案(EAV)
     * @return 结果
     */
    @Override
    public int updateQcBizAnswer(QcBizAnswer qcBizAnswer)
    {
        return qcBizAnswerMapper.updateQcBizAnswer(qcBizAnswer);
    }

    /**
     * 批量删除问卷答案(EAV)
     * 
     * @param ids 需要删除的问卷答案(EAV)主键
     * @return 结果
     */
    @Override
    public int deleteQcBizAnswerByIds(Long[] ids)
    {
        return qcBizAnswerMapper.deleteQcBizAnswerByIds(ids);
    }

    /**
     * 删除问卷答案(EAV)信息
     * 
     * @param id 问卷答案(EAV)主键
     * @return 结果
     */
    @Override
    public int deleteQcBizAnswerById(Long id)
    {
        return qcBizAnswerMapper.deleteQcBizAnswerById(id);
    }
}
