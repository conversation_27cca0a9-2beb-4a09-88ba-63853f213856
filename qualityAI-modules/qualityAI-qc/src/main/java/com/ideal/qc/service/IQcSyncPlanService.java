package com.ideal.qc.service;

import java.util.List;
import com.ideal.qc.domain.QcSyncPlan;

/**
 * 数据同步计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IQcSyncPlanService 
{
    /**
     * 查询数据同步计划
     * 
     * @param id 数据同步计划主键
     * @return 数据同步计划
     */
    public QcSyncPlan selectQcSyncPlanById(Long id);

    /**
     * 查询数据同步计划列表
     * 
     * @param qcSyncPlan 数据同步计划
     * @return 数据同步计划集合
     */
    public List<QcSyncPlan> selectQcSyncPlanList(QcSyncPlan qcSyncPlan);

    /**
     * 新增数据同步计划
     * 
     * @param qcSyncPlan 数据同步计划
     * @return 结果
     */
    public int insertQcSyncPlan(QcSyncPlan qcSyncPlan);

    /**
     * 修改数据同步计划
     * 
     * @param qcSyncPlan 数据同步计划
     * @return 结果
     */
    public int updateQcSyncPlan(QcSyncPlan qcSyncPlan);

    /**
     * 批量删除数据同步计划
     * 
     * @param ids 需要删除的数据同步计划主键集合
     * @return 结果
     */
    public int deleteQcSyncPlanByIds(Long[] ids);

    /**
     * 删除数据同步计划信息
     * 
     * @param id 数据同步计划主键
     * @return 结果
     */
    public int deleteQcSyncPlanById(Long id);
}
