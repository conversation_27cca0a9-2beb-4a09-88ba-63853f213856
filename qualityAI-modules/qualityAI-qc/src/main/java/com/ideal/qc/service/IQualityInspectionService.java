package com.ideal.qc.service;

import com.alibaba.fastjson.JSONObject;

public interface IQualityInspectionService {

    /**
     * 处理ASR文本与质检正则匹配
     * @param asrText 完整的ASR识别结果
     * @param pattern 质检正则表达式
     * @return 是否匹配
     */
    public boolean performQualityCheck(String asrText, String pattern) ;

    /**
     * 质检规则匹配(正则)
     * @param scoreItemId
     * @param content
     * @param callId
     * @return
     */
    public JSONObject regexRule(Long scoreItemId, JSONObject content, Long callId) ;
}
