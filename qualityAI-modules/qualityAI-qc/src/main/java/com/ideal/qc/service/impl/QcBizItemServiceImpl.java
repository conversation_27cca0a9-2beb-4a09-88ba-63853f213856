package com.ideal.qc.service.impl;

import java.io.IOException;
import java.util.*;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcel;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.alibaba.nacos.shaded.com.google.gson.reflect.TypeToken;
import com.ideal.common.core.exception.ServiceException;
import com.ideal.common.core.utils.DateUtils;
import com.ideal.qc.domain.QcBizAnswer;
import com.ideal.qc.domain.QcDataset;
import com.ideal.qc.listener.BizDynamicListener;
import com.ideal.qc.mapper.QcBizAnswerMapper;
import com.ideal.qc.mapper.QcDatasetMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.QcBizItemMapper;
import com.ideal.qc.domain.QcBizItem;
import com.ideal.qc.service.IQcBizItemService;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 业务数据明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
@Slf4j
public class QcBizItemServiceImpl implements IQcBizItemService 
{
    @Resource
    private QcBizItemMapper qcBizItemMapper;

    @Resource
    private QcDatasetMapper qcDatasetMapper;

    @Resource
    private QcBizAnswerMapper answerMapper;
    /**
     * 查询业务数据明细
     * 
     * @param id 业务数据明细主键
     * @return 业务数据明细
     */
    @Override
    public QcBizItem selectQcBizItemById(Long id)
    {
        return qcBizItemMapper.selectQcBizItemById(id);
    }

    /**
     * 查询业务数据明细列表
     * 
     * @param qcBizItem 业务数据明细
     * @return 业务数据明细
     */
    @Override
    public List<QcBizItem> selectQcBizItemList(QcBizItem qcBizItem)
    {
        return qcBizItemMapper.selectQcBizItemList(qcBizItem);
    }

    /**
     * 新增业务数据明细
     * 
     * @param qcBizItem 业务数据明细
     * @return 结果
     */
    @Override
    public int insertQcBizItem(QcBizItem qcBizItem)
    {
        qcBizItem.setCreateTime(DateUtils.getNowDate());
        return qcBizItemMapper.insertQcBizItem(qcBizItem);
    }

    /**
     * 修改业务数据明细
     * 
     * @param qcBizItem 业务数据明细
     * @return 结果
     */
    @Override
    public int updateQcBizItem(QcBizItem qcBizItem)
    {
        return qcBizItemMapper.updateQcBizItem(qcBizItem);
    }

    /**
     * 批量删除业务数据明细
     * 
     * @param ids 需要删除的业务数据明细主键
     * @return 结果
     */
    @Override
    public int deleteQcBizItemByIds(Long[] ids)
    {
        return qcBizItemMapper.deleteQcBizItemByIds(ids);
    }

    /**
     * 删除业务数据明细信息
     * 
     * @param id 业务数据明细主键
     * @return 结果
     */
    @Override
    public int deleteQcBizItemById(Long id)
    {
        return qcBizItemMapper.deleteQcBizItemById(id);
    }

    @Override
    public QcBizItem selectQcBizItemByNo(String bizNo,String type,Long manualDetailId) {
        QcBizItem item = qcBizItemMapper.selectQcBizItemByNo(bizNo);
        if("1".equals(type)||"2".equals(type)){//type1为人工质检获取人工质检的模版Id和manualtaskdetailid,2为人工复检，取smarttaskdetailid,3为详情，需要查询人工和智能结果
            Map<String,Long> qt = qcBizItemMapper.selectQcManualTaskDetailById(manualDetailId);
            if(qt!=null){
                item.setTempId(qt.get("template_id"));
                item.setTaskDetailId(qt.get("id"));
            }
            if("2".equals(type)){
                String aiResult = qcBizItemMapper.getAiResultByManualDetailId(manualDetailId);
                item.setAiResult(aiResult);
            }
        }else{
            List<Map<String,Object>> manualList = qcBizItemMapper.selectQcManualTaskDetailByNo(bizNo);
            if(manualList!=null&&manualList.size()>0){
                item.setManualList(manualList);
            }
        }
        return item;
    }

    @Override
    public List<QcBizItem> importExcel(MultipartFile file) throws IOException {
        BizDynamicListener listener = new BizDynamicListener();
        EasyExcel.read(file.getInputStream(), listener)
                .headRowNumber(1)   // 表头 1 行
                .sheet(0)
                .doRead();
        return listener.getItems();
    }
    /**
     * rowDataList         — BizDynamicListener 产出的全部 QcBizItem
     * operName            — 当前登录人
     * dataType            — voice / biz ；这里固定写 "biz"
     */
    @Override
    public String importBizItem(List<QcBizItem> rowDataList, String operName) {
        log.info("开始导入业务数据明细，操作人：{}，数据条数：{}", operName, rowDataList.size());

        if (rowDataList.isEmpty()) {
            throw new ServiceException("导入数据不能为空！");
        }

        int successNum = 0, failureNum = 0, rowIdx = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        /* ❶ 当前批次数据集，初始置空；达到 2000 条或首次时创建 */
        QcDataset currentDs = null;
        int currentDsSuccessCount = 0; // 当前数据集成功导入的条数

        for (QcBizItem item : rowDataList) {
            try {
                /* ----------  判断是否需要新建数据集  ---------- */
                if (currentDs == null || currentDsSuccessCount >= 2000) {
                    // 更新上一个数据集的总数（如果存在）
                    if (currentDs != null) {
                        if (currentDsSuccessCount > 0) {
                            // 有成功导入的数据，更新数据集总数
                            currentDs.setTotalCount((long) currentDsSuccessCount);
                            qcDatasetMapper.updateQcDataset(currentDs);
                            log.info("数据集更新完成，ID：{}，总数：{}", currentDs.getId(), currentDsSuccessCount);
                        } else {
                            // 没有成功导入任何数据，删除空数据集
                            qcDatasetMapper.deleteQcDatasetById(currentDs.getId());
                            log.info("删除空数据集，ID：{}", currentDs.getId());
                        }
                    }

                    // 创建新数据集
                    currentDs = new QcDataset();
                    currentDs.setName("业务导入_" + System.currentTimeMillis());
                    currentDs.setDataType("biz");
                    currentDs.setCreateBy(operName);
                    currentDs.setProcessStatus("new");
                    currentDs.setTotalCount(0L); // 初始为0
                    qcDatasetMapper.insertQcDataset(currentDs);
                    currentDsSuccessCount = 0; // 重置当前数据集成功计数
                }
                rowIdx++; // 行号 ++

                /* ---------- 唯一性校验 + 写明细 ---------- */
                if (item.getUniqueKey() == null || item.getUniqueKey().trim().isEmpty()) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum)
                            .append("、第 ").append(rowIdx).append(" 行：唯一主键号不能为空");
                } else if (qcBizItemMapper.selectQcBizItemByNo(item.getUniqueKey()) == null) {
                    item.setDatasetId(currentDs.getId());
                    item.setCreateBy(operName);
                    item.setTenantId(1L); // 设置默认租户ID
                    qcBizItemMapper.insertQcBizItem(item);

                    // 可选：拆 EAV
                    if (item.getAnswersJson() != null && !item.getAnswersJson().trim().isEmpty()) {
                        try {
                            List<Map<String,String>> ans = new Gson().fromJson(
                                    item.getAnswersJson(),
                                    new TypeToken<List<Map<String,String>>>(){}.getType());
                            int answerSuccessCount = 0;
                            for (Map<String,String> a : ans) {
                                try {
                                    QcBizAnswer qa = new QcBizAnswer();
                                    qa.setItemId(item.getId());
                                    qa.setQuesCode(a.get("quesCode"));
                                    qa.setAnsValue(a.get("ansValue"));
                                    qa.setTenantId(1L);
                                    qa.setCreateBy(operName);
                                    qa.setCreateTime(new Date());
                                    int result = answerMapper.insertQcBizAnswer(qa);
                                    if (result > 0) {
                                        answerSuccessCount++;
                                    }
                                } catch (Exception e) {
                                    log.warn("插入答案失败，业务唯一号：{}，问题代码：{}，错误：{}",
                                            item.getUniqueKey(), a.get("quesCode"), e.getMessage());
                                }
                            }
                            log.debug("业务唯一号：{}，成功插入答案：{}/{}条",
                                    item.getUniqueKey(), answerSuccessCount, ans.size());
                        } catch (Exception e) {
                            log.warn("解析答案JSON失败，业务唯一号：{}，错误：{}", item.getUniqueKey(), e.getMessage());
                        }
                    }

                    successNum++;
                    currentDsSuccessCount++; // 只有成功导入时才增加当前数据集计数
                    successMsg.append("<br/>").append(successNum)
                            .append("、业务唯一号 ").append(item.getUniqueKey())
                            .append(" 导入成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum)
                            .append("、业务唯一号 ").append(item.getUniqueKey())
                            .append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、业务唯一号 " + item.getUniqueKey() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }

        /* ❂ 更新最后一批数据集的 total_count */
        if (currentDs != null) {
            if (currentDsSuccessCount > 0) {
                // 有成功导入的数据，更新数据集总数
                currentDs.setTotalCount((long) currentDsSuccessCount);
                qcDatasetMapper.updateQcDataset(currentDs);
                log.info("最后数据集更新完成，ID：{}，总数：{}", currentDs.getId(), currentDsSuccessCount);
            } else {
                // 没有成功导入任何数据，删除空数据集
                qcDatasetMapper.deleteQcDatasetById(currentDs.getId());
                log.info("删除空数据集，ID：{}", currentDs.getId());
            }
        }

        log.info("导入完成，成功：{}条，失败：{}条", successNum, failureNum);

        if (failureNum > 0) {
            if (successNum > 0) {
                // 部分成功的情况
                String message = "导入完成！成功 " + successNum + " 条，失败 " + failureNum + " 条。<br/>" +
                        "<strong>成功记录：</strong>" + successMsg.toString() + "<br/>" +
                        "<strong>失败记录：</strong>" + failureMsg.toString();
                return message;
            } else {
                // 全部失败的情况
                failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            }
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public void exportBizItemWithDynamicColumns(HttpServletResponse response, QcBizItem qcBizItem) {
        try {
            // 1. 查询数据
            List<QcBizItem> list = qcBizItemMapper.selectQcBizItemList(qcBizItem);
            if (list.isEmpty()) {
                log.warn("没有找到要导出的数据");
                return;
            }

            // 2. 收集所有动态列名
            Set<String> allDynamicColumns = new LinkedHashSet<>();
            for (QcBizItem item : list) {
                if (item.getAnswersJson() != null && !item.getAnswersJson().trim().isEmpty()) {
                    try {
                        List<Map<String, String>> answers = new Gson().fromJson(
                                item.getAnswersJson(),
                                new TypeToken<List<Map<String, String>>>(){}.getType());
                        for (Map<String, String> answer : answers) {
                            String quesCode = answer.get("quesCode");
                            if (quesCode != null && !quesCode.trim().isEmpty()) {
                                allDynamicColumns.add(quesCode.trim());
                            }
                        }
                    } catch (Exception e) {
                        log.warn("解析答案JSON失败，ID：{}，错误：{}", item.getId(), e.getMessage());
                    }
                }
            }

            // 3. 构建导出数据
            List<Map<String, Object>> exportData = new ArrayList<>();
            for (QcBizItem item : list) {
                Map<String, Object> row = buildExportRow(item, allDynamicColumns);
                exportData.add(row);
            }

            // 4. 构建表头
            List<String> headers = buildExportHeaders(allDynamicColumns);

            // 5. 导出Excel
            exportToExcel(response, exportData, headers, "业务数据明细");

        } catch (Exception e) {
            log.error("导出业务数据明细失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 构建导出行数据
     */
    private Map<String, Object> buildExportRow(QcBizItem item, Set<String> allDynamicColumns) {
        Map<String, Object> row = new LinkedHashMap<>();

        // 固定列（按照导入模板的顺序）
        row.put("所属平台", item.getPlatform());
        row.put("所属项目", item.getProjectName());
        row.put("指标名称", item.getMetricName());
        row.put("子指标名称", item.getSubMetricName());
        row.put("服务中心", item.getServiceCenter());
        row.put("坐席工号", item.getAgentId());
        row.put("坐席姓名", item.getAgentName());
        row.put("答卷开始时间", item.getAnswerStartAt());
        row.put("答卷结束时间", item.getAnswerEndAt());
        row.put("通话时长(s)", item.getCallDurationS());
        row.put("日期", item.getRecordDate());
        row.put("答卷编号", item.getSurveyCode());
        row.put("拨打号码", item.getCallerPhone());
        row.put("业务类型", item.getBusinessType());
        row.put("运营商", item.getCarrier());
        row.put("唯一主键号", item.getUniqueKey());
        row.put("省份", item.getProvince());
        row.put("地市", item.getCity());
        row.put("联系人号码", item.getContactPhone());
        row.put("区号", item.getAreaCode());
        row.put("产品类型", item.getProductType());
        row.put("服务订单编码", item.getServiceOrderId());
        row.put("服务类型", item.getServiceType());
        row.put("竣工时间", item.getFinishTime());
        row.put("产品名称", item.getProductName());
        row.put("销售点名称", item.getSalesPoint());
        row.put("任务结果", item.getTaskResult());
        row.put("录音文件", item.getRecordingFile());
        row.put("来电时间", item.getCallTime());
        row.put("一级质检结果", item.getQcLvl1Result());
        row.put("一级质检评语", item.getQcLvl1Comment());
        row.put("一级质检员工号", item.getQcLvl1EmpId());
        row.put("二级质检结果", item.getQcLvl2Result());
        row.put("二级质检评语", item.getQcLvl2Comment());
        row.put("二级质检员工号", item.getQcLvl2EmpId());
        row.put("小结备注", item.getSummaryRemark());

        // 动态列（问卷答案）
        Map<String, String> answerMap = new HashMap<>();
        if (item.getAnswersJson() != null && !item.getAnswersJson().trim().isEmpty()) {
            try {
                List<Map<String, String>> answers = new Gson().fromJson(
                        item.getAnswersJson(),
                        new TypeToken<List<Map<String, String>>>(){}.getType());
                for (Map<String, String> answer : answers) {
                    String quesCode = answer.get("quesCode");
                    String ansValue = answer.get("ansValue");
                    if (quesCode != null && !quesCode.trim().isEmpty()) {
                        answerMap.put(quesCode.trim(), ansValue);
                    }
                }
            } catch (Exception e) {
                log.warn("解析答案JSON失败，ID：{}，错误：{}", item.getId(), e.getMessage());
            }
        }

        // 添加所有动态列，没有答案的填空
        for (String column : allDynamicColumns) {
            row.put(column, answerMap.getOrDefault(column, ""));
        }

        return row;
    }

    /**
     * 构建导出表头
     */
    private List<String> buildExportHeaders(Set<String> allDynamicColumns) {
        List<String> headers = new ArrayList<>();

        // 固定列表头（与导入模板保持一致）
        headers.addAll(Arrays.asList(
            "所属平台", "所属项目", "指标名称", "子指标名称", "服务中心", "坐席工号", "坐席姓名",
            "答卷开始时间", "答卷结束时间", "通话时长(s)", "日期", "答卷编号", "拨打号码", "业务类型",
            "运营商", "唯一主键号", "省份", "地市", "联系人号码", "区号", "产品类型", "服务订单编码",
            "服务类型", "竣工时间", "产品名称", "销售点名称", "任务结果", "录音文件", "来电时间",
            "一级质检结果", "一级质检评语", "一级质检员工号", "二级质检结果", "二级质检评语",
            "二级质检员工号", "小结备注"
        ));

        // 动态列表头
        headers.addAll(allDynamicColumns);

        return headers;
    }

    /**
     * 导出到Excel
     */
    private void exportToExcel(HttpServletResponse response, List<Map<String, Object>> data,
                              List<String> headers, String fileName) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream())
                    .head(buildExcelHeaders(headers))
                    .sheet(fileName)
                    .doWrite(buildExcelData(data, headers));

        } catch (Exception e) {
            log.error("导出Excel失败", e);
            throw new RuntimeException("导出Excel失败：" + e.getMessage());
        }
    }

    /**
     * 构建EasyExcel表头
     */
    private List<List<String>> buildExcelHeaders(List<String> headers) {
        List<List<String>> excelHeaders = new ArrayList<>();
        for (String header : headers) {
            List<String> headerRow = new ArrayList<>();
            headerRow.add(header);
            excelHeaders.add(headerRow);
        }
        return excelHeaders;
    }

    /**
     * 构建EasyExcel数据
     */
    private List<List<Object>> buildExcelData(List<Map<String, Object>> data, List<String> headers) {
        List<List<Object>> excelData = new ArrayList<>();
        for (Map<String, Object> row : data) {
            List<Object> excelRow = new ArrayList<>();
            for (String header : headers) {
                Object value = row.get(header);
                excelRow.add(value != null ? value : "");
            }
            excelData.add(excelRow);
        }
        return excelData;
    }
}
