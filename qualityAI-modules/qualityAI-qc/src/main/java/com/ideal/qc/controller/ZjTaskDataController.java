package com.ideal.qc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.common.core.utils.StringUtils;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.ZjTaskData;
import com.ideal.qc.service.IZjTaskDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 质检源数据Controller
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@RestController
@RequestMapping("/taskData")
public class ZjTaskDataController extends BaseController
{
    @Autowired
    private IZjTaskDataService zjTaskDataService;
    /**
     * 查询质检源数据列表
     */
    @RequiresPermissions("quality:taskData:list")
    @GetMapping("/list")
    public TableDataInfo list(ZjTaskData zjTaskData)
    {
        startPage();
        List<ZjTaskData> list = zjTaskDataService.selectZjTaskDataList(zjTaskData);
        return getDataTable(list);
    }

    /**
     * 导出质检源数据列表
     */
    @RequiresPermissions("quality:taskData:export")
    @Log(title = "质检源数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZjTaskData zjTaskData)
    {
        List<ZjTaskData> list = zjTaskDataService.selectZjTaskDataList(zjTaskData);
        ExcelUtil<ZjTaskData> util = new ExcelUtil<ZjTaskData>(ZjTaskData.class);
        util.exportExcel(response, list, "质检源数据数据");
    }

    /**
     * 获取质检源数据详细信息
     */
    @GetMapping(value = "/getVideoText/{onlyNo}")
    public AjaxResult getVideoText(@PathVariable("onlyNo") String onlyNo)
    {
        String text = zjTaskDataService.getVideoText(onlyNo);
        if(StringUtils.isNotEmpty(text)){
            List<String> keys = zjTaskDataService.getVideoTextMatchKey(onlyNo);
            JSONObject json = JSONObject.parseObject(text);
            String videoUrl = json.getString("wavCid");
            JSONArray msgList = json.getJSONArray("sentenceArray");
            JSONObject result = new JSONObject();
            result.put("videoUrl",videoUrl);
            result.put("msgList",msgList);
            result.put("keys",keys);
            return success(result);
        }else{
            return error("没有转译文本，请联系管理员");
        }
    }
    /**
     * 获取录音信息
     */
    @RequiresPermissions("quality:taskData:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(zjTaskDataService.selectZjTaskDataById(id));
    }
}
