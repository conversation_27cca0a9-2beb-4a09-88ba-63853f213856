<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ZjQuestionnaireMapper">
    
    <resultMap type="com.ideal.qc.domain.ZjQuestionnaire" id="ZjQuestionnaireResult">
        <result property="id"    column="id"    />
        <result property="qsNo"    column="qs_no"    />
        <result property="qsName"    column="qs_name"    />
        <result property="qsType"    column="qs_type"    />
        <result property="qsDes"    column="qs_des"    />
        <result property="isValid"    column="is_valid"    />
        <result property="tenantCode"    column="tenant_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />

        <collection property="questionList" ofType="com.ideal.qc.domain.Question" column="qs_no" select="getQuestionList"/>
    </resultMap>
    <resultMap type="com.ideal.qc.domain.Question" id="QuestionResult">
        <id     property="id"        column="id"       />
        <result property="qusNo"      column="qus_no"     />
        <result property="qusName"      column="qus_name"     />
        <result property="qusType"      column="qus_type"     />
        <result property="isValid"        column="is_valid"        />
        <result property="createBy"      column="create_by"     />
        <result property="createTime"    column="create_time"   />
        <result property="updateBy"      column="update_by"     />
        <result property="updateTime"    column="update_time"   />
        <result property="maxV"    column="max_v"   />
        <result property="minV"    column="min_v"   />
        <result property="qsSeq"    column="qs_seq"   />

        <collection property="options" ofType="com.ideal.qc.domain.QuestionAnswer" column="id" select="getOptions"/>
    </resultMap>
    <sql id="selectZjQuestionnaireVo">
        select id, qs_no, qs_name, qs_type, qs_des, is_valid, tenant_code, create_by, create_time, update_by, update_time from zj_questionnaire
    </sql>
    <select id="getOptions" resultType="com.ideal.qc.domain.QuestionAnswer">
        select question_id questionId,answer,seq from zj_question_answer where question_id=#{id} order by seq
    </select>
    <select id="getQuestionList" resultMap="QuestionResult">
        select q.id, q.qus_no, q.qus_name, q.qus_type, q.is_valid, q.create_by, q.create_time,q.update_by,q.update_time,q.max_v,q.min_v,
            s.qs_seq
        from zj_question q left join zj_questionnaire_question s on s.qs_no=q.qus_no
        where q.is_valid='0' and s.qsn_no=#{qs_no}
    </select>
    <select id="selectZjQuestionnaireList" parameterType="ZjQuestionnaire" resultMap="ZjQuestionnaireResult">
        <include refid="selectZjQuestionnaireVo"/>
        <where>  
            <if test="qsNo != null  and qsNo != ''"> and qs_no = #{qsNo}</if>
            <if test="qsName != null  and qsName != ''"> and qs_name like concat('%', #{qsName}, '%')</if>
            <if test="qsType != null  and qsType != ''"> and qs_type = #{qsType}</if>
            <if test="qsDes != null  and qsDes != ''"> and qs_des = #{qsDes}</if>
            <if test="isValid != null  and isValid != ''"> and is_valid = #{isValid}</if>
            <if test="tenantCode != null  and tenantCode != ''"> and tenant_code = #{tenantCode}</if>
        </where>
    </select>
    
    <select id="selectZjQuestionnaireById" parameterType="Long" resultMap="ZjQuestionnaireResult">
        <include refid="selectZjQuestionnaireVo"/>
        where id = #{id}
    </select>

    <insert id="insertZjQuestionnaire" parameterType="ZjQuestionnaire" useGeneratedKeys="true" keyProperty="id">
        insert into zj_questionnaire
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="qsNo != null">qs_no,</if>
            <if test="qsName != null">qs_name,</if>
            <if test="qsType != null">qs_type,</if>
            <if test="qsDes != null">qs_des,</if>
            <if test="isValid != null">is_valid,</if>
            <if test="tenantCode != null">tenant_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="qsNo != null">#{qsNo},</if>
            <if test="qsName != null">#{qsName},</if>
            <if test="qsType != null">#{qsType},</if>
            <if test="qsDes != null">#{qsDes},</if>
            <if test="isValid != null">#{isValid},</if>
            <if test="tenantCode != null">#{tenantCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateZjQuestionnaire" parameterType="ZjQuestionnaire">
        update zj_questionnaire
        <trim prefix="SET" suffixOverrides=",">
            <if test="qsNo != null">qs_no = #{qsNo},</if>
            <if test="qsName != null">qs_name = #{qsName},</if>
            <if test="qsType != null">qs_type = #{qsType},</if>
            <if test="qsDes != null">qs_des = #{qsDes},</if>
            <if test="isValid != null">is_valid = #{isValid},</if>
            <if test="tenantCode != null">tenant_code = #{tenantCode},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZjQuestionnaireById" parameterType="Long">
        delete from zj_questionnaire where id = #{id}
    </delete>

    <delete id="deleteZjQuestionnaireByIds" parameterType="String">
        delete from zj_questionnaire where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="delQsQuetion" parameterType="String">
        delete from zj_questionnaire_question where qsn_no=#{value}
    </delete>

    <insert id="saveQsQuetion" parameterType="Map">
        insert into zj_questionnaire_question(qsn_no,qs_no,qs_seq,tenant_code)
        values (#{qsnNo},#{qsNo},#{qsSeq},#{tenantCode})
    </insert>
</mapper>