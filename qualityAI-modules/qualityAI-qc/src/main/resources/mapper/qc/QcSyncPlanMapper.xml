<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcSyncPlanMapper">
    
    <resultMap type="QcSyncPlan" id="QcSyncPlanResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="dataType"    column="data_type"    />
        <result property="planType"    column="plan_type"    />
        <result property="cronExpr"    column="cron_expr"    />
        <result property="windowStart"    column="window_start"    />
        <result property="windowEnd"    column="window_end"    />
        <result property="apiId"    column="api_id"    />
        <result property="needAsr"    column="need_asr"    />
        <result property="channelMode"    column="channel_mode"    />
        <result property="hotwordPkg"    column="hotword_pkg"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectQcSyncPlanVo">
        select id, name, data_type, plan_type, cron_expr, window_start, window_end, api_id, need_asr, channel_mode, hotword_pkg, is_enabled, tenant_id, create_time from qc_sync_plan
    </sql>

    <select id="selectQcSyncPlanList" parameterType="QcSyncPlan" resultMap="QcSyncPlanResult">
        <include refid="selectQcSyncPlanVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="dataType != null  and dataType != ''"> and data_type = #{dataType}</if>
            <if test="planType != null  and planType != ''"> and plan_type = #{planType}</if>
            <if test="cronExpr != null  and cronExpr != ''"> and cron_expr = #{cronExpr}</if>
            <if test="windowStart != null  and windowStart != ''"> and window_start = #{windowStart}</if>
            <if test="windowEnd != null  and windowEnd != ''"> and window_end = #{windowEnd}</if>
            <if test="apiId != null "> and api_id = #{apiId}</if>
            <if test="needAsr != null "> and need_asr = #{needAsr}</if>
            <if test="channelMode != null  and channelMode != ''"> and channel_mode = #{channelMode}</if>
            <if test="hotwordPkg != null  and hotwordPkg != ''"> and hotword_pkg = #{hotwordPkg}</if>
            <if test="isEnabled != null "> and is_enabled = #{isEnabled}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
        </where>
    </select>
    
    <select id="selectQcSyncPlanById" parameterType="Long" resultMap="QcSyncPlanResult">
        <include refid="selectQcSyncPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcSyncPlan" parameterType="QcSyncPlan" useGeneratedKeys="true" keyProperty="id">
        insert into qc_sync_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="dataType != null and dataType != ''">data_type,</if>
            <if test="planType != null and planType != ''">plan_type,</if>
            <if test="cronExpr != null">cron_expr,</if>
            <if test="windowStart != null">window_start,</if>
            <if test="windowEnd != null">window_end,</if>
            <if test="apiId != null">api_id,</if>
            <if test="needAsr != null">need_asr,</if>
            <if test="channelMode != null">channel_mode,</if>
            <if test="hotwordPkg != null">hotword_pkg,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="dataType != null and dataType != ''">#{dataType},</if>
            <if test="planType != null and planType != ''">#{planType},</if>
            <if test="cronExpr != null">#{cronExpr},</if>
            <if test="windowStart != null">#{windowStart},</if>
            <if test="windowEnd != null">#{windowEnd},</if>
            <if test="apiId != null">#{apiId},</if>
            <if test="needAsr != null">#{needAsr},</if>
            <if test="channelMode != null">#{channelMode},</if>
            <if test="hotwordPkg != null">#{hotwordPkg},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateQcSyncPlan" parameterType="QcSyncPlan">
        update qc_sync_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="dataType != null and dataType != ''">data_type = #{dataType},</if>
            <if test="planType != null and planType != ''">plan_type = #{planType},</if>
            <if test="cronExpr != null">cron_expr = #{cronExpr},</if>
            <if test="windowStart != null">window_start = #{windowStart},</if>
            <if test="windowEnd != null">window_end = #{windowEnd},</if>
            <if test="apiId != null">api_id = #{apiId},</if>
            <if test="needAsr != null">need_asr = #{needAsr},</if>
            <if test="channelMode != null">channel_mode = #{channelMode},</if>
            <if test="hotwordPkg != null">hotword_pkg = #{hotwordPkg},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcSyncPlanById" parameterType="Long">
        delete from qc_sync_plan where id = #{id}
    </delete>

    <delete id="deleteQcSyncPlanByIds" parameterType="String">
        delete from qc_sync_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>