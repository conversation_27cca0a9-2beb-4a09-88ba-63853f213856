import request from '@/utils/request'

// 查询人工质检任务列表
export function manualTaskList(query) {
  return request({
    url: '/qc/manual/task/list',
    method: 'get',
    params: query
  })
}

// 分页查询人工质检任务列表
export function manualTaskPageList(query) {
  return request({
    url: '/qc/manual/task/list/page',
    method: 'get',
    params: query
  })
}

//禁用/启用任务
export function updateTaskStatus(data) {
  return request({
    url: '/qc/manual/task/enable/status',
    method: 'post',
    data: data
  })
}


//修改任务具体内容
export function updateTask(data) {
  return request({
    url: '/qc/manual/task/update',
    method: 'post',
    data: data
  })
}


//新增任务
export function addTask(data) {
  return request({
    url: '/qc/manual/task/add',
    method: 'post',
    data: data
  })
}

//获取所有已完成的智能质检任务
export function getSmartTaskList(query) {
  return request({
    url: '/qc/manual/task/smart/task/list',
    method: 'get',
    params: query
  })
}

// 根据id查询智能质检任务
export function getSmartTaskById(id) {
  return request({
    url: '/qc/manual/task/smart/task/get/' + id,
    method: 'get'
  })
}


// 根据任务id获取当前任务的已完成数量及期限完成时间
export function getCountAndTime(id) {
  return request({
    url: '/qc/manual/task/finish/count/' + id,
    method: 'get'
  })
}

//根据条件查询任务下所有详情
export function detailList(query,data) {
  return request({
    url: '/qc/manual/task/detail/list',
    method: 'post',
    params: query,
    data: data
  })
}

// 获取人工质检结果表中的所有命中规则、命中的评分项
export function getRulesFromManualResult(query) {
  return request({
    url: '/qc/manual/task/result/rule/list',
    method: 'get',
    params: query
  })
}

// 获取人工质检结果表中的所有命中规则、命中的评分项
export function getDetail(id) {
  return request({
    url: '/qc/manual/task/detail/'+id,
    method: 'get'
  })
}

export function manualResultList(data) {
  return request({
    url: '/qc/qcBICheck/manualResultList',
    method: 'post',
	data:data
  })
}
