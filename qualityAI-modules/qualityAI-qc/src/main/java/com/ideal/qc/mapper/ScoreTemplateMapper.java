package com.ideal.qc.mapper;

import java.util.List;
import com.ideal.qc.domain.ScoreTemplate;

/**
 * 评分模板Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface ScoreTemplateMapper
{
    /**
     * 查询评分模板
     *
     * @param id 评分模板主键
     * @return 评分模板
     */
    public ScoreTemplate selectScoreTemplateById(Long id);

    /**
     * 查询评分模板列表
     *
     * @param scoreTemplate 评分模板
     * @return 评分模板集合
     */
    public List<ScoreTemplate> selectScoreTemplateList(ScoreTemplate scoreTemplate);

    /**
     * 新增评分模板
     *
     * @param scoreTemplate 评分模板
     * @return 结果
     */
    public int insertScoreTemplate(ScoreTemplate scoreTemplate);

    /**
     * 修改评分模板
     *
     * @param scoreTemplate 评分模板
     * @return 结果
     */
    public int updateScoreTemplate(ScoreTemplate scoreTemplate);

    /**
     * 删除评分模板
     *
     * @param id 评分模板主键
     * @return 结果
     */
    public int deleteScoreTemplateById(Long id);

    /**
     * 批量删除评分模板
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScoreTemplateByIds(Long[] ids);
}
