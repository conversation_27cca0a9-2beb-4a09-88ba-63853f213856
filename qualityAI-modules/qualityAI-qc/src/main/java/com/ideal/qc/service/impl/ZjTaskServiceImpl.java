package com.ideal.qc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ideal.common.core.utils.DateUtils;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.ZjDataAiresult;
import com.ideal.qc.domain.ZjDataTemplate;
import com.ideal.qc.domain.ZjTask;
import com.ideal.qc.domain.ZjTaskScoreData;
import com.ideal.qc.mapper.ZjDataMapper;
import com.ideal.qc.mapper.ZjDataTemplateMapper;
import com.ideal.qc.mapper.ZjTaskMapper;
import com.ideal.qc.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 质检任务Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ZjTaskServiceImpl implements IZjTaskService
{
    @Autowired
    private ZjTaskMapper zjTaskMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ZjDataMapper zjDataMapper;

    @Autowired
    private ZjDataTemplateMapper zjDataTemplateMapper;


    private final ILLMService  illmService;

    private final IQualityInspectionService iQualityInspectionService;

    private final IZjDataAiresultService iZjDataAiresultService;


    public ZjTaskServiceImpl(ILLMService illmService, IQualityInspectionService iQualityInspectionService, IZjDataAiresultService iZjDataAiresultService) {
        this.illmService = illmService;
        this.iQualityInspectionService = iQualityInspectionService;
        this.iZjDataAiresultService = iZjDataAiresultService;
    }


    /**
     * 查询质检任务
     *
     * @param taskId 质检任务主键
     * @return 质检任务
     */
    @Override
    public ZjTask selectZjTaskByTaskId(Long taskId)
    {
        return zjTaskMapper.selectZjTaskByTaskId(taskId);
    }

    /**
     * 查询质检任务列表
     *
     * @param zjTask 质检任务
     * @return 质检任务
     */
    @Override
    public List<ZjTask> selectZjTaskList(ZjTask zjTask)
    {
        return zjTaskMapper.selectZjTaskList(zjTask);
    }

    /**
     * 新增质检任务
     *
     * @param zjTask 质检任务
     * @return 结果
     */
    @Override
    public int insertZjTask(ZjTask zjTask)
    {
        zjTask.setCreateTime(DateUtils.getNowDate());
        return zjTaskMapper.insertZjTask(zjTask);
    }

    /**
     * 质检数据关联模板
     * @param zjTask
     */
    @Override
    public void insertZjDataTemplate(ZjTask zjTask) {
        zjTask.setCreateTime(DateUtils.getNowDate());
        zjTask.setCreateBy(SecurityUtils.getUsername());
        zjTaskMapper.insertZjDataTemplate(zjTask);
    }

    /**
     * 修改质检任务
     *
     * @param zjTask 质检任务
     * @return 结果
     */
    @Override
    public int updateZjTask(ZjTask zjTask)
    {
        return zjTaskMapper.updateZjTask(zjTask);
    }

    /**
     * 批量删除质检任务
     *
     * @param taskIds 需要删除的质检任务主键
     * @return 结果
     */
    @Override
    public int deleteZjTaskByTaskIds(Long[] taskIds)
    {
        return zjTaskMapper.deleteZjTaskByTaskIds(taskIds);
    }

    /**
     * 删除质检任务信息
     *
     * @param taskId 质检任务主键
     * @return 结果
     */
    @Override
    public int deleteZjTaskByTaskId(Long taskId)
    {
        return zjTaskMapper.deleteZjTaskByTaskId(taskId);
    }

    @Override
    public void startTask(ZjTask zjTask) {
        try {
            //查询对话
            ZjDataTemplate zjDataTemplate=new ZjDataTemplate();
            zjDataTemplate.setZjTaskId(zjTask.getTaskId());

            List<ZjDataTemplate> zjDataTemplateList=zjDataTemplateMapper.selectZjDataTemplateList(zjDataTemplate);

            for (ZjDataTemplate zjData:zjDataTemplateList){
                String zjDataId=zjData.getDataId();
                List<ZjTaskScoreData> zjTaskScoreData=zjTaskMapper.getZjTaskScoreData(zjDataId);
                for (ZjTaskScoreData data : zjTaskScoreData) {
                    try {
                        log.info("类型："+data.getDetectionMode());
                        if ("PATTERN".equals(data.getDetectionMode())) { // 获取类型
                            log.info("开始："+data.getNature());
                            JSONObject regexResult=new JSONObject();
                            log.info("调用规则=");
                            regexResult=iQualityInspectionService.regexRule(data.getZjScoreItemId(), JSONObject.parseObject(data.getContent()),NumberUtils.toLong(data.getCallId(), 0L));
                            log.info("规则评分请求返回：" + regexResult.toString());
                            log.info("result:"+regexResult.getBoolean("result"));
                            boolean result = regexResult.getBoolean("result");
                            // 检查终止条件
                            if ("0".equals(data.getNature()) && result) {
                                log.info("触发致命项，循环结束");
                                ZjDataAiresult zjDataAiresult=new ZjDataAiresult();
                                zjDataAiresult.setDataNo(data.getCallId());
                                zjDataAiresult.setTemplateId(data.getTemplateId());
                                zjDataAiresult.setCreateTime(DateUtils.getNowDate());
                                zjDataAiresult.setItemId(data.getZjScoreItemId());
                                zjDataAiresult.setAiResult("废卷");
                                iZjDataAiresultService.insertZjDataAiresult(zjDataAiresult);
                                //更新状态
                                ZjDataTemplate zjDataTemplateNew=new ZjDataTemplate();
                                zjDataTemplateNew.setZjStatus("1");
                                zjDataTemplateNew.setDataId(zjDataId);
                                zjDataTemplateMapper.updateZjDataTemplateStatus(zjDataTemplateNew);
                                break; // 终止循环
                            }else if (!"0".equals(data.getNature()) && result) {
                                log.info("非致命项，循环继续");
                                log.info("result:"+regexResult.getBoolean("result"));
                                ZjDataAiresult zjDataAiresult=new ZjDataAiresult();
                                zjDataAiresult.setDataNo(data.getCallId());
                                zjDataAiresult.setTemplateId(data.getTemplateId());
                                zjDataAiresult.setCreateTime(DateUtils.getNowDate());
                                zjDataAiresult.setItemId(data.getZjScoreItemId());
                                zjDataAiresult.setAiResult("待修改");
                                iZjDataAiresultService.insertZjDataAiresult(zjDataAiresult);
                                //更新状态
                                ZjDataTemplate zjDataTemplateNew=new ZjDataTemplate();
                                zjDataTemplateNew.setZjStatus("1");
                                zjDataTemplateNew.setDataId(zjDataId);
                                zjDataTemplateMapper.updateZjDataTemplateStatus(zjDataTemplateNew);
                            }else{
                                log.info("合格");
                                log.info("result:"+regexResult.getBoolean("result"));
                                ZjDataAiresult zjDataAiresult=new ZjDataAiresult();
                                zjDataAiresult.setDataNo(data.getCallId());
                                zjDataAiresult.setTemplateId(data.getTemplateId());
                                zjDataAiresult.setCreateTime(DateUtils.getNowDate());
                                zjDataAiresult.setItemId(data.getZjScoreItemId());
                                zjDataAiresult.setAiResult("合格");
                                iZjDataAiresultService.insertZjDataAiresult(zjDataAiresult);
                                //更新状态
                                ZjDataTemplate zjDataTemplateNew=new ZjDataTemplate();
                                zjDataTemplateNew.setZjStatus("1");
                                zjDataTemplateNew.setDataId(zjDataId);
                                zjDataTemplateMapper.updateZjDataTemplateStatus(zjDataTemplateNew);
                            }

                        } else {
                            //调用AI大模型
                            AjaxResult ajaxResult= illmService.generateText(data.getRuleId(),data.getCallId(),data.getDetectionContent(),data.getContent());
                            log.info("AI处理数据：" + ajaxResult);
                            if(ajaxResult.isSuccess()){
                                String aiData = ajaxResult.get("data").toString(); // 假设返回数据在"data"字段
                                if ("0".equals(data.getNature()) && aiData=="是") {
                                    ZjDataAiresult zjDataAiresult=new ZjDataAiresult();
                                    zjDataAiresult.setDataNo(data.getCallId());
                                    zjDataAiresult.setTemplateId(data.getTemplateId());
                                    zjDataAiresult.setCreateTime(DateUtils.getNowDate());
                                    zjDataAiresult.setItemId(data.getZjScoreItemId());
                                    zjDataAiresult.setAiResult("废卷");
                                    iZjDataAiresultService.insertZjDataAiresult(zjDataAiresult);
                                    //更新状态
                                    ZjDataTemplate zjDataTemplateNew=new ZjDataTemplate();
                                    zjDataTemplateNew.setZjStatus("1");
                                    zjDataTemplateNew.setDataId(zjDataId);
                                    zjDataTemplateMapper.updateZjDataTemplateStatus(zjDataTemplateNew);

                                }else if (!"0".equals(data.getNature()) && aiData=="是") {
                                    ZjDataAiresult zjDataAiresult=new ZjDataAiresult();
                                    zjDataAiresult.setDataNo(data.getCallId());
                                    zjDataAiresult.setTemplateId(data.getTemplateId());
                                    zjDataAiresult.setCreateTime(DateUtils.getNowDate());
                                    zjDataAiresult.setItemId(data.getZjScoreItemId());
                                    zjDataAiresult.setAiResult("待修改");
                                    iZjDataAiresultService.insertZjDataAiresult(zjDataAiresult);
                                    //更新状态
                                    ZjDataTemplate zjDataTemplateNew=new ZjDataTemplate();
                                    zjDataTemplateNew.setZjStatus("1");
                                    zjDataTemplateNew.setDataId(zjDataId);
                                    zjDataTemplateMapper.updateZjDataTemplateStatus(zjDataTemplateNew);

                                }else{
                                    ZjDataAiresult zjDataAiresult=new ZjDataAiresult();
                                    zjDataAiresult.setDataNo(data.getCallId());
                                    zjDataAiresult.setTemplateId(data.getTemplateId());
                                    zjDataAiresult.setCreateTime(DateUtils.getNowDate());
                                    zjDataAiresult.setItemId(data.getZjScoreItemId());
                                    zjDataAiresult.setAiResult("合格");
                                    iZjDataAiresultService.insertZjDataAiresult(zjDataAiresult);
                                    //更新状态
                                    ZjDataTemplate zjDataTemplateNew=new ZjDataTemplate();
                                    zjDataTemplateNew.setZjStatus("1");
                                    zjDataTemplateNew.setDataId(zjDataId);
                                    zjDataTemplateMapper.updateZjDataTemplateStatus(zjDataTemplateNew);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理任务失败: " + data, e);
                        continue;
                    }
                }
            }
        }catch (Exception e){
            log.info(e.getMessage());
        }
    }


    private String processRequestWithTimeout(ZjTaskScoreData data, long timeout, TimeUnit unit)
            throws Exception {

        ExecutorService executor = Executors.newSingleThreadExecutor();
        Future<String> future = executor.submit(() -> processPatternRequest(data));
        try {
            return future.get(timeout, unit);
        } finally {
            future.cancel(true);
            executor.shutdown();
        }
    }

    private String processPatternRequest(ZjTaskScoreData data) {
        String url = "https://api.example.com/create";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("name", "test");
        requestBody.put("value", 123);
        // 可以根据data添加更多参数
        // requestBody.put("taskId", data.getId());

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

        // 可以添加响应状态检查
        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("API请求失败: " + response.getStatusCode());
        }

        return response.getBody();
    }
}
