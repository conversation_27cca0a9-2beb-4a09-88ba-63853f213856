package com.ideal.qc.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.ZjData;
import com.ideal.qc.service.IZjDataService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 质检源数据Controller
 * 
 * <AUTHOR>
 * @date 2025-03-27
 */
@RestController
@RequestMapping("/data")
public class ZjDataController extends BaseController
{
    @Autowired
    private IZjDataService zjDataService;

    /**
     * 查询质检源数据列表
     */
    @RequiresPermissions("quality:data:list")
    @GetMapping("/list")
    public TableDataInfo list(ZjData zjData)
    {
        startPage();
        List<ZjData> list = zjDataService.selectZjDataList(zjData);
        return getDataTable(list);
    }

    /**
     * 导出质检源数据列表
     */
    @RequiresPermissions("quality:data:export")
    @Log(title = "质检源数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZjData zjData)
    {
        List<ZjData> list = zjDataService.selectZjDataList(zjData);
        ExcelUtil<ZjData> util = new ExcelUtil<ZjData>(ZjData.class);
        util.exportExcel(response, list, "质检源数据数据");
    }

    /**
     * 获取质检源数据详细信息
     */
    @GetMapping(value = "/getVideoText/{onlyNo}")
    public AjaxResult getVideoText(@PathVariable("onlyNo") String onlyNo)
    {
        String text = zjDataService.getVideoText(onlyNo);
        if(StringUtils.isNotEmpty(text)){
            List<String> keys = zjDataService.getVideoTextMatchKey(onlyNo);
            JSONObject json = JSONObject.parseObject(text);
            String videoUrl = json.getString("wavCid");
            JSONArray msgList = json.getJSONArray("sentenceArray");
            JSONObject result = new JSONObject();
            result.put("videoUrl",videoUrl);
            result.put("msgList",msgList);
            result.put("keys",keys);
            return success(result);
        }else{
            return error("没有转译文本，请联系管理员");
        }
    }
    /**
     * 获取录音信息
     */
    @RequiresPermissions("quality:data:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(zjDataService.selectZjDataById(id));
    }
    /**
     * 新增质检源数据
     */
    @RequiresPermissions("quality:data:add")
    @Log(title = "质检源数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZjData zjData)
    {
        return toAjax(zjDataService.insertZjData(zjData));
    }

    /**
     * 修改质检源数据
     */
    @RequiresPermissions("quality:data:edit")
    @Log(title = "质检源数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZjData zjData)
    {
        return toAjax(zjDataService.updateZjData(zjData));
    }

    /**
     * 删除质检源数据
     */
    @RequiresPermissions("quality:data:remove")
    @Log(title = "质检源数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zjDataService.deleteZjDataByIds(ids));
    }
}
