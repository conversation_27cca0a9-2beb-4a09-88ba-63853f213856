<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ZjTaskDataMapper">
    <resultMap type="com.ideal.qc.domain.ZjTaskData" id="ZjTaskDataResult">
        <result property="id"    column="id"    />
        <result property="platform"    column="platform"    />
        <result property="project"    column="project"    />
        <result property="targetName"    column="target_name"    />
        <result property="targetSubName"    column="target_sub_name"    />
        <result property="serviceCenter"    column="service_center"    />
        <result property="workNo"    column="work_no"    />
        <result property="workName"    column="work_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="longTime"    column="long_time"    />
        <result property="dataDate"    column="data_date"    />
        <result property="qsNo"    column="qs_no"    />
        <result property="telephone"    column="telephone"    />
        <result property="businessType"    column="business_type"    />
        <result property="serviceProvider"    column="service_provider"    />
        <result property="onlyNo"    column="only_no"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="area"    column="area"    />
        <result property="productType"    column="product_type"    />
        <result property="serviceOrderNo"    column="service_order_no"    />
        <result property="serviceType"    column="service_type"    />
        <result property="completionTime"    column="completion_time"    />
        <result property="productName"    column="product_name"    />
        <result property="saleName"    column="sale_name"    />
        <result property="seqNo"    column="seq_no"    />
        <result property="testMonth"    column="test_month"    />
        <result property="callResult"    column="call_result"    />
        <result property="taskResult"    column="task_result"    />
        <result property="audioUrl"    column="audio_url"    />
        <result property="incomingTime"    column="incoming_time"    />
        <result property="zjStatus"    column="zj_status"    />
        <result property="templateId"    column="templateId"    />

        <collection property="list" ofType="com.ideal.qc.domain.ZjDataEvaluation" column="id" select="getEvaluationList"/>
    </resultMap>
    <select id="getEvaluationList" resultType="com.ideal.qc.domain.ZjDataEvaluation">
        select id, question, answer, zj_data_id zjDataId, seq from zj_data_evaluation
        where zj_data_id = #{id} order by seq asc
    </select>
    <sql id="selectZjTaskDataVo">
        select id, platform, project, target_name, target_sub_name, service_center, work_no, work_name, start_time, end_time, long_time, data_date, qs_no, telephone, business_type,
               service_provider, only_no, province, city, contact_phone, area, product_type, service_order_no, service_type, completion_time, product_name, sale_name, seq_no,
               test_month, call_result, task_result, audio_url, incoming_time,
               (select zj_status from zj_data_template t where t.data_id = s.id) zj_status,
               (select zj_template_id from zj_data_template t where t.data_id = s.id) templateId
        from zj_data s
    </sql>

    <select id="selectZjTaskDataList" parameterType="ZjTaskData" resultMap="ZjTaskDataResult">
        <include refid="selectZjTaskDataVo"/>
        <where>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="project != null  and project != ''"> and project = #{project}</if>
            <if test="targetName != null  and targetName != ''"> and target_name like concat('%', #{targetName}, '%')</if>
            <if test="targetSubName != null  and targetSubName != ''"> and target_sub_name like concat('%', #{targetSubName}, '%')</if>
            <if test="serviceCenter != null  and serviceCenter != ''"> and service_center = #{serviceCenter}</if>
            <if test="workNo != null  and workNo != ''"> and work_no = #{workNo}</if>
            <if test="workName != null  and workName != ''"> and work_name like concat('%', #{workName}, '%')</if>
            <if test="startTime != null  and startTime != ''"> and start_time = #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and end_time = #{endTime}</if>
            <if test="longTime != null  and longTime != ''"> and long_time = #{longTime}</if>
            <if test="dataDate != null  and dataDate != ''"> and data_date = #{dataDate}</if>
            <if test="qsNo != null  and qsNo != ''"> and qs_no = #{qsNo}</if>
            <if test="telephone != null  and telephone != ''"> and telephone = #{telephone}</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="serviceProvider != null  and serviceProvider != ''"> and service_provider = #{serviceProvider}</if>
            <if test="onlyNo != null  and onlyNo != ''"> and only_no = #{onlyNo}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="serviceOrderNo != null  and serviceOrderNo != ''"> and service_order_no = #{serviceOrderNo}</if>
            <if test="serviceType != null  and serviceType != ''"> and service_type = #{serviceType}</if>
            <if test="completionTime != null  and completionTime != ''"> and completion_time = #{completionTime}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="saleName != null  and saleName != ''"> and sale_name like concat('%', #{saleName}, '%')</if>
            <if test="seqNo != null  and seqNo != ''"> and seq_no = #{seqNo}</if>
            <if test="testMonth != null  and testMonth != ''"> and test_month = #{testMonth}</if>
            <if test="callResult != null  and callResult != ''"> and call_result = #{callResult}</if>
            <if test="taskResult != null  and taskResult != ''"> and task_result = #{taskResult}</if>
            <if test="audioUrl != null  and audioUrl != ''"> and audio_url = #{audioUrl}</if>
            <if test="incomingTime != null  and incomingTime != ''"> and incoming_time = #{incomingTime}</if>
            <if test="zjTaskId != null  and zjTaskId != ''"> and
                exists(select * from zj_data_template t where t.data_id = s.id and t.zj_task_id=#{zjTaskId})
            </if>
        </where>
    </select>
    <select id="selectZjTaskDataById" parameterType="Long" resultMap="ZjTaskDataResult">
        <include refid="selectZjTaskDataVo"/>
        where id = #{id}
    </select>

    <select id="getVideoText" resultType="java.lang.String" parameterType="java.lang.String">
        select asr_result from zj_ai_book_asr_result s where only_no=#{value}
    </select>

    <select id="getVideoTextMatchKey" resultType="java.lang.String" parameterType="java.lang.String">
        select match_key from zj_data_airesult_matchkey s where data_no=#{value}
    </select>
</mapper>
