package com.ideal.qc.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcHotWord;
import com.ideal.qc.service.IQcHotWordService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * ASR热词配置Controller
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/hotWord")
public class QcHotWordController extends BaseController
{
    @Autowired
    private IQcHotWordService qcHotWordService;

    /**
     * 查询ASR热词配置列表
     */
    @RequiresPermissions("quality:hotWord:list")
    @GetMapping("/list")
    public TableDataInfo list(QcHotWord qcHotWord)
    {
        startPage();
        List<QcHotWord> list = qcHotWordService.selectQcHotWordList(qcHotWord);
        return getDataTable(list);
    }

    /**
     * 查询ASR热词配置树结构
     */
    @RequiresPermissions("quality:hotWord:list")
    @GetMapping("/tree")
    public AjaxResult tree(QcHotWord qcHotWord)
    {
        List<QcHotWord> list = qcHotWordService.selectQcHotWordTree(qcHotWord);
        return success(list);
    }

    /**
     * 导出ASR热词配置列表
     */
    @RequiresPermissions("quality:hotWord:export")
    @Log(title = "ASR热词配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QcHotWord qcHotWord)
    {
        List<QcHotWord> list = qcHotWordService.selectQcHotWordList(qcHotWord);
        ExcelUtil<QcHotWord> util = new ExcelUtil<QcHotWord>(QcHotWord.class);
        util.exportExcel(response, list, "ASR热词配置数据");
    }

    /**
     * 获取ASR热词配置详细信息
     */
    @RequiresPermissions("quality:hotWord:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(qcHotWordService.selectQcHotWordById(id));
    }

    /**
     * 新增ASR热词配置
     */
    @RequiresPermissions("quality:hotWord:add")
    @Log(title = "ASR热词配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QcHotWord qcHotWord)
    {
        if ("0".equals(qcHotWordService.checkWordNameUnique(qcHotWord)))
        {
            return toAjax(qcHotWordService.insertQcHotWord(qcHotWord));
        }
        return error("新增热词'" + qcHotWord.getWordName() + "'失败，热词名称已存在");
    }

    /**
     * 修改ASR热词配置
     */
    @RequiresPermissions("quality:hotWord:edit")
    @Log(title = "ASR热词配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QcHotWord qcHotWord)
    {
        if ("0".equals(qcHotWordService.checkWordNameUnique(qcHotWord)))
        {
            return toAjax(qcHotWordService.updateQcHotWord(qcHotWord));
        }
        return error("修改热词'" + qcHotWord.getWordName() + "'失败，热词名称已存在");
    }

    /**
     * 删除ASR热词配置
     */
    @RequiresPermissions("quality:hotWord:remove")
    @Log(title = "ASR热词配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(qcHotWordService.deleteQcHotWordByIds(ids));
    }

    /**
     * 获取启用的热词字符串
     */
    @GetMapping("/enabled")
    public AjaxResult getEnabledHotWords()
    {
        String hotWords = qcHotWordService.getEnabledHotWordsString();
        return success(hotWords);
    }

    /**
     * 校验热词名称
     */
    @PostMapping("/checkWordNameUnique")
    public AjaxResult checkWordNameUnique(@RequestBody QcHotWord qcHotWord)
    {
        return success(qcHotWordService.checkWordNameUnique(qcHotWord));
    }
}
