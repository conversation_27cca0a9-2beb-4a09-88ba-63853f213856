package com.ideal.qc.service.impl;

import java.util.List;
import com.ideal.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.QcListenLogMapper;
import com.ideal.qc.domain.QcListenLog;
import com.ideal.qc.service.IQcListenLogService;

import javax.annotation.Resource;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
public class QcListenLogServiceImpl implements IQcListenLogService 
{
    @Resource
    private QcListenLogMapper qcListenLogMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public QcListenLog selectQcListenLogById(Long id)
    {
        return qcListenLogMapper.selectQcListenLogById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param qcListenLog 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<QcListenLog> selectQcListenLogList(QcListenLog qcListenLog)
    {
        return qcListenLogMapper.selectQcListenLogList(qcListenLog);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param qcListenLog 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertQcListenLog(QcListenLog qcListenLog)
    {
        qcListenLog.setCreateTime(DateUtils.getNowDate());
        return qcListenLogMapper.insertQcListenLog(qcListenLog);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param qcListenLog 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateQcListenLog(QcListenLog qcListenLog)
    {
        return qcListenLogMapper.updateQcListenLog(qcListenLog);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteQcListenLogByIds(Long[] ids)
    {
        return qcListenLogMapper.deleteQcListenLogByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteQcListenLogById(Long id)
    {
        return qcListenLogMapper.deleteQcListenLogById(id);
    }
}
