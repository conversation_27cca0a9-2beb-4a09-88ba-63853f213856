package com.ideal.qc.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.constant.QcConstants;
import com.ideal.common.core.utils.DateUtils;
import com.ideal.common.core.utils.StringUtils;
import com.ideal.common.redis.service.RedisService;
import com.ideal.qc.domain.QcDatasetItem;
import com.ideal.qc.domain.QcSmartTaskDetail;
import com.ideal.qc.mapper.QcBizItemMapper;
import com.ideal.qc.mapper.QcDatasetItemMapper;
import com.ideal.qc.mapper.QcRuleMapper;
import com.ideal.qc.mapper.QcScoringTemplateMapper;
import com.ideal.qc.mapper.QcSmartTaskMapper;
import com.ideal.qc.service.IRuleConditionService;
import com.ideal.qc.utils.AsrUtils;

import lombok.extern.slf4j.Slf4j;


/**
 * 
 */
@Service
@Slf4j
public class RuleConditionServiceImpl implements IRuleConditionService{
	@Autowired
	private QcDatasetItemMapper qdiaMapper;
	@Autowired
	private QcSmartTaskMapper qstMapper;
	@Autowired
	private QcRuleMapper qrMapper;
	private SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	@Autowired
	private RedisService redisService;

	private JSONObject doInvoker(Object o,String method,Object[] params) {
		JSONObject result = new JSONObject();
		switch(method) {
			case "1":
				method = "doSemantics";
				break;
			case "2":
				method = "doKeyWord";
				break;
			case "3":
				method = "doKeyWordRepeat";
				break;
			case "4":
				method = "doKeyWord";
				break;
			case "5":
				method = "doRegular";
				break;
			case "6":
				method = "doSpeedRecog";
				break;
		}
		try {
			if(params != null) {
				Class[] c = new Class[params.length];
				for(int i=0;i<params.length;i++) {
					c[i] = params[i]==null?null:params[i].getClass();
				}
				result = (JSONObject)o.getClass().getMethod(method,c).invoke(o, params);
			}else {
				result = (JSONObject)o.getClass().getMethod(method, new Class[] {}).invoke(o);
			}
			
		}catch(Exception e) {
		}finally {
//			//System.out.println("------"+method+"----");
//			//System.out.println("------"+result.toString()+"----");
		}
		return result;
	}
	/**
	 * 语义规则匹配 
	 * @param param
	 * @return
	 */
	public JSONObject doSemantics(JSONObject bus,JSONObject sData,ArrayList<Map> sddList) {
		JSONObject ret = new JSONObject();
		Boolean isHit = null;
		List<Map> semResult = (List<Map>) sData.get("semResult");
		for(int i=0;i<semResult.size();i++) {
			String[] ids = ((String) semResult.get(i).get("ruleCondId")).split(",");
			for(int j=0;j<ids.length;j++) {
				if(bus.getString("condId").equals(ids[j])) {
					String result = (String) semResult.get(i).get("result");
					JSONObject rObj = new JSONObject();
					try {
						rObj = JSONObject.parseObject(result);
						double score = 0;
						for(Map sdd:sddList) {
							if(sdd.get("label").equals(rObj.getString("result"))) {
								score = Double.parseDouble((String) sdd.get("value"));
								break;
							}
						}
						if(score != 0) {
							isHit = true;
							JSONObject tmp = new JSONObject();
							tmp.put((String)semResult.get(i).get("matchRule"), rObj.getString("result"));
							ret.put("semResult", tmp);
						}
					}finally {
						
					}
					
					break;
				}
			}
			if(isHit != null) {
				break;
			}
		}
		if(isHit == null) {
			isHit = false;
		}
		ret.put("result", isHit);
		return ret;
	}
	/**
	 * 关键词匹配 
	 * @param param
	 * @return
	 */
	public JSONObject doKeyWord(JSONObject bus,JSONObject sData,JSONObject record) {
		JSONObject ret = new JSONObject();
		boolean isHit = false;
		JSONArray kls = bus.getJSONArray("dynamicTags");
		for(int i=0;i<kls.size();i++) {
			String tmp = (String)kls.get(i);
			String text = (String)AsrUtils.getAsrLabelValue(sData.getJSONObject(QcConstants.REDIS_ASR_CONF_KEY), record, QcConstants.REDIS_ASR_CONF_TEXT);
			if(text != null && text.indexOf(tmp) > -1) {
				isHit = true;
				break;
			}
		}
		ret.put("result", isHit);
		return ret;
	}
	
	/**
	 * 计算重复的关键字
	 * @param bus
	 * @param sData
	 * @param record
	 * @return
	 */
	public JSONObject doKeyWordRepeat(JSONObject bus,JSONObject sData,JSONObject record) {
		JSONObject ret = new JSONObject();
        Pattern pattern = Pattern.compile(bus.getString("keyWord"));
        String text = (String)AsrUtils.getAsrLabelValue(sData.getJSONObject(QcConstants.REDIS_ASR_CONF_KEY), record, QcConstants.REDIS_ASR_CONF_TEXT);
        Matcher matcher = pattern.matcher(text);
        int count = 0;
        while (matcher.find()) { // 查找所有匹配项
            count++;
        }
		ret.put("result", count);
		return ret;
	}
	public JSONObject doKeyWordLib(JSONObject bus,JSONObject sData,JSONObject record) {
		JSONObject ret = new JSONObject();
        boolean isHit = false;
		ret.put("result", isHit);
		return ret;
	}
	
	public JSONObject doRegular(JSONObject bus,JSONObject sData,JSONObject record) {
		JSONObject ret = new JSONObject();
        Pattern pattern = Pattern.compile(bus.getString("reg"));
        String text = (String)AsrUtils.getAsrLabelValue(sData.getJSONObject(QcConstants.REDIS_ASR_CONF_KEY), record, QcConstants.REDIS_ASR_CONF_TEXT);
        Matcher matcher = pattern.matcher(text);
        boolean isHit = false;
        if (matcher.matches()) { // 查找所有匹配项
        	isHit = true;
		}
		ret.put("result", isHit);
		return ret;
	}
	/**
	 * 判断语速
	 * @param bus
	 * @param sData
	 * @param record
	 * @return
	 */
	public JSONObject doSpeedRecog(JSONObject bus,JSONObject sData,JSONObject record) {
		JSONObject ret = new JSONObject();
		boolean isHit = false;
		double speed = bus.getDoubleValue("speed");
		double text = (double)AsrUtils.getAsrLabelValue(sData.getJSONObject(QcConstants.REDIS_ASR_CONF_KEY), record, QcConstants.REDIS_ASR_CONF_SPEED);
		if(text > speed) {
			isHit = true;
		}
		ret.put("result", isHit);
		return ret;
	}
	/**
	 * sData QcSmartTaskDetail
	 */
	@Override
	public JSONObject doTemplate(JSONObject sData,JSONObject jt) {
		//System.out.println(sData.toString());
		//判断语义规则start
		Map param = new HashMap();
		param.put("bizNo", sData.getString("bizNo"));
		param.put("taskDetId", sData.getString("id"));
//		param.put("status", "done");
		List<Map> semResult = qstMapper.queryQcSmartSemanticsResult(param);
		JSONObject upd = new JSONObject();
		if(semResult.size() > 0) {
			if(!"done".equals(semResult.get(0).get("status"))) {
				//语义规则任务未完成
				upd.put("id", sData.getLong("id"));
				upd.put("status", QcConstants.SMART_TASK_DETAIL_STATUS_AI_DOING);
				upd.put("updateBy", "system");
				qstMapper.updateQcSmartTaskDetail(upd);
				return null;
			}else {
				sData.put("semResult", semResult);
			}
		}
		//判断语义规则end
		QcDatasetItem qdi = new QcDatasetItem();
		qdi.setBizNo(sData.getString("bizNo"));
		List<QcDatasetItem> qdis = qdiaMapper.selectQcDatasetItemList(qdi);
		JSONArray ol0 = new JSONArray();
		JSONArray ol1 = new JSONArray();
		for(int j=0;j<qdis.size();j++) {
			try {
				String videoTxt = qdis.get(j).getAsrResult();
				JSONObject ot = JSONObject.parseObject(videoTxt);
				JSONArray oriL = (JSONArray)AsrUtils.getAsrLabelValue(sData.getJSONObject(QcConstants.REDIS_ASR_CONF_KEY), ot, QcConstants.REDIS_ASR_CONF_SENTENCEARRAY);
//				JSONArray oriL = ot.getJSONArray(sData.getJSONObject(QcConstants.REDIS_ASR_CONF_KEY).getString(QcConstants.REDIS_ASR_CONF_SENTENCEARRAY));
				for(int i=0;i<oriL.size();i++) {
					int spk = (int)AsrUtils.getAsrLabelValue(sData.getJSONObject(QcConstants.REDIS_ASR_CONF_KEY), oriL.getJSONObject(i), QcConstants.REDIS_ASR_CONF_SPK);
					if(1 == spk) {
						ol1.add(oriL.getJSONObject(i).fluentPut("index", ol1.size()));
					}else if(0 == spk) {
						ol0.add(oriL.getJSONObject(i).fluentPut("index", ol0.size()));
					}
				}
			}catch(Exception e) {}
		}
		sData.put("msgList0", ol0);
		sData.put("msgList1", ol1);
//		
		QcSmartTaskDetail qstd = new QcSmartTaskDetail();
		qstd.setId(sData.getLongValue("id"));
		JSONObject ret = new JSONObject();
		if(ol0.size() !=0 && ol1.size() !=0) {
			ret = doScoreTemplate(jt,sData,qrMapper.selectDictDataByType("qc_semantics_result_type"));
			//System.out.println("------jt---- "+jt);
			//System.out.println(ret.toJSONString());
			qstd.setMacInspectScore(ret.getDoubleValue("fScore"));
			qstd.setHitRuleCount(ret.getIntValue("hrCount"));
			qstd.setHitMinusRuleCount(ret.getIntValue("hmrCount"));
			qstd.setHitPlusRuleCount(ret.getIntValue("hprCount"));
			qstd.setHitItemCount(ret.getIntValue("sCount"));
			if(ret.containsKey("macInspectResult") && ret.getString("macInspectResult") != null && !"".equals(ret.getString("macInspectResult"))) {
				qstd.setMacInspectResult(ret.getString("macInspectResult"));
			}else if(semResult.size() != 0){
				qstd.setMacInspectResult("合格");
			}
		}
		//任务执行完成
		qstd.setStatus(QcConstants.SMART_TASK_DETAIL_STATUS_FINISH);
		qstd.setUpdateBy("admin");
		qstd.setTemplateHitRule(jt.toString());
		upd = JSONObject.parse(JSON.toJSONString(qstd));
		upd.put("macInspectTime", sf.format(new Date()));
//		qstMapper.updateQcSmartTaskDetail(qstd);
		if(ret.containsKey("detailList") && ret.getJSONArray("detailList").size() >0 ) {
			//插入详细明细数据
//			JSONObject dRet = new JSONObject();
			upd.put("detailList", ret.getJSONArray("detailList"));
			upd.put("taskDetailId", sData.getLongValue("id"));
			//智能任务明细重跑后，删除历史数据
			upd.put("delDetail", 1);
//			//System.out.println(dRet.toJSONString());
//			qstMapper.addQcSmartTaskResult(dRet);
		}
		qstMapper.updateQcSmartTaskDetail(upd);
		return ret;
	}
	public JSONObject doScoreTemplate(JSONObject temp,JSONObject sData,List sddList) {
		JSONObject ret = new JSONObject();
//		JSONObject hitL = new JSONObject();
		JSONArray sRet = new JSONArray();
//		JSONArray smRet = new JSONArray();
		int score = temp.getIntValue("basicScore");
//		JSONArray scores = temp.getJSONArray("qcScoringItemDTOList");
		JSONArray scores = temp.getJSONArray("children");
		int hrCount = 0;
		int sCount = 0;
		int hprCount = 0;
		int hmrCount = 0;
		JSONObject semObj = new JSONObject();
		if(scores != null && scores.size() >0 ) {
			for(int i=0;i<scores.size();i++) {
				JSONObject scoreObj = scores.getJSONObject(i);
				JSONObject sr = doScore(scoreObj,sData,sddList);
				if(sr.getBooleanValue("result")) {//命中筛选条件,不过滤
//					hitL = sr.getJSONObject("detail");
					scoreObj.put("isHit", true);
//					sr.put("sItemName", scoreObj.getString("scoringItemName"));
					sr.put("sItemName", scoreObj.getString("name"));
//					sRet.add(sr);
					JSONArray aTemp = sr.getJSONArray("detail");
					for(int j=0;j<aTemp.size();j++) {
						aTemp.getJSONObject(j).put("itemName", scoreObj.getString("name"));
						aTemp.getJSONObject(j).put("itemResult", "1");
					}
					sRet.addAll(aTemp);
//					smRet.addAll(sr.getJSONArray("detail"));
					score += sr.getIntValue("score");
					if(sr.getIntValue("score") <0) {
						hmrCount += sr.getIntValue("hrCount");
					}else {
						hprCount += sr.getIntValue("hrCount");
					}
					hrCount += sr.getIntValue("hrCount");
					sCount++;
					semObj.putAll(sr.getJSONObject("semObj"));
				}else {
					scoreObj.put("isHit", false);
				}
			}
		}
		ret.put("score", score);
		if(score > temp.getIntValue("highestScore")) {
			score = temp.getIntValue("highestScore");
		}else if(score < temp.getIntValue("lowestScore")) {
			score = temp.getIntValue("lowestScore");
		}
		ret.put("fScore", score);
//		ret.put("name", temp.getString("scoringTemplateName"));
		ret.put("macInspectResult", calculateScoreBySemantics(semObj, sddList).getString("result"));
		ret.put("name", temp.getString("name"));
		ret.put("hrCount", hrCount);
		ret.put("hmrCount", hmrCount);
		ret.put("hprCount", hprCount);
		ret.put("sCount", sCount);
		ret.put("detailList", sRet);
		return ret;
	}
	private JSONObject doScore(JSONObject score,JSONObject sData,List sddList) {
		JSONObject ret = new JSONObject();
		boolean result = false;
		JSONArray hitL = new JSONArray();
//		JSONArray hitRules = score.getJSONArray("qcScoringHitRuleGroupDTOList");
		JSONArray hitRules = score.getJSONArray("children");
		int hrCount = 0;
		double sd = 0;
		JSONObject tmp = new JSONObject();
		JSONObject semObj = new JSONObject();
		if(hitRules != null && hitRules.size() >0 && score.getIntValue("status") == 1) {
			for(int i=0;i<hitRules.size();i++) {
				JSONObject hitRule = hitRules.getJSONObject(i);
				hitRule.put("itemId", score.getLong("itemId"));
				JSONObject sr = doScoreCond(hitRule,sData,sddList);
				hrCount += sr.getIntValue("hrCount");
				if(sr.getBooleanValue("result")) {//命中筛选条件,不过滤
					result = true;
					hitL = sr.getJSONArray("detail");
					hitRule.put("isHit", true);
					semObj.putAll(sr.getJSONObject("semResult"));
					tmp = calculateScoreBySemantics(sr.getJSONObject("semResult"), sddList);
					sd = tmp.getDoubleValue("score");
					if(sd > score.getIntValue("hitRuleScore")) {
						sd = score.getIntValue("hitRuleScore");
					}
					break;
				}
				hitRule.put("isHit", false);
			}
			ret.put("semObj", semObj);
		}
		ret.put("result", result);
		ret.put("detail", result?hitL:new JSONArray());
		ret.put("score", result?sd:0);
		ret.put("hrCount", result?hrCount:0);
		return ret;
	}
	/**
	 * 根据规则判断会话是否命中
	 * @param item 评分条件
	 * @param sData 会话数据
	 * @return
	 */
	private JSONObject doScoreCond(JSONObject item,JSONObject sData,List sddList) {
		JSONObject ret = new JSONObject();
		boolean result = false;
		boolean isAll = false;
		if("1".equals(item.getString("ruleGroupMode"))) {
			isAll = true;
		}
//		JSONArray list = item.getJSONArray("qcScoringHitRuleItemList");
		JSONArray list = item.getJSONArray("children");
		JSONArray hitL = new JSONArray();
		JSONObject semResult = new JSONObject();
		int hrCount = 0;
		if(item.getIntValue("status") == 1) {
			
			for(int i=0;i<list.size();i++) {
				JSONObject tmp = list.getJSONObject(i);
				if("2".equals(tmp.getString("ruleClassification"))) {
					continue;
				}else if(tmp.getIntValue("status") != 1){
					continue;
				}
				JSONObject ruleConf = JSONObject.parseObject(redisService.getCacheMapValue("qcRule", tmp.getString("ruleId")));
				JSONObject lResult = doRuleFilter(ruleConf, sData,sddList);
				if(lResult.getBooleanValue("result")) {//命中
					result = true;
					if(!lResult.getJSONObject("semResult").isEmpty()) {
						semResult.putAll(lResult.getJSONObject("semResult"));
					}
					hrCount++;
//				hitL.putAll(lResult.getJSONObject("detail"));
					lResult.put("ruleId", tmp.getIntValue("ruleId"));
					lResult.put("ruleItemId", tmp.getIntValue("id"));
					lResult.put("itemId", tmp.getIntValue("itemId"));
					lResult.put("ruleName", ruleConf.getString("ruleName"));
					hitL.add(lResult);
					tmp.put("isHit", true);
					if(!isAll) {
						break;
					}
				}else {//未命中
					result = false;
					tmp.put("isHit", false);
					if(isAll) {
						break;
					}
				}
			}
		}
		ret.put("result", result);
		ret.put("detail", result?hitL:new JSONArray());
		ret.put("hrCount", result?hrCount:0);
		ret.put("semResult", semResult);
		return ret;
	}
	/**
	 * 根据规则判断会话是否命中
	 * @param qcRule 规则对象
	 * @param sData 会话数据
	 * @return
	 */
	private JSONObject doRuleFilter(JSONObject qcRule,JSONObject sData,List sddList) {
//		log.info("qc--"+qcRule);
		JSONObject ret = new JSONObject();
		ret.put("semResult", new JSONObject());
		boolean result = false;
		JSONArray filter = qcRule.getJSONArray("filterList");
		JSONArray hit = qcRule.getJSONArray("hitList");
		JSONObject semResult = new JSONObject();
		//规则命中条件是否包含语义识别
		boolean isContSemat = qcRule.getBooleanValue("containSemantics");
		if(isContSemat) {
			//判断语义任务是否完成
			
			return null;
		}
		JSONObject tmp = new JSONObject();
		if(qcRule.getIntValue("ruleType") == QcConstants.RULE_TYPE_SMART) {
			//System.out.println("--doSmartRule:"+qcRule.getIntValue("ruleId")+" "+qcRule.getString("ruleName"));
			//是否过滤该会话 默认过滤
			tmp = doRuleJudge(filter,"1",sData,null);
			if(!tmp.getBooleanValue("result")) {
				//不满足规则筛选条件
				return ret;
			}else {
				//判断规则命中条件
				tmp = doRuleJudge(hit,"2",sData,sddList);
				if(tmp.getBooleanValue("result")) {
					//命中该规则
					result = true;
					if(!tmp.getJSONObject("semResult").isEmpty()) {
						semResult.putAll(tmp.getJSONObject("semResult"));
					}
				}
			}
			//System.out.println("--doSmartRule end:"+qcRule.getIntValue("ruleId")+" "+tmp);
		}else if(qcRule.getIntValue("ruleType") == QcConstants.RULE_TYPE_FLOW){
			//System.out.println("--doFlowRule start :"+qcRule.getIntValue("ruleId")+" "+qcRule.getString("ruleName"));
			//System.out.println("--doFlowRule 筛选会话 start :");
			//是否过滤该会话 默认过滤
			tmp = doRuleJudge(hit,"2",sData,sddList);
			//System.out.println("--doFlowRule 筛选会话 end :" +tmp);
			if(!tmp.getBooleanValue("result")) {
				//不满足规则筛选条件
				//System.out.println("--doFlowRule:"+qcRule.getIntValue("ruleId")+" "+qcRule.getString("ruleName")+" " +tmp);
				return ret;
			}else {
				JSONArray sr = qcRule.getJSONArray("subList");
				for(int i=0;i<sr.size();i++) {
					//System.out.println("--doFlowRule subList "+i+" "+sr.getJSONObject(i));
					tmp = doRuleFilter(JSONObject.parseObject(redisService.getCacheMapValue("qcRule", sr.getJSONObject(i).getString("ruleId"))), sData,sddList);
					if(!tmp.getBooleanValue("result")) {
						//命中该规则
						result = false;
						if(!tmp.getJSONObject("semResult").isEmpty()) {
							semResult.putAll(tmp.getJSONObject("semResult"));
						}
						break;
					}
				}
			}
			//System.out.println("--doFlowRule end:"+qcRule.getIntValue("ruleId")+" "+qcRule.getString("ruleName"));
		}
		
		ret.put("result", result);
		ret.put("detail", result?tmp.getJSONObject("detail").toJSONString():"{}");
		ret.put("semResult", result?semResult.toString():"{}");
//		//System.out.println(tmp.toString());
//		log.info(tmp.toString());
		
		return ret;
	}
	/**
	 * 判断规则 满足任意子条件即命中
	 * @param qrcs
	 * @param condType
	 * @param sData 会话数据
	 * @return
	 */
	private JSONObject doRuleJudge(JSONArray qrcs,String condType,JSONObject sData,List sddList) {
		//System.out.println("---doRuleJudge start");
		JSONObject ret = new JSONObject();
		boolean result = false;
		JSONObject hitL = new JSONObject();
		JSONObject semResult = new JSONObject();
		if(qrcs != null && qrcs.size() >0 ) {
			for(int i=0;i<qrcs.size();i++) {
				JSONObject sr = new JSONObject();
				if("1".equals(condType)) {
					sr = doFilterBus(qrcs.getJSONObject(i).getJSONObject("ruleCondDetail"), sData);
				}else {
					sr = doCondJudge(qrcs.getJSONObject(i),condType,sData,sddList);
				}
				if(sr.getBooleanValue("result")) {//命中筛选条件,不过滤
					result = true;
					if(!sr.getJSONObject("semResult").isEmpty()) {
						semResult.putAll(sr.getJSONObject("semResult"));
					}
					hitL = sr.getJSONObject("detail");
					break;
				}
			}
		}else {
			result = true;
		}
		ret.put("result", result);
		ret.put("detail", hitL);
		ret.put("semResult", semResult);
		//System.out.println("---doRuleJudge end" +ret);
		return ret;
	}
	/**
	 * 判断是否命中条件
	 * or 命中任意条件 则返回true
	 * and 至少一条未命中 则返回false
	 * @param qcRuleCond 规则明细
	 * @param condType 条件类型 1 过滤 2 命中
	 * @param sData 会话数据
	 * @return
	 */
	private JSONObject doCondJudge(JSONObject qcRuleCond,String condType,JSONObject sData,List sddList) {
		JSONObject ret = new JSONObject();
		JSONObject rule = qcRuleCond.getJSONObject("JRuleCondDetail");
		boolean result = false;
		//0 任一(or) 1 全部(and)
		int isAll = rule.getIntValue("isAll");
		//System.out.println("----规则条件 doCondJudge start isAll--" + isAll);
		JSONArray subRules = rule.getJSONArray("options");
		//当前条件结果对象
		JSONObject lResult = null;
		JSONObject hitL = new JSONObject();
		JSONObject semResult = new JSONObject();
		for(int i=0;i<subRules.size();i++) {
			JSONObject subObj = subRules.getJSONObject(i);
			subObj.put("condId", qcRuleCond.getIntValue("id"));
			lResult = doSubCondJudge(subObj,condType,sData,lResult,sddList);
			if(lResult.getBooleanValue("result")) {//命中
				result = true;
				if(!lResult.getJSONObject("semResult").isEmpty()) {
					semResult.putAll(lResult.getJSONObject("semResult"));
				}
				hitL.putAll(arrToObj(lResult.getJSONArray("hitL"),sData));
				if(isAll == 0) {
					break;
				}
			}else {//未命中
				result = false;
				if(isAll == 1) {
					break;
				}
			}
		}
		ret.put("result", result);
		ret.put("detail", result?hitL:new JSONObject());
		ret.put("semResult", semResult);
		//System.out.println("----规则条件 doCondJudge end--"+ ret);
		return ret;
	}
	private JSONObject arrToObj(JSONArray arr,JSONObject sData) {
		JSONObject obj = new JSONObject();
		if(arr != null) {
			for(int i=0;i<arr.size();i++) {
				int spk = (int)AsrUtils.getAsrLabelValue(sData.getJSONObject(QcConstants.REDIS_ASR_CONF_KEY), arr.getJSONObject(i), QcConstants.REDIS_ASR_CONF_SPK);
				String text = (String)AsrUtils.getAsrLabelValue(sData.getJSONObject(QcConstants.REDIS_ASR_CONF_KEY), arr.getJSONObject(i), QcConstants.REDIS_ASR_CONF_TEXT);
				obj.put(arr.getJSONObject(i).getIntValue("index")+"", (spk==1?"【坐席】":"【客户】")+text);
			}
		}
		return obj;
	}
	/**
	 * 判断是否命中子条件
	 * @param busObj
	 * @param condType
	 * @param sData 会话数据
	 * @param lResult 上一个条件结果对象
	 * @return
	 */
	private JSONObject doSubCondJudge(JSONObject busObj,String condType,JSONObject sData,JSONObject lResult,List sddList) {
		//System.out.println("-----规则条件子项 doSubCondJudge start");
		//System.out.println("-----规则条件子项 doSubCondJudge id--" + busObj.getIntValue("id"));
//		if("1".equals(condType)) {//过滤条件
//			return doFilterBus(busObj,sData);
//		}else 
		if("2".equals(condType)) {//命中条件
			JSONObject ret = new JSONObject();
			String op = busObj.getString("op");
			String scene = busObj.getString("scene");
			int role = busObj.getIntValue("role");
			int rule = busObj.getIntValue("rule");
			ret.put("semResult", new JSONObject());
			boolean isHit = false;
			JSONArray dt = new JSONArray();
			if(rule == 1) {//语义规则
				JSONObject tmp = doInvoker(this, rule+"", new Object[] {busObj,sData,sddList});
				isHit = tmp.getBooleanValue("result");
				ret.put("semResult", tmp.getJSONObject("semResult"));
			}else {
				JSONArray meArr = sData.getJSONArray("msgList"+role);
				//System.out.println(meArr.toString());
				int beg = 0;
				int end = meArr.size()-1;
				int num = busObj.getIntValue("num");
				int[][] pos = new int[meArr.size()][2];
				JSONArray tl = null;
				switch(scene) {
				case "1":
					tl = meArr;
					pos[0][0] = beg;
					pos[0][1] = end;
					break;
				case "2":
					end = (num - 1) > end ? end : (num-1);
					pos[0][0] = beg;
					pos[0][1] = end;
					tl = meArr;
					break;
				case "3":
					beg = (end - num + 1) < beg? 0 : (end - num + 1);
					pos[0][0] = beg;
					pos[0][1] = end;
					tl = meArr;
					break;
				case "4":
				case "5":
					tl = meArr;
					pos = doFilterMsgList(lResult.getJSONArray("hitL"),meArr.size(),scene,num);
					break;
				case "6":
					tl = lResult.getJSONArray("hitL");
					end = tl.size()-1;
					pos[0][0] = beg;
					pos[0][1] = end;
					break;
				default:
					break;
				}
				if(lResult == null || role == lResult.getIntValue("role")){
					JSONObject sRet = null;
					int kwsCount = 0;
					for(int j=0;j<pos.length;j++) {
						if(j>0 && pos[j][1] ==0) {
							break;
						}
						for(int i=pos[j][0];i<=pos[j][1];i++) {
							if(i >= tl.size()) {
								break;
							}
							sRet = doInvoker(this, rule+"", new Object[] {busObj,sData,tl.getJSONObject(i)});
							if(3 == busObj.getIntValue("rule")) {
								if(sRet.getIntValue("result") > 0) {
									kwsCount += sRet.getIntValue("result");
									dt.add(tl.getJSONObject(i));
								}
							}else if(sRet.getBooleanValue("result")) {
								isHit = true;
								dt.add(tl.getJSONObject(i));
							}else {
								
							}
						}
					}
					if(3 == rule) {
						if(kwsCount > busObj.getIntValue("kwsCount")) {
							isHit = true;
						}else {
							dt = new JSONArray();
						}
					}
					
				}
			}
			ret.put("result", isHit);
			ret.put("hitL", dt);
			ret.put("role", role);
			//System.out.println("-----规则条件子项 doSubCondJudge end --" + ret);
			return ret;
		}
		return null;
	}
	/**
	 * 过滤会话消息
	 * @param param
	 * @param sData
	 * @return
	 */
	private int[][] doFilterMsgList(JSONArray list,int length,String flag,int count) {
		int[][] ret = new int[length][2];
		int cBeg = -1;
		int cEnd = length;
		int cIndex = -1;
		int index = 0;
		for(int i=0;i<list.size();i++) {
			JSONObject obt = list.getJSONObject(i);
			cIndex = obt.getIntValue("index");
			if("4".equals(flag)) {
				cIndex++;
				cBeg = cIndex ;
				cEnd = cIndex + count ;
			}else {
				cIndex--;
				cEnd = cIndex ;
				cBeg = cIndex - count;
			}
			if(index > 0) {
				if(ret[index-1][1] >= cBeg-1) {
					ret[index-1][1] = ret[index-1][1] >= cEnd?ret[index-1][1]:cEnd;
					if(ret[index-1][1] > length-1) {
						break;
					}
					continue;
				}
			}
			cBeg = cBeg < -1?0:cBeg;
			if(cEnd > length-1) {
				cEnd = length-1;
				ret[index][0] = cBeg;
				ret[index][1] = cEnd;
				break;
			}else {
				ret[index][0] = cBeg;
				ret[index][1] = cEnd;
			}
			index = index + 1;
		}
		return ret;
	}
	/**
	 * 过滤会话信息
	 * @param param 比较规则
	 * @param sData 会话数据
	 * @return
	 */
	@Override
	public JSONObject doFilterBus(JSONObject param,JSONObject sData) {
		JSONObject ret = new JSONObject();
		boolean isFilter = false;
		if(param.isEmpty()) {
			isFilter = true;
		}else {
			if(sData != null && !sData.isEmpty()) {
				boolean btmp = false;
				int isAll = param.getIntValue("isAll");
				//0 不包含 1 包含
				JSONArray ftList = param.getJSONArray("options");
				for(int i=0;i<ftList.size();i++) {
					JSONObject tmp = ftList.getJSONObject(i);
					btmp = doSubFilter(tmp, sData);
					if(btmp) {//命中
						isFilter = true;
						if(isAll == 0) {
							break;
						}
					}else {//未命中
						isFilter = false;
						if(isAll == 1) {
							break;
						}
					}		
				}
			}
		}
		ret.put("result", isFilter);
		return ret;
	}
	
	/**
	 * 过滤业务数据
	 * @param param 比较规则
	 * @param sData 业务数据
	 * @return
	 */
	private boolean doSubFilter(JSONObject param,JSONObject sData) {
		boolean ret = false;
		String op = param.getString("op");
		String key = param.getString("key");
		Integer kType = param.getIntValue("type");
		switch(op) {
			case "less":
//				if(2 == kType) {
//					ret = sData.getIntValue(key) < param.getIntValue("val1");
//				}else if(3 == kType) {
//					ret = DateUtils.compare(sData.getString(key),param.getString("val1")) <= 0;
//				}
//				break;
			case "more":
				if(2 == kType) {
					ret = sData.getIntValue(key) > param.getIntValue("val1");
				}else if(3 == kType) {
					ret = DateUtils.compare(sData.getString(key),param.getString("val1")) >= 0;
				}
				ret = "less".equals(op)?!ret:ret;
				break;
			case "equal":
				if(2 == kType) {
					ret = sData.getIntValue(key) == param.getIntValue("val1");
				}else if(3 == kType) {
					ret = DateUtils.compare(sData.getString(key),param.getString("val1")) == 0;
				}else if(1 == kType) {
					ret = param.getString("val1").equals(sData.getString(key));
				}
				break;
			case "in":
//				if(2 == kType) {
//					ret = sData.getIntValue(key) > param.getIntValue("val1") && sData.getIntValue(key) < param.getIntValue("val2");
//				}else if(3 == kType) {
//					ret = DateUtils.compare(sData.getString(key),param.getString("val1")) >= 0
//							&& DateUtils.compare(sData.getString(key),param.getString("val2")) <= 0;
//				}
//				break;
			case "nIn":
				if(2 == kType) {
					ret = sData.getIntValue(key) < param.getIntValue("val1") || sData.getIntValue(key) > param.getIntValue("val2");
				}else if(3 == kType) {
					ret = DateUtils.compare(sData.getString(key),param.getString("val1")) < 0
							|| DateUtils.compare(sData.getString(key),param.getString("val2")) > 0;
				}
				ret = "in".equals(op)?!ret:ret;
				break;
			case "contain":
//				if(StringUtils.isNotBlank(sData.getString(key))) {
//					for(int i=0;i<param.getJSONArray("inputKeys").size();i++) {
//						String tmp = param.getJSONArray("inputKeys").getString(i);
//						if(sData.getString(key).indexOf(tmp) > -1) {
//							ret = true;
//							break;
//						}
//					}
//				}
//				break;
			case "nContain":
				ret = true;
				if(StringUtils.isNotBlank(sData.getString(key))) {
					for(int i=0;i<param.getJSONArray("inputKeys").size();i++) {
						String tmp = param.getJSONArray("inputKeys").getString(i);
						if(sData.getString(key).indexOf(tmp) > -1) {
							ret = false;
							break;
						}
					}
				}
				ret = "contain".equals(op)?!ret:ret;
				break;
			default:
				break;
		}
		return ret;
	}
	/**
	 * 取分数最低的结果
	 * @param param 结果列表
	 * @param sddList 结果分数配置
	 * @return
	 */
	private JSONObject calculateScoreBySemantics(JSONObject param,List<Map> sddList) {
		double currScore = 0;
		String result = "";
		if(!param.isEmpty()) {
			for(String key : param.keySet()) {
				//key Semantics ruleId
				for(Map sdd:sddList) {
					if(sdd.get("label").equals(param.getString(key))) {
						if(currScore > Double.parseDouble((String) sdd.get("value"))) {
							currScore = Double.parseDouble((String) sdd.get("value"));
							result = (String) sdd.get("label");
							break;
						}
					}
				}
			}
		}
		JSONObject tmp = new JSONObject();
		tmp.put("score", currScore);
		tmp.put("result", result);
		return tmp;
	}
}
