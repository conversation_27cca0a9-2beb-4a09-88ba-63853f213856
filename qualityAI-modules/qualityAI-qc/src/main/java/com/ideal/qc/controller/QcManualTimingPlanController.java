package com.ideal.qc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.qc.domain.*;
import com.ideal.qc.mapper.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/manual/plan/timing")
public class QcManualTimingPlanController extends BaseController {

    @Autowired
    private QcSmartPlanMapper qcSmartPlanMapper;

    @Autowired
    private QcManualPlanMapper qcManualPlanMapper;

    @Autowired
    private QcSmartTaskMapper qcSmartTaskMapper;

    @Autowired
    private QcBizItemMapper qcBizItemMapper;

    @Autowired
    private QcManualTaskMapper qcManualTaskMapper;

    @Autowired
    private QcManualTaskDetailMapper qcManualTaskDetailMapper;

    @Autowired
    private QcScoringTemplateMapper qcScoringTemplateMapper;

    private static Logger logger = LoggerFactory.getLogger(QcManualTimingPlanController.class);

    /**
     *执行人工质检定时计划
     */
    @GetMapping("/exec")
    public AjaxResult getSmartPlanList(){
        executeTask();
        return success();
    }

    @Transactional
    public void executeTask(){
        try {
            // 获取当前日期
            LocalDate today = LocalDate.now();
            // 获取今天是星期几（返回DayOfWeek枚举）
            DayOfWeek dayOfWeek = today.getDayOfWeek();
            // 获取数字表示（1=周一，2=周二，...，7=周日）
            int dayValue = dayOfWeek.getValue();

            //查询表中已启用的定时计划
            List<QcManualPlan> timingPlans = qcManualPlanMapper.selectQcManualTimingPlan();
            //遍历计划，看是否有当天需要执行的
            for (QcManualPlan timingPlan : timingPlans) {
                //获取当前定时计划是在哪几天执行的
                List<Integer> weekDays = JSON.parseArray(timingPlan.getWeekDays(), Integer.class);
                //如果包含当天则执行该计划
                if (weekDays.contains(dayValue)){
                    addManualTask(timingPlan);
                }else {
                    continue;
                }
            }
        } catch (Exception e) {
            logger.error("执行人工质检定时计划出错:",e);
            throw new RuntimeException(e.getMessage());
        }
    }


    /**
     *执行定时计划，分配任务
     */
    public void addManualTask(QcManualPlan qcManualPlan){
        QcManualTask qcManualTask = new QcManualTask();
        //生成任务名称
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("_yyyyMMdd_HH");
        String formattedDateTime = now.format(formatter);
        qcManualTask.setTaskName(qcManualPlan.getPlanName() + formattedDateTime);
        //任务类型 (1:定时任务   0:手动任务)
        qcManualTask.setTaskType("1");
        //质检类型 (1:人工抽检 2:人工复检)
        qcManualTask.setManualType(qcManualPlan.getManualType());
        qcManualTask.setDataRange(qcManualPlan.getDataRange());
        qcManualTask.setExtractType(qcManualPlan.getExtractType());
        qcManualTask.setExtractValue(qcManualPlan.getExtractValue());
        qcManualTask.setSmartPlan(qcManualPlan.getSmartPlan());
        qcManualTask.setTemplateId(qcManualPlan.getTemplateId());
        qcManualTask.setHitRules(qcManualPlan.getHitRules());
        qcManualTask.setInspectorRate(qcManualPlan.getInspectorRate());
        //状态
        qcManualTask.setStatus("0");
        qcManualTask.setEnableStatus("1");
        //计划id
        qcManualTask.setPlanId(qcManualPlan.getId());
        //计划名称
        qcManualTask.setPlanName(qcManualPlan.getPlanName());
        qcManualTask.setCreateTime(new Date());
        qcManualTask.setUpdateTime(new Date());
        //将任务落表，并获取任务id
        qcManualTaskMapper.insertQcManualTask(qcManualTask);
//            int a = 1/0;
        //如果是人工抽检，则去原始数据表筛选数据
        if("1".equals(qcManualTask.getManualType())){
            samplingAndAllocation(qcManualTask,qcManualPlan);
        }
        //如果是人工复检,则去智能质检结果表筛选数据
        if ("2".equals(qcManualTask.getManualType())){
            recheckAndAllocation(qcManualTask,qcManualPlan);
        }
    }


    public Date getDeadLineTime(JSONObject jsonObject){
        if ("day".equals(jsonObject.getString("unit"))){
            Integer days = jsonObject.getInteger("value");
            //获取该天数后的日期
            LocalDate currentDate = LocalDate.now();
            LocalDate futureDate = currentDate.plusDays(days);
            LocalDateTime futureDateTime = futureDate.atStartOfDay();
            // 转换为Date对象
            return Date.from(futureDateTime.atZone(ZoneId.systemDefault()).toInstant());
        }else if ("month".equals(jsonObject.getString("unit"))){
            Integer months = jsonObject.getInteger("value");
            Integer days = jsonObject.getInteger("value");
            //获取该天数后的日期
            LocalDate currentDate = LocalDate.now();
            LocalDate futureDate = currentDate.plusMonths(months);
            LocalDateTime futureDateTime = futureDate.atStartOfDay();
            // 转换为Date对象
            return Date.from(futureDateTime.atZone(ZoneId.systemDefault()).toInstant());
        }else {
            return new Date();
        }
    }


    /**
     * 筛选人工抽检的数据并分配到质检员
     */
    public void samplingAndAllocation(QcManualTask qcManualTask, QcManualPlan qcManualPlan){
        //获取数据时间范围
        String dataRange = qcManualPlan.getDataRange();
        String dataRangeDate = "";
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if ("1".equals(dataRange)){
            //一小时内
            dataRangeDate = now.minusHours(1).format(formatter);
        }else if ("2".equals(dataRange)){
            //两小时内
            dataRangeDate = now.minusHours(2).format(formatter);
        }else if("3".equals(dataRange)){
            //四小时内
            dataRangeDate = now.minusHours(4).format(formatter);
        }else if ("4".equals(dataRange)){
            //八小时内
            dataRangeDate = now.minusHours(8).format(formatter);
        }else if ("5".equals(dataRange)){
            //24小时内
            dataRangeDate = now.minusHours(24).format(formatter);
        }else if ("6".equals(dataRange)){
            //七天内
            dataRangeDate = now.minusDays(7).format(formatter);
        }
        //组装查询条件
        Map<String,Object> map = new HashMap<>();
        map.put("dataRangeDate",dataRangeDate);
        List<QcBizItem> qcBizItems = qcBizItemMapper.selectDataForSampling(map);
        //根据页面配置的抽取数量切割list
        String extractType = qcManualTask.getExtractType();
        Integer extractValue = qcManualTask.getExtractValue();
        List<QcBizItem> resultList = new ArrayList<>();
        int originalSize = qcBizItems.size();
        if ("count".equals(extractType)) {
            // 按数量切割
            int count = Math.min(extractValue, originalSize); // 确保不超过原列表大小
            resultList = qcBizItems.subList(0, count);
        } else if ("percent".equalsIgnoreCase(extractType)) {
            // 按百分比切割
            int count = (int) Math.ceil(originalSize * extractValue / 100.0); // 向上取整
            resultList = qcBizItems.subList(0, count);
        }
        // 按坐席工号分组
        Map<String, List<QcBizItem>> recordsByAgent = resultList.stream()
                .collect(Collectors.groupingBy(QcBizItem::getAgentId));
        //设置任务的会话数量
        qcManualTask.setSessionCount(resultList.size() > 0 ? resultList.size() : 0);
        //更新人工质检计划的相关字段
        qcManualPlan.setDataCount(qcManualTask.getSessionCount());
        //设置已选座席数量
        qcManualPlan.setSeatCount(recordsByAgent.keySet().size());
        qcManualPlan.setUpdateTime(new Date());
        qcManualPlanMapper.updateQcManualPlan(qcManualPlan);
        //将筛选完成的数据，根据比例分配给质检员并落到人工质检任务详情表
        //定义Map,key表示质检员工号，value表示比例
        Map<String,Double> ratioMap = new HashMap<>();
        //获取页面参数
        JSONArray jsonArray = com.alibaba.fastjson.JSON.parseArray(qcManualTask.getInspectorRate());
        for (int i = 0; i < jsonArray.size(); i++) {
            ratioMap.put(jsonArray.getJSONObject(i).get("name").toString(),Double.valueOf(jsonArray.getJSONObject(i).get("ratio").toString()));
        }
        List<String> keyList = ratioMap.keySet().stream().collect(Collectors.toList());
        //设置分配的质检
        qcManualTask.setInspectors(JSONObject.toJSONString(keyList));
        allocateTaskForSampling(resultList,ratioMap,qcManualTask);
    }


    /**
     * 根据质检员比例分配任务(人工抽检)
     */
    public void allocateTaskForSampling(List<QcBizItem> tasks,Map<String,Double> userRatios,QcManualTask qcManualTask){
        //定义根据质检员分配后的结果,key表示质检员，value表示对应的数据集
        Map<String,List<QcBizItem>> resultMap = new HashMap<>();
        userRatios.keySet().forEach(userName -> resultMap.put(userName, new ArrayList<>()));
        // 随机打乱任务顺序，确保分配公平
        Collections.shuffle(tasks);
        int totalTasks = tasks.size();
        int remainingTasks = totalTasks;
        int index = 0;
        // 遍历用户，按比例分配任务
        List<String> userNames = new ArrayList<>(userRatios.keySet());
        for (int i = 0; i < userNames.size() - 1; i++) {
            String userName = userNames.get(i);
            double ratio = userRatios.get(userName);
            // 计算应分配的任务数量（向下取整）
            int taskCount = (int) Math.floor(totalTasks * ratio / 100);
            // 分配任务
            List<QcBizItem> assignedTasks = tasks.subList(index, index + taskCount);
            resultMap.get(userName).addAll(assignedTasks);

            index += taskCount;
            remainingTasks -= taskCount;
        }
        // 最后一个用户分配剩余任务，避免舍入误差
        String lastUserName = userNames.get(userNames.size() - 1);
        resultMap.get(lastUserName).addAll(tasks.subList(index, index + remainingTasks));
        //移除没有分到任务的质检员，没有分到的质检员在任务列表中和人工质检台看不到该任务
        resultMap.entrySet().removeIf(entry ->
                entry.getValue() == null || entry.getValue().isEmpty()
        );
        //实际分配到数据的质检员字段
        List<String> realList = resultMap.keySet().stream().collect(Collectors.toList());
        qcManualTask.setRealInspectors(JSONObject.toJSONString(realList));
        qcManualTask.setUpdateTime(new Date());
        //更新task表
        qcManualTaskMapper.updateQcManualTask(qcManualTask);
        batchInsertDataForSampling(resultMap,qcManualTask);
    }

    /**
     * 将分配后的数据集落表(人工抽检)
     */
    public void batchInsertDataForSampling(Map<String,List<QcBizItem>> resultMap,QcManualTask qcManualTask){
        List<QcManualTaskDetail> qcManualTaskDetailList = new ArrayList<>();
//        String currTempStr = JSON.toJSONString(qcScoringTemplateMapper.getQcScoringTemplate(qcManualTask.getTemplateId()));
        for (Map.Entry<String, List<QcBizItem>> entry : resultMap.entrySet()) {
            String userName = entry.getKey();
            List<QcBizItem> qcBizItemss = entry.getValue();
            //遍历结果集,将相关字段值赋给人工质检结果表
            for (QcBizItem qcBizItem : qcBizItemss) {
                QcManualTaskDetail qcManualTaskDetail = new QcManualTaskDetail();
                qcManualTaskDetail.setInspector(userName);//质检员工号
                qcManualTaskDetail.setBusinessId("");
                qcManualTaskDetail.setManualTaskId(qcManualTask.getId());
                qcManualTaskDetail.setManualType("1");
                qcManualTaskDetail.setBizNo(qcBizItem.getUniqueKey());
                qcManualTaskDetail.setSessionLength(qcBizItem.getCallDurationS());
                qcManualTaskDetail.setWorkNo(qcBizItem.getAgentId());
                qcManualTaskDetail.setWorkName(qcBizItem.getAgentName());
                qcManualTaskDetail.setManualInspectScore(null);
                qcManualTaskDetail.setManualInspectTime(null);
                qcManualTaskDetail.setStatus("");
                qcManualTaskDetail.setFinishStatus("0");
                qcManualTaskDetail.setTemplateId(qcManualTask.getTemplateId());
//                qcManualTaskDetail.setTemplateHitRule(currTempStr);
                qcManualTaskDetail.setCreateTime(new Date());
                qcManualTaskDetail.setUpdateTime(new Date());
                qcManualTaskDetailList.add(qcManualTaskDetail);
            }
        }
        //将人工质检结果落表
        if (qcManualTaskDetailList.size() > 0){
            qcManualTaskDetailMapper.batchInsert(qcManualTaskDetailList);
        }
    }


    /**
     * 筛选人工复检的数据并分配到质检员
     */
    public void recheckAndAllocation(QcManualTask qcManualTask,QcManualPlan qcManualPlan){
        //命中规则
        List<Integer> hitRules = JSON.parseArray(qcManualTask.getHitRules(), Integer.class);
        //获取数据时间范围
        String dataRange = qcManualPlan.getDataRange();
        String dataRangeDate = "";
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if ("1".equals(dataRange)){
            //一小时内
            dataRangeDate = now.minusHours(1).format(formatter);
        }else if ("2".equals(dataRange)){
            //两小时内
            dataRangeDate = now.minusHours(2).format(formatter);
        }else if("3".equals(dataRange)){
            //四小时内
            dataRangeDate = now.minusHours(4).format(formatter);
        }else if ("4".equals(dataRange)){
            //八小时内
            dataRangeDate = now.minusHours(8).format(formatter);
        }else if ("5".equals(dataRange)){
            //24小时内
            dataRangeDate = now.minusHours(24).format(formatter);
        }else if ("6".equals(dataRange)){
            //七天内
            dataRangeDate = now.minusDays(7).format(formatter);
        }
        //组装查询条件
        Map<String,Object> map = new HashMap<>();
        map.put("planId",qcManualTask.getSmartPlan());
        map.put("hitRules",hitRules);
        map.put("dataRangeDate",dataRangeDate);
        List<QcSmartTaskDetail> qcSmartTaskDetails = qcSmartTaskMapper.selectSmartTaskDetailForRecheck(map);
        // 按坐席工号分组
        Map<String, List<QcSmartTaskDetail>> recordsByAgent = qcSmartTaskDetails.stream()
                .collect(Collectors.groupingBy(QcSmartTaskDetail::getWorkNo));
        //设置任务的会话数量
        qcManualTask.setSessionCount(qcSmartTaskDetails.size() > 0 ? qcSmartTaskDetails.size() : 0);
        //更新人工质检计划的相关字段
        qcManualPlan.setDataCount(qcManualTask.getSessionCount());
        //设置已选座席数量
        qcManualPlan.setSeatCount(recordsByAgent.keySet().size());
        qcManualPlan.setUpdateTime(new Date());
        qcManualPlanMapper.updateQcManualPlan(qcManualPlan);
        //将筛选完成的数据，根据比例分配给质检员并落到人工质检任务详情表
        //定义Map,key表示质检员工号，value表示比例
        Map<String,Double> ratioMap = new HashMap<>();
        //获取页面参数
        JSONArray jsonArray = com.alibaba.fastjson.JSON.parseArray(qcManualTask.getInspectorRate());
        for (int i = 0; i < jsonArray.size(); i++) {
            ratioMap.put(jsonArray.getJSONObject(i).get("name").toString(),Double.valueOf(jsonArray.getJSONObject(i).get("ratio").toString()));
        }
        List<String> keyList = ratioMap.keySet().stream().collect(Collectors.toList());
        //设置分配的质检员
        qcManualTask.setInspectors(JSONObject.toJSONString(keyList));
        allocateTaskForRecheck(qcSmartTaskDetails,ratioMap,qcManualTask);

    }

    /**
     * 根据质检员比例分配任务(人工复检)
     */
    public void allocateTaskForRecheck(List<QcSmartTaskDetail> tasks,Map<String,Double> userRatios,QcManualTask qcManualTask){
        //定义根据质检员分配后的结果,key表示质检员，value表示对应的数据集
        Map<String,List<QcSmartTaskDetail>> resultMap = new HashMap<>();
        userRatios.keySet().forEach(userName -> resultMap.put(userName, new ArrayList<>()));
        // 随机打乱任务顺序，确保分配公平
        Collections.shuffle(tasks);
        int totalTasks = tasks.size();
        int remainingTasks = totalTasks;
        int index = 0;
        // 遍历用户，按比例分配任务
        List<String> userNames = new ArrayList<>(userRatios.keySet());
        for (int i = 0; i < userNames.size() - 1; i++) {
            String userName = userNames.get(i);
            double ratio = userRatios.get(userName);
            // 计算应分配的任务数量（向下取整）
            int taskCount = (int) Math.floor(totalTasks * ratio / 100);
            // 分配任务
            List<QcSmartTaskDetail> assignedTasks = tasks.subList(index, index + taskCount);
            resultMap.get(userName).addAll(assignedTasks);

            index += taskCount;
            remainingTasks -= taskCount;
        }
        // 最后一个用户分配剩余任务，避免舍入误差
        String lastUserName = userNames.get(userNames.size() - 1);
        resultMap.get(lastUserName).addAll(tasks.subList(index, index + remainingTasks));
        //移除没有分到任务的质检员，没有分到的质检员在任务列表中和人工质检台看不到该任务
        resultMap.entrySet().removeIf(entry ->
                entry.getValue() == null || entry.getValue().isEmpty()
        );
        //实际分配到数据的质检员字段
        List<String> realList = resultMap.keySet().stream().collect(Collectors.toList());
        qcManualTask.setRealInspectors(JSONObject.toJSONString(realList));
        qcManualTask.setUpdateTime(new Date());
        //更新task表
        qcManualTaskMapper.updateQcManualTask(qcManualTask);
        batchInsertDataForRecheck(resultMap,qcManualTask);
    }

    /**
     * 将分配后的数据集落表(人工复检)
     */
    public void batchInsertDataForRecheck(Map<String,List<QcSmartTaskDetail>> resultMap,QcManualTask qcManualTask){
        List<QcManualTaskDetail> qcManualTaskDetailList = new ArrayList<>();
//        String currTempStr = JSON.toJSONString(qcScoringTemplateMapper.getQcScoringTemplate(qcManualTask.getTemplateId()));
        for (Map.Entry<String, List<QcSmartTaskDetail>> entry : resultMap.entrySet()) {
            String userName = entry.getKey();
            List<QcSmartTaskDetail> smartTaskDetails = entry.getValue();
            //遍历智能质检结果集,将相关字段值赋给人工质检结果表
            for (QcSmartTaskDetail smartTaskDetail : smartTaskDetails) {
                QcManualTaskDetail qcManualTaskDetail = new QcManualTaskDetail();
                qcManualTaskDetail.setInspector(userName);//质检员工号
                qcManualTaskDetail.setBusinessId("");
                qcManualTaskDetail.setManualTaskId(qcManualTask.getId());
                qcManualTaskDetail.setSmartTaskDetailId(smartTaskDetail.getId());
                qcManualTaskDetail.setManualType("2");
                qcManualTaskDetail.setBizNo(smartTaskDetail.getBizNo());
                qcManualTaskDetail.setSessionLength(smartTaskDetail.getSessionLength());
                qcManualTaskDetail.setWorkNo(smartTaskDetail.getWorkNo());
                qcManualTaskDetail.setWorkName(smartTaskDetail.getWorkName());
                qcManualTaskDetail.setMacInspectScore(smartTaskDetail.getMacInspectScore());
                qcManualTaskDetail.setMacInspectTime(smartTaskDetail.getMacInspectTime());
                qcManualTaskDetail.setFinishStatus("0");
                //如果是人工复检，则模板用智能质检任务对应的模板
                qcManualTaskDetail.setTemplateId(smartTaskDetail.getTemplateId());
//                qcManualTaskDetail.setTemplateHitRule(currTempStr);
                qcManualTaskDetail.setCreateTime(new Date());
                qcManualTaskDetail.setUpdateTime(new Date());
                qcManualTaskDetailList.add(qcManualTaskDetail);
            }
        }
        //将人工质检结果落表
        if (qcManualTaskDetailList.size() > 0){
            qcManualTaskDetailMapper.batchInsert(qcManualTaskDetailList);
        }
    }



}
