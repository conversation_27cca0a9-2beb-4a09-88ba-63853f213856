<template>
    <div class="app-container">
		<div :content="form.id?'编辑质检计划':'新增质检计划'" style="margin-bottom:10px;">
			<div class="el-dialog-title dy-title">{{title}}</div>
			<el-button v-if="op!='view'"class="btn" type="primary" @click="submit" :loading="submiting">保 存</el-button>
			<el-button class="btn" @click="close">取 消</el-button>
	    </div>
		<el-divider class="dy-divider"></el-divider>
        <el-form :model="form" ref="queryForm" size="small" :rules="rules" :inline="true" label-width="90px" style="max-height:750px;overflow-x:hidden;overflow-y:auto;" v-loading="loading" element-loading-text="拼命加载中">
<!--			<el-form-item label="" class="no-label_el-form-item" style="width: 100%">-->
			<el-row :gutter="10" style="width: 100%">
				<el-col :span="8">
					<el-form-item label="计划名称" prop="planName">
		                <el-input v-model="form.planName" placeholder="请输入计划名称" clearable show-word-limit :disabled="op == 'view'"/>
		            </el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="评分模板" prop="template">
						<el-select v-model="form.scoreTemplate" :disabled="op == 'view'">
							<el-option
						      v-for="(itemt,indext) in tempList"
						      :key="itemt.dictValue"
						      :label="itemt.dictLabel"
						      :value="itemt.dictValue"
							  :disabled="itemt.status==0">
						    </el-option>
						</el-select>
		            </el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="过滤空会话" prop="">
						<el-select v-model="form.filterEmptySession" :disabled="op == 'view'">
							<el-option :key="1" label="是" :value="1"></el-option>
							<el-option :key="2" label="否" :value="2"></el-option>
						</el-select>
		            </el-form-item>
				</el-col>
			</el-row>
<!--			</el-form-item>-->
            <el-form-item label="获取数据" style="width: 100%" prop="getData">
				<template v-for="(itemd,indexd) in form.timeList">
					<el-row style="width: 100%">
						<el-col>
							每天<el-select v-model="itemd.h" style="max-width:80px;margin:5px 5px;":disabled="op == 'view'">
							    <el-option
							      v-for="hour in 24"
							      :key="hour-1"
							      :label="`${(hour-1).toString().padStart(2, '0')}`"
							      :value="`${(hour-1).toString().padStart(2, '0')}`">
							    </el-option>
							</el-select>
						  	获取<el-select v-model="itemd.bf" style="max-width:100px;margin:5px 5px;":disabled="op == 'view'">
								<el-option label="前一天" value="-"></el-option>
							    <el-option label="当天" value="+"></el-option>
							</el-select>
							<el-time-picker v-model="itemd.bt" style="max-width:120px;margin:5px 5px;" value-format="HH:mm:ss" :picker-options="{format:'HH:mm:ss'}":disabled="op == 'view'"/>
							到<el-select v-model="itemd.ef" style="max-width:100px;margin:5px 5px;":disabled="op == 'view'">
								<el-option label="前一天" value="-"></el-option>
							    <el-option label="当天" value="+"></el-option>
							</el-select>
							<el-time-picker v-model="itemd.et" style="max-width:120px;margin:5px 5px;" value-format="HH:mm:ss" :picker-options="{format:'HH:mm:ss'}":disabled="op == 'view'"/>
							的数据
							<el-button v-if="form.timeList.length>1" icon="el-icon-minus" size="mini" @click="delRow(indexd)" :disabled="op == 'view'"/>
							<el-button size="mini" icon="el-icon-plus" @click="addRow" :disabled="op == 'view'"/>
							<el-button v-if="itemd.id != null && itemd.id !=''" size="mini" icon="el-icon-video-play" @click="execPlan(indexd)" />
						</el-col>
					</el-row>
				</template>
            </el-form-item>
			<div id="planForm">
				<filterModal :filterList="filterList" type="flData" :op="op" ref="filterModal"></filterModal>
			</div>
        </el-form>
    </div>
</template>

<script>

import { planList, updatePlan,execPlan,checkFilterForm} from "@/api/quality/smart";
import filterModal from "@/views/quality/ruleConfig/common/busFilter";
export default {
	name:"planEdit",
	components:{
		filterModal
	},
    data() {
        return {
			form:{planType:1,filterEmptySession:2,status:1,timeList:[],deleteList:[]},
			loading :false,
			submiting:false,
			dcList:[],
			tempList:[],
			filterList:[],
			rules:{
				planName: [{ required: true, message: '', trigger: '11' }],
             	getData: [{ required: true, message: '', trigger: '11' }],
             	template: [{ required: true, message: '', trigger: '11' }],
			},
			op:'edit',
			title:''
        }
    },
    computed: {
    },
    watch: {
    },

  	created() {
		this.$refs.rules.clearValidate();
  	},

    methods: {
		reload(row,list,op){
			if(op){
				this.op = op;
			}
			if(list){
				this.tempList = list;
			}
			if(this.op == 'view'){
				this.title = '查看智能质检计划';
			}else{
				this.title = (row && row.id)?'编辑智能质检计划':'新增智能质检计划';
			}
			if(!row || !row.id || row.id == null || row.id == ''){
				this.addRow();
				return ;
			}
			this.loading = true;
			planList({},{isPg:false,id:row.id}).then(response=>{
				if(response.rows && response.rows.length>0){
					var temp = response.rows[0];
					for(var i in temp.timeList){
						var tmp = temp.timeList[i];
						tmp["h"] = tmp.planExecTime.substring(0,2);
						tmp["bf"] = tmp.dataBegTime.substring(0,1);
						tmp["bt"] = tmp.dataBegTime.substring(1);
						tmp["ef"] = tmp.dataEndTime.substring(0,1);
						tmp["et"] = tmp.dataEndTime.substring(1);
					}
					if(temp.followMatchDetail && temp.followMatchDetail != null){
						this.filterList.push({ruleCondDetail:JSON.parse(temp.followMatchDetail)});
					}
					this.form = temp;
				}
				this.loading = false;
			});
		},
		addRow(){
			this.form.timeList.push({})
		},
		delRow(index){
			var tmp = JSON.parse(JSON.stringify(this.form.timeList[index]));
			if(tmp.id && tmp.id !=''){
				if(!this.form.deleteList || this.form.deleteList == null){
					this.form.deleteList = [];
				}
				this.form.deleteList.push(tmp);
			}
			this.form.timeList.splice(index,1);
		},
		execPlan(index){
			var tmp = JSON.parse(JSON.stringify(this.form.timeList[index]));
			tmp["id"] = tmp.planId;
			tmp["status"] = 1;
			tmp["planType"] = 1;
			execPlan(tmp).then(response=>{
				this.$message({message: "执行成功",type: 'success'});
			});
		},
		close(){
			this.form = {planType:1,filterEmptySession:2,status:1,timeList:[],deleteList:[]}
			this.dcList = [];
			this.filterList=[];
			this.$emit('goList',true,'plan');
			this.$emit('doPlanList');
			this.op = 'edit';
		},
		submit(){
			var tmp = [];
			if(!this.form.planName||this.form.planName==''){
				this.$message({message: "计划名称为空",type: 'error'});
				return ;
			}else if(!this.form.scoreTemplate||this.form.scoreTemplate==''){
				this.$message({message: "未选择模板",type: 'error'});
				return ;
			}
			for(var i in this.form.timeList){
				var tip = "数据获取第"+(parseInt(i)+1)+"行未配置";
				if(!this.form.timeList[i].h){
					tip += "数据执行时间";
					this.$message({message: tip,type: 'error'});
					return ;
				}else if(!this.form.timeList[i].bf || !this.form.timeList[i].bt || !this.form.timeList[i].ef || !this.form.timeList[i].et){
					tip += "数据执行时间";
					this.$message({message: tip,type: 'error'});
					return ;
				}
				tmp.push({
					planExecTime:this.form.timeList[i].h+":00:00",
					dataBegTime:this.form.timeList[i].bf+this.form.timeList[i].bt,
					dataEndTime:this.form.timeList[i].ef+this.form.timeList[i].et,
					id:this.form.timeList[i].id
				});
			}
			tip = checkFilterForm(this.filterList,1);
			if(tip){
				this.$message({message: tip,type: 'error'});
				return 
			}
			this.form['timeList'] = tmp;
			if(this.filterList.length>0){
				this.form["followMatchDetail"] = JSON.stringify(this.filterList[0].ruleCondDetail);
			}else{
				this.form["followMatchDetail"] = null;
			}
			this.submiting = true;
			updatePlan(this.form).then(response=>{
				this.reload({id:response.data});
				if(this.form.id){
					this.$message({message: "修改成功",type: 'success'});
				}else{
					this.$message({message: "新增成功",type: 'success'});
				}
				this.close();
			}).finally(v=>{
				this.submiting = false;
			});
		}
    }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.inputWidth{
  width: 450px!important;
}
.selectS{
  width: 80px!important;
  margin-right: 10px;
}
.selectM{
  width: 90px!important;
  margin-right: 10px;
}
.selectL{
  width: 180px!important;
  margin-right: 10px;
}
.ruleBox{
  margin-bottom: 20px;
  .ruleTop{
    background: #ebebeb;
    padding: 2px 15px;
    cursor: pointer;
  }
  .ruleCon{
     padding: 18px 15px;
     font-size: 12px;
     background-color: #fafafa;
  }
  button{
        padding: 9px;
  }
}
.dialog-footer{
  width: 100%;
  margin-top: 40px;
  justify-content: center;

}
.orBt{
  margin-bottom: 15px;
}
.hitBox{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top:20px;
  row-gap: 10px; 
}
.keyInput{
  width: 150px;
  margin-right: 10px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 125px;
  margin-right: 10px;
  vertical-align: bottom;
}
.btn{
	float:right;
	margin-right:5px;
}
.no-label_el-form-item .el-form-item__content{
	margin-left: 0px!important;
}
#planForm .el-form-item__content{
	width:90%;
}
</style>
