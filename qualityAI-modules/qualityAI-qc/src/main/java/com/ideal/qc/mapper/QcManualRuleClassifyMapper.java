package com.ideal.qc.mapper;

import com.ideal.qc.domain.QcManualRuleClassify;

import java.util.List;

public interface QcManualRuleClassifyMapper {

    /**
     * 查询人工规则分类
     *
     * @param classifyId 人工规则分类主键
     * @return 人工规则分类
     */
    public QcManualRuleClassify selectQcManualRuleClassifyByClassifyId(Long classifyId);

    /**
     * 查询人工规则分类列表
     *
     * @param qcManualRuleClassify 人工规则分类
     * @return 人工规则分类集合
     */
    public List<QcManualRuleClassify> selectQcManualRuleClassifyList(QcManualRuleClassify qcManualRuleClassify);

    /**
     * 新增人工规则分类
     *
     * @param qcManualRuleClassify 人工规则分类
     * @return 结果
     */
    public int insertQcManualRuleClassify(QcManualRuleClassify qcManualRuleClassify);

    /**
     * 修改人工规则分类
     *
     * @param qcManualRuleClassify 人工规则分类
     * @return 结果
     */
    public int updateQcManualRuleClassify(QcManualRuleClassify qcManualRuleClassify);

    /**
     * 删除人工规则分类
     *
     * @param classifyId 人工规则分类主键
     * @return 结果
     */
    public int deleteQcManualRuleClassifyByClassifyId(Long classifyId);

    /**
     * 批量删除人工规则分类
     *
     * @param classifyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQcManualRuleClassifyByClassifyIds(Long[] classifyIds);

}
