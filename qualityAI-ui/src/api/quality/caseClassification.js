import request from '@/utils/request'

// 查询案例分类列表
export function getQcCaseClassificationList(data) {
  return request({
    url: '/qc/case/classification/list',
    method: 'post',
    data
  })
}

// 新增案例分类
export function addQcCaseClassification(data) {
  return request({
    url: '/qc/case/classification/add',
    method: 'post',
    data: data
  })
}

// 修改案例分类
export function updateQcCaseClassification(data) {
  return request({
    url: '/qc/case/classification/update',
    method: 'post',
    data: data
  })
}

// 删除案例分类
export function removeQcCaseClassification(id) {
  return request({
    url: '/qc/case/classification/remove/' + id,
    method: 'post'
  })
}
