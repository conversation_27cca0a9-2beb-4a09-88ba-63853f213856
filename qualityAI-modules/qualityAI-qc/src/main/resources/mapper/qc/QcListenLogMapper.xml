<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcListenLogMapper">
    
    <resultMap type="com.ideal.qc.domain.QcListenLog" id="QcListenLogResult">
        <result property="id"    column="id"    />
        <result property="listenLong"    column="listen_long"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="bizNo"    column="biz_no"    />
    </resultMap>

    <sql id="selectQcListenLogVo">
        select id, listen_long, create_by, create_time,biz_no from qc_listen_log
    </sql>

    <select id="selectQcListenLogList" parameterType="QcListenLog" resultMap="QcListenLogResult">
        <include refid="selectQcListenLogVo"/>
        <where>  
            <if test="listenLong != null  and listenLong != ''"> and listen_long = #{listenLong}</if>
            <if test="bizNo != null  and bizNo != ''"> and biz_no = #{bizNo}</if>
        </where>
    </select>
    
    <select id="selectQcListenLogById" parameterType="Long" resultMap="QcListenLogResult">
        <include refid="selectQcListenLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcListenLog" parameterType="QcListenLog" useGeneratedKeys="true" keyProperty="id">
        insert into qc_listen_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="listenLong != null">listen_long,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="bizNo != null">biz_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="listenLong != null">#{listenLong},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="bizNo != null">#{bizNo},</if>
         </trim>
    </insert>

    <update id="updateQcListenLog" parameterType="QcListenLog">
        update qc_listen_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="listenLong != null">listen_long = #{listenLong},</if>
            <if test="bizNo != null">biz_no = #{bizNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcListenLogById" parameterType="Long">
        delete from qc_listen_log where id = #{id}
    </delete>

    <delete id="deleteQcListenLogByIds" parameterType="String">
        delete from qc_listen_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>