package com.ideal.system.domain;

import com.ideal.common.core.utils.TreeNode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.List;

/**
 * 租户资源对象 sys_tenant_menu
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public class SysTenantMenu implements TreeNode<SysTenantMenu> {
    private static final long serialVersionUID = 1L;

    /** 菜单Id */
    private Long menuId;
    private Long parentId;
    private String menuName;
    private String visible;
    private String status;
    private String menuType;
    private String icon;
    private Integer order_num;
    private List<SysTenantMenu> children = new ArrayList<SysTenantMenu>();

    public void setMenuId(Long menuId)
    {
        this.menuId = menuId;
    }

    public Long getMenuId()
    {
        return menuId;
    }

    @Override
    public Long getId() {
        return this.menuId;
    }

    @Override
    public Long getPid() {
        return this.parentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getVisible() {
        return visible;
    }

    public void setVisible(String visible) {
        this.visible = visible;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getOrder_num() {
        return order_num;
    }

    public void setOrder_num(Integer order_num) {
        this.order_num = order_num;
    }

    public List<SysTenantMenu> getChildren() {
        return children;
    }

    public void setChildren(List<SysTenantMenu> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("menuId", getMenuId())
                .append("parentId", getParentId())
                .append("menuName", getMenuName())
                .append("visible", getVisible())
                .append("status", getStatus())
                .append("menuType", getMenuType())
                .append("icon", getIcon())
                .append("order_num", getOrder_num())

                .toString();
    }
}
