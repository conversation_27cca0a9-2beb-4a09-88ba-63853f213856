package com.ideal.qc.service;

import java.util.List;
import com.ideal.qc.domain.ZjData;

/**
 * 质检源数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface IZjDataService 
{
    /**
     * 查询质检源数据
     * 
     * @param id 质检源数据主键
     * @return 质检源数据
     */
    public ZjData selectZjDataById(Long id);

    /**
     * 查询质检源数据列表
     * 
     * @param zjData 质检源数据
     * @return 质检源数据集合
     */
    public List<ZjData> selectZjDataList(ZjData zjData);

    /**
     * 新增质检源数据
     * 
     * @param zjData 质检源数据
     * @return 结果
     */
    public int insertZjData(ZjData zjData);

    /**
     * 修改质检源数据
     * 
     * @param zjData 质检源数据
     * @return 结果
     */
    public int updateZjData(ZjData zjData);

    /**
     * 批量删除质检源数据
     * 
     * @param ids 需要删除的质检源数据主键集合
     * @return 结果
     */
    public int deleteZjDataByIds(Long[] ids);

    /**
     * 删除质检源数据信息
     * 
     * @param id 质检源数据主键
     * @return 结果
     */
    public int deleteZjDataById(Long id);

    String getVideoText(String onlyNo);

    List<String> getVideoTextMatchKey(String onlyNo);
}
