import request from '@/utils/request'

// 查询评分细则列表
export function getQcScoringItemList(params,data) {
  return request({
    url: '/qc/scoring/item/list',
    method: 'post',
    params,
    data
  })
}

// 查询评分细则详情
export function getQcScoringItem(scoringItemId) {
  return request({
    url: '/qc/scoring/item/query/' + scoringItemId,
    method: 'post'
  })
}

// 新增评分细则
export function addQcScoringItem(data) {
  return request({
    url: '/qc/scoring/item/add',
    method: 'post',
    data: data
  })
}

// 修改评分细则
export function updateQcScoringItem(data) {
  return request({
    url: '/qc/scoring/item/update',
    method: 'post',
    data: data
  })
}

// 删除评分模板细则
export function removeQcScoringItem(scoringItemId) {
  return request({
    url: '/qc/scoring/item/remove/' + scoringItemId,
    method: 'post'
  })
}

// 修改评分细则状态
export function updateQcScoringItemStatus(data) {
  return request({
    url: '/qc/scoring/item/update/status',
    method: 'post',
    data: data
  })
}