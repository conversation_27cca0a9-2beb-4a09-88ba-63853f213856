import request from '@/utils/request'

// 查询质检源数据列表
export function listData(query) {
  return request({
    url: '/qc/data/list',
    method: 'get',
    params: query
  })
}

// 查询质检源数据详细
export function getData(id) {
  return request({
    url: '/qc/data/' + id,
    method: 'get'
  })
}

// 新增质检源数据
export function addData(data) {
  return request({
    url: '/qc/data',
    method: 'post',
    data: data
  })
}

// 修改质检源数据
export function updateData(data) {
  return request({
    url: '/qc/data',
    method: 'put',
    data: data
  })
}

// 删除质检源数据
export function delData(id) {
  return request({
    url: '/qc/data/' + id,
    method: 'delete'
  })
}
