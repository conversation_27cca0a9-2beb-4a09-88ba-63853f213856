import request from '@/utils/request'

// 查询人工复检列表
export function listReview(query) {
  return request({
    url: '/qc/review/selectReviewData',
    method: 'get',
    params: query
  })
}

// 查询人工复检详细
export function getReview(id) {
  return request({
    url: '/qc/review/' + id,
    method: 'get'
  })
}

// 新增人工复检
export function addReview(data) {
  return request({
    url: '/qc/review',
    method: 'post',
    data: data
  })
}

// 修改人工复检
export function updateReview(data) {
  return request({
    url: '/qc/review',
    method: 'put',
    data: data
  })
}

// 删除人工复检
export function delReview(id) {
  return request({
    url: '/qc/review/' + id,
    method: 'delete'
  })
}

// 录音详情、评测详情
export function reviewInfo(id) {
  return request({
    url: '/qc/data/' + id,
    method: 'get'
  })
}

// 质检结果
export function aiResList(query) {
  return request({
    url: '/qc/review/selectAiResultData',
    method: 'get',
    params: query
  })
}

// 模板列表
export function tempList(query) {
  return request({
    url: '/qc/review/selectAiTemplateData',
    method: 'get',
    params: query
  })
}


// 录音地址和文本
export function soundInfo(id) {
  return request({
    url: '/qc/data/getVideoText/'+ id,
    method: 'get'
  })
}
//复检详情
export function reviewDetail(id) {
  return request({
    url: '/qc/review/getReviewListByDataId/'+id,
    method: 'get'
  })
}