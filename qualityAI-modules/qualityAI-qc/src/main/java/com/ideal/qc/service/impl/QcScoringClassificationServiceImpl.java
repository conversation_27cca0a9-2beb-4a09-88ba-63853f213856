package com.ideal.qc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.exception.ServiceException;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcScoringClassification;
import com.ideal.qc.domain.QcScoringItem;
import com.ideal.qc.mapper.QcScoringClassificationMapper;
import com.ideal.qc.service.QcScoringClassificationService;
import com.ideal.qc.service.QcScoringItemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 评分分类表Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/14 10:01
 */
@Service
public class QcScoringClassificationServiceImpl implements QcScoringClassificationService {

    @Resource
    private QcScoringClassificationMapper qcScoringClassificationMapper;
    @Resource
    private QcScoringItemService qcScoringItemService;

    /**
     * 查询评分模板分类列表
     */
    @Override
    public List<QcScoringClassification> getQcScoringClassificationList(Long scoringTemplateId) {
        return qcScoringClassificationMapper.getQcScoringClassificationList(scoringTemplateId);
    }

    /**
     * 新增评分模板分类
     */
    @Override
    public void addQcScoringClassification(JSONObject payload) {
        String scoringClassificationName = payload.getString("scoringClassificationName");
        if (ObjectUtil.isEmpty(scoringClassificationName)) {
            throw new ServiceException("评分模板分类名称不允许为空");
        }
        Long scoringTemplateId = payload.getLong("scoringTemplateId");
        if (ObjectUtil.isEmpty(scoringTemplateId)) {
            throw new ServiceException("未关联评分模板");
        }
        payload.put("createTime", DateTime.now());
        payload.put("createBy", SecurityUtils.getUsername());
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        payload.put("tenantId", SecurityUtils.getTenantId());
        qcScoringClassificationMapper.addQcScoringClassification(payload);
    }

    /**
     * 修改评分模板分类
     */
    @Override
    public void updateQcScoringClassification(JSONObject payload) {
        String scoringClassificationName = payload.getString("scoringClassificationName");
        if (ObjectUtil.isEmpty(scoringClassificationName)) {
            throw new ServiceException("评分模板分类名称不允许为空");
        }
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        qcScoringClassificationMapper.updateQcScoringClassification(payload);
    }

    /**
     * 删除评分模板分类
     */
    @Override
    public void removeQcScoringClassification(Long id) {
        List<QcScoringItem> qcScoringItemList = qcScoringItemService.getQcScoringItemByScoringClassificationIds(id);
        if (CollUtil.isNotEmpty(qcScoringItemList)) {
            throw new ServiceException("请先删除与其关联的评分细则");
        }
        qcScoringClassificationMapper.removeQcScoringClassification(id);
    }

    /**
     * 查询评分模板分类列表根据模板id列表
     */
    @Override
    public List<QcScoringClassification> getQcScoringClassificationListByScoringTemplateIds(Long[] scoringTemplateIds) {
        return qcScoringClassificationMapper.getQcScoringClassificationListByScoringTemplateIds(scoringTemplateIds);
    }
}
