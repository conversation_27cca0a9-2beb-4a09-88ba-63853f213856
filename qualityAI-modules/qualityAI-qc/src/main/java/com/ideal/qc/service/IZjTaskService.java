package com.ideal.qc.service;


import com.ideal.qc.domain.ZjTask;

import java.util.List;

/**
 * 质检任务Service接口
 *
 * <AUTHOR>
 */
public interface IZjTaskService
{
    /**
     * 查询质检任务
     *
     * @param taskId 质检任务主键
     * @return 质检任务
     */
    public ZjTask selectZjTaskByTaskId(Long taskId);

    /**
     * 查询质检任务列表
     *
     * @param zjTask 质检任务
     * @return 质检任务集合
     */
    public List<ZjTask> selectZjTaskList(ZjTask zjTask);

    /**
     * 新增质检任务
     *
     * @param zjTask 质检任务
     * @return 结果
     */
    public int insertZjTask(ZjTask zjTask);

    //新增质检数据关联模板
    public void insertZjDataTemplate(ZjTask zjTask);

    /**
     * 修改质检任务
     *
     * @param zjTask 质检任务
     * @return 结果
     */
    public int updateZjTask(ZjTask zjTask);

    /**
     * 批量删除质检任务
     *
     * @param taskIds 需要删除的质检任务主键集合
     * @return 结果
     */
    public int deleteZjTaskByTaskIds(Long[] taskIds);

    /**
     * 删除质检任务信息
     *
     * @param taskId 质检任务主键
     * @return 结果
     */
    public int deleteZjTaskByTaskId(Long taskId);

    public void startTask(ZjTask zjTask);
}
