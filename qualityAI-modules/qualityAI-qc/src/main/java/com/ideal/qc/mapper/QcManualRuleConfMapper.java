package com.ideal.qc.mapper;


import com.ideal.qc.domain.QcManualRuleConf;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

public interface QcManualRuleConfMapper {

    /**
     * 查询人工规则配置
     *
     * @param id 人工规则配置主键
     * @return 人工规则配置
     */
    public QcManualRuleConf selectQcManualRuleConfById(Long id);

    /**
     * 查询人工规则配置列表
     *
     * @return 人工规则配置集合
     */
    public List<QcManualRuleConf> selectQcManualRuleConfList(Map<String, Object> params);


    public List<QcManualRuleConf> selectAll();

    /**
     * 新增人工规则配置
     *
     * @param qcManualRuleConf 人工规则配置
     * @return 结果
     */
    public int insertQcManualRuleConf(QcManualRuleConf qcManualRuleConf);

    /**
     * 批量插入
     * @param list
     */
    public void batchInsert(List<QcManualRuleConf> list);

    /**
     * 修改人工规则配置
     *
     * @param qcManualRuleConf 人工规则配置
     * @return 结果
     */
    public int updateQcManualRuleConf(QcManualRuleConf qcManualRuleConf);

    /**
     * 删除人工规则配置
     *
     * @param id 人工规则配置主键
     * @return 结果
     */
    public int deleteQcManualRuleConfById(Long id);

    /**
     * 批量删除人工规则配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQcManualRuleConfByIds(Long[] ids);


    /**
     * 查询当前分类id下是否有人工规则
     */

    int getCountByClassify(Long classifyId);

}
