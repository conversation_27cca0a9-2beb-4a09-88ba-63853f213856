<template>
  <div class="app-container home">
<!--
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12" style="padding-left: 20px">
        <h2>智能质检系统</h2>
      </el-col>

      <el-col :sm="24" :lg="12" style="padding-left: 50px">
      </el-col>
    </el-row>
    <el-divider />
-->
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span>统计信息</span>
          </div>
          <div class="body card-body">
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span>信息</span>
          </div>
          <div class="body card-body">
          </div>
          <!--
          <el-collapse accordion>
            <el-collapse-item title="AAAAAA">
              <ol>
                <li>11111</li>
                <li>111111</li>
                <li>333333</li>
              </ol>
            </el-collapse-item>

            <el-collapse-item title="BBBBB">
              <ol>
                <li>11111111111</li>
                <li>22222222222222</li>
                <li>33333333333333</li>
              </ol>
            </el-collapse-item>
            <el-collapse-item title="CCCCCCCCCCC">
              <ol>
                <ol>
                  <li>11111111111</li>
                  <li>22222222222222</li>
                  <li>33333333333333</li>
                </ol>
              </ol>
            </el-collapse-item>

          </el-collapse>
          -->
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span>待办</span>
          </div>
          <div class="body card-body">
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "3.6.5",
    };
  },
  methods: {
    goTarget(href) {
      window.open(href, "_blank");
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  .card-body {
    height: 100px;
  }
}
</style>

