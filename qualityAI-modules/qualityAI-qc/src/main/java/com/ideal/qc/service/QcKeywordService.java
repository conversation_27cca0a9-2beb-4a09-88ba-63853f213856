package com.ideal.qc.service;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcKeyword;

import java.util.List;

/**
 * 关键词Service接口
 *
 * <AUTHOR>
 * @date 2025/5/20 17:45
 */
public interface QcKeywordService {

    /**
     * 查询关键词列表
     *
     * @param payload 需要的一些参数
     * @return 关键词列表
     */
    List<QcKeyword> getQcKeywordList(JSONObject payload);

    /**
     * 新增关键词
     *
     * @param payload 需要的一些参数
     */
    void addQcKeyword(JSONObject payload);

    /**
     * 修改关键词
     *
     * @param payload 需要的一些参数
     */
    void updateQcKeyword(JSONObject payload);

    /**
     * 删除关键词
     *
     * @param id 关键词ID
     */
    void removeQcKeyword(Long id);

    /**
     * 查询关键词列表根据关键词库id
     *
     * @param id id
     * @return 关键词列表
     */
    List<QcKeyword> getQcKeywordByKeywordIds(Long id);
}
