<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ScoreTemplateMapper">

    <resultMap type="ScoreTemplate" id="ScoreTemplateResult">
        <result property="id"    column="id"    />
        <result property="tplName"    column="tpl_name"    />
        <result property="remark"    column="remark"    />
        <result property="itemCnt"    column="item_cnt"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectScoreTemplateVo">
        select id, tpl_name, remark, item_cnt, status, create_time, update_time from zj_score_template
    </sql>

    <select id="selectScoreTemplateList" parameterType="ScoreTemplate" resultMap="ScoreTemplateResult">
        <include refid="selectScoreTemplateVo"/>
        <where>
            <if test="tplName != null  and tplName != ''"> and tpl_name like concat('%', #{tplName}, '%')</if>
            <if test="itemCnt != null "> and item_cnt = #{itemCnt}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectScoreTemplateById" parameterType="Long" resultMap="ScoreTemplateResult">
        <include refid="selectScoreTemplateVo"/>
        where id = #{id}
    </select>

    <insert id="insertScoreTemplate" parameterType="ScoreTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into zj_score_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tplName != null and tplName != ''">tpl_name,</if>
            <if test="remark != null">remark,</if>
            <if test="itemCnt != null">item_cnt,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tplName != null and tplName != ''">#{tplName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="itemCnt != null">#{itemCnt},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateScoreTemplate" parameterType="ScoreTemplate">
        update zj_score_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="tplName != null and tplName != ''">tpl_name = #{tplName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="itemCnt != null">item_cnt = #{itemCnt},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScoreTemplateById" parameterType="Long">
        delete from zj_score_template where id = #{id}
    </delete>

    <delete id="deleteScoreTemplateByIds" parameterType="String">
        delete from zj_score_template where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>