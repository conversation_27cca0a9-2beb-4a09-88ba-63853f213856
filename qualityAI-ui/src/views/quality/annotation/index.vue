<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="唯一主键号" prop="onlyNo">
        <el-input
          v-model="queryParams.onlyNo"
          placeholder="请输入唯一主键号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:annotation:edit']"
        >修改</el-button>
      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:annotation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:annotation:export']"
        >导出标注</el-button>
      </el-col>
<!--      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleImport"
          v-hasPermi="['system:annotation:export']"
        >导入音频临时</el-button>
      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="annotationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一主键号" align="center" prop="onlyNo"  show-overflow-tooltip/>
      <el-table-column label="原始url地址" align="center" prop="oldUrl" show-overflow-tooltip/>
      <el-table-column label="最新url地址" align="center" prop="newUrl" show-overflow-tooltip/>
      <el-table-column label="最新音频名称" align="center" prop="newUrlName" show-overflow-tooltip/>
      <el-table-column label="开始时间" align="center" prop="startTime" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag :type="formatMilliseconds(scope.row.startTime)">
            {{ formatMilliseconds(scope.row.startTime) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag :type="formatMilliseconds(scope.row.endTime)">
            {{ formatMilliseconds(scope.row.endTime) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="原始文本" align="center" prop="originalText" show-overflow-tooltip/>
      <el-table-column label="正确文本" align="center" prop="correctText" show-overflow-tooltip/>
      <el-table-column label="标注类型" align="center" prop="type"show-overflow-tooltip >
        <template slot-scope="scope">
          <el-tag :type="getTagType(scope.row.type)">
            {{ getTypeLabel(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注信息" align="center" prop="note" show-overflow-tooltip/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:annotation:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:annotation:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改质检标注对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
<!--        <el-form-item label="原数据Id" prop="dataId">
          <el-input v-model="form.dataId" placeholder="请输入原数据Id" disabled/>
        </el-form-item>-->
        <el-form-item label="原始url地址" prop="oldUrl">
          <el-input v-model="form.oldUrl" type="textarea" placeholder="请输入内容" disabled/>
        </el-form-item>
        <el-form-item label="最新url地址" prop="newUrl">
          <el-input v-model="form.newUrl" type="textarea" placeholder="请输入内容" disabled/>
        </el-form-item>
        <el-form-item label="最新音频名称" prop="newUrlName">
          <el-input v-model="form.newUrlName" type="textarea" placeholder="请输入内容" disabled/>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-input v-model="form.startTime" placeholder="请输入开始时间" disabled/>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-input v-model="form.endTime" placeholder="请输入结束时间" disabled/>
        </el-form-item>
        <el-form-item label="原始文本" prop="originalText">
          <el-input v-model="form.originalText" type="textarea" placeholder="请输入内容" disabled/>
        </el-form-item>
        <el-form-item label="正确文本" prop="correctText">
          <el-input v-model="form.correctText" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注信息" prop="note">
          <el-input v-model="form.note" type="textarea" placeholder="请输入内容" :maxlength="200"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAnnotation, getAnnotation, delAnnotation, addAnnotation, updateAnnotation } from "@/api/quality/annotation";
import request from "@/utils/request";

export default {
  name: "Annotation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 质检标注表格数据
      annotationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dataId: null,
        oldUrl: null,
        newUrl: null,
        newUrlName: null,
        startTime: null,
        endTime: null,
        originalText: null,
        correctText: null,
        type: null,
        note: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    formatMilliseconds(ms) {
      // 1. 将毫秒转为秒
      const totalSeconds = Math.floor(ms / 1000);

      // 2. 计算小时、分钟、秒
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      // 3. 格式化为 2 位数（补零）
      const pad = (num) => num.toString().padStart(2, '0');

      return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
    },
    /** 查询质检标注列表 */
    getList() {
      this.loading = true;
      listAnnotation(this.queryParams).then(response => {
        this.annotationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 获取标签类型
    getTagType(type) {
      switch(type) {
        case 'correction': return 'danger'
        case 'highlight': return 'warning'
        case 'question': return 'primary'
        case 'comment': return 'success'
        default: return 'info'
      }
    },

    // 获取类型标签
    getTypeLabel(type) {
      const types = [
        { value: 'correction', label: '文本修正' },
        { value: 'highlight', label: '重点标注' },
        { value: 'question', label: '疑问点' },
        { value: 'comment', label: '评注' }
      ]
      const item = types.find(t => t.value === type)
      return item ? item.label : '未知类型'
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        dataId: null,
        oldUrl: null,
        newUrl: null,
        newUrlName: null,
        startTime: null,
        endTime: null,
        originalText: null,
        correctText: null,
        type: null,
        note: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加质检标注";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAnnotation(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改质检标注";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAnnotation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAnnotation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除质检标注编号为"' + ids + '"的数据项？').then(function() {
        return delAnnotation(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('qc/api/audio/exportSamples', {
        ...this.queryParams
      }, `audio_samples_${new Date().getTime()}.zip`)
    },
    handleImport() {
      request({
        url: 'qc/api/audio/importDb',
        method: 'post',
        data: {}
      })
    }
  }
};
</script>
