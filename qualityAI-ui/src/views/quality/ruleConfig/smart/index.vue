<template>
  <div class="app-container">
    <el-row :gutter="40">
      <!--部门数据-->
      <el-col :span="4" style="border-right:1px solid #F2F6FC;padding:0;">
        <div class="sortItem row spaceBet" @click="(click) => sortSelect(click)">
          <div style="font-weight: 900;">全部分类</div>
          <i class="el-icon-plus" @click="handleAdd"></i>
        </div>
        <div class="sortChild">
          <div v-for="(item,index) in sortList" :key="index" :class="`sortItem row spaceBet childItem ${item.dictCode==currSort?'selected':''}`" @click="(click) => sortSelect(click,item)">
            <div style="">{{item.dictLabel}}</div>
            <el-dropdown size="mini" @command="(command) => handleCommand(command, item)">
              <i class="el-icon-more"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="handleReset" icon="el-icon-refresh-right">重命名</el-dropdown-item>
<!--                <el-dropdown-item command="handleDelete" icon="el-icon-delete">删除</el-dropdown-item>-->
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-col>
      <!--用户数据-->
	  <el-col :span="20">
		<list @goEdit="goEdit" ref="ruleList" ></list>
		<el-dialog title="" :visible.sync="visit.edit" width="1400px" 
			:close-on-press-escape="false"
			:show-close="false"
			:close-on-click-modal="false"
			custom-class="no-header-dialog"
			height="860px"
		>
			<edit  @goList="goList" @getList="getList" ref="editRule"></edit>
		</el-dialog>
	   </el-col>
    </el-row>
	
    <!-- 添加或修改分类对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body
		:close-on-press-escape="false"
		:show-close="false"
		:close-on-click-modal="false"
	>
      <el-form ref="sortForm" :model="sortForm" :rules="rules" label-width="80px">
        <el-form-item label="分类名称" prop="dictLabel">
          <el-input v-model="sortForm.dictLabel" placeholder="请输入分类名称" maxlength="30" show-word-limit/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sortSubmitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { queryListByType,listData, addData, updateData } from "@/api/system/dict/data";
import edit from './edit.vue'
import list from './list.vue'
export default {
	components: {
		list,edit
	},
  	name: "User",
  	data() {
    	return {
	  		visit:{list:true,edit:false},
      		// 遮罩层
      		loading: true,
      		// 弹出层标题
      		title: "",
      		open: false,
      		currSort:0,
      		// 表单参数
      		sortForm: {dictLabel:""},
      		// 查询参数
      		queryParams: {
        		pageNum: 1,
        		pageSize: 10
        
      		},
	  		queryData:{
				ruleGroup:"",
				status:1,
				ruleType:"1"
	  		},
      // 表单校验
      rules: {
        dictLabel: [{ required: true, message: "分类名称不能为空", trigger: "11" }],
        
      },
      sortList:[
        {}
      ],
	  ruleList:[]
    };
  },
  watch: {
   
  },
  created() {
	queryListByType	({dictTypeList:["quality_smart_rule_group","quality_rule_classificate"],status:"0"}).then(response => {
		if(response.data){
			this.sortList = response.data["quality_smart_rule_group"];
			this.ruleTypeList = response.data["quality_rule_classificate"];
		}
	  });
  },
 	methods: {
		goEdit(row,op){
			var tmp = {};
			if(row && row.ruleId && row.ruleId != '' ){
				tmp = row;
			}else{
				if(!this.$refs.ruleList.queryData.ruleGroup || this.$refs.ruleList.queryData.ruleGroup == ''){
					this.$message({
			          message: '请选择规则分类',
			          type: 'warning'
			        });
					return;
				}else{
					tmp["ruleGroup"] = this.$refs.ruleList.queryData.ruleGroup;
				}
			}
			this.visit.edit = true;
			this.$nextTick(()=>{
				this.$refs.editRule.reload(tmp,op);
			})
		},
		goList(){
			this.visit.edit = false;
		},
		getList(){
			this.visit.edit = false;
			this.$refs.ruleList.getList();
		},
		/** 查询用户列表 */
	    getSortList() {
	      	this.loading = true;
	      	listData({dictType:"quality_smart_rule_group",pageNum:1,pageSize:100}).then(response => {
	          	this.sortList = response.rows;
	          	this.loading = false;
	        });
	    },
	    // 取消按钮
	    cancel() {
	      	this.open = false;
	    },
	    // 表单重置
	    reset() {
	      	this.sortForm = {};
	    },
	    // 更多操作触发
	    handleCommand(command, row) {
	    	switch (command) {
	        	case "handleReset":
	          		this.handleUpdate(row);
	          		break;
	        	default:
	          		break;
	      	}
	    },
	    /** 新增按钮操作 */
	    handleAdd() {
	      	this.reset();
	      	this.open = true;
	      	this.title = "添加分类";
	      
	    },
	    /** 修改按钮操作 */
	    handleUpdate(row) {
	      	this.reset();
	      	let sortForm = JSON.parse(JSON.stringify(row))
	      	this.sortForm = sortForm
	      	this.open = true;
	      	this.title = "修改分类名称";
	    },
   
	    /** 提交按钮 */
	    sortSubmitForm: function() {
			if(!this.sortForm.dictLabel || this.sortForm.dictLabel == ''){
				this.$message.error('分类名称不能为空');
				return
			}
			if(this.sortForm.dictCode && this.sortForm.dictCode!=''){
				updateData(this.sortForm).then(response => {
	          		this.$modal.msgSuccess("修改成功");
	          		this.open = false;
	          		this.getSortList();
		        });
				return;
			}
			this.sortForm['dictValue'] = this.sortList.length+1;
			this.sortForm['status']='0';
			this.sortForm['dictType'] = 'quality_smart_rule_group';
	        addData(this.sortForm).then(response => {
	          	this.$modal.msgSuccess("修改成功");
	          	this.open = false;
	          	this.getSortList();
	        }).finally(v=>{
				this.sortForm = {};
			});
	    },
		sortSelect(click,item){
			if(item){
				this.currSort = item.dictCode;
			}else{
				this.currSort = 0;
			}
			this.$refs.ruleList.queryData.ruleGroup = item?item.dictValue:null;
			this.$refs.ruleList.getList();
		}
	}
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.sortItem{
  border-left: 2px solid white;
  font-size: 13px;
  padding: 8px 14px;
  cursor: pointer;
}
.sortItem:hover{
  border-left: 2px solid #409EFF;
  background: #ecf5ff;
}
.sortItem.selected{
  border-left: 2px solid rgb(0, 255, 64);
  background: #ecf5ff;
}
.childItem{
  padding-left:30px;
}
.no-header-dialog .el-dialog__header {
  display: none;
}
.no-header-dialog .el-dialog__body {
  padding:10px 10px;
}
.app-container{
	padding:5px;
}
</style>