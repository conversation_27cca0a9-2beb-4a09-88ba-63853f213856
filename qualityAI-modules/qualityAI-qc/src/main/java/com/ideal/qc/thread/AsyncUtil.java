package com.ideal.qc.thread;
import java.util.List;


import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcSmartTaskDetail;
import com.ideal.qc.service.IQcSmartTaskService;


@Component
@EnableAsync
public class AsyncUtil {
	@Async(value="taskExecutor")
	public void doSmartTask(IQcSmartTaskService qstService,List<QcSmartTaskDetail> list,JSONObject tData) {
		qstService.batchDoSmartTaskDetail(list,tData);
	}
	
}
