package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcKeyword;
import com.ideal.qc.service.QcKeywordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 关键词Controller接口
 *
 * <AUTHOR>
 * @date 2025/5/20 17:38
 */
@RestController
@RequestMapping("/keyword")
public class QcKeywordController extends BaseController {

    @Resource
    private QcKeywordService qcKeywordService;

    /**
     * 查询关键词列表
     */
    @RequiresPermissions("quality:keyword:list")
    @PostMapping("/list")
    public TableDataInfo getQcKeywordList(@RequestBody JSONObject payload) {
        startPage();
        List<QcKeyword> list = qcKeywordService.getQcKeywordList(payload);
        return getDataTable(list);
    }

    /**
     * 新增关键词
     */
    @RequiresPermissions("quality:keyword:add")
    @PostMapping("/add")
    public AjaxResult addQcKeyword(@RequestBody JSONObject payload) {
        qcKeywordService.addQcKeyword(payload);
        return AjaxResult.success();
    }

    /**
     * 修改关键词
     */
    @RequiresPermissions("quality:keyword:edit")
    @PostMapping("/update")
    public AjaxResult updateQcKeyword(@RequestBody JSONObject payload) {
        qcKeywordService.updateQcKeyword(payload);
        return AjaxResult.success();
    }

    /**
     * 删除关键词
     */
    @RequiresPermissions("quality:keyword:remove")
    @PostMapping("/remove/{id}")
    public AjaxResult removeQcKeyword(@PathVariable Long id) {
        qcKeywordService.removeQcKeyword(id);
        return AjaxResult.success();
    }
}
