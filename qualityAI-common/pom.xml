<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ideal</groupId>
        <artifactId>qualityAI</artifactId>
        <version>3.6.5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>qualityAI-common-log</module>
        <module>qualityAI-common-core</module>
        <module>qualityAI-common-redis</module>
        <module>qualityAI-common-seata</module>
        <module>qualityAI-common-swagger</module>
        <module>qualityAI-common-security</module>
        <module>qualityAI-common-sensitive</module>
        <module>qualityAI-common-datascope</module>
        <module>qualityAI-common-datasource</module>
        <module>qualityAI-common-model</module>
    </modules>

    <artifactId>qualityAI-common</artifactId>
    <packaging>pom</packaging>

    <description>
        qualityAI-common通用模块
    </description>

</project>
