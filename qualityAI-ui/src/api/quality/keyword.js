import request from '@/utils/request'

// 查询关键词列表
export function getQcKeywordList(params, data) {
  return request({
    url: '/qc/keyword/list',
    method: 'post',
    params,
    data
  })
}

// 新增关键词
export function addQcKeyword(data) {
  return request({
    url: '/qc/keyword/add',
    method: 'post',
    data: data
  })
}

// 修改关键词
export function updateQcKeyword(data) {
  return request({
    url: '/qc/keyword/update',
    method: 'post',
    data: data
  })
}

// 删除关键词
export function removeQcKeyword(id) {
  return request({
    url: '/qc/keyword/remove/' + id,
    method: 'post'
  })
}
