package com.ideal.qc.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ideal.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcBizItem;
import com.ideal.qc.service.IQcBizItemService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 业务数据明细Controller
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/bizItem")
public class QcBizItemController extends BaseController
{
    @Autowired
    private IQcBizItemService qcBizItemService;

    /**
     * 查询业务数据明细列表
     */
    @RequiresPermissions("quality:bizItem:list")
    @GetMapping("/list")
    public TableDataInfo list(QcBizItem qcBizItem)
    {
        startPage();
        List<QcBizItem> list = qcBizItemService.selectQcBizItemList(qcBizItem);
        return getDataTable(list);
    }

    /**
     * 导出业务数据明细列表（标准导出）
     */
    @RequiresPermissions("quality:bizItem:export")
    @Log(title = "业务数据明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QcBizItem qcBizItem)
    {
        List<QcBizItem> list = qcBizItemService.selectQcBizItemList(qcBizItem);
        ExcelUtil<QcBizItem> util = new ExcelUtil<QcBizItem>(QcBizItem.class);
        util.exportExcel(response, list, "业务数据明细数据");
    }

    /**
     * 导出业务数据明细列表（拆分动态列）
     */
    @RequiresPermissions("quality:bizItem:export")
    @Log(title = "业务数据明细动态导出", businessType = BusinessType.EXPORT)
    @PostMapping("/exportWithDynamicColumns")
    public void exportWithDynamicColumns(HttpServletResponse response, QcBizItem qcBizItem)
    {
        qcBizItemService.exportBizItemWithDynamicColumns(response, qcBizItem);
    }

    /**
     * 获取业务数据明细详细信息
     */
    @RequiresPermissions("quality:bizItem:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(qcBizItemService.selectQcBizItemById(id));
    }

    /**
     * 新增业务数据明细
     */
    @RequiresPermissions("quality:bizItem:add")
    @Log(title = "业务数据明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QcBizItem qcBizItem)
    {
        return toAjax(qcBizItemService.insertQcBizItem(qcBizItem));
    }

    /**
     * 修改业务数据明细
     */
    @RequiresPermissions("quality:bizItem:edit")
    @Log(title = "业务数据明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QcBizItem qcBizItem)
    {
        return toAjax(qcBizItemService.updateQcBizItem(qcBizItem));
    }

    /**
     * 删除业务数据明细
     */
    @RequiresPermissions("quality:bizItem:remove")
    @Log(title = "业务数据明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(qcBizItemService.deleteQcBizItemByIds(ids));
    }

    @RequiresPermissions("quality:bizItem:query")
    @GetMapping(value = "/byNo/{bizNo}/{type}/{manualDetailId}")
    public AjaxResult getInfo(@PathVariable("bizNo") String bizNo,@PathVariable("type") String type,@PathVariable("manualDetailId") Long manualDetailId)
    {
        return success(qcBizItemService.selectQcBizItemByNo(bizNo,type,manualDetailId));
    }


    @RequiresPermissions("quality:bizItem:import")
    @Log(title = "导入业务数据明细", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        List<QcBizItem> rowDataList = qcBizItemService.importExcel(file);
        String operName = SecurityUtils.getUsername();
        String message = qcBizItemService.importBizItem(rowDataList, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<QcBizItem> util = new ExcelUtil<QcBizItem>(QcBizItem.class);
        util.importTemplateExcel(response, "业务数据明细数据");
    }
}
