package com.ideal.qc.controller;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ideal.common.core.utils.StringUtils;
import com.ideal.common.core.utils.uuid.UUID;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.Question;
import com.ideal.qc.domain.QuestionAnswer;
import com.ideal.qc.service.IQuestionService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.ZjQuestionnaire;
import com.ideal.qc.service.IZjQuestionnaireService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 问卷Controller
 * 
 * <AUTHOR>
 * @date 2025-03-24
 */
@RestController
@RequestMapping("/questionnaire")
public class ZjQuestionnaireController extends BaseController
{
    @Autowired
    private IZjQuestionnaireService zjQuestionnaireService;
    @Autowired
    private IQuestionService questionService;

    /**
     * 查询问卷列表
     */
    @RequiresPermissions("quality:questionnaire:list")
    @GetMapping("/list")
    public TableDataInfo list(ZjQuestionnaire zjQuestionnaire)
    {
        startPage();
        List<ZjQuestionnaire> list = zjQuestionnaireService.selectZjQuestionnaireList(zjQuestionnaire);
        return getDataTable(list);
    }

    /**
     * 导出问卷列表
     */
    @RequiresPermissions("quality:questionnaire:export")
    @Log(title = "问卷", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZjQuestionnaire zjQuestionnaire)
    {
        List<ZjQuestionnaire> list = zjQuestionnaireService.selectZjQuestionnaireList(zjQuestionnaire);
        ExcelUtil<ZjQuestionnaire> util = new ExcelUtil<ZjQuestionnaire>(ZjQuestionnaire.class);
        util.exportExcel(response, list, "问卷数据");
    }

    /**
     * 获取问卷详细信息
     */
    @RequiresPermissions("quality:questionnaire:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(zjQuestionnaireService.selectZjQuestionnaireById(id));
    }

    /**
     * 新增问卷
     */
    @RequiresPermissions("quality:questionnaire:add")
    @Log(title = "问卷", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZjQuestionnaire zjQuestionnaire)
    {
        zjQuestionnaire.setCreateBy(SecurityUtils.getUsername());
        return toAjax(zjQuestionnaireService.insertZjQuestionnaire(zjQuestionnaire));
    }
    /**
     * 修改问卷题目
     */
    @PostMapping("/updateQuestionnaireQus")
    public AjaxResult updateQuestionnaireQus(@RequestBody ZjQuestionnaire zjQuestionnaire)
    {
        zjQuestionnaireService.delQsQuetion(zjQuestionnaire.getQsNo());
        List<Question> list = zjQuestionnaire.getQuestionList();
        for(int i=0;i<list.size();i++){
            Question qs = list.get(i);
            Map<String,Object> map = new HashMap();
            map.put("qsnNo",zjQuestionnaire.getQsNo());
            map.put("qsNo",qs.getQusNo());
            map.put("qsSeq",qs.getQsSeq());
            map.put("tenantCode",zjQuestionnaire.getTenantCode());
            zjQuestionnaireService.saveQsQuetion(map);
        }
        return toAjax(1);
    }

    /**
     * 修改问卷
     */
    @RequiresPermissions("quality:questionnaire:edit")
    @Log(title = "问卷", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZjQuestionnaire zjQuestionnaire)
    {
        return toAjax(zjQuestionnaireService.updateZjQuestionnaire(zjQuestionnaire));
    }

    /**
     * 删除问卷
     */
    @RequiresPermissions("quality:questionnaire:remove")
    @Log(title = "问卷", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zjQuestionnaireService.deleteZjQuestionnaireByIds(ids));
    }

    @Log(title = "问卷导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(inputStream); // 对于xlsx文件
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
            int rows = sheet.getLastRowNum();//获得多少行的行数
            if(rows<2){
                return error("导入数据为空，导入失败，请重新导入!");
            }
            int rowStart = 1;
            Row first = sheet.getRow(0);//获取问卷信息
            ZjQuestionnaire qs = new ZjQuestionnaire();
            qs.setQsName(first.getCell(1).getStringCellValue());
            qs.setQsDes("导入模版");
            qs.setTenantCode("6000");
            qs.setQsNo(UUID.randomUUID().toString());
            qs.setIsValid("0");
            zjQuestionnaireService.insertZjQuestionnaire(qs);

            List <Question> list = new ArrayList<Question>();
            for (int i = rowStart; i <= rows; i++) {//遍历每行数据
                Row row = sheet.getRow(i);
                Question temp = new Question();
                temp.setIsValid("0");
                temp.setQusNo(UUID.randomUUID().toString());
                temp.setQsSeq(rowStart);
                String v = row.getCell(1).getStringCellValue();
                if(StringUtils.isEmpty(v)){
                    break;
                }
                temp.setQusName(v);
                String qusType = row.getCell(2).getStringCellValue();
                temp.setQusType(qusType);
                if("得分".equals(qusType)){
                    Double min = row.getCell(3).getNumericCellValue();
                    temp.setMinV(min.intValue());
                    Double max = row.getCell(4).getNumericCellValue();
                    temp.setMaxV(max.intValue());
                    questionService.insertQus(temp);
                }else if("单选".equals(qusType)||"多选".equals(qusType)){
                    questionService.insertQus(temp);
                    int cellItemStart = 3;
                    int cellItemEnd = row.getLastCellNum();
                    for(int j=cellItemStart;j<cellItemEnd;j++){
                        QuestionAnswer qa = new QuestionAnswer();
                        qa.setQuestionId(temp.getId());
                        String vt = row.getCell(j).getStringCellValue();
                        if(StringUtils.isEmpty(vt)){
                            break;
                        }
                        qa.setAnswer(vt);
                        qa.setSeq(j-2);
                        questionService.insertQusAnswer(qa);
                    }
                }else{
                    questionService.insertQus(temp);
                }


                Map<String,Object> map = new HashMap();
                map.put("qsnNo",qs.getQsNo());
                map.put("qsNo",temp.getQusNo());
                map.put("qsSeq",i);
                map.put("tenantCode","6000");
                zjQuestionnaireService.saveQsQuetion(map);
            }

            workbook.close(); // 关闭Workbook以释放资源
        } catch (IOException e) {
            e.printStackTrace();
            toAjax(0);
        }
        return toAjax(1);
    }

    @Log(title = "问卷模版", businessType = BusinessType.EXPORT)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            String fileName = "/问卷模版.xlsx";
            response.setContentType(request.getServletContext().getMimeType(fileName));
            // 设置Content-Disposition
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            InputStream is = ZjQuestionnaireController.class.getResourceAsStream(fileName);

            //FileInputStream fis = new FileInputStream( request.getContextPath()+"问卷模版.xlsx");
            OutputStream os = response.getOutputStream();
            try {
                int count = 0;
                byte[] buffer = new byte[1024 * 1024];
                while ((count = is.read(buffer)) != -1)
                    os.write(buffer, 0, count);
                os.flush();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (os != null)
                    os.close();
                if (is != null)
                    is.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("文件下载失败:" + e.getMessage());
        }

    }
}
