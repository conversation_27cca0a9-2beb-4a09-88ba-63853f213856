<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcScoringItemMapper">

    <resultMap id="qcScoringItemResultMap" type="QcScoringItem">
        <result property="id"    column="id"    />
        <result property="scoringClassificationId" column="scoring_classification_id" />
        <result property="scoringItemName" column="scoring_item_name" />
        <result property="scoringItemDescription" column="scoring_item_description" />
        <result property="hitRuleScore" column="hit_rule_score" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="templateId" column="template_id" />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

   <resultMap id="qcScoringItemDTOResultMap_1" type="com.ideal.qc.dto.QcScoringItemDTO">
        <result property="id"    column="id"    />
        <result property="scoringClassificationId" column="scoring_classification_id" />
        <result property="scoringItemName" column="scoring_item_name" />
        <result property="scoringItemDescription" column="scoring_item_description" />
        <result property="hitRuleScore" column="hit_rule_score" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="templateId" column="template_id" />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
        <result property="scoringClassificationName" column="scoring_classification_name" />
        <result property="scoringTemplateName" column="scoring_template_name" />
    </resultMap>

    <select id="getQcScoringItemList" resultMap="qcScoringItemDTOResultMap_1">
        SELECT
            qsi.id,
            qsi.scoring_classification_id,
            qsi.scoring_item_name,
            qsi.scoring_item_description,
            qsi.hit_rule_score,
            qsi.create_by,
            qsi.create_time,
            qsi.update_by,
            qsi.update_time,
            qsi.remark,
            qsi.status,
            qsi.template_id,
            qsi.is_deleted,
            qsc.scoring_classification_name,
            qst.scoring_template_name
        FROM
            qc_scoring_item qsi
        LEFT JOIN
            qc_scoring_classification qsc ON qsc.id = qsi.scoring_classification_id
        LEFT JOIN
            qc_scoring_template qst ON qst.id = qsc.scoring_template_id
        WHERE
            qsc.scoring_template_id = #{scoringTemplateId}
        <if test="scoringClassificationId != null">
            AND qsi.scoring_classification_id = #{scoringClassificationId}
        </if>
        <if test="scoringItemName != null and scoringItemName != ''">
            AND qsi.scoring_item_name LIKE concat('%', #{scoringItemName}, '%')
        </if>
        AND qsi.is_deleted = '0'
        ORDER BY
            qsi.id ASC
    </select>

    <resultMap id="qcScoringItemDTOResultMap_2" type="com.ideal.qc.dto.QcScoringItemDTO">
        <result property="id"    column="id"    />
        <result property="scoringClassificationId" column="scoring_classification_id" />
        <result property="scoringItemName" column="scoring_item_name" />
        <result property="scoringItemDescription" column="scoring_item_description" />
        <result property="hitRuleScore" column="hit_rule_score" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="templateId" column="template_id" />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
        <collection property="qcScoringHitRuleGroupDTOList" select="getQcScoringHitRuleGroup" column="id" />
    </resultMap>

    <select id="getQcScoringItem" resultMap="qcScoringItemDTOResultMap_2">
        SELECT
            qsi.id,
            qsi.scoring_classification_id,
            qsi.scoring_item_name,
            qsi.scoring_item_description,
            qsi.hit_rule_score,
            qsi.create_by,
            qsi.create_time,
            qsi.update_by,
            qsi.update_time,
            qsi.remark,
            qsi.status,
            qsi.template_id,
            qsi.is_deleted
        FROM
            qc_scoring_item qsi
        WHERE
            qsi.id = #{scoringItemId}
            AND qsi.is_deleted = '0'
    </select>

    <resultMap id="qcScoringHitRuleGroupDTOResultMap" type="com.ideal.qc.dto.QcScoringHitRuleGroupDTO">
        <result property="id" column="id" />
        <result property="scoringItemId" column="scoring_item_id" />
        <result property="ruleGroupMode" column="rule_group_mode" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="busSort" column="bus_sort" />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
        <collection property="qcScoringHitRuleItemList" select="getQcScoringHitRuleItem" column="id" />
    </resultMap>

    <select id="getQcScoringHitRuleGroup" resultMap="qcScoringHitRuleGroupDTOResultMap">
        SELECT
            qshrg.id,
            qshrg.scoring_item_id,
            qshrg.rule_group_mode,
            qshrg.create_by,
            qshrg.create_time,
            qshrg.update_by,
            qshrg.update_time,
            qshrg.remark,
            qshrg.bus_sort,
            qshrg.status,
            qshrg.is_deleted
        FROM
            qc_scoring_hit_rule_group qshrg
        WHERE
            qshrg.scoring_item_id = #{scoringItemId}
            AND qshrg.is_deleted = '0'
        ORDER BY
            qshrg.id ASC
    </select>

    <resultMap id="qcScoringHitRuleItemResultMap" type="QcScoringHitRuleItem">
        <result property="id" column="id" />
        <result property="scoringHitRuleGroupId" column="scoring_hit_rule_group_id" />
        <result property="ruleId" column="rule_id" />
        <result property="ruleClassification" column="rule_classification" />
        <result property="busSort" column="bus_sort" />
        <result property="ruleName" column="rule_name" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="status" column="status" />
        <result property="templateId" column="template_id" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>
    <select id="getQcScoringHitRuleItem" resultMap="qcScoringHitRuleItemResultMap">
        SELECT
            qshri.id,
            qshri.scoring_hit_rule_group_id,
            qshri.rule_id,
            qshri.rule_classification,
            qshri.create_by,
            qshri.create_time,
            qshri.update_by,
            qshri.update_time,
            qshri.remark,
            qshri.status,
            qshri.rule_name,
            qshri.template_id,
            qshri.is_deleted
        FROM
            qc_scoring_hit_rule_item qshri
        WHERE
            qshri.scoring_hit_rule_group_id = #{scoringHitRuleGroupId}
            AND qshri.is_deleted = '0'
        ORDER BY
            qshri.id ASC
    </select>

    <insert id="addQcScoringItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
            qc_scoring_item
        ( tenant_id, scoring_classification_id, scoring_item_name, scoring_item_description, hit_rule_score, create_time, create_by, update_time, update_by, remark, template_id )
        VALUES
        ( #{tenantId}, #{scoringClassificationId}, #{scoringItemName}, #{scoringItemDescription}, #{hitRuleScore}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy}, #{remark}, #{templateId})
    </insert>

    <update id="updateQcScoringItem">
        UPDATE qc_scoring_item
        SET scoring_classification_id = #{scoringClassificationId},
            scoring_item_name = #{scoringItemName},
            scoring_item_description = #{scoringItemDescription},
            hit_rule_score = #{hitRuleScore},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark},
            status = #{status}
        WHERE
            id = #{id}
    </update>

    <select id="getQcScoringItemByScoringClassificationIds" resultMap="qcScoringItemResultMap">
        SELECT
            qsi.id,
            qsi.scoring_classification_id,
            qsi.scoring_item_name,
            qsi.scoring_item_description,
            qsi.hit_rule_score,
            qsi.create_by,
            qsi.create_time,
            qsi.update_by,
            qsi.update_time,
            qsi.remark,
            qsi.status
        FROM
            qc_scoring_item qsi
        WHERE
            qsi.scoring_classification_id = #{scoringClassificationId}
            AND qsi.is_deleted = '0'
        ORDER BY
            qsi.id ASC
    </select>

    <update id="removeQcScoringItem">
        UPDATE qc_scoring_item
        SET is_deleted = '1'
        WHERE
            id = #{id}
    </update>

    <update id="updateQcScoringItemStatus">
        UPDATE qc_scoring_item
        SET status = #{status}
        WHERE
            id = #{id}
    </update>

    <resultMap id="qcScoringItemDTOResultMap_3" type="com.ideal.qc.dto.QcScoringItemDTO">
        <result property="id"    column="id"    />
        <result property="scoringClassificationId" column="scoring_classification_id" />
        <result property="scoringItemName" column="scoring_item_name" />
        <result property="scoringItemDescription" column="scoring_item_description" />
        <result property="hitRuleScore" column="hit_rule_score" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="getQcScoringItemListByTemplateId" resultMap="qcScoringItemDTOResultMap_3">
        SELECT
            qsi.id,
            qsi.scoring_classification_id,
            qsi.scoring_item_name,
            qsi.scoring_item_description,
            qsi.hit_rule_score,
            qsi.create_by,
            qsi.create_time
        FROM
            qc_scoring_item qsi
        LEFT JOIN
            qc_scoring_classification qsc ON qsc.id = qsi.scoring_classification_id
        LEFT JOIN
            qc_scoring_template qst ON qst.id = qsc.scoring_template_id
        WHERE
            qsc.scoring_template_id = #{scoringTemplateId}
            AND qsi.is_deleted = '0' and qsi.status = '1'
        ORDER BY
            qsi.id ASC
    </select>
</mapper>