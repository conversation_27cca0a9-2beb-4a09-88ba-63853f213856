package com.ideal.qc.service;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcKeywordLibrary;

import java.util.List;

/**
 * 关键词库Service接口
 *
 * <AUTHOR>
 * @date 2025/5/20 17:45
 */
public interface QcKeywordLibraryService {

    /**
     * 查询关键词库列表
     *
     * @param payload 需要的一些参数
     * @return 关键词库列表
     */
    List<QcKeywordLibrary> getQcKeywordLibraryList(JSONObject payload);

    /**
     * 新增关键词库
     *
     * @param payload 需要的一些参数
     */
    void addQcKeywordLibrary(JSONObject payload);

    /**
     * 修改关键词库
     *
     * @param payload 需要的一些参数
     */
    void updateQcKeywordLibrary(JSONObject payload);

    /**
     * 删除关键词库
     *
     * @param id 关键词库ID
     */
    void removeQcKeywordLibrary(Long id);

    /**
     * 查询关键词库列表根据关键词库分类id
     *
     * @param id id
     * @return 关键词库列表
     */
    List<QcKeywordLibrary> getQcKeywordLibraryByKeywordLibraryClassificationIds(Long id);
}
