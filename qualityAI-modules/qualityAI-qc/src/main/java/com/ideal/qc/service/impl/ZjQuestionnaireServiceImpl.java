package com.ideal.qc.service.impl;

import java.util.List;
import java.util.Map;

import com.ideal.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.ZjQuestionnaireMapper;
import com.ideal.qc.domain.ZjQuestionnaire;
import com.ideal.qc.service.IZjQuestionnaireService;

/**
 * 问卷Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-24
 */
@Service
public class ZjQuestionnaireServiceImpl implements IZjQuestionnaireService 
{
    @Autowired
    private ZjQuestionnaireMapper zjQuestionnaireMapper;

    /**
     * 查询问卷
     * 
     * @param id 问卷主键
     * @return 问卷
     */
    @Override
    public ZjQuestionnaire selectZjQuestionnaireById(Long id)
    {
        return zjQuestionnaireMapper.selectZjQuestionnaireById(id);
    }

    /**
     * 查询问卷列表
     * 
     * @param zjQuestionnaire 问卷
     * @return 问卷
     */
    @Override
    public List<ZjQuestionnaire> selectZjQuestionnaireList(ZjQuestionnaire zjQuestionnaire)
    {
        return zjQuestionnaireMapper.selectZjQuestionnaireList(zjQuestionnaire);
    }

    /**
     * 新增问卷
     * 
     * @param zjQuestionnaire 问卷
     * @return 结果
     */
    @Override
    public int insertZjQuestionnaire(ZjQuestionnaire zjQuestionnaire)
    {
        zjQuestionnaire.setCreateTime(DateUtils.getNowDate());
        return zjQuestionnaireMapper.insertZjQuestionnaire(zjQuestionnaire);
    }

    /**
     * 修改问卷
     * 
     * @param zjQuestionnaire 问卷
     * @return 结果
     */
    @Override
    public int updateZjQuestionnaire(ZjQuestionnaire zjQuestionnaire)
    {
        zjQuestionnaire.setUpdateTime(DateUtils.getNowDate());
        return zjQuestionnaireMapper.updateZjQuestionnaire(zjQuestionnaire);
    }

    /**
     * 批量删除问卷
     * 
     * @param ids 需要删除的问卷主键
     * @return 结果
     */
    @Override
    public int deleteZjQuestionnaireByIds(Long[] ids)
    {
        return zjQuestionnaireMapper.deleteZjQuestionnaireByIds(ids);
    }

    /**
     * 删除问卷信息
     * 
     * @param id 问卷主键
     * @return 结果
     */
    @Override
    public int deleteZjQuestionnaireById(Long id)
    {
        return zjQuestionnaireMapper.deleteZjQuestionnaireById(id);
    }

    @Override
    public void delQsQuetion(String qsNo) {
        zjQuestionnaireMapper.delQsQuetion(qsNo);
    }

    @Override
    public void saveQsQuetion(Map<String, Object> map) {
        zjQuestionnaireMapper.saveQsQuetion(map);
    }
}
