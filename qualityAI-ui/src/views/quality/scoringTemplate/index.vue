<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="mini" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模板名称" prop="scoringTemplateName">
        <el-input v-model="queryParams.scoringTemplateName" placeholder="请输入模板名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="模板类型" prop="scoringTemplateType">
        <el-select v-model="queryParams.scoringTemplateType" placeholder="请选择模板类型" clearable>
          <el-option v-for="dict in dict.type.quality_scoring_template_type" :key="dict.value" :label="dict.label"
            :value="dict.value" @keyup.enter.native="handleQuery" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['quality:scoringTemplate:add']">新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="scoringTemplateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="模板名称" align="left" prop="scoringTemplateName" />
      <el-table-column label="模板类型" align="center" prop="scoringTemplateType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.quality_scoring_template_type" :value="scope.row.scoringTemplateType" />
        </template>
      </el-table-column>
      <el-table-column label="基本分数" align="center" prop="basicScore" />
      <el-table-column label="最高分数" align="center" prop="highestScore" />
      <el-table-column label="最低分数" align="center" prop="lowestScore" />
      <el-table-column label="是否启用" align="center">
        <template slot-scope="scope">
          <el-switch active-value="1" inactive-value="0" v-model="scope.row.status"
            @change="handleScoringTemplateStatus(scope.row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['quality:scoringTemplate:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['quality:scoringTemplate:remove']">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="viewTempate(scope.row)">预览</el-button>
          <el-button size="mini" type="text" @click="openScoringClassification(scope.row)"
            v-hasPermi="['quality:scoringClassification:query']">更多<i class="el-icon-d-arrow-right"></i></el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body :close-on-click-modal="false">
      <el-form size="mini" :inline="true" label-position="top" ref="scoringTemplateForm" :model="scoringTemplateForm"
        :rules="scoringTemplateAddOrUpdateFormRules" label-width="100px" class="scoringTemplateAddOrUpdateForm">
        <el-row>
          <el-col>
            <el-form-item label="模板名称" prop="scoringTemplateName">
              <el-input v-model="scoringTemplateForm.scoringTemplateName" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="模板类型" prop="scoringTemplateType">
              <el-select v-model="scoringTemplateForm.scoringTemplateType" placeholder="请选择模板类型" clearable>
                <el-option v-for="dict in dict.type.quality_scoring_template_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="基本分数" prop="basicScore">
              <el-input type="number" v-model="scoringTemplateForm.basicScore" placeholder="请输入基本分数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="最高分数" prop="highestScore">
              <el-input type="number" v-model="scoringTemplateForm.highestScore" placeholder="请输入基本分数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="最低分数" prop="lowestScore">
              <el-input type="number" v-model="scoringTemplateForm.lowestScore" placeholder="请输入基本分数" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="conf.view.title" :visible.sync="conf.view.open" width="1600px" height="800px" append-to-body :close-on-click-modal="false">
      <div style="text-align: right;">
        <el-button type="primary" @click="conf.view.open = false" size="mini">关 闭</el-button>
      </div>
      <div v-if="conf.view.open">
        <tempViewModal :viewParam="conf.view.param" ref="tempViewModal"></tempViewModal>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getQcScoringTemplateList, addQcScoringTemplate, updateQcScoringTemplate, removeQcScoringTemplate } from "@/api/quality/scoringTemplate";
import tempViewModal from "@/views/quality/smart/view/template2.vue";
export default {
  name: "QualityScoringTemplate",
  dicts: ['quality_scoring_template_type'],
  components: {
    tempViewModal
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      scoringTemplateList: [],
      title: "",
      open: false,
      conf: {
        view: {
          title: "",
          open: false,
          param: { tempId: 0 }
        }
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        scoringTemplateName: null,
        scoringTemplateType: null
      },
      scoringTemplateAddOrUpdateFormRules: {
        scoringTemplateName: [
          { required: true, message: "模板名称不能为空", trigger: "blur" }
        ],
        scoringTemplateType: [
          { required: true, message: "模板类型不能为空", trigger: "change" }
        ],
        basicScore: [
          { required: true, message: "基本分数不能为空", trigger: "blur" }
        ],
        highestScore: [
          { required: true, message: "最高分数不能为空", trigger: "blur" }
        ],
        lowestScore: [
          { required: true, message: "最低分数不能为空", trigger: "blur" }
        ]
      },
      scoringTemplateForm: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      const queryParams = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize
      };
      const payload = {
        scoringTemplateName: this.queryParams.scoringTemplateName,
        scoringTemplateType: this.queryParams.scoringTemplateType,
      };
      getQcScoringTemplateList(queryParams, payload).then(response => {
        this.scoringTemplateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    cancel() {
      this.open = false;
      this.getList();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleAdd() {
      this.scoringTemplateForm = {};
      this.open = true;
      this.title = "添加评分模板";
    },
    handleUpdate(row) {
      this.scoringTemplateForm = row;
      this.open = true;
      this.title = "修改评分模板";
    },
    submitForm() {
      this.$refs["scoringTemplateForm"].validate(valid => {
        if (valid) {
          if (this.scoringTemplateForm.id != null) {
            updateQcScoringTemplate(this.scoringTemplateForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQcScoringTemplate(this.scoringTemplateForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否删除？').then(function () {
        return removeQcScoringTemplate(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    openScoringClassification(row) {
      const scoringTemplateId = row.id;
      this.$router.push("/quality/scoringTemplate/scoringClassification/" + scoringTemplateId);
    },
    handleScoringTemplateStatus(row) {
      this.loading = true;
      this.scoringTemplateForm = row;
      updateQcScoringTemplate(this.scoringTemplateForm).then(response => {
        if (row.status === '0') {
          this.$modal.msgSuccess("禁用成功");
        }
        if (row.status === '1') {
          this.$modal.msgSuccess("启用成功");
        }
        this.getList();
        this.loading = false;
      });
    },
    viewTempate(row) {
      this.conf.view.title = row.scoringTemplateName + "-预览";
      this.conf.view.open = true;
      this.conf.view.param.tempId = row.id;
	  this.conf.view.param.op = 'view';
      console.log(row);
    }
  }
};
</script>

<style scoped>
.scoringTemplateAddOrUpdateForm .el-form-item {
  width: 100%;
}

::v-deep .scoringTemplateAddOrUpdateForm .el-form-item .el-form-item__label {
  padding: 0px;
}

.scoringTemplateAddOrUpdateForm .el-select {
  width: 100%;
}
</style>