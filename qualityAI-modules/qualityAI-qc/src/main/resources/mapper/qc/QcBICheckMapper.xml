<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcBICheckMapper">


    <sql id="selectQcBizItemVo">
        select id, dataset_id, platform, project_name, metric_name, sub_metric_name, service_center, agent_id, agent_name, answer_start_at, answer_end_at, call_duration_s, record_date, survey_code, caller_phone, business_type, carrier, unique_key, province, city, contact_phone, area_code, product_type, service_order_id, service_type, finish_time, product_name, sales_point, task_result, recording_file, call_time, qc_lvl1_result, qc_lvl1_comment, qc_lvl1_emp_id, qc_lvl2_result, qc_lvl2_comment, qc_lvl2_emp_id, summary_remark, answers_json, tenant_id, create_time from qc_biz_item
    </sql>

    <select id="getVideoText" parameterType="String" resultType="Map">
        SELECT s.asr_result,s.id,s.file_id FROM qc_dataset_item s WHERE s.biz_no=#{value}
    </select>

    <select id="getVideoTextMatchKey" parameterType="Long" resultType="String">
        <!--select match_key from zj_data_airesult_matchkey s where data_no=#{value}-->
        SELECT s.hot_name FROM qc_asr_hotword s
    </select>

    <insert id="insertQcDataResult" parameterType="com.ideal.qc.domain.QcDataResult" useGeneratedKeys="true" keyProperty="id">
        insert into qc_data_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizNo != null">biz_no,</if>
            <if test="templateId != null">template_id,</if>
            <if test="aiResult != null">ai_result,</if>
            <if test="aiIsRight != null">ai_is_right,</if>
            <if test="qcResult != null">qc_result,</if>
            <if test="qcType != null">qc_type,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="invalidDesc != null">invalid_desc,</if>
            <if test="remark != null">remark,</if>
            <if test="manualDetailId != null">manual_detail_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bizNo != null">#{bizNo},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="aiResult != null">#{aiResult},</if>
            <if test="aiIsRight != null">#{aiIsRight},</if>
            <if test="qcResult != null">#{qcResult},</if>
            <if test="qcType != null">#{qcType},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="invalidDesc != null">#{invalidDesc},</if>
            <if test="remark != null">#{remark},</if>
            <if test="manualDetailId != null">#{manualDetailId},</if>
        </trim>
    </insert>

    <select id="getResultListByBizNo" parameterType="String" resultType="com.ideal.qc.domain.QcDataResult">
        select *,(SELECT d.scoring_template_name FROM qc_scoring_template d WHERE d.id=s.template_id) templateName from qc_data_result s where s.biz_no=#{bizNo}
    </select>

    <select id="tempFirstList" resultType="java.lang.String" parameterType="String" >
        SELECT s.template_hit_rule FROM qc_manual_task_detail s WHERE s.id=#{value}
    </select>

    <select id="tempAIList" resultType="java.lang.String" parameterType="String" >
        SELECT s.template_hit_rule FROM qc_smart_task_detail s WHERE s.biz_no=#{value}
    </select>

    <update id="updateManualStatus" parameterType="com.ideal.qc.domain.QcDataResult">
        UPDATE qc_manual_task_detail s SET s.finish_status='1',s.manual_inspect_time=NOW()
        WHERE s.id=#{manualDetailId}
    </update>
    <update id="updateSmartStatus" parameterType="com.ideal.qc.domain.QcDataResult">
        UPDATE qc_smart_task_detail s SET s.status='5',s.check_inspect_time=NOW() WHERE s.id=#{aiDetailId}
    </update>

    <select id="getQcScoringHitRuleGroup" resultType="java.util.Map" parameterType="Long">
        SELECT (SELECT i.scoring_item_name FROM qc_scoring_item i WHERE i.id=s.scoring_item_id) itemName,scoring_item_id itemId,'规则组' groupName
        FROM qc_scoring_hit_rule_group s WHERE s.id=#{value}
    </select>

    <select id="getAiResultByCondition" resultType="java.util.Map" parameterType="Map">
        SELECT s.id,s.smart_result FROM qc_smart_task_result s WHERE s.rule_id=#{ruleId} AND s.task_detail_id=#{taskDetailId} and s.rule_item_id=#{ruleItemId}
    </select>

    <!--<update id="updateAiSmartResult">
        update qc_smart_task_result set recheck_result=#{manualResult},remark=#{remark},first_recheck_time=now() where id = #{aiResultId}
    </update>-->

    <!--<insert id="addQcSmartTaskResult" parameterType="com.ideal.qc.domain.QcManualTaskResult" >
        insert into qc_smart_task_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskDetailId != null and taskDetailId != ''">task_detail_id,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="itemId != null and itemId != ''">item_id,</if>
            <if test="ruleId != null and ruleId != ''">rule_id,</if>
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="ruleItemId != null and ruleItemId != ''">rule_item_id,</if>
            &lt;!&ndash; insert 时，智能结果为空 表示时复检插入 &ndash;&gt;
            <if test="manualResult != null and manualResult != ''">recheck_Result,</if>
            first_recheck_time,
            create_time,
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskDetailId != null and taskDetailId != ''">#{taskDetailId},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="itemId != null and itemId != ''">#{itemId},</if>
            <if test="ruleId != null and ruleId != ''">#{ruleId},</if>
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="ruleItemId != null and ruleItemId != ''">#{ruleItemId},</if>
            &lt;!&ndash; insert 时，智能结果为空 表示时复检插入 &ndash;&gt;
            <if test="manualResult != null and manualResult != ''">#{manualResult},</if>
           now(),
            now(),
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>-->

    <select id="getSmartCheckList" resultType="com.ideal.qc.domain.QcManualTaskResult" parameterType="String">
        SELECT
        s.task_detail_id taskDetailId,
        (SELECT scoring_item_name FROM qc_scoring_item r WHERE r.id=s.item_id) itemName,
        s.item_id itemId,
        s.rule_id ruleId,
        s.rule_name ruleName,
        <!--(SELECT rule_name FROM qc_rule r WHERE r.rule_id=s.rule_id) ruleName,-->
        s.smart_result smartResult,
        s.recheck_result recheckResult,
        s.rule_item_id ruleItemId,
        s.remark,
        '规则组' ruleGroupName,
        s.create_time createTime,
        (SELECT t.scoring_template_name FROM qc_scoring_template t WHERE t.id=d.template_id) templateName,
        item_result itemResult
        FROM qc_smart_task_result s LEFT JOIN qc_smart_task_detail d ON s.task_detail_id=d.id
        WHERE d.biz_no = #{value}
    </select>

    <update id="updateSmartCountStatus" parameterType="com.ideal.qc.domain.QcDataResult">
        UPDATE qc_manual_task_detail s SET
        s.hit_rule_count=(SELECT hit_rule_count FROM qc_smart_task_detail q WHERE q.id=#{aiDetailId}),
        s.hit_minus_rule_count=(SELECT hit_minus_rule_count FROM qc_smart_task_detail q WHERE q.id=#{aiDetailId}),
        s.hit_plus_rule_count=(SELECT hit_plus_rule_count FROM qc_smart_task_detail q WHERE q.id=#{aiDetailId}),
        s.manual_inspect_score=(SELECT q.check_inspect_score FROM qc_smart_task_detail q WHERE q.id=#{aiDetailId}),
        s.hit_rule_count=(SELECT hit_rule_count FROM qc_smart_task_detail q WHERE q.id=#{aiDetailId})
        WHERE s.id=#{manualDetailId}
    </update>

    <update id="updateManualResult" parameterType="map">
        update qc_manual_task_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="workNo != null and workNo != ''">work_no=#{workNo},</if>
            <if test="workName != null and workName != ''">work_name=#{workName},</if>
            <if test="sessionLength != null">session_length=#{sessionLength},</if>
            <if test="hitRuleCount != null">hit_rule_count=#{hitRuleCount},</if>
            <if test="macInspectScore != null and macInspectScore != ''">mac_inspect_score=#{macInspectScore},</if>
            <if test="macInspectTime != null and macInspectTime != ''">mac_inspect_time=now(),</if>
            <if test="checkInspectTime != null and checkInspectTime != ''">manual_inspect_time=#{checkInspectTime},</if>
            <if test="status != null and status != ''">status=#{status},</if>
            update_time=now(),
            <if test="updateBy != null and updateBy != ''">update_by=#{updateBy},</if>
            <if test="hitPlusRuleCount != null ">hit_plus_rule_count=#{hitPlusRuleCount},</if>
            <if test="hitMinusRuleCount != null">hit_minus_rule_count=#{hitMinusRuleCount},</if>
            <if test="templateHitRule != null and templateHitRule != ''">template_hit_rule=#{templateHitRule},</if>
            <if test="checkInspectScore != null">manual_inspect_score=#{checkInspectScore},</if>
            <if test="macInspectResult != null and macInspectResult != ''">mac_inspect_result=#{macInspectResult},</if>
            <if test="checkInspectResult != null and checkInspectResult != ''">check_inspect_result=#{checkInspectResult},</if>
        </trim>
        where id=#{id}
        <if test="detailList != null and detailList.size()>0">
            <foreach item="item" collection="detailList" >
                ;
                <if test="item.id == null or item.id == ''">
                    <include refid="insertQcManualTaskResultSql" />
                </if>
                <if test="item.id != null and item.id != ''">
                    <include refid="updateQcManualTaskResultSql" />
                </if>
            </foreach>
        </if>
    </update>
    <sql id="updateQcManualTaskResultSql" >
        update qc_manual_task_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="item.itemName != null and item.itemName != ''">item_name=#{item.itemName},</if>
            <if test="item.ruleId != null and item.ruleId != ''">rule_id=#{item.ruleId},</if>
            <if test="item.ruleItemId != null and item.ruleItemId != ''">rule_item_id=#{item.ruleItemId},</if>
            <if test="item.smartResult != null and item.smartResult != ''">smart_result=#{item.smartResult},</if>
            <if test="item.recheckResult != null and item.recheckResult != ''">recheck_result=#{item.recheckResult},</if>
            <if test="item.finalResult != null and item.finalResult != ''">final_result=#{item.finalResult},</if>
            update_time=now(),
            <if test="updateBy != null and updateBy != ''">update_by=#{updateBy},</if>
            <if test="item.updateBy != null and item.updateBy != ''">update_by=#{item.updateBy},</if>
            <if test="item.smartResultDetail != null and item.smartResultDetail != ''">smart_result_detail=#{item.smartResultDetail},</if>
            <if test="item.ruleName != null and item.ruleName != ''">rule_name=#{item.ruleName},</if>
            <if test="item.itemId != null and item.itemId != ''">item_id=#{item.itemId},</if>
            <if test="item.itemResult != null and item.itemResult != ''">item_result=#{item.itemResult},</if>
            <if test="item.firstRecheckTime != null and item.firstRecheckTime != ''">first_recheck_time=now(),</if>
            <if test="remark != null and remark != ''">remark=#{remark},</if>
        </trim>
        where id=#{item.id}
    </sql>
    <!-- insert qc_smart_task_result sql -->
    <sql id="insertQcManualTaskResultSql" >
        insert into qc_manual_task_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskDetailId != null and taskDetailId != ''">task_detail_id,</if>
            <if test="item.itemName != null and item.itemName != ''">item_name,</if>
            <if test="item.itemId != null and item.itemId != ''">item_id,</if>
            <if test="item.ruleId != null and item.ruleId != ''">rule_id,</if>
            <if test="item.ruleItemId != null and item.ruleItemId != ''">rule_item_id,</if>
            <if test="item.smartResult != null and item.smartResult != ''">smart_result,</if>
            <if test="item.result != null and item.result != ''">smart_result,</if>
            <!-- insert 时，智能结果为空 表示时复检插入 -->
            <if test="item.smartResult == null or item.smartResult == ''">first_recheck_time,</if>
            <if test="item.recheckResult != null and item.recheckResult != ''">recheck_Result,</if>
            <if test="item.firstRecheckTime != null and item.firstRecheckTime != ''">first_recheck_time,</if>
            <if test="item.finalResult != null and item.finalResult != ''">final_result,</if>
            create_time,
            <if test="item.createBy != null and item.createBy != ''">create_by,</if>
            <if test="item.detail != null and item.detail != ''">smart_result_detail,</if>
            <if test="item.semResult != null and item.semResult != ''">semantics_Result,</if>
            <if test="item.ruleName != null and item.ruleName != ''">rule_name,</if>
            <if test="item.itemResult != null and item.itemResult != ''">item_result,</if>
            <if test="item.remark != null and item.remark != ''">remark,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskDetailId != null and taskDetailId != ''">#{taskDetailId},</if>
            <if test="item.itemName != null and item.itemName != ''">#{item.itemName},</if>
            <if test="item.itemId != null and item.itemId != ''">#{item.itemId},</if>
            <if test="item.ruleId != null and item.ruleId != ''">#{item.ruleId},</if>
            <if test="item.ruleItemId != null and item.ruleItemId != ''">#{item.ruleItemId},</if>
            <if test="item.smartResult != null and item.smartResult != ''">#{item.smartResult},</if>
            <if test="item.result != null and item.result != ''">#{item.result},</if>
            <!-- insert 时，智能结果为空 表示时复检插入 -->
            <if test="item.smartResult == null or item.smartResult == ''">now(),</if>
            <if test="item.recheckResult != null and item.recheckResult != ''">#{item.recheckResult},</if>
            <if test="item.firstRecheckTime != null and item.firstRecheckTime != ''">now(),</if>
            <if test="item.finalResult != null and item.finalResult != ''">#{item.finalResult},</if>
            now(),
            <if test="item.createBy != null and item.createBy != ''">#{item.createBy},</if>
            <if test="item.detail != null and item.detail != ''">#{item.detail},</if>
            <if test="item.semResult != null and item.semResult != ''">#{item.semResult},</if>
            <if test="item.ruleName != null and item.ruleName != ''">#{item.ruleName},</if>
            <if test="item.itemResult != null and item.itemResult != ''">#{item.itemResult},</if>
            <if test="item.remark != null and item.remark != ''">#{item.remark},</if>
        </trim>
    </sql>
    <select id="manualResultList" resultType="com.ideal.qc.domain.QcManualTaskResult">
        select * from qc_manual_task_result s where s.task_detail_id=#{value}
    </select>

    <select id="getResultByDetailId" parameterType="String" resultType="com.ideal.qc.domain.QcDataResult">
        select *,(SELECT d.scoring_template_name FROM qc_scoring_template d WHERE d.id=s.template_id) templateName from qc_data_result s where s.manual_detail_id=#{value}
    </select>
</mapper>