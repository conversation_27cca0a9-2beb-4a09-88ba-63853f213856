package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcScoringClassification;
import com.ideal.qc.service.QcScoringClassificationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 评分分类表Controller接口
 *
 * <AUTHOR>
 * @date 2025/5/14 17:15
 */
@RestController
@RequestMapping("/scoring/classification")
public class QcScoringClassificationController extends BaseController {

    @Resource
    private QcScoringClassificationService qcScoringClassificationService;

    /**
     * 查询评分模板分类列表
     */
    @RequiresPermissions("quality:scoringClassification:query")
    @PostMapping("/list/{scoringTemplateId}")
    public AjaxResult getQcScoringClassificationList(@PathVariable("scoringTemplateId") Long scoringTemplateId) {
        List<QcScoringClassification> result = qcScoringClassificationService.getQcScoringClassificationList(scoringTemplateId);
        return AjaxResult.success(result);
    }

    /**
     * 新增评分模板分类
     */
    @RequiresPermissions("quality:scoringClassification:add")
    @PostMapping("/add")
    public AjaxResult addQcScoringClassification(@RequestBody JSONObject payload) {
        qcScoringClassificationService.addQcScoringClassification(payload);
        return AjaxResult.success();
    }

    /**
     * 修改评分模板分类
     */
    @RequiresPermissions("quality:scoringClassification:edit")
    @PostMapping("/update")
    public AjaxResult updateQcScoringClassification(@RequestBody JSONObject payload) {
        qcScoringClassificationService.updateQcScoringClassification(payload);
        return AjaxResult.success();
    }

    /**
     * 删除评分模板分类
     */
    @RequiresPermissions("quality:scoringClassification:remove")
    @PostMapping("/remove/{id}")
    public AjaxResult removeQcScoringClassification(@PathVariable Long id) {
        qcScoringClassificationService.removeQcScoringClassification(id);
        return AjaxResult.success();
    }
}
