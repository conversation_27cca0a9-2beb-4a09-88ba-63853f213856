<template>
  <div class="inspection-task-system">

    <!-- 主内容区域 -->
    <div class="content-container">
      <!-- 左侧任务列表 -->
      <div class="task-list-container">
        <div class="search-section">
          <!-- <div class="search-title">
            <i class="el-icon-search"></i>
            <span>任务搜索</span>
          </div> -->
          <div class="search-form">
            <div class="date-range-container">
              <el-date-picker v-model="taskSearchForm.dateRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" size="small"
                class="date-range-picker" clearable></el-date-picker>
            </div>

            <div class="search-input-container">
              <el-input v-model="taskSearchForm.taskName" placeholder="任务名称" clearable size="small"
                class="search-input" />
              <!-- <i slot="prefix" class="el-input__icon el-icon-search"></i> -->
            </div>

            <div class="type-select-container">
              <el-select v-model="taskSearchForm.taskType" placeholder="类型" clearable size="small" class="type-select">
                <el-option label="定时任务" value="1"></el-option>
                <el-option label="手动任务" value="0"></el-option>
              </el-select>
            </div>

            <div class="search-btn-container">
              <el-button type="primary" icon="el-icon-search" size="small" @click="searchTaskList">搜索</el-button>
            </div>
          </div>
        </div>

        <div class="task-list">
          <div class="task-header">
            <span>任务列表</span>
            <span>共 <span class="task-count">{{ taskList.length }}</span> 项</span>
          </div>
          <div v-if="taskList.length > 0" class="scrollable-task-list">
            <div v-for="task in taskList" :key="task.id" class="task-item" :class="{ active: selectedTask.id === task.id }" @click="selectTask(task)">
              <div class="task-name">
                <i class="el-icon-document"></i>
                <!-- 已完成 -->
                <i v-if="task.status == 1" class="dot" style="background: #67c23a;"></i>
                <!-- 未完成 -->
                <i v-else class="dot" style="background: #f56c6c;"></i>
                <span>{{ task.taskName }}</span>
              </div>
              <!-- <span>期限完成时间:</span>
              <span>{{ task.deadlinTime }}</span> -->
            </div>
          </div>
          <div class="empty-state" v-else>
            <i class="el-icon-folder-opened"></i>
            <div class="empty-text">未找到相关任务</div>

          </div>
        </div>
      </div>

      <!-- 右侧任务明细 -->
      <div class="detail-container">
        <div class="detail-header">
          <div class="detail-title">
            <!-- <i class="el-icon-tickets"></i> -->
            <span>
              {{ selectedTask.taskName || '请选择任务' }}

              <!-- <span class="stat-item total">
                <i class="dot" style="background: #409eff;"></i>总任务：{{ taskDetailsTotal }}
              </span> -->
              <span class="stat-item uncompleted">
                <i class="dot" style="background: #B0E2FF;"></i>会话数：{{ sessionCount }}
              </span>
              <span class="stat-item completed">
                <i class="dot" style="background: #67c23a;"></i>已完成：{{ finishCount }}
              </span>
              <!-- <span class="stat-item total">
                <i class="dot" style="background: #c0c4cc;"></i>期限完成时间：{{ deadlineTime }}
              </span> -->
            </span>
          </div>
        </div>

        <div class="detail-search">
          <div class="compact-search-grid">
            <div class="compact-search-row">
              <div class="compact-search-item">
                <div class="compact-search-label">完成状态:</div>
                <el-select v-model="detailSearchForm.finishStatus" placeholder="请选择状态" clearable size="small"
                  class="compact-select">
                  <!-- <el-option label="全部" value="all"></el-option> -->
                  <el-option label="已完成" value="1"></el-option>
                  <el-option label="未完成" value="0"></el-option>
                </el-select>
              </div>

              <div class="compact-search-item">
                <div class="compact-search-label">座席工号:</div>
                <el-input v-model="detailSearchForm.workNo" placeholder="请输入座席工号" size="small" class="compact-input"
                  clearable></el-input>
              </div>

              <div class="compact-search-item">
                <div class="compact-search-label">座席姓名:</div>
                <el-input v-model="detailSearchForm.workName" placeholder="请输入座席姓名" size="small" class="compact-input"
                  clearable></el-input>
              </div>

              <div class="compact-search-item">
                <div class="compact-search-label">命中规则(可多选):</div>
                <el-select v-model="detailSearchForm.hitRules" placeholder="请选择命中规则" clearable size="small"
                  class="compact-select" multiple>
                  <el-option v-for="item in hitRuleOptions" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </div>


              <div class="compact-search-item">
                <div class="compact-search-label">评分项:</div>
                <el-select v-model="detailSearchForm.hitItem" placeholder="请选择评分项" clearable size="small"
                  class="compact-select">
                  <el-option v-for="item in hitItemOptions" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </div>

              <div class="compact-search-item">
                <div class="compact-search-label">会话时长(秒):</div>
                <div class="compact-amount-range">
                  <el-input v-model="detailSearchForm.minSession" placeholder="最小值" class="compact-range-input"
                    type="number" min="0" size="small" clearable></el-input>
                  <span class="range-separator">-</span>
                  <el-input v-model="detailSearchForm.maxSession" placeholder="最大值" class="compact-range-input"
                    type="number" min="0" size="small" clearable></el-input>
                </div>
              </div>
            </div>
          </div>

          <div class="compact-search-actions">
            <el-button @click="resetDetailSearch" size="small" class="btn btn-reset">重置</el-button>
            <el-button type="primary" @click="handleSearchDetails" size="small" class="btn btn-search">查询</el-button>
          </div>
        </div>

        <!-- <div class="detail-table"> -->
        <!-- <div class="table-container"> -->
        <el-table :data="taskDetails" border style="width: 100%" height="100%">
          <el-table-column prop="manualType" label="质检类型" width="80" align="center" fixed>
            <template slot-scope="scope">
              {{ scope.row.manualType == '1' ? '人工抽检' : '人工复检' }}
            </template>
          </el-table-column>
          <el-table-column prop="finishStatus" label="状态" width="100" align="center">
            <template slot-scope="scope">
              {{ scope.row.finishStatus == '0' ? '未完成' : '已完成' }}
            </template>
          </el-table-column>
          <el-table-column prop="inspector" label="质检员工号" width="90" align="center"></el-table-column>
          <el-table-column prop="workNo" label="座席工号" width="80" align="center">
              <template slot-scope="scope">
                <span>{{ getName(scope.row.workNo) }}</span>
              </template>
          </el-table-column>
          <el-table-column prop="workName" label="座席姓名" width="80" align="center">
              <template slot-scope="scope">
                <span>{{ getName(scope.row.workName) }}</span>
              </template>
          </el-table-column>
          <el-table-column prop="sessionLength" label="会话时长" width="80" align="center"></el-table-column>
          <el-table-column prop="manualInspectScore" label="人工质检得分" width="100" align="center"></el-table-column>
          <el-table-column prop="manualInspectTime" label="人工完成时间" width="100" align="center"></el-table-column>
          <el-table-column prop="hitRuleCount" label="命中规则数量" width="100" align="center"></el-table-column>
          <el-table-column prop="macInspectScore" label="机检得分" width="80" align="center"></el-table-column>
          <el-table-column prop="macInspectTime" label="机检完成时间" width="100" align="center"></el-table-column>
          <el-table-column prop="bizNo" label="会话ID" width="140" align="center"></el-table-column>
          <el-table-column prop="planName" label="抽检计划名称" width="120" align="center"></el-table-column>
          <el-table-column prop="taskName" label="抽检任务名称" width="120" align="center"></el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <!-- <div class="actions-cell"> -->
              <el-button size="mini" type="text"  v-if="scope.row.finishStatus == '0'" @click="manualCheck(scope.row)">质检</el-button>
              <el-button size="mini" type="text"  v-if="scope.row.finishStatus == '1'" @click="showCheckResult(scope.row)">查看</el-button>
              <!-- <el-button size="mini" type="text" @click="manualCheck(scope.row)">标记</el-button> -->
              <!-- <el-button size="mini" class="action-btn btn-return" @click="returnItem(scope.row)">
                      返回
                    </el-button> -->
              <!-- </div> -->
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="taskDetailsTotal > 0" :total="taskDetailsTotal" :page.sync="detailQueryParams.pageNum"
          :limit.sync="detailQueryParams.pageSize" @pagination="searchDetails" />
        <!-- </div> -->
        <!-- </div> -->
      </div>
    </div>
  </div>
</template>

<script>

import { manualTaskList, detailList, getRulesFromManualResult, getCountAndTime } from "@/api/quality/manualTask";
import { getAllRulesById } from "@/api/quality/manualPlan";
import { Utils } from '@/api/util/common';



export default {
  name: 'manualTaskDetail',
  data() {
    return {
      // 任务搜索条件
      taskSearchForm: {
        dateRange: ['', ''],
        taskName: '',
        type: ''
      },

      // 选中的任务
      selectedTask: {},

      //任务明细列表total
      taskDetailsTotal: 0,
      //已完成的任务明细数量
      finishCount: null,
      //该任务总会话数量
      sessionCount: null,
      //当前任务期限完成时间
      deadlineTime: null,

      // 明细搜索条件
      detailQueryParams: {
        pageNum: 1,
        pageSize: 10,
      },

      detailSearchForm: {
        finishStatus: '',
        workNo: '',
        workName: '',
        hitRules: [],
        hitItem: null,
        minSession: null,
        maxSession: null
      },
      //命中规则列表
      hitRuleOptions: [],
      //命中评分项列表
      hitItemOptions: [],
      // 任务列表数据
      taskList: [],
      // 当前任务下的明细列表
      taskDetails: [],
    };
  },
  computed: {

  },
  created() {
    this.getTaskList()
  },


  methods: {

    //脱敏
    getName(v){
      return Utils.getName(v)
    },

    // 搜索任务
    searchTaskList() {
      this.getTaskList()
    },

    //查询任务列表
    getTaskList() {
      manualTaskList(this.taskSearchForm).then(response => {
        const data = response.data
        this.taskList = data
        // 默认选中第一个任务
        // if (this.taskList.length > 0) {
        //   this.selectTask(this.taskList[0]);
        // }
      }
      );
    },

    // 选择任务
    selectTask(task) {
      console.log('status',task.status)
      this.selectedTask = task;

      this.detailQueryParams.pageNum = 1
      this.detailQueryParams.pageSize = 10

      this.detailSearchForm.manualTaskId = this.selectedTask.id
      this.detailSearchForm.finishStatus = ''
      this.detailSearchForm.workNo = ''
      this.detailSearchForm.workName = ''
      this.detailSearchForm.hitRules = []
      this.detailSearchForm.hitItem = null
      this.detailSearchForm.minSession = null
      this.detailSearchForm.maxSession = null
      this.getCountAndTime()
      this.getHitRuleOptions()
      this.searchDetails()
    },



    //重置明细搜索条件
    resetDetailSearch(){
    this.detailSearchForm.finishStatus = ''
      this.detailSearchForm.workNo = ''
      this.detailSearchForm.workName = ''
      this.detailSearchForm.hitRules = []
      this.detailSearchForm.hitItem = null
      this.detailSearchForm.minSession = null
      this.detailSearchForm.maxSession = null
    },

    //任务明细搜索按钮
    handleSearchDetails() {
      this.detailQueryParams.pageNum = 1
      this.searchDetails()
    },

    //查询当前任务下的已完成数量及期限完成时间
    getCountAndTime() {
      getCountAndTime(this.detailSearchForm.manualTaskId).then(response => {
        if(!response.data.finishCount){
          this.finishCount = 0
        }else{
          this.finishCount = response.data.finishCount
        }
        this.sessionCount = response.data.sessionCount
      }
      );
    },

    // 查询当前任务下的任务明细
    searchDetails() {
      detailList(this.detailQueryParams, this.detailSearchForm).then(response => {
        this.taskDetails = response.rows
        this.taskDetailsTotal = response.total
      }
      );
    },

    //获取当前任务的模板下的评分规则、评分项
    getHitRuleOptions() {
      getAllRulesById(this.selectedTask.templateId).then(response => {
        const data = response.data
        this.hitRuleOptions = data.rules.map(item => ({
          label: item.name,
          value: item.id
        }));
        this.hitItemOptions = data.items.map(item => ({
          label: item.scoringItemName,
          value: item.id
        }));
      }
      )
    },
    // 跳转到质检台
    manualCheck(row) {
      this.$router.push({ path: '/quality-new-check/index', query: { bizNo: row.bizNo, type: row.manualType, manualDetailId: row.id, smartDetailId: row.smartTaskDetailId } })
    },
    //查看质检详情
    showCheckResult(row) {
      this.$router.push({ path: '/quality-new-detail/index', query: {bizNo:row.bizNo,type:row.manualType,manualDetailId:row.id,aiDetailId:row.smartTaskDetailId}})
    },

    // 根据分数获取样式类
    // getScoreClass(score) {
    //   if (score >= 95) return 'score-high';
    //   if (score >= 85) return 'score-medium';
    //   return 'score-low';
    // },

  }
};
</script>

<style lang="scss" scoped>
.inspection-task-system {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", Arial, sans-serif;

  .header {
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;
    background: linear-gradient(120deg, #1a73e8, #0d47a1);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      font-size: 20px;
      font-weight: 600;
      display: flex;
      align-items: center;

      i {
        margin-right: 12px;
        font-size: 24px;
      }
    }

    .date-range {
      background: rgba(255, 255, 255, 0.15);
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 14px;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
      }
    }
  }

  .content-container {
    flex: 1;
    display: flex;
    padding: 16px;
    overflow: hidden;
    background-color: #f5f7fa;

    /* 左侧任务列表样式 */
    .task-list-container {
      width: 320px;
      background: #fff;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
      margin-right: 16px;
      display: flex;
      flex-direction: column;

      .search-section {
        background: #fff;
        padding: 16px;
        border-bottom: 1px solid #ebeef5;

        .search-title {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 12px;
          color: #1a73e8;
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
          }
        }

        .search-form {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          grid-template-rows: auto auto;
          gap: 10px;

          .date-range-container {
            grid-column: 1 / span 2;

            .date-range-picker {
              width: 100%;

              ::v-deep .el-range-separator {
                width: 20px;
              }
            }
          }

          .search-input-container {
            grid-column: 1 / span 1;

            .search-input {
              width: 100%;

              ::v-deep .el-input__prefix {
                left: 8px;
                display: flex;
                align-items: center;
              }

              ::v-deep .el-input__inner {
                padding-left: 32px;
              }
            }
          }

          .type-select-container {
            grid-column: 2 / span 1;

            .type-select {
              width: 100%;
            }
          }

          .search-btn-container {
            grid-column: 1 / span 2;
            display: flex;
            justify-content: flex-end;

            .search-btn {
              width: 40px;
              padding: 9px;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
      }

      .task-list {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .task-header {
          padding: 12px 16px;
          border-bottom: 1px solid #ebeef5;
          font-weight: 600;
          color: #606266;
          display: flex;
          justify-content: space-between;
          background-color: #f8f9fa;

          .task-count {
            color: #1a73e8;
            font-weight: 600;
          }
        }

        .scrollable-task-list {
          flex: 1;
          overflow-y: auto;
          overflow-x: hidden;
        }

        .task-item {
          padding: 14px 16px;
          border-bottom: 1px solid #f0f2f5;
          cursor: pointer;
          transition: background 0.2s;

          &:hover {
            background: #f5f7fa;
          }

          &.active {
            background: #ecf5ff;
            border-left: 3px solid #1a73e8;
          }

          .task-name {
            font-weight: 600;
            margin-bottom: 8px;
            color: #303133;
            display: flex;
            align-items: center;
            font-size: 15px;

            i {
              margin-right: 8px;
              color: #1a73e8;
              font-size: 18px;
            }
          }

          .task-dates {
            display: flex;
            gap: 8px;
            font-size: 13px;
            color: #909399;
          }
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px;
          color: #909399;
          text-align: center;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #c0c4cc;
          }

          .empty-text {
            font-size: 16px;
          }
        }
      }
    }

    /* 右侧任务明细样式 */
    .detail-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
      overflow: hidden;

      .detail-header {
        padding: 16px 24px;
        border-bottom: 1px solid #ebeef5;
        background-color: #f8f9fa;

        .detail-title {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;

          i {
            margin-right: 10px;
            color: #1a73e8;
          }
        }
      }

      .detail-search {
        padding: 16px 24px;
        border-bottom: 1px solid #ebeef5;

        .compact-search-grid {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .compact-search-row {
            display: flex;
            gap: 15px;
          }

          .compact-search-item {
            flex: 1;
            display: flex;
            flex-direction: column;

            .compact-search-label {
              font-size: 13px;
              color: #606266;
              margin-bottom: 6px;
              font-weight: 500;
              white-space: nowrap;
            }

            .compact-select,
            .compact-input {
              width: 100%;
            }

            .compact-amount-range {
              display: flex;
              align-items: center;
              gap: 8px;

              .compact-range-input {
                flex: 1;
              }

              .range-separator {
                color: #909399;
                font-size: 14px;
                padding: 0 5px;
              }
            }
          }
        }

        .compact-search-actions {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          margin-top: 15px;

          .btn {
            padding: 8px 20px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;

            &.btn-search {
              background: #1a73e8;
              color: white;
              border: none;

              &:hover {
                background: #0d47a1;
              }
            }

            &.btn-reset {
              background: #fff;
              color: #606266;
              border: 1px solid #dcdfe6;

              &:hover {
                background: #f5f7fa;
              }
            }
          }
        }
      }

      .detail-table {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .table-container {
          flex: 1;
          overflow: auto;
          padding: 0 16px;

          .system-name {
            color: #1a73e8;
            font-weight: 500;
          }

          .score-cell {
            font-weight: 600;

            &.score-high {
              color: #67c23a;
            }

            &.score-medium {
              color: #e6a23c;
            }

            &.score-low {
              color: #f56c6c;
            }
          }

          .actions-cell {
            display: flex;
            gap: 6px;
            justify-content: center;

            .action-btn {
              padding: 5px 10px;
              border-radius: 4px;
              font-size: 12px;
              cursor: pointer;
              transition: all 0.2s;
              border: none;

              &.btn-view {
                background: #ecf5ff;
                color: #1a73e8;
                border: 1px solid #b3d8ff;
              }

              &.btn-return {
                background: #f0f9eb;
                color: #67c23a;
                border: 1px solid #c2e7b0;
              }

              &:hover {
                opacity: 0.9;
                transform: translateY(-1px);
              }
            }
          }
        }

        .pagination {
          padding: 12px 16px;
          border-top: 1px solid #ebeef5;
          display: flex;
          justify-content: flex-end;
          background-color: #f8f9fa;
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .content-container {
    flex-direction: column;

    .task-list-container {
      width: 100% !important;
      margin-bottom: 16px;
      margin-right: 0 !important;
    }

    .detail-container {
      margin-left: 0 !important;
    }
  }
}

@media (max-width: 992px) {
  .task-list-container .search-form {
    grid-template-columns: 1fr !important;

    .date-range-container {
      grid-column: 1 !important;
    }

    .search-input-container {
      grid-column: 1 !important;
    }

    .type-select-container {
      grid-column: 1 !important;
    }

    .search-btn-container {
      grid-column: 1 !important;
    }
  }

  .detail-search .compact-search-row {
    flex-direction: column !important;
    gap: 12px !important;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;
    padding: 12px 16px !important;
  }

  .detail-header {
    padding: 12px 16px !important;
  }

  .detail-search {
    padding: 12px 16px !important;
  }

  .content-container {
    padding: 8px !important;
  }
}




/* 统计信息容器：Flex 让内部同排 */
.stats {
  display: flex;
  align-items: center;
  gap: 16px;
  /* 每个统计项间距 */
}

/* 单个统计项样式 */
.stat-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

/* 彩色小圆点 */
.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}
</style>
