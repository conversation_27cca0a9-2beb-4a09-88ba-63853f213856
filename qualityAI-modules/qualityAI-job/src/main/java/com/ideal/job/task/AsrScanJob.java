package com.ideal.job.task;

import com.ideal.job.service.AsrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * asr定时任务调度
 * 
 * <AUTHOR>
@Component("asrScanJob")
public class AsrScanJob
{

    private static final String JOB_NAME = "aiBookAsrTask";
    @Autowired
    private AsrService asrService;
    public void aiBookAsrParamsTask(String ctx){
        asrService.handlePendingTasks(50);
    }
   

}
