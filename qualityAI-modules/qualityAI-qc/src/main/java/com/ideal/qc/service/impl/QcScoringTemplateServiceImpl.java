package com.ideal.qc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.constant.QcConstants;
import com.ideal.common.core.exception.ServiceException;
import com.ideal.common.redis.service.RedisService;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcScoringClassification;
import com.ideal.qc.domain.QcScoringTemplate;
import com.ideal.qc.mapper.QcScoringTemplateMapper;
import com.ideal.qc.service.QcScoringClassificationService;
import com.ideal.qc.service.QcScoringTemplateService;
import com.ideal.qc.vo.QcScoringTemplateVo;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * 评分模板表Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/14 10:01
 */
@Service
public class QcScoringTemplateServiceImpl implements QcScoringTemplateService {

    @Resource
    private QcScoringTemplateMapper qcScoringTemplateMapper;
    @Resource
    private QcScoringClassificationService qcScoringClassificationService;
    @Resource
	private RedisService redisService;

    /**
     * 查询评分模板列表
     */
    @Override
    public List<QcScoringTemplate> getQcScoringTemplateList(JSONObject payload) {
        return qcScoringTemplateMapper.getQcScoringTemplateList(payload);
    }

    /**
     * 新增评分模板
     */
    @Override
    public void addQcScoringTemplate(JSONObject payload) {
        this.judgmentOperation(payload);
        payload.put("createTime", DateTime.now());
        payload.put("createBy", SecurityUtils.getUsername());
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        payload.put("tenantId", SecurityUtils.getTenantId());
        qcScoringTemplateMapper.addQcScoringTemplate(payload);
        reload();
    }

    /**
     * 修改评分模板
     */
    @Override
    public void updateQcScoringTemplate(JSONObject payload) {
        this.judgmentOperation(payload);
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        qcScoringTemplateMapper.updateQcScoringTemplate(payload);
        reload();
    }

    /**
     * 删除评分模板
     */
    @Override
    public void removeQcScoringTemplate(Long[] ids) {
        List<QcScoringClassification> qcScoringClassificationList = qcScoringClassificationService.getQcScoringClassificationListByScoringTemplateIds(ids);
        if (CollUtil.isNotEmpty(qcScoringClassificationList)) {
            throw new ServiceException("请先删除与其关联的评分分类");
        }
        qcScoringTemplateMapper.removeQcScoringTemplate(ids);
        reload();
    }

    /**
     * 查询评分模板详情根据评分模板ID
     */
    @Override
    public QcScoringTemplateVo getQcScoringTemplate(Long id) {
        return qcScoringTemplateMapper.getQcScoringTemplate(id);
    }

    /**
     * 查询所有状态正常的评分模板
     */
    @Override
    public List<QcScoringTemplate> getAllQcScoringTemplate() {
        return qcScoringTemplateMapper.getAllQcScoringTemplate();
    }

    private void judgmentOperation(JSONObject payload) {
        String scoringTemplateName = payload.getString("scoringTemplateName");
        if (ObjectUtil.isEmpty(scoringTemplateName)) {
            throw new ServiceException("模板名称不允许为空");
        }
        String scoringTemplateType = payload.getString("scoringTemplateType");
        if (ObjectUtil.isEmpty(scoringTemplateType)) {
            throw new ServiceException("模板类型不允许为空");
        }
        Integer basicScore = payload.getInteger("basicScore");
        if (ObjectUtil.isEmpty(basicScore)) {
            throw new ServiceException("基本分数不允许为空");
        }
        if (basicScore < QcConstants.SCORING_TEMPLATE_LOWEST_SCORE || basicScore > QcConstants.SCORING_TEMPLATE_HIGHEST_SCORE) {
            throw new ServiceException("基本分数应为0~100的整数");
        }
        Integer highestScore = payload.getInteger("highestScore");
        if (ObjectUtil.isEmpty(highestScore)) {
            throw new ServiceException("最高分数不允许为空");
        }
        if (highestScore < QcConstants.SCORING_TEMPLATE_LOWEST_SCORE || highestScore > QcConstants.SCORING_TEMPLATE_HIGHEST_SCORE) {
            throw new ServiceException("最高分数应为0~100的整数");
        }
        Integer lowestScore = payload.getInteger("lowestScore");
        if (ObjectUtil.isEmpty(lowestScore)) {
            throw new ServiceException("最低分数不允许为空");
        }
        if (lowestScore < QcConstants.SCORING_TEMPLATE_LOWEST_SCORE || lowestScore > QcConstants.SCORING_TEMPLATE_HIGHEST_SCORE) {
            throw new ServiceException("最低分数应为0~100的整数");
        }
    }
    @PostConstruct
    private void reload() {
    	JSONObject ret = new JSONObject();
    	JSONObject params = new JSONObject();
    	//params.put("status", "1");
    	List<QcScoringTemplate> list = qcScoringTemplateMapper.getQcScoringTemplateList(params);
    	for(int i=0;i<list.size();i++) {
            QcScoringTemplateVo tmp = getQcScoringTemplate(list.get(i).getId());
    		ret.put(list.get(i).getId()+"", JSON.toJSONString(tmp));
    	}
    	redisService.setCacheMap("qcScoreTemp", ret);
    }
}
