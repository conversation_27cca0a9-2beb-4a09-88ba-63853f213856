import request from '@/utils/request'

// 查询质检规则列表
export function listRule(query) {
  return request({
    url: '/qc/manual/conf/list',
    method: 'get',
    params: query
  })
}

// 新增质检规则
export function addRule(data) {
  return request({
    url: '/qc/manual/conf/add',
    method: 'post',
    data: data
  })
}

// 修改质检规则
export function updateRule(data) {
  return request({
    url: '/qc/manual/conf/edit',
    method: 'post',
    data: data
  })
}

// 删除质检规则
export function delRule(ruleId) {
  return request({
    url: '/qc/manual/conf/del/' + ruleId,
    method: 'get'
  })
}

// 查询质检规则列表
export function getAll() {
  return request({
    url: '/qc/manual/conf/getAll',
    method: 'get'
  })
}
