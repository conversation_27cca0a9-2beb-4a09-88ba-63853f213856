import request from '@/utils/request'

// 查询人工质检计划列表
export function manualListPlan(query) {
  return request({
    url: '/qc/manual/plan/list',
    method: 'get',
    params: query
  })
}


//查询所有智能质检计划
export function smartPlanList(query) {
  return request({
    url: '/qc/manual/plan/smart/list',
    method: 'get',
    params: query
  })
}

// 根据智能质检计划id查询模板id
export function getSmartPlanById(id) {
  return request({
    url: '/qc/manual/plan/smart/get/' + id,
    method: 'get'
  })
}

//根据模板id查询所有的规则
export function getAllRulesById(id) {
  return request({
    url: '/qc/scoring/hit/rule/item/by/' + id,
    method: 'post'
  })
}

//查询所有状态正常的模板
export function getAllQcTemplate() {
  return request({
    url: '/qc/scoring/template/all',
    method: 'post'
  })
}


//获取所有状态正常的用户列表
export function getAllUser() {
  return request({
    url: '/system/user/get/all/user/list',
    method: 'get'
  })
}

// 新增人工质检计划
export function addPlan(data) {
  return request({
    url: '/qc/manual/plan/add',
    method: 'post',
    data: data
  })
}

//启用/禁用定时计划
export function updatePlanStatus(data) {
  return request({
    url: '/qc/manual/plan/enable/status',
    method: 'post',
    data: data
  })
}


//修改计划具体内容
export function updatePlan(data) {
  return request({
    url: '/qc/manual/plan/update',
    method: 'post',
    data: data
  })
}