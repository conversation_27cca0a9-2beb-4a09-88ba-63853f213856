package com.ideal.qc.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcBusinessLabel;

import java.util.List;

/**
 * 业务标签Mapper接口
 *
 * <AUTHOR>
 * @date 2025/5/23 9:37
 */
public interface QcBusinessLabelMapper {

    /**
     * 查询业务标签列表
     *
     * @param payload 需要的一些参数
     * @return 业务标签列表
     */
    List<QcBusinessLabel> getQcBusinessLabelList(JSONObject payload);

    /**
     * 新增业务标签
     *
     * @param payload 需要的一些参数
     */
    void addQcBusinessLabel(JSONObject payload);

    /**
     * 修改业务标签
     *
     * @param payload 需要的一些参数
     */
    void updateQcBusinessLabel(JSONObject payload);

    /**
     * 删除业务标签
     *
     * @param id 业务标签ID
     */
    void removeQcBusinessLabel(Long id);
}
