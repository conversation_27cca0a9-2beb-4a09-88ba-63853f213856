package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcManualPlan;
import com.ideal.qc.domain.QcSmartPlan;
import com.ideal.qc.mapper.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/manual/plan")
public class QcManualPlanController extends BaseController {

    @Autowired
    private QcSmartPlanMapper qcSmartPlanMapper;

    @Autowired
    private QcManualPlanMapper qcManualPlanMapper;

    @Autowired
    private QcSmartTaskMapper qcSmartTaskMapper;

    @Autowired
    private QcBizItemMapper qcBizItemMapper;

    @Autowired
    private QcManualTaskMapper qcManualTaskMapper;

    @Autowired
    private QcManualTaskDetailMapper qcManualTaskDetailMapper;

    @Autowired
    private QcScoringTemplateMapper qcScoringTemplateMapper;




    private static Logger logger = LoggerFactory.getLogger(QcManualPlanController.class);


    /**
     * 模糊查询人工计划列表
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> params)
    {
        String beginTime = "";
        String endTime = "";
        if (null != params.get("dateRange[0]") && null != params.get("dateRange[1]")){
            beginTime = params.get("dateRange[0]").toString();
            endTime = params.get("dateRange[1]").toString();
            if (beginTime.equals(endTime)){
                //如果开始时间和结束时间是同一天，手动改为00:00:00 到 23:59:59
                beginTime += " 00:00:00";
                endTime += " 23:59:59";
            }
        }
        params.put("beginTime",beginTime);
        params.put("endTime",endTime);
        startPage();
        List<QcManualPlan> list = qcManualPlanMapper.selectQcManualPlanList(params);
        return getDataTable(list);
    }


    /**
     * 查询所有智能质检计划
     */
    @GetMapping("/smart/list")
    public AjaxResult getSmartPlanList(){
        List<QcSmartPlan> smartPlanList = qcSmartPlanMapper.selectQcSmartPlanList(new JSONObject());
        return success(smartPlanList);
    }

    /**
     * 根据智能质检计划id查询模板id
     */
    @GetMapping("/smart/get/{id}")
    public AjaxResult getSmartPlanById(@PathVariable("id") Long id){
        QcSmartPlan qcSmartPlan = qcSmartPlanMapper.selectById(id);
        return success(qcSmartPlan);
    }



    /**
     * 禁用/启用计划
     */
    @PostMapping("/enable/status")
    @Transactional
    public AjaxResult updateEnableStatus(@RequestBody QcManualPlan qcManualPlan)
    {
        qcManualPlan.setUpdateBy(SecurityUtils.getUsername());
        qcManualPlan.setUpdateTime(new Date());
        qcManualPlanMapper.updateQcManualPlan(qcManualPlan);
        return success();
    }



    /**
     * 修改计划具体内容
     */
    @PostMapping("/update")
    @Transactional
    public AjaxResult updateTask(@RequestBody JSONObject payload)
    {
        try {
            QcManualPlan qcManualPlan = new QcManualPlan();
            qcManualPlan.setId(payload.getLong("id"));
            qcManualPlan.setPlanName(payload.getString("planName"));
            qcManualPlan.setDataRange(payload.getString("dataRange"));
            qcManualPlan.setExtractType(payload.getString("extractType"));
            qcManualPlan.setExtractValue(payload.getInteger("extractValue"));
            qcManualPlan.setSmartPlan(payload.getLong("smartPlan"));
            qcManualPlan.setTemplateId(payload.getLong("templateId"));
            qcManualPlan.setWeekDays(payload.getJSONArray("weekDays").toJSONString());
            qcManualPlan.setHitRules(payload.getJSONArray("hitRules").toJSONString());
            qcManualPlan.setInspectorRate(payload.getJSONArray("inspectorRate").toJSONString());
            qcManualPlan.setStatus("1");
            qcManualPlan.setUpdateBy(SecurityUtils.getUsername());
            qcManualPlan.setUpdateTime(new Date());
            qcManualPlanMapper.updateQcManualPlanDetail(qcManualPlan);
        } catch (Exception e) {
            logger.error("修改人工质检定时计划出错:",e);
            throw new RuntimeException(e.getMessage());
        }
        return success();
    }


    /**
     * 新增人工质检计划
     */
    @PostMapping("/add")
    @Transactional
    public AjaxResult add(@RequestBody JSONObject payload)
    {
        try {
            QcManualPlan qcManualPlan = new QcManualPlan();
            qcManualPlan.setPlanName(payload.getString("planName"));
            qcManualPlan.setPlanType(payload.getString("planType"));
            qcManualPlan.setManualType(payload.getString("manualType"));
            qcManualPlan.setDataRange(payload.getString("dataRange"));
            qcManualPlan.setExtractType(payload.getString("extractType"));
            qcManualPlan.setExtractValue(payload.getInteger("extractValue"));
            qcManualPlan.setSmartPlan(payload.getLong("smartPlan"));
            qcManualPlan.setTemplateId(payload.getLong("templateId"));
            qcManualPlan.setHitRules(payload.getJSONArray("hitRules").toJSONString());
            qcManualPlan.setWeekDays(payload.getJSONArray("weekDays").toJSONString());
            qcManualPlan.setInspectorRate(payload.getJSONArray("inspectorRate").toJSONString());
            qcManualPlan.setStatus("1");
            qcManualPlan.setFollowMatchDetail("");
            qcManualPlan.setDatasetId(null);
            qcManualPlan.setCreateBy(SecurityUtils.getUsername());
            qcManualPlan.setCreateTime(new Date());
            qcManualPlanMapper.insertQcManualPlan(qcManualPlan);
        } catch (Exception e) {
            logger.error("新增人工质检计划出错:",e);
            throw new RuntimeException(e.getMessage());
        }
        return success();
    }


}
