package com.ideal.qc.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.exception.ServiceException;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcBusinessLabel;
import com.ideal.qc.mapper.QcBusinessLabelMapper;
import com.ideal.qc.service.QcBusinessLabelService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 业务标签Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/23 9:25
 */
@Service
public class QcBusinessLabelServiceImpl implements QcBusinessLabelService {

    @Resource
    private QcBusinessLabelMapper qcBusinessLabelMapper;

    /**
     * 查询业务标签列表
     */
    @Override
    public List<QcBusinessLabel> getQcBusinessLabelList(JSONObject payload) {
        return qcBusinessLabelMapper.getQcBusinessLabelList(payload);
    }

    /**
     * 新增业务标签
     */
    @Override
    public void addQcBusinessLabel(JSONObject payload) {
        String businessLabelType = payload.getString("businessLabelType");
        if (ObjectUtil.isEmpty(businessLabelType)) {
            throw new ServiceException("业务标签类型不允许为空");
        }
        if (ObjectUtil.equal(businessLabelType, "1")) {
            String businessLabelStringMode = payload.getString("businessLabelStringMode");
            if (ObjectUtil.isEmpty(businessLabelStringMode)) {
                throw new ServiceException("未选定字符模式");
            }
        }
        String businessLabelName = payload.getString("businessLabelName");
        if (ObjectUtil.isEmpty(businessLabelName)) {
            throw new ServiceException("字段中文名称不允许为空");
        }
        String businessLabelKey = payload.getString("businessLabelKey");
        if (ObjectUtil.isEmpty(businessLabelKey)) {
            throw new ServiceException("字段名称不允许为空");
        }
        JSONArray jsonArray = payload.getJSONArray("businessLabelValue");
        if (ObjectUtil.isNotEmpty(jsonArray)) {
            payload.put("businessLabelValue", jsonArray.toJSONString());
        }
        payload.put("createTime", DateTime.now());
        payload.put("createBy", SecurityUtils.getUsername());
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        payload.put("tenantId", SecurityUtils.getTenantId());
        qcBusinessLabelMapper.addQcBusinessLabel(payload);
    }

    /**
     * 修改业务标签
     */
    @Override
    public void updateQcBusinessLabel(JSONObject payload) {
        String businessLabelType = payload.getString("businessLabelType");
        if (ObjectUtil.isEmpty(businessLabelType)) {
            throw new ServiceException("业务标签类型不允许为空");
        }
        if (ObjectUtil.equal(businessLabelType, "1")) {
            String businessLabelStringMode = payload.getString("businessLabelStringMode");
            if (ObjectUtil.isEmpty(businessLabelStringMode)) {
                throw new ServiceException("未选定字符模式");
            }
        }
        String businessLabelName = payload.getString("businessLabelName");
        if (ObjectUtil.isEmpty(businessLabelName)) {
            throw new ServiceException("字段中文名称不允许为空");
        }
        String businessLabelKey = payload.getString("businessLabelKey");
        if (ObjectUtil.isEmpty(businessLabelKey)) {
            throw new ServiceException("字段名称不允许为空");
        }
        JSONArray jsonArray = payload.getJSONArray("businessLabelValue");
        if (ObjectUtil.isNotEmpty(jsonArray)) {
            payload.put("businessLabelValue", jsonArray.toJSONString());
        }
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        qcBusinessLabelMapper.updateQcBusinessLabel(payload);
    }

    /**
     * 删除业务标签
     */
    @Override
    public void removeQcBusinessLabel(Long id) {
        qcBusinessLabelMapper.removeQcBusinessLabel(id);
    }
}
