package com.ideal.qc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.utils.DateUtils;
import com.ideal.common.core.utils.StringUtils;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.*;
import com.ideal.qc.mapper.QcSmartTaskMapper;
import com.ideal.qc.service.IScoreItemService;
import com.ideal.qc.service.IZjDataReviewService;
import com.ideal.qc.service.QcBICheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 人工质检、人工复检Controller
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@RestController
@RequestMapping("/qcBICheck")
public class QcBICheckController extends BaseController
{
    @Autowired
    private QcBICheckService qcBICheckService;
    @Value("${file_url}")
    private String file_url;
    /**
     * 获取质检源数据详细信息
     */
    @GetMapping(value = "/getVideoAndText/{bizNo}")
    public AjaxResult getVideoText(@PathVariable("bizNo") String bizNo)
    {
        List<Map<String,Object>> textList = qcBICheckService.getVideoText(bizNo);
        List<String> videoList = new ArrayList<>();
        List<JSONObject> txtList = new ArrayList<>();
        if(textList!=null&&textList.size()>0){
            List<String> keys = qcBICheckService.getVideoTextMatchKey(bizNo);
            for(int i=0;i<textList.size();i++){
                Map<String,Object> map = textList.get(i);
                String text = (String)map.get("asr_result");
                Object id = map.get("id");
                String file_id = (String)map.get("file_id");
                JSONObject json = JSONObject.parseObject(text);
                String videoUrl = file_url+file_id;//json.getString("wavCid");
                JSONArray msgList = json.getJSONArray("sentenceArray");
                JSONObject txtMsg = new JSONObject();
                txtMsg.put("datasetItemId",id);
                txtMsg.put("list",msgList);
                videoList.add(videoUrl);
                JSONObject temp = new JSONObject();
                temp.put("msgList",txtMsg);
                txtList.add(temp);
            }

            JSONObject result = new JSONObject();
            result.put("videoList",videoList);
            result.put("txtList",txtList);
            result.put("keys",keys);
            return success(result);
        }else{
            return error("没有转译文本，请联系管理员");
        }
    }
    /**
     * 新增人工复检
     */
    @Log(title = "人工质检", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QcDataResult qcDataResult)
    {
        qcDataResult.setCreateBy(SecurityUtils.getUsername());
        qcBICheckService.insertQcDataResult(qcDataResult);

        //更新人工质检库和智能质检库
        qcBICheckService.updateManualStatus(qcDataResult);
        if("2".equals(qcDataResult.getQcType())){//更新智能质检库
            qcBICheckService.updateSmartStatus(qcDataResult);
            qcBICheckService.updateSmartCountStatus(qcDataResult);//修改复检命中规则
        }
        return success();
    }
    @PostMapping(value = "/addManual")
    public AjaxResult addManual(@RequestBody JSONObject param)
    {
        param.put("updateBy",SecurityUtils.getUsername());
        param.put("createBy",SecurityUtils.getUsername());
        qcBICheckService.updateManualResult(param);
        return success();
    }
    @PostMapping(value = "/manualResultList")
    public TableDataInfo manualResultList(@RequestBody JSONObject json)
    {
        String taskDetailId = json.getString("taskDetailId");
        List<QcManualTaskResult> list = qcBICheckService.manualResultList(taskDetailId);
        return getDataTable(list);
    }
    @PostMapping(value = "/addManualResult")
    public AjaxResult addManualResult(@RequestBody JSONArray array)
    {
        for(int i=0;i<array.size();i++){
            JSONObject js = array.getJSONObject(i);
            QcManualTaskResult manual = com.alibaba.fastjson.JSONObject.parseObject(js.toJSONString(),QcManualTaskResult.class);
            manual.setCreateBy(SecurityUtils.getUsername());
            qcBICheckService.addQcManualTaskResult(manual);
        }
        return success();
    }
    @PostMapping(value = "/addAIResult")
    public AjaxResult addAIResult(@RequestBody JSONArray array)
    {
        JSONArray newArray = new JSONArray();
        /*for(int i=0;i<array.size();i++){
            JSONObject js = array.getJSONObject(i);
            QcManualTaskResult manual = com.alibaba.fastjson.JSONObject.parseObject(js.toJSONString(),QcManualTaskResult.class);
            if(manual.getAiResultId()==null||manual.getAiResultId().isEmpty()){
                manual.setCreateBy(SecurityUtils.getUsername());
                qcBICheckService.addQcSmartTaskResult(manual);
            }else{
                qcBICheckService.updateAiSmartResult(manual);
            }
        }*/

        return success();
    }
    @GetMapping(value = "/getManualResult/{bizNo}")
    public AjaxResult getManualResult(@PathVariable("bizNo") String bizNo)
    {
        List<QcManualTaskResult> list = qcBICheckService.getManualResult(bizNo);
        return success(list);
    }
    @GetMapping(value = "/getSmartCheckList/{bizNo}")
    public AjaxResult getSmartCheckList(@PathVariable("bizNo") String bizNo)
    {
        List<QcManualTaskResult> list = qcBICheckService.getSmartCheckList(bizNo);
        return success(list);
    }
    @GetMapping(value = "/getQcScoringHitRuleGroup/{groupId}/{bizType}/{ruleId}/{ruleItemId}/{taskDetailId}")
    public AjaxResult getQcScoringHitRuleGroup(@PathVariable("groupId") Long groupId,@PathVariable("bizType") String bizType,
                                               @PathVariable("ruleId") String ruleId,@PathVariable("ruleItemId") String ruleItemId,
                                               @PathVariable("taskDetailId") String taskDetailId)
    {
        //根据组ID获取itemId及itemName,组名字
        Map<String,String> result = qcBICheckService.getQcScoringHitRuleGroup(groupId);
        if("2".equals(bizType)){
            Map<String,String> param = new HashMap<>();
            param.put("taskDetailId",taskDetailId);
            param.put("ruleId",ruleId);
            param.put("ruleItemId",ruleItemId);
            Map<String,Object> aiResult = qcBICheckService.getAiResultByCondition(param);
            if(aiResult!=null){
                result.put("aiResult",aiResult.get("smart_result")+"");
                result.put("aiResultId",aiResult.get("id")+"");
            }
        }
        return success(result);
    }
    /**
     * 查询人工质检结果
     */
    @GetMapping(value = "/getResultListByBizNo/{bizNo}")
    public AjaxResult getResultListByBizNo(@PathVariable("bizNo") String bizNo)
    {
        List<QcDataResult> list = qcBICheckService.getResultListByBizNo(bizNo);
        return success(list);
    }
    @GetMapping(value = "/getResultByDetailId/{manualDetailId}")
    public AjaxResult getResultByDetailId(@PathVariable("manualDetailId") String manualDetailId)
    {
        List<QcDataResult> list = qcBICheckService.getResultByDetailId(manualDetailId);
        return success(list);
    }
    @GetMapping(value = "/tempFirstList/{id}")
    public AjaxResult tempFirstList(@PathVariable("id") String id)
    {
        String treeDate = qcBICheckService.tempFirstList(id);
        return success(treeDate);
    }
    @GetMapping(value = "/tempAIList/{bizNo}")
    public AjaxResult tempAIList(@PathVariable("bizNo") String bizNo)
    {
        String treeDate = qcBICheckService.tempAIList(bizNo);
        return success(treeDate);
    }

   /* *//**
     * 修改人工复检
     *//*
    @RequiresPermissions("quality:review:edit")
    @Log(title = "人工复检", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZjDataReview zjDataReview)
    {
        return toAjax(zjDataReviewService.updateZjDataReview(zjDataReview));
    }

    *//**
     * 删除人工复检
     *//*
    @RequiresPermissions("quality:review:remove")
    @Log(title = "人工复检", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zjDataReviewService.deleteZjDataReviewByIds(ids));
    }*/
}
