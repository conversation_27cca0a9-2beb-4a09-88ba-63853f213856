<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcScoringHitRuleItemMapper">

    <resultMap id="qcScoringHitRuleItemResultMap" type="QcScoringHitRuleItem">
        <result property="id"    column="id"    />
        <result property="scoringTemplateName"    column="scoring_template_name"    />
        <result property="scoringTemplateType" column="scoring_template_type" />
        <result property="basicScore"    column="basic_score"    />
        <result property="highestScore"    column="highest_score"    />
        <result property="lowestScore"    column="lowest_score"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="status" column="status" />
    </resultMap>

    <insert id="addScoringHitRuleItem">
        INSERT INTO
            qc_scoring_hit_rule_item
        ( tenant_id, scoring_hit_rule_group_id, rule_id, rule_classification, status, create_time, create_by, update_time, update_by, remark, bus_sort, template_id, rule_name )
        VALUES
        (#{tenantId}, #{scoringHitRuleGroupId}, #{ruleId}, #{ruleClassification}, #{status}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy}, #{remark}, #{busSort}, #{templateId}, #{ruleName})
    </insert>

    <update id="removeScoringHitRuleItemBatch">
        UPDATE qc_scoring_hit_rule_item SET
        is_deleted = '1'
        where id in
        <foreach item="item" collection="qcScoringHitRuleItemIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateScoringHitRuleItem">
        update qc_scoring_hit_rule_item SET
            rule_id = #{ruleId},
            rule_classification = #{ruleClassification},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark},
            status = #{status},
            bus_sort = #{busSort},
            rule_name = #{ruleName}
        where id = #{id}
    </update>

    <select id="getQcScoringHitRuleItemIds" resultType="java.lang.Long">
        SELECT
            qshri.id
        FROM
            qc_scoring_hit_rule_item qshri
        WHERE
            qshri.scoring_hit_rule_group_id = #{scoringHitRuleGroupId}
          AND qshri.is_deleted = '0'
        ORDER BY
            qshri.id ASC
    </select>

    <update id="removeScoringHitRuleItemBatchByGroupIds">
        UPDATE qc_scoring_hit_rule_item SET
        is_deleted = '1'
        where scoring_hit_rule_group_id in
        <foreach item="item" collection="qcScoringHitRuleGroupIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getRuleNameByRuleId" resultType="java.lang.String">
        select rule_name from qc_rule where rule_id = #{ruleId}
    </select>

    <select id="getRuleNameById" resultType="java.lang.String">
        select rule_name from qc_manual_rule_conf where id = #{ruleId}
    </select>

    <resultMap id="qcScoringHitRuleItemVoResultMap" type="com.ideal.qc.vo.QcScoringHitRuleItemVo">
        <result property="id" column="id" />
        <result property="scoringHitRuleGroupId" column="scoring_hit_rule_group_id" />
        <result property="ruleId" column="rule_id" />
        <result property="ruleClassification" column="rule_classification" />
        <result property="busSort" column="bus_sort" />
        <result property="name" column="rule_name" />
        <result property="templateId" column="template_id" />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <select id="getQcScoringHitRuleItemListByTemplateId" resultMap="qcScoringHitRuleItemVoResultMap">
        SELECT
            qshri.id,
            qshri.scoring_hit_rule_group_id,
            qshri.rule_id,
            qshri.rule_classification,
            qshri.bus_sort,
            CASE
                WHEN qshri.rule_classification = '1' THEN CONCAT(qshri.rule_name, '（智能规则）')
                WHEN qshri.rule_classification = '2' THEN CONCAT(qshri.rule_name, '（人工规则）')
                WHEN qshri.rule_classification = '3' THEN CONCAT(qshri.rule_name, '（流程规则）')
                ELSE qshri.rule_name
            END AS rule_name,
            qshri.template_id,
            qshri.status,
            qshri.is_deleted
        FROM
            qc_scoring_hit_rule_item qshri
        LEFT JOIN
            qc_scoring_hit_rule_group qshrg ON qshrg.id = qshri.scoring_hit_rule_group_id
        LEFT JOIN
            qc_scoring_item qsi ON qsi.id = qshrg.scoring_item_id
        LEFT JOIN
            qc_scoring_classification qsc ON qsc.id = qsi.scoring_classification_id
        LEFT JOIN
            qc_scoring_template qst ON qst.id = qsc.scoring_template_id
        WHERE
            qst.id = #{scoringTemplateId}
          AND qshri.is_deleted = '0'
          AND qshri.status = '1'
        ORDER BY
            qshri.id ASC
    </select>
</mapper>