<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcDataAnnotationMapper">
    
    <resultMap type="com.ideal.qc.domain.QcDataAnnotation" id="QcDataAnnotationResult">
        <result property="id"    column="id"    />
        <result property="dataId"    column="data_id"    />
        <result property="oldUrl"    column="old_url"    />
        <result property="newUrl"    column="new_url"    />
        <result property="newUrlName"    column="new_url_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="originalText"    column="original_text"    />
        <result property="correctText"    column="correct_text"    />
        <result property="type"    column="type"    />
        <result property="note"    column="note"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="onlyNo"    column="onlyNo"    />
    </resultMap>

    <sql id="selectQcDataAnnotationVo">
        select id, data_id, old_url, new_url, new_url_name, start_time, end_time, original_text, correct_text, type, note, create_time, create_by, update_time, update_by,
        (SELECT d.biz_no FROM qc_dataset_item d WHERE d.id=s.data_id) onlyNo from qc_data_annotation s
    </sql>

    <select id="selectQcDataAnnotationList" parameterType="com.ideal.qc.domain.QcDataAnnotation" resultMap="QcDataAnnotationResult">
        <include refid="selectQcDataAnnotationVo"/>
        <where>  
            <if test="dataId != null "> and data_id = #{dataId}</if>
            <if test="oldUrl != null  and oldUrl != ''"> and old_url = #{oldUrl}</if>
            <if test="newUrl != null  and newUrl != ''"> and new_url = #{newUrl}</if>
            <if test="newUrlName != null  and newUrlName != ''"> and new_url_name like concat('%', #{newUrlName}, '%')</if>
            <if test="onlyNo != null  and onlyNo != ''"> and exists(SELECT d.biz_no FROM qc_dataset_item d  where d.id=s.data_id and biz_no = #{onlyNo})</if>
            <if test="endTime != null  and endTime != ''"> and end_time = #{endTime}</if>
            <if test="originalText != null  and originalText != ''"> and original_text = #{originalText}</if>
            <if test="correctText != null  and correctText != ''"> and correct_text = #{correctText}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="note != null  and note != ''"> and note = #{note}</if>
        </where>
    </select>
    
    <select id="selectQcDataAnnotationById" parameterType="Long" resultMap="QcDataAnnotationResult">
        <include refid="selectQcDataAnnotationVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcDataAnnotation" parameterType="com.ideal.qc.domain.QcDataAnnotation" useGeneratedKeys="true" keyProperty="id">
        insert into qc_data_annotation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataId != null">data_id,</if>
            <if test="oldUrl != null">old_url,</if>
            <if test="newUrl != null">new_url,</if>
            <if test="newUrlName != null">new_url_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="originalText != null">original_text,</if>
            <if test="correctText != null">correct_text,</if>
            <if test="type != null">type,</if>
            <if test="note != null">note,</if>
            create_time,
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataId != null">#{dataId},</if>
            <if test="oldUrl != null">#{oldUrl},</if>
            <if test="newUrl != null">#{newUrl},</if>
            <if test="newUrlName != null">#{newUrlName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="originalText != null">#{originalText},</if>
            <if test="correctText != null">#{correctText},</if>
            <if test="type != null">#{type},</if>
            <if test="note != null">#{note},</if>
            now(),
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateQcDataAnnotation" parameterType="com.ideal.qc.domain.QcDataAnnotation">
        update qc_data_annotation
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="oldUrl != null">old_url = #{oldUrl},</if>
            <if test="newUrl != null">new_url = #{newUrl},</if>
            <if test="newUrlName != null">new_url_name = #{newUrlName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="originalText != null">original_text = #{originalText},</if>
            <if test="correctText != null">correct_text = #{correctText},</if>
            <if test="type != null">type = #{type},</if>
            <if test="note != null">note = #{note},</if>
            update_time = now(),
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcDataAnnotationById" parameterType="Long">
        delete from qc_data_annotation where id = #{id}
    </delete>

    <delete id="deleteQcDataAnnotationByIds" parameterType="String">
        delete from qc_data_annotation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>