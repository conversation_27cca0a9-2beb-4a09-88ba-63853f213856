package com.ideal.qc.service.impl;

import java.util.Collections;
import java.util.List;
import com.ideal.common.core.utils.DateUtils;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.ScoreItemMapper;
import com.ideal.qc.domain.ScoreItem;
import com.ideal.qc.service.IScoreItemService;

import javax.annotation.Resource;

/**
 * 评分规则模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Service
public class ScoreItemServiceImpl implements IScoreItemService
{
    @Resource
    private ScoreItemMapper scoreItemMapper;

    /**
     * 查询评分项
     *
     * @param id 评分项主键
     * @return 评分项
     */
    @Override
    public ScoreItem selectScoreItemById(Long id)
    {
        return scoreItemMapper.selectScoreItemById(id);
    }

    /**
     * 查询评分项列表
     *
     * @param scoreItem 评分项
     * @return 评分项
     */
    @Override
    public List<ScoreItem> selectScoreItemList(ScoreItem scoreItem)
    {
        return scoreItemMapper.selectScoreItemList(scoreItem);
    }

    /**
     * 获取评分项分类节点
     * @param scoreItem
     * @return
     */
    @Override
    public List<ScoreItem> selectScoreItemPnode(ScoreItem scoreItem) {
        return scoreItemMapper.selectScoreItemPnode(scoreItem);
    }

    @Override
    public List<ScoreItem> selectScoreItemByTplId(ScoreItem scoreItem) {
        return scoreItemMapper.selectScoreItemByTplId(scoreItem);
    }

    /**
     * 新增评分项
     *
     * @param scoreItem 评分项
     * @return 结果
     */
    @Override
    public int insertScoreItem(ScoreItem scoreItem)
    {
        scoreItem.setCreateTime(DateUtils.getNowDate());
        return scoreItemMapper.insertScoreItem(scoreItem);
    }

    /**
     * 修改评分项
     *
     * @param scoreItem 评分项
     * @return 结果
     */
    @Override
    public int updateScoreItem(ScoreItem scoreItem)
    {
        return scoreItemMapper.updateScoreItem(scoreItem);
    }

    /**
     * 批量删除评分项
     *
     * @param ids 需要删除的评分项主键
     * @return 结果
     */
    @Override
    public int deleteScoreItemByIds(Long[] ids)
    {
        return scoreItemMapper.deleteScoreItemByIds(ids);
    }

    /**
     * 删除评分项信息
     *
     * @param id 评分项主键
     * @return 结果
     */
    @Override
    public int deleteScoreItemById(Long id)
    {
        return scoreItemMapper.deleteScoreItemById(id);
    }
}
