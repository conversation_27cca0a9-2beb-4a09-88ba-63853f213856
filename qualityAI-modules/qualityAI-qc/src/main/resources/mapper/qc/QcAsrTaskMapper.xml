<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcAsrTaskMapper">
    
    <resultMap type="QcAsrTask" id="QcAsrTaskResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="priority"    column="priority"    />
        <result property="status"    column="status"    />
        <result property="retryCount"    column="retry_count"    />
        <result property="nextTryTime"    column="next_try_time"    />
        <result property="message"    column="message"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectQcAsrTaskVo">
        select id, item_id, priority, status, retry_count, next_try_time, message, tenant_id, create_time from qc_asr_task
    </sql>

    <select id="selectQcAsrTaskList" parameterType="QcAsrTask" resultMap="QcAsrTaskResult">
        <include refid="selectQcAsrTaskVo"/>
        <where>  
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="priority != null "> and priority = #{priority}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="retryCount != null "> and retry_count = #{retryCount}</if>
            <if test="nextTryTime != null "> and next_try_time = #{nextTryTime}</if>
            <if test="message != null  and message != ''"> and message = #{message}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
        </where>
    </select>
    
    <select id="selectQcAsrTaskById" parameterType="Long" resultMap="QcAsrTaskResult">
        <include refid="selectQcAsrTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcAsrTask" parameterType="QcAsrTask" useGeneratedKeys="true" keyProperty="id">
        insert into qc_asr_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="priority != null">priority,</if>
            <if test="status != null">status,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="nextTryTime != null">next_try_time,</if>
            <if test="message != null">message,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="priority != null">#{priority},</if>
            <if test="status != null">#{status},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="nextTryTime != null">#{nextTryTime},</if>
            <if test="message != null">#{message},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateQcAsrTask" parameterType="QcAsrTask">
        update qc_asr_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="status != null">status = #{status},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="nextTryTime != null">next_try_time = #{nextTryTime},</if>
            <if test="message != null">message = #{message},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcAsrTaskById" parameterType="Long">
        delete from qc_asr_task where id = #{id}
    </delete>

    <delete id="deleteQcAsrTaskByIds" parameterType="String">
        delete from qc_asr_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>