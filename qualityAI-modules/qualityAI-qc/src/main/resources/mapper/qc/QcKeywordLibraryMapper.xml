<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcKeywordLibraryMapper">

    <resultMap id="qcKeywordLibraryResultMap" type="QcKeywordLibrary">
        <result property="id"    column="id"    />
        <result property="keywordLibraryClassificationId" column="keyword_library_classification_id" />
        <result property="keywordLibraryName" column="keyword_library_name" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="keywordLibraryClassificationName" column="keyword_library_classification_name" />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
        <association property="keywordNumber" select="getQcKeywordNumber" column="id" />
    </resultMap>

    <select id="getQcKeywordLibraryList" resultMap="qcKeywordLibraryResultMap">
        SELECT
            qkl.id,
            qkl.keyword_library_classification_id,
            qkl.keyword_library_name,
            qkl.create_by,
            qkl.create_time,
            qkl.update_by,
            qkl.update_time,
            qkl.remark,
            qkl.status,
            qkl.is_deleted,
            qklc.keyword_library_classification_name
        FROM
            qc_keyword_library qkl
        LEFT JOIN
            qc_keyword_library_classification qklc ON qklc.id = qkl.keyword_library_classification_id
        WHERE
            qkl.is_deleted = '0'
        <if test="keywordLibraryClassificationId != null">
            AND qkl.keyword_library_classification_id = #{keywordLibraryClassificationId}
        </if>
        <if test="keywordLibraryName != null and keywordLibraryName != ''">
            AND qkl.keyword_library_name LIKE concat('%', #{keywordLibraryName}, '%')
        </if>
        ORDER BY
            qkl.id ASC
    </select>

    <select id="getQcKeywordNumber" resultType="java.lang.Integer">
        SELECT
            count( qk.id )
        FROM
            qc_keyword qk
        WHERE
            qk.keyword_library_id = #{keywordLibraryId}
            AND qk.is_deleted = '0'
    </select>

    <insert id="addQcKeywordLibrary">
        INSERT INTO
            qc_keyword_library
        ( tenant_id, keyword_library_classification_id, keyword_library_name, create_time, create_by, update_time, update_by, remark )
        VALUES
        ( #{tenantId}, #{keywordLibraryClassificationId}, #{keywordLibraryName}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy}, #{remark})
    </insert>

    <update id="updateQcKeywordLibrary">
        UPDATE qc_keyword_library
        SET keyword_library_classification_id = #{keywordLibraryClassificationId},
            keyword_library_name = #{keywordLibraryName},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark},
            status = #{status}
        WHERE
            id = #{id}
    </update>

    <update id="removeQcKeywordLibrary">
        UPDATE qc_keyword_library SET
            is_deleted = '1'
        WHERE
            id = #{id}
    </update>

    <select id="getQcKeywordLibraryByKeywordLibraryClassificationIds" resultMap="qcKeywordLibraryResultMap">
        SELECT
            qkl.id,
            qkl.keyword_library_classification_id,
            qkl.keyword_library_name,
            qkl.create_by,
            qkl.create_time,
            qkl.update_by,
            qkl.update_time,
            qkl.status,
            qkl.is_deleted
        FROM
            qc_keyword_library qkl
        WHERE
            qkl.keyword_library_classification_id = #{keywordLibraryClassificationId}
            AND qkl.is_deleted = '0'
        ORDER BY
            qkl.id ASC
    </select>
</mapper>