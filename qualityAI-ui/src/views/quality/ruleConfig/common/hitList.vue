<template>
      <el-form-item label="规则命中条件" prop="hitList">
        <div class="ruleBox" v-for="(item,index) in hitList" :key="index">
		<template v-if="item.status != '2'" >
			<div class="i-or-div">
          		<i v-if="index!=0" class="i-or" circle >或</i>
			</div>
          <div class="ruleTop row spaceBet">
            <div>条件组合{{index+1}}</div>
            <i v-if="op != 'view'" class="el-icon-close" @click="cutRule('hitList',index)"></i>
          </div>
          <div class="ruleCon">
            <div class="row">
              满足下列
              <el-select v-model="item.ruleCondDetail.isAll" placeholder="请选择" class="selectS" style="margin-left:10px;" :disabled="op == 'view'">
                <el-option label="全部" value="1" />
                <el-option label="任一" value="0" />
              </el-select>
              条件，则判定【条件组合{{index+1}}】命中
            </div>
            <div v-for="(item2,index2) in item.ruleCondDetail.options" :key="index2" class="hitBox">
              <el-select v-model="item2.scene" placeholder="请选择" class="selectL" :disabled="op == 'view'">
                <el-option v-for="items in sceneList" :key="items.id" :label="items.name" :value="items.id" :disabled="index2<1 &&items.id>2"></el-option>
              </el-select>
			  <template v-if="item2.scene==2 || item2.scene==3 || item2.scene==4 || item2.scene==5">
	              <el-select  v-model="item2.num" placeholder="请选择" class="selectM" :disabled="op == 'view'">
	                <el-option v-for="items in numList" :key="items.id" :label="items.name" :value="items.id" ></el-option>
	              </el-select>句中
			  </template>
              <el-select v-model="item2.role" placeholder="请选择" class="selectS" :disabled="op == 'view'">
                <el-option label="坐席" value="1" />
                <el-option label="客户" value="0" />
              </el-select>的回复中
              <el-select v-model="item2.isContain" placeholder="请选择" class="selectM" style="margin-left:10px;" :disabled="op == 'view'">
                <el-option label="包含" value="1" />
                <el-option label="不包含" value="0" />
              </el-select>
              <el-select v-model="item2.rule" placeholder="请选择" @change="chgHitRule($event,index,index2)" style="margin-right:10px;width:120px;" :disabled="op == 'view'">
                <el-option v-for="items in hitRuleList" :key="items.id" :label="items.name" :value="items.id"></el-option>
              </el-select>
              <!-- 语义标签 -->
              <el-select v-if="item2.rule==1" v-model="item.semanticsIds" placeholder="请选择" class="selectL" :disabled="op == 'view'">
                <el-option v-for="items in ruleList" :key="items.id" :label="items.agentName" :value="items.id"></el-option>
              </el-select>
              <!-- 关键词 -->
              <div v-if="item2.rule==2">
                <el-input class="input-new-tag" placeholder="输入按回车确定"  v-model="item2.inputKey" ref="saveTagInput" :disabled="op == 'view'"
                  @keyup.enter.native="handleInputConfirm(index,index2,item2.inputKey)" @blur="handleInputConfirm(index,index2,item2.inputKey)">
                </el-input>
                <el-tag :key="tag+index" v-for="tag in item2.dynamicTags" :disable-transitions="false" @close="handleClose(index,index2,tag)" style="margin-right:10px;" :closable="op != 'view'">
                  {{tag}}
                </el-tag>
              </div>
			  <!-- 关键词重复 -->
	          <div v-if="item2.rule==3">
	          	<el-input class="input-new-tag" placeholder="输入按回车确定"  v-model="item2.keyWord" ref="saveTagInput" :disabled="op == 'view'"/>重复大于
				<el-input class="input-new-tag" v-model="item2.kwsCount" type="number" :disabled="op == 'view'"/>次
	          </div>
			  <!-- 关键词库 -->
	          <div v-if="item2.rule==4">
				  <el-select size="small" style="width: 100%" v-model="item2.dynamicTags" :disabled="op == 'view'" multiple collapse-tags clearable placeholder="请选择">
		              <el-option v-for="item in kwl.list" :key="item.name" :label="item.name" :value="item.name"> </el-option>
		              <el-pagination
		                @size-change="getKwlList"
		                @current-change="getKwlList"
		                :current-page.sync="kwl.query.pageNum"
		                :page-size="kwl.query.pageSize"
		                layout="total, prev, pager, next"
		                :total="kwl.query.total"
		              >
		          	  </el-pagination>
		     	  </el-select>
	          </div>
			  <!-- 正则表达式 -->
	          <div v-if="item2.rule==5">
	          	<el-input class="input-new-tag" placeholder="输入正则表达式" v-model="item2.reg" :disabled="op == 'view'"/>
	          </div>
			  <!-- 语速 -->
  	          <div v-if="item2.rule==6">
  	          	大于<el-input class="input-new-tag" placeholder="输入语速值" v-model="item2.speed" type="number" :disabled="op == 'view'"/>
  	          </div>

              <el-button icon="el-icon-minus" :disabled="index2==0 || op == 'view'" @click="cutHitOpt(index,index2)"></el-button>
              <el-button icon="el-icon-plus" :disabled="item.ruleCondDetail.options[index2].rule==1 || op == 'view'"@click="addHitOpt(index)" style="margin-left:5px;"></el-button>
            </div>
		  </div>
		</template>
        </div>
        <el-button v-if="op != 'view'" icon="el-icon-plus" plain type="primary" @click="addFit">添加组合规则</el-button>
      </el-form-item>

</template>

<script>
import {recordList,kwlList} from "@/api/quality/rule.js";
export default {
  	name: "smartEdit",
  	props:{
		hitList:Array,
		op:String
  	},
	data() {
    	return {
      		info:{id:""},
  			form: {
				ruleType:'',
        		filterList:[],
        		hitList:[],
				deleteList:[]
      		},
      		rules: {
        		sortName: [{ required: true, message: "分类名称不能为空", trigger: "blur" }]
      		},
      		ruleList:[],
      		sceneList:[
		        {id:1,name:'全会话中'},
		        {id:2,name:'会话开始后'},
		        {id:3,name:'会话结束前'},
		        {id:4,name:'上一个条件之后'},
		        {id:5,name:'上一个条件之前'},
		        {id:6,name:'上一个条件命中语句'}
			],
      		numList:[
		        {id:1,name:'1'},
		        {id:2,name:'2'},
		        {id:3,name:'3'},
		        {id:4,name:'4'},
		        {id:5,name:'5'}
      		],
      		hitRuleList:[
		        {id:1,name:'大模型'},
		        {id:2,name:'关键词'},
		        {id:3,name:'关键词重复'},
		        {id:4,name:'关键词库'},
		        {id:5,name:'正则表达式'},
		        {id:6,name:'语速识别'}
      		],		
	  		kwList:{
				"1":[{id:1,name:'语义标签'},{id:2,name:'关键词'}],
				"2":[{id:3,name:'关键词重复'},{id:4,name:'关键词库'}],
  				"3":[{id:5,name:'正则表达式'},{id:6,name:'语速识别'}]
	  		},
	  		kwl:{
				list:[],
				query:{
					total:0,
					pageNum: 1,
			        pageSize: 5
				}
	  		}
    	};
  	},
  	created() {
		recordList({},{}).then(response=>{
			this.ruleList = response.rows;
		});
		this.getKwlList();
  	},
	methods: {
    	cutRule(key,index){
			var count = 0;
			for(var i in this[key]){
				if(this[key][i].status != 2){
					count++;
				}
			}
			if(count<=1){
				this.$message({message: "至少保留一组命中条件",type: 'warning'});
				return;
			}
			if(this[key][index].id && this[key][index].id != ''){
				this[key][index].status = 2
				this[key].push(this[key][index])
		  	}
		  	this[key].splice(index,1);
		},
    	chgHitRule(e,index,index2){
      		const obj = this._props.hitList[index].ruleCondDetail.options[index2]
		  	if(e != 1){
				this._props.hitList[index]["semanticsIds"] = null;
		  	}
      		if(e ==2){
       			this.$set(obj,'dynamicTags',[])
      		}else{
        		if(obj.dynamicTags){
	          		delete obj.dynamicTags
	    		}
      		}
		},
    	addFit(){
      		this._props.hitList.push({ruleCondType:2,ruleId:this.info.id,ruleCondDetail:{isAll:'1',scene:1,isOr:1,options:[{scene:1,role:'1',isContain:'1',rule:1}]}})
    	},
    	cutHit(item,index){
      		this._props.hitList[index]["isDelete"] = true;
    	},
    	addHitOpt(index){
      		this._props.hitList[index].ruleCondDetail.options.push({scene:1,role:'1',isContain:'1',rule:1})
    	},
    	cutHitOpt(index,index2){
      		this._props.hitList[index].ruleCondDetail.options.splice(index2,1);
    	},
    	handleInputConfirm(index,index2,inputValue) {
      		const obj = this._props.hitList[index].ruleCondDetail.options[index2]
      		if (inputValue) {
        		obj.dynamicTags.push(inputValue);
        		this.$set(obj,'dynamicTags',obj.dynamicTags)
      		}
      		this.$set(obj,'inputKey','')
		},
    	handleClose(index,index2,tag) {
      		const dynamicTags = this._props.hitList[index].ruleCondDetail.options[index2].dynamicTags;
      		dynamicTags.splice(dynamicTags.indexOf(tag), 1);

    	},
		getKwlList(){
			kwlList(this.kwl.query,{}).then(response=>{
				this.kwl.list = response.rows;
				this.kwl.query.total = response.total;
			});
		}
  	}
};
</script>
<style rel="stylesheet/scss" lang="scss">
.inputWidth{
  width: 450px!important;
}
.selectS{
  width: 80px!important;
  margin-left: 10px;
  margin-right: 10px;
}
.selectM{
  width: 90px!important;
  margin-right: 10px;
  margin-left: 10px;
}
.selectL{
  width: 180px!important;
  margin-right: 10px;
  margin-left: 10px;
}
.ruleBox{
  margin-bottom: 20px;
  .ruleTop{
    background: #ebebeb;
    padding: 2px 15px;
    cursor: pointer;
  }
  .ruleCon{
     padding: 18px 15px;
     font-size: 12px;
     background-color: #fafafa;
  }
  button{
        padding: 9px;
  }
}
.dialog-footer{
  width: 100%;
  margin-top: 40px;
  justify-content: center;

}
.orBt{
  margin-bottom: 15px;
}
.hitBox{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top:20px;
  row-gap: 10px; 
}
.keyInput{
  width: 150px;
  margin-right: 10px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 125px;
  margin-left: 10px;
  margin-right: 10px;
  vertical-align: bottom;
}
.btn{
	float:right;
	margin-right:5px;
}
.no-label_el-form-item .el-form-item__content{
	margin-left: 0px!important;
}
</style>
