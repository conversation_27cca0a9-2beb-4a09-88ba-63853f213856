package com.ideal.qc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.exception.ServiceException;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcKeywordLibrary;
import com.ideal.qc.domain.QcKeywordLibraryClassification;
import com.ideal.qc.mapper.QcKeywordLibraryClassificationMapper;
import com.ideal.qc.service.QcKeywordLibraryClassificationService;
import com.ideal.qc.service.QcKeywordLibraryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 关键词库分类Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/20 17:46
 */
@Service
public class QcKeywordLibraryClassificationServiceImpl implements QcKeywordLibraryClassificationService {

    @Resource
    private QcKeywordLibraryClassificationMapper qcKeywordLibraryClassificationMapper;
    @Resource
    private QcKeywordLibraryService qcKeywordLibraryService;

    /**
     * 查询关键词库分类列表
     */
    @Override
    public List<QcKeywordLibraryClassification> getQcKeywordLibraryClassificationList(JSONObject payload) {
        return qcKeywordLibraryClassificationMapper.getQcKeywordLibraryClassificationList(payload);
    }

    /**
     * 新增关键词库分类
     */
    @Override
    public void addQcKeywordLibraryClassification(JSONObject payload) {
        payload.put("createTime", DateTime.now());
        payload.put("createBy", SecurityUtils.getUsername());
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        payload.put("tenantId", SecurityUtils.getTenantId());
        qcKeywordLibraryClassificationMapper.addQcKeywordLibraryClassification(payload);
    }

    /**
     * 修改关键词库分类
     */
    @Override
    public void updateQcKeywordLibraryClassification(JSONObject payload) {
        qcKeywordLibraryClassificationMapper.updateQcKeywordLibraryClassification(payload);
    }

    /**
     * 删除关键词库分类
     */
    @Override
    public void removeQcKeywordLibraryClassification(Long id) {
        List<QcKeywordLibrary> qcKeywordLibraryList = qcKeywordLibraryService.getQcKeywordLibraryByKeywordLibraryClassificationIds(id);
        if (CollUtil.isNotEmpty(qcKeywordLibraryList)) {
            throw new ServiceException("请先删除与其关联的关键词库");
        }
        qcKeywordLibraryClassificationMapper.removeQcKeywordLibraryClassification(id);
    }
}
