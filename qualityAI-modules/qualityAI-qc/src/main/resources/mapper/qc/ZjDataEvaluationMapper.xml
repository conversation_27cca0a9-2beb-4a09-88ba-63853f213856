<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ZjDataEvaluationMapper">
    
    <resultMap type="com.ideal.qc.domain.ZjDataEvaluation" id="ZjDataEvaluationResult">
        <result property="id"    column="id"    />
        <result property="question"    column="question"    />
        <result property="answer"    column="answer"    />
        <result property="zjDataId"    column="zj_data_id"    />
        <result property="seq"    column="seq"    />
    </resultMap>

    <sql id="selectZjDataEvaluationVo">
        select id, question, answer, zj_data_id, seq from zj_data_evaluation
    </sql>

    <select id="selectZjDataEvaluationList" parameterType="ZjDataEvaluation" resultMap="ZjDataEvaluationResult">
        <include refid="selectZjDataEvaluationVo"/>
        <where>  
            <if test="question != null  and question != ''"> and question = #{question}</if>
            <if test="answer != null  and answer != ''"> and answer = #{answer}</if>
            <if test="zjDataId != null "> and zj_data_id = #{zjDataId}</if>
            <if test="seq != null "> and seq = #{seq}</if>
        </where>
    </select>
    
    <select id="selectZjDataEvaluationById" parameterType="Long" resultMap="ZjDataEvaluationResult">
        <include refid="selectZjDataEvaluationVo"/>
        where id = #{id}
    </select>

    <insert id="insertZjDataEvaluation" parameterType="ZjDataEvaluation" useGeneratedKeys="true" keyProperty="id">
        insert into zj_data_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="question != null">question,</if>
            <if test="answer != null">answer,</if>
            <if test="zjDataId != null">zj_data_id,</if>
            <if test="seq != null">seq,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="question != null">#{question},</if>
            <if test="answer != null">#{answer},</if>
            <if test="zjDataId != null">#{zjDataId},</if>
            <if test="seq != null">#{seq},</if>
         </trim>
    </insert>

    <update id="updateZjDataEvaluation" parameterType="ZjDataEvaluation">
        update zj_data_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="question != null">question = #{question},</if>
            <if test="answer != null">answer = #{answer},</if>
            <if test="zjDataId != null">zj_data_id = #{zjDataId},</if>
            <if test="seq != null">seq = #{seq},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZjDataEvaluationById" parameterType="Long">
        delete from zj_data_evaluation where id = #{id}
    </delete>

    <delete id="deleteZjDataEvaluationByIds" parameterType="String">
        delete from zj_data_evaluation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>