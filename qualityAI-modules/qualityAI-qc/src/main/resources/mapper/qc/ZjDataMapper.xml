<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ZjDataMapper">
    <resultMap type="com.ideal.qc.domain.ZjData" id="ZjDataResult">
        <result property="id"    column="id"    />
        <result property="platform"    column="platform"    />
        <result property="project"    column="project"    />
        <result property="targetName"    column="target_name"    />
        <result property="targetSubName"    column="target_sub_name"    />
        <result property="serviceCenter"    column="service_center"    />
        <result property="workNo"    column="work_no"    />
        <result property="workName"    column="work_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="longTime"    column="long_time"    />
        <result property="dataDate"    column="data_date"    />
        <result property="qsNo"    column="qs_no"    />
        <result property="telephone"    column="telephone"    />
        <result property="businessType"    column="business_type"    />
        <result property="serviceProvider"    column="service_provider"    />
        <result property="onlyNo"    column="only_no"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="area"    column="area"    />
        <result property="productType"    column="product_type"    />
        <result property="serviceOrderNo"    column="service_order_no"    />
        <result property="serviceType"    column="service_type"    />
        <result property="completionTime"    column="completion_time"    />
        <result property="productName"    column="product_name"    />
        <result property="saleName"    column="sale_name"    />
        <result property="seqNo"    column="seq_no"    />
        <result property="testMonth"    column="test_month"    />
        <result property="callResult"    column="call_result"    />
        <result property="taskResult"    column="task_result"    />
        <result property="audioUrl"    column="audio_url"    />
        <result property="incomingTime"    column="incoming_time"    />
        <result property="zjStatus"    column="zj_status"    />
        <result property="templateId"    column="templateId"    />

        <collection property="list" ofType="com.ideal.qc.domain.ZjDataEvaluation" column="id" select="getEvaluationList"/>
    </resultMap>
    <select id="getEvaluationList" resultType="com.ideal.qc.domain.ZjDataEvaluation">
        select id, question, answer, zj_data_id zjDataId, seq from zj_data_evaluation
        where zj_data_id = #{id} order by seq asc
    </select>
    <sql id="selectZjDataVo">
        select id, platform, project, target_name, target_sub_name, service_center, work_no, work_name, start_time, end_time, long_time, data_date, qs_no, telephone, business_type,
               service_provider, only_no, province, city, contact_phone, area, product_type, service_order_no, service_type, completion_time, product_name, sale_name, seq_no,
               test_month, call_result, task_result, audio_url, incoming_time,
               (select zj_status from zj_data_template t where t.data_id = s.id) zj_status,
               (select zj_template_id from zj_data_template t where t.data_id = s.id) templateId
        from zj_data s
    </sql>

    <select id="selectZjDataList" parameterType="ZjData" resultMap="ZjDataResult">
        <include refid="selectZjDataVo"/>
        <where>  
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="project != null  and project != ''"> and project = #{project}</if>
            <if test="targetName != null  and targetName != ''"> and target_name like concat('%', #{targetName}, '%')</if>
            <if test="targetSubName != null  and targetSubName != ''"> and target_sub_name like concat('%', #{targetSubName}, '%')</if>
            <if test="serviceCenter != null  and serviceCenter != ''"> and service_center = #{serviceCenter}</if>
            <if test="workNo != null  and workNo != ''"> and work_no = #{workNo}</if>
            <if test="workName != null  and workName != ''"> and work_name like concat('%', #{workName}, '%')</if>
            <if test="startTime != null  and startTime != ''"> and start_time = #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and end_time = #{endTime}</if>
            <if test="longTime != null  and longTime != ''"> and long_time = #{longTime}</if>
            <if test="dataDate != null  and dataDate != ''"> and data_date = #{dataDate}</if>
            <if test="qsNo != null  and qsNo != ''"> and qs_no = #{qsNo}</if>
            <if test="telephone != null  and telephone != ''"> and telephone = #{telephone}</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="serviceProvider != null  and serviceProvider != ''"> and service_provider = #{serviceProvider}</if>
            <if test="onlyNo != null  and onlyNo != ''"> and only_no = #{onlyNo}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="serviceOrderNo != null  and serviceOrderNo != ''"> and service_order_no = #{serviceOrderNo}</if>
            <if test="serviceType != null  and serviceType != ''"> and service_type = #{serviceType}</if>
            <if test="completionTime != null  and completionTime != ''"> and completion_time = #{completionTime}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="saleName != null  and saleName != ''"> and sale_name like concat('%', #{saleName}, '%')</if>
            <if test="seqNo != null  and seqNo != ''"> and seq_no = #{seqNo}</if>
            <if test="testMonth != null  and testMonth != ''"> and test_month = #{testMonth}</if>
            <if test="callResult != null  and callResult != ''"> and call_result = #{callResult}</if>
            <if test="taskResult != null  and taskResult != ''"> and task_result = #{taskResult}</if>
            <if test="audioUrl != null  and audioUrl != ''"> and audio_url = #{audioUrl}</if>
            <if test="incomingTime != null  and incomingTime != ''"> and incoming_time = #{incomingTime}</if>
            <if test="zjStatus != null  and zjStatus != ''"> and
                exists(select * from zj_data_template t where t.data_id = s.id and zj_status=#{zjStatus})
            </if>
        </where>
    </select>
    
    <select id="selectZjDataById" parameterType="Long" resultMap="ZjDataResult">
        <include refid="selectZjDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertZjData" parameterType="ZjData" useGeneratedKeys="true" keyProperty="id">
        insert into zj_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platform != null">platform,</if>
            <if test="project != null">project,</if>
            <if test="targetName != null">target_name,</if>
            <if test="targetSubName != null">target_sub_name,</if>
            <if test="serviceCenter != null">service_center,</if>
            <if test="workNo != null">work_no,</if>
            <if test="workName != null">work_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="longTime != null">long_time,</if>
            <if test="dataDate != null">data_date,</if>
            <if test="qsNo != null">qs_no,</if>
            <if test="telephone != null">telephone,</if>
            <if test="businessType != null">business_type,</if>
            <if test="serviceProvider != null">service_provider,</if>
            <if test="onlyNo != null">only_no,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="area != null">area,</if>
            <if test="productType != null">product_type,</if>
            <if test="serviceOrderNo != null">service_order_no,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="completionTime != null">completion_time,</if>
            <if test="productName != null">product_name,</if>
            <if test="saleName != null">sale_name,</if>
            <if test="seqNo != null">seq_no,</if>
            <if test="testMonth != null">test_month,</if>
            <if test="callResult != null">call_result,</if>
            <if test="taskResult != null">task_result,</if>
            <if test="audioUrl != null">audio_url,</if>
            <if test="incomingTime != null">incoming_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platform != null">#{platform},</if>
            <if test="project != null">#{project},</if>
            <if test="targetName != null">#{targetName},</if>
            <if test="targetSubName != null">#{targetSubName},</if>
            <if test="serviceCenter != null">#{serviceCenter},</if>
            <if test="workNo != null">#{workNo},</if>
            <if test="workName != null">#{workName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="longTime != null">#{longTime},</if>
            <if test="dataDate != null">#{dataDate},</if>
            <if test="qsNo != null">#{qsNo},</if>
            <if test="telephone != null">#{telephone},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="serviceProvider != null">#{serviceProvider},</if>
            <if test="onlyNo != null">#{onlyNo},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="area != null">#{area},</if>
            <if test="productType != null">#{productType},</if>
            <if test="serviceOrderNo != null">#{serviceOrderNo},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="completionTime != null">#{completionTime},</if>
            <if test="productName != null">#{productName},</if>
            <if test="saleName != null">#{saleName},</if>
            <if test="seqNo != null">#{seqNo},</if>
            <if test="testMonth != null">#{testMonth},</if>
            <if test="callResult != null">#{callResult},</if>
            <if test="taskResult != null">#{taskResult},</if>
            <if test="audioUrl != null">#{audioUrl},</if>
            <if test="incomingTime != null">#{incomingTime},</if>
         </trim>
    </insert>

    <update id="updateZjData" parameterType="ZjData">
        update zj_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="platform != null">platform = #{platform},</if>
            <if test="project != null">project = #{project},</if>
            <if test="targetName != null">target_name = #{targetName},</if>
            <if test="targetSubName != null">target_sub_name = #{targetSubName},</if>
            <if test="serviceCenter != null">service_center = #{serviceCenter},</if>
            <if test="workNo != null">work_no = #{workNo},</if>
            <if test="workName != null">work_name = #{workName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="longTime != null">long_time = #{longTime},</if>
            <if test="dataDate != null">data_date = #{dataDate},</if>
            <if test="qsNo != null">qs_no = #{qsNo},</if>
            <if test="telephone != null">telephone = #{telephone},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="serviceProvider != null">service_provider = #{serviceProvider},</if>
            <if test="onlyNo != null">only_no = #{onlyNo},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="area != null">area = #{area},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="serviceOrderNo != null">service_order_no = #{serviceOrderNo},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="completionTime != null">completion_time = #{completionTime},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="saleName != null">sale_name = #{saleName},</if>
            <if test="seqNo != null">seq_no = #{seqNo},</if>
            <if test="testMonth != null">test_month = #{testMonth},</if>
            <if test="callResult != null">call_result = #{callResult},</if>
            <if test="taskResult != null">task_result = #{taskResult},</if>
            <if test="audioUrl != null">audio_url = #{audioUrl},</if>
            <if test="incomingTime != null">incoming_time = #{incomingTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZjDataById" parameterType="Long">
        delete from zj_data where id = #{id}
    </delete>

    <delete id="deleteZjDataByIds" parameterType="String">
        delete from zj_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getVideoText" resultType="java.lang.String" parameterType="java.lang.String">
        select asr_result from zj_ai_book_asr_result s where only_no=#{value}
    </select>

    <select id="getVideoTextMatchKey" resultType="java.lang.String" parameterType="java.lang.String">
        select match_key from zj_data_airesult_matchkey s where data_no=#{value}
    </select>

    <select id="getDbTemp" resultType="java.lang.String">
        select v from tempbk
    </select>

    <insert id="saveAsrTemp" parameterType="Map">
        insert into zj_ai_book_asr_result(only_no,file_path,asr_result) values (#{only_no},
        #{file_path},
        #{asr_result}
        )
    </insert>

</mapper>