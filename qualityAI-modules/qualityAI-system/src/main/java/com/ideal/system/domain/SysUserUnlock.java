package com.ideal.system.domain;

/**
 * 账户解锁实体类
 *
 * <AUTHOR>
 * @date 2025/6/27 17:10
 */
public class SysUserUnlock {

    private String workNo;
    private String userName;
    private String nickName;
    private Long lockTime;

    public String getWorkNo() {
        return workNo;
    }

    public SysUserUnlock setWorkNo(String workNo) {
        this.workNo = workNo;
        return this;
    }

    public String getUserName() {
        return userName;
    }

    public SysUserUnlock setUserName(String userName) {
        this.userName = userName;
        return this;
    }

    public String getNickName() {
        return nickName;
    }

    public SysUserUnlock setNickName(String nickName) {
        this.nickName = nickName;
        return this;
    }

    public Long getLockTime() {
        return lockTime;
    }

    public SysUserUnlock setLockTime(Long lockTime) {
        this.lockTime = lockTime;
        return this;
    }
}
