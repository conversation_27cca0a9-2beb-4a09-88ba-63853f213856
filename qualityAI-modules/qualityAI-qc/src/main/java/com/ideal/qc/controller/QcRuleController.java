package com.ideal.qc.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcRule;
import com.ideal.qc.service.IQcRuleService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 质检规则Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequestMapping("/rule")
public class QcRuleController extends BaseController
{
    @Autowired
    private IQcRuleService qcRuleService;
    
    /**
     * 查询质检规则列表
     */
    @RequiresPermissions("quality:rule:list")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody JSONObject qcRule)
    {
    	if(!qcRule.containsKey("isPg") || qcRule.getBooleanValue("isPg") == true) {
    		startPage();
    	}
        List<QcRule> list = qcRuleService.selectQcRuleList(qcRule);
        return getDataTable(list);
    }
    /**
     * 查询关键词库列表
     */
    @RequiresPermissions("quality:rule:list")
    @PostMapping("/kwl/list")
    public TableDataInfo kwlList()
    {
		startPage();
        List list = qcRuleService.queryKeyWordLibList();
        return getDataTable(list);
    }
    /**
     * 查询大模型规则列表
     */
    @RequiresPermissions("quality:rule:list")
    @PostMapping("/record/list")
    public TableDataInfo recordList(@RequestBody JSONObject param)
    {
        List list = qcRuleService.selectQcRuleRecording(param);
        return getDataTable(list);
    }

    /**
     * 导出质检规则列表
     */
    @RequiresPermissions("quality:rule:export")
    @Log(title = "质检规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JSONObject qcRule)
    {
        List<QcRule> list = qcRuleService.selectQcRuleList(qcRule);
        ExcelUtil<QcRule> util = new ExcelUtil<QcRule>(QcRule.class);
        util.exportExcel(response, list, "质检规则数据");
    }

    /**
     * 获取质检规则详细信息
     */
    @RequiresPermissions("quality:rule:query")
    @PostMapping("/query")
    public AjaxResult getInfo(@RequestBody QcRule QcRule)
    {
        return success(qcRuleService.selectQcRule(QcRule));
    }

    /**
     * 修改质检规则
     */
    @RequiresPermissions("quality:rule:edit")
    @Log(title = "质检规则", businessType = BusinessType.UPDATE)
    @PostMapping
    public AjaxResult edit(@RequestBody QcRule qcRule)
    {	qcRule.setUpdateBy(SecurityUtils.getUsername());
    	qcRule.setCreateBy(SecurityUtils.getUsername());
        return success(qcRuleService.updateQcRule(qcRule));
    }

    /**
     * 重新加载质检规则到缓存
     */
    @RequiresPermissions("quality:rule:reload")
    @Log(title = "质检规则", businessType = BusinessType.DELETE)
    @PostMapping("/reload")
    public AjaxResult reload()
    {
    	qcRuleService.reloadRules();
        return AjaxResult.success();
    }
}
