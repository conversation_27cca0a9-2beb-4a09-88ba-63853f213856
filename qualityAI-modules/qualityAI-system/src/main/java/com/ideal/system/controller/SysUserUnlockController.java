package com.ideal.system.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.system.domain.SysUserUnlock;
import com.ideal.system.service.ISysUserUnlockService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 账户解锁Controller接口
 *
 * <AUTHOR>
 * @date 2025/6/27 16:35
 */
@RestController
@RequestMapping("/unlock")
public class SysUserUnlockController extends BaseController {

    @Resource
    private ISysUserUnlockService sysUserUnlockService;

    /**
     * 获取已锁定账户列表
     */
    @RequiresPermissions("monitor:unlock:list")
    @PostMapping("/list")
    public TableDataInfo getUserUnlockList(@RequestBody JSONObject payload) {
        startPage();
        List<SysUserUnlock> result = sysUserUnlockService.getUserUnlockList(payload);
        return getDataTable(result);
    }

    /**
     * 账户解锁
     */
    @PostMapping("/{userName}")
        public AjaxResult unlockUser(@PathVariable String userName) {
        sysUserUnlockService.unlockUser(userName);
        return AjaxResult.success();
    }
}
