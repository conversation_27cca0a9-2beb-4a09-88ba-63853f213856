package com.ideal.qc.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcDataset;
import com.ideal.qc.service.IQcDatasetService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 数据集Controller
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/dataset")
public class QcDatasetController extends BaseController
{
    @Autowired
    private IQcDatasetService qcDatasetService;

    /**
     * 查询数据集列表
     */
    @RequiresPermissions("quality:dataset:list")
    @GetMapping("/list")
    public TableDataInfo list(QcDataset qcDataset)
    {
        startPage();
        List<QcDataset> list = qcDatasetService.selectQcDatasetList(qcDataset);
        return getDataTable(list);
    }
    /**
     * 查询数据集列表
     */
//    @RequiresPermissions("quality:dataset:list")
    @PostMapping("/list/byMap")
    public TableDataInfo listByMap(@RequestBody JSONObject param)
    {
    	if(!param.containsKey("isPg") || param.getBooleanValue("isPg")) {
    		startPage();
    	}
        List<QcDataset> list = qcDatasetService.selectQcDatasetListByMap(param);
        return getDataTable(list);
    }

    /**
     * 导出数据集列表
     */
    @RequiresPermissions("quality:dataset:export")
    @Log(title = "数据集", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QcDataset qcDataset)
    {
        List<QcDataset> list = qcDatasetService.selectQcDatasetList(qcDataset);
        ExcelUtil<QcDataset> util = new ExcelUtil<QcDataset>(QcDataset.class);
        util.exportExcel(response, list, "数据集数据");
    }

    /**
     * 获取数据集详细信息
     */
    @RequiresPermissions("quality:dataset:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(qcDatasetService.selectQcDatasetById(id));
    }

    /**
     * 新增数据集
     */
    @RequiresPermissions("quality:dataset:add")
    @Log(title = "数据集", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QcDataset qcDataset)
    {
        return toAjax(qcDatasetService.insertQcDataset(qcDataset));
    }

    /**
     * 修改数据集
     */
    @RequiresPermissions("quality:dataset:edit")
    @Log(title = "数据集", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QcDataset qcDataset)
    {
        return toAjax(qcDatasetService.updateQcDataset(qcDataset));
    }

    /**
     * 删除数据集
     */
    @RequiresPermissions("quality:dataset:remove")
    @Log(title = "数据集", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(qcDatasetService.deleteQcDatasetByIds(ids));
    }
}
