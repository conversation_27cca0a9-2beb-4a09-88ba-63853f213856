package com.ideal.qc.service;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcScoringClassification;
import com.ideal.qc.domain.QcScoringTemplate;

import java.util.List;

/**
 * 评分分类表Service接口
 *
 * <AUTHOR>
 * @date 2025/5/14 17:17
 */
public interface QcScoringClassificationService {

    /**
     * 查询评分模板分类列表
     *
     * @param scoringTemplateId 评分模板ID
     * @return 评分分类列表
     */
    List<QcScoringClassification> getQcScoringClassificationList(Long scoringTemplateId);

    /**
     * 新增评分模板分类
     *
     * @param payload 需要的一些参数
     */
    void addQcScoringClassification(JSONObject payload);

    /**
     * 修改评分模板分类
     *
     * @param payload 需要的一些参数
     */
    void updateQcScoringClassification(JSONObject payload);

    /**
     * 删除评分模板分类
     *
     * @param id 评分模板分类id
     */
    void removeQcScoringClassification(Long id);

    /**
     * 查询评分模板分类列表根据模板id列表
     *
     * @param scoringTemplateIds 评分模板id列表
     * @return 评分模板分类列表
     */
    List<QcScoringClassification> getQcScoringClassificationListByScoringTemplateIds(Long[] scoringTemplateIds);
}
