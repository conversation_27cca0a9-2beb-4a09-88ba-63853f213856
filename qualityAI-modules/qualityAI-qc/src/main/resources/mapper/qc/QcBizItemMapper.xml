<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcBizItemMapper">
    
    <resultMap type="QcBizItem" id="QcBizItemResult">
        <result property="id"    column="id"    />
        <result property="datasetId"    column="dataset_id"    />
        <result property="platform"    column="platform"    />
        <result property="projectName"    column="project_name"    />
        <result property="metricName"    column="metric_name"    />
        <result property="subMetricName"    column="sub_metric_name"    />
        <result property="serviceCenter"    column="service_center"    />
        <result property="agentId"    column="agent_id"    />
        <result property="agentName"    column="agent_name"    />
        <result property="answerStartAt"    column="answer_start_at"    />
        <result property="answerEndAt"    column="answer_end_at"    />
        <result property="callDurationS"    column="call_duration_s"    />
        <result property="recordDate"    column="record_date"    />
        <result property="surveyCode"    column="survey_code"    />
        <result property="callerPhone"    column="caller_phone"    />
        <result property="businessType"    column="business_type"    />
        <result property="carrier"    column="carrier"    />
        <result property="uniqueKey"    column="unique_key"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="areaCode"    column="area_code"    />
        <result property="productType"    column="product_type"    />
        <result property="serviceOrderId"    column="service_order_id"    />
        <result property="serviceType"    column="service_type"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="productName"    column="product_name"    />
        <result property="salesPoint"    column="sales_point"    />
        <result property="taskResult"    column="task_result"    />
        <result property="recordingFile"    column="recording_file"    />
        <result property="callTime"    column="call_time"    />
        <result property="qcLvl1Result"    column="qc_lvl1_result"    />
        <result property="qcLvl1Comment"    column="qc_lvl1_comment"    />
        <result property="qcLvl1EmpId"    column="qc_lvl1_emp_id"    />
        <result property="qcLvl2Result"    column="qc_lvl2_result"    />
        <result property="qcLvl2Comment"    column="qc_lvl2_comment"    />
        <result property="qcLvl2EmpId"    column="qc_lvl2_emp_id"    />
        <result property="summaryRemark"    column="summary_remark"    />
        <result property="answersJson"    column="answers_json"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="asrResult"    column="asr_result"    />
    </resultMap>

    <sql id="selectQcBizItemVo">
        select id, dataset_id, platform, project_name, metric_name, sub_metric_name, service_center, agent_id, agent_name, answer_start_at, answer_end_at, call_duration_s, record_date, survey_code, caller_phone, business_type, carrier, unique_key, province, city, contact_phone, area_code, product_type, service_order_id, service_type, finish_time, product_name, sales_point, task_result, recording_file, call_time, qc_lvl1_result, qc_lvl1_comment, qc_lvl1_emp_id, qc_lvl2_result, qc_lvl2_comment, qc_lvl2_emp_id, summary_remark, answers_json, tenant_id, create_time from qc_biz_item
    </sql>

    <select id="selectQcBizItemList" parameterType="QcBizItem" resultMap="QcBizItemResult">
        <include refid="selectQcBizItemVo"/>
        <where>  
            <if test="datasetId != null "> and dataset_id = #{datasetId}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="metricName != null  and metricName != ''"> and metric_name like concat('%', #{metricName}, '%')</if>
            <if test="subMetricName != null  and subMetricName != ''"> and sub_metric_name like concat('%', #{subMetricName}, '%')</if>
            <if test="serviceCenter != null  and serviceCenter != ''"> and service_center = #{serviceCenter}</if>
            <if test="agentId != null  and agentId != ''"> and agent_id = #{agentId}</if>
            <if test="agentName != null  and agentName != ''"> and agent_name like concat('%', #{agentName}, '%')</if>
            <if test="answerStartAt != null "> and answer_start_at = #{answerStartAt}</if>
            <if test="answerEndAt != null "> and answer_end_at = #{answerEndAt}</if>
            <if test="callDurationS != null "> and call_duration_s = #{callDurationS}</if>
            <if test="recordDate != null "> and record_date = #{recordDate}</if>
            <if test="surveyCode != null  and surveyCode != ''"> and survey_code = #{surveyCode}</if>
            <if test="callerPhone != null  and callerPhone != ''"> and caller_phone = #{callerPhone}</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="carrier != null  and carrier != ''"> and carrier = #{carrier}</if>
            <if test="uniqueKey != null  and uniqueKey != ''"> and unique_key = #{uniqueKey}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="serviceOrderId != null  and serviceOrderId != ''"> and service_order_id = #{serviceOrderId}</if>
            <if test="serviceType != null  and serviceType != ''"> and service_type = #{serviceType}</if>
            <if test="finishTime != null "> and finish_time = #{finishTime}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="salesPoint != null  and salesPoint != ''"> and sales_point = #{salesPoint}</if>
            <if test="taskResult != null  and taskResult != ''"> and task_result = #{taskResult}</if>
            <if test="recordingFile != null  and recordingFile != ''"> and recording_file = #{recordingFile}</if>
            <if test="callTime != null "> and call_time = #{callTime}</if>
            <if test="qcLvl1Result != null  and qcLvl1Result != ''"> and qc_lvl1_result = #{qcLvl1Result}</if>
            <if test="qcLvl1Comment != null  and qcLvl1Comment != ''"> and qc_lvl1_comment = #{qcLvl1Comment}</if>
            <if test="qcLvl1EmpId != null  and qcLvl1EmpId != ''"> and qc_lvl1_emp_id = #{qcLvl1EmpId}</if>
            <if test="qcLvl2Result != null  and qcLvl2Result != ''"> and qc_lvl2_result = #{qcLvl2Result}</if>
            <if test="qcLvl2Comment != null  and qcLvl2Comment != ''"> and qc_lvl2_comment = #{qcLvl2Comment}</if>
            <if test="qcLvl2EmpId != null  and qcLvl2EmpId != ''"> and qc_lvl2_emp_id = #{qcLvl2EmpId}</if>
            <if test="summaryRemark != null  and summaryRemark != ''"> and summary_remark = #{summaryRemark}</if>
            <if test="answersJson != null  and answersJson != ''"> and answers_json = #{answersJson}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
        </where>
    </select>
    
    <select id="selectQcBizItemById" parameterType="Long" resultMap="QcBizItemResult">
        <include refid="selectQcBizItemVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcBizItem" parameterType="QcBizItem" useGeneratedKeys="true" keyProperty="id">
        insert into qc_biz_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="datasetId != null">dataset_id,</if>
            <if test="platform != null">platform,</if>
            <if test="projectName != null">project_name,</if>
            <if test="metricName != null">metric_name,</if>
            <if test="subMetricName != null">sub_metric_name,</if>
            <if test="serviceCenter != null">service_center,</if>
            <if test="agentId != null">agent_id,</if>
            <if test="agentName != null">agent_name,</if>
            <if test="answerStartAt != null">answer_start_at,</if>
            <if test="answerEndAt != null">answer_end_at,</if>
            <if test="callDurationS != null">call_duration_s,</if>
            <if test="recordDate != null">record_date,</if>
            <if test="surveyCode != null">survey_code,</if>
            <if test="callerPhone != null">caller_phone,</if>
            <if test="businessType != null">business_type,</if>
            <if test="carrier != null">carrier,</if>
            <if test="uniqueKey != null">unique_key,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="productType != null">product_type,</if>
            <if test="serviceOrderId != null">service_order_id,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="productName != null">product_name,</if>
            <if test="salesPoint != null">sales_point,</if>
            <if test="taskResult != null">task_result,</if>
            <if test="recordingFile != null">recording_file,</if>
            <if test="callTime != null">call_time,</if>
            <if test="qcLvl1Result != null">qc_lvl1_result,</if>
            <if test="qcLvl1Comment != null">qc_lvl1_comment,</if>
            <if test="qcLvl1EmpId != null">qc_lvl1_emp_id,</if>
            <if test="qcLvl2Result != null">qc_lvl2_result,</if>
            <if test="qcLvl2Comment != null">qc_lvl2_comment,</if>
            <if test="qcLvl2EmpId != null">qc_lvl2_emp_id,</if>
            <if test="summaryRemark != null">summary_remark,</if>
            <if test="answersJson != null">answers_json,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="datasetId != null">#{datasetId},</if>
            <if test="platform != null">#{platform},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="metricName != null">#{metricName},</if>
            <if test="subMetricName != null">#{subMetricName},</if>
            <if test="serviceCenter != null">#{serviceCenter},</if>
            <if test="agentId != null">#{agentId},</if>
            <if test="agentName != null">#{agentName},</if>
            <if test="answerStartAt != null">#{answerStartAt},</if>
            <if test="answerEndAt != null">#{answerEndAt},</if>
            <if test="callDurationS != null">#{callDurationS},</if>
            <if test="recordDate != null">#{recordDate},</if>
            <if test="surveyCode != null">#{surveyCode},</if>
            <if test="callerPhone != null">#{callerPhone},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="carrier != null">#{carrier},</if>
            <if test="uniqueKey != null">#{uniqueKey},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="productType != null">#{productType},</if>
            <if test="serviceOrderId != null">#{serviceOrderId},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="productName != null">#{productName},</if>
            <if test="salesPoint != null">#{salesPoint},</if>
            <if test="taskResult != null">#{taskResult},</if>
            <if test="recordingFile != null">#{recordingFile},</if>
            <if test="callTime != null">#{callTime},</if>
            <if test="qcLvl1Result != null">#{qcLvl1Result},</if>
            <if test="qcLvl1Comment != null">#{qcLvl1Comment},</if>
            <if test="qcLvl1EmpId != null">#{qcLvl1EmpId},</if>
            <if test="qcLvl2Result != null">#{qcLvl2Result},</if>
            <if test="qcLvl2Comment != null">#{qcLvl2Comment},</if>
            <if test="qcLvl2EmpId != null">#{qcLvl2EmpId},</if>
            <if test="summaryRemark != null">#{summaryRemark},</if>
            <if test="answersJson != null">#{answersJson},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateQcBizItem" parameterType="QcBizItem">
        update qc_biz_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="datasetId != null">dataset_id = #{datasetId},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="metricName != null">metric_name = #{metricName},</if>
            <if test="subMetricName != null">sub_metric_name = #{subMetricName},</if>
            <if test="serviceCenter != null">service_center = #{serviceCenter},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="agentName != null">agent_name = #{agentName},</if>
            <if test="answerStartAt != null">answer_start_at = #{answerStartAt},</if>
            <if test="answerEndAt != null">answer_end_at = #{answerEndAt},</if>
            <if test="callDurationS != null">call_duration_s = #{callDurationS},</if>
            <if test="recordDate != null">record_date = #{recordDate},</if>
            <if test="surveyCode != null">survey_code = #{surveyCode},</if>
            <if test="callerPhone != null">caller_phone = #{callerPhone},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="carrier != null">carrier = #{carrier},</if>
            <if test="uniqueKey != null">unique_key = #{uniqueKey},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="serviceOrderId != null">service_order_id = #{serviceOrderId},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="salesPoint != null">sales_point = #{salesPoint},</if>
            <if test="taskResult != null">task_result = #{taskResult},</if>
            <if test="recordingFile != null">recording_file = #{recordingFile},</if>
            <if test="callTime != null">call_time = #{callTime},</if>
            <if test="qcLvl1Result != null">qc_lvl1_result = #{qcLvl1Result},</if>
            <if test="qcLvl1Comment != null">qc_lvl1_comment = #{qcLvl1Comment},</if>
            <if test="qcLvl1EmpId != null">qc_lvl1_emp_id = #{qcLvl1EmpId},</if>
            <if test="qcLvl2Result != null">qc_lvl2_result = #{qcLvl2Result},</if>
            <if test="qcLvl2Comment != null">qc_lvl2_comment = #{qcLvl2Comment},</if>
            <if test="qcLvl2EmpId != null">qc_lvl2_emp_id = #{qcLvl2EmpId},</if>
            <if test="summaryRemark != null">summary_remark = #{summaryRemark},</if>
            <if test="answersJson != null">answers_json = #{answersJson},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcBizItemById" parameterType="Long">
        delete from qc_biz_item where id = #{id}
    </delete>

    <delete id="deleteQcBizItemByIds" parameterType="String">
        delete from qc_biz_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectQcBizItemByNo" resultMap="QcBizItemResult">
        <include refid="selectQcBizItemVo"/>
        where unique_key = #{bizNo}
    </select>

    <select id="selectQcSmartTaskDetailByNo" parameterType="String" resultType="com.ideal.qc.domain.QcSmartTaskDetail">
        SELECT s.template_id,s.id FROM qc_smart_task_detail s WHERE s.biz_no=#{value}
    </select>

    <select id="selectQcManualTaskDetailByNo" resultType="java.util.Map">
        SELECT s.template_id,s.id,s.manual_type,s.smart_task_detail_id FROM qc_manual_task_detail s WHERE s.biz_no=#{value}
    </select>
    <select id="queryQcDatasetItemAndBus" parameterType="map" resultMap="QcBizItemResult">
        select (select GROUP_CONCAT(qdi.asr_result) from qc_dataset_item qdi where qdi.biz_no=qbi.unique_key group by qdi.biz_no) as asr_result,
        qbi.dataset_id, qbi.platform,qbi.project_name,qbi.metric_name,qbi.sub_metric_name,qbi.service_center,qbi.agent_id,qbi.agent_name,qbi.answer_start_at,
        qbi.answer_end_at,qbi.call_duration_s,qbi.record_date,qbi.survey_code,qbi.caller_phone,qbi.business_type,qbi.carrier,qbi.unique_key,qbi.province,
        qbi.city,qbi.contact_phone,qbi.area_code,qbi.product_type,qbi.service_order_id,qbi.service_type,qbi.finish_time,qbi.product_name,qbi.sales_point,
        qbi.task_result,qbi.recording_file,qbi.call_time,qbi.qc_lvl1_result,qbi.qc_lvl1_comment,qbi.qc_lvl1_emp_id,qbi.qc_lvl2_result,qbi.qc_lvl2_comment,
        qbi.qc_lvl2_emp_id,qbi.summary_remark,qbi.answers_json
        from qc_biz_item qbi 
        <where> 
            <if test="cBegTime != null and cBegTime != ''"> and qbi.create_time > #{cBegTime}</if>
            <if test="cEndTime != null and cEndTime != ''"> and qbi.create_time &lt; #{cEndTime}</if>
            <if test="uBegTime != null and uBegTime != ''"> and qbi.update_time > #{uBegTime}</if>
            <if test="uEndTime != null and uEndTime != ''"> and qbi.update_time &lt; #{uEndTime}</if>
            <if test="asrStatus != null and asrStatus != ''"> and qbi.asr_status = #{asrStatus}</if>
            <if test="datasetId != null and datasetId != ''"> and qbi.dataset_id = #{datasetId}</if>
        	<if test="filterEmpty != null and filterEmpty == 1"> and qbi.call_duration_s >= 0 </if>
        </where>
    </select>

    <!--人工抽检数据集-->
    <select id="selectDataForSampling" resultMap="QcBizItemResult">
    select
        dataset_id,unique_key, call_duration_s,agent_id,agent_name,create_time
    from
        qc_biz_item
    where
    	update_time &gt; #{dataRangeDate}
    	and asr_status = 'done'
    	and agent_id is not null
    </select>

    <select id="selectQcManualTaskDetailById" resultType="java.util.Map" parameterType="Long">
        SELECT s.template_id,s.id FROM qc_manual_task_detail s WHERE s.id=#{value}
    </select>

    <select id="getAiResultByManualDetailId" resultType="java.lang.String">
        SELECT mac_inspect_result FROM qc_smart_task_detail s,qc_manual_task_detail m WHERE s.id=m.smart_task_detail_id AND m.id=#{value}
    </select>
</mapper>