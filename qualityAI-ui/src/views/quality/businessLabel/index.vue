<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="中文名称" prop="businessLabelName">
        <el-input v-model="queryParams.businessLabelName" placeholder="请输入字段中文名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标签类型" prop="businessLabelType">
        <el-select v-model="queryParams.businessLabelType" placeholder="请选择标签类型" clearable>
          <el-option v-for="dict in dict.type.quality_business_label_type" :key="dict.value" :label="dict.label"
            :value="dict.value" @keyup.enter.native="handleQuery" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['quality:businessLabel:add']">新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="businessLabelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="字段中文名称" align="center" prop="businessLabelName" />
      <el-table-column label="标签类型" align="center" prop="businessLabelType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.quality_business_label_type" :value="scope.row.businessLabelType" />
        </template>
      </el-table-column>
      <el-table-column label="字段名称" align="center" prop="businessLabelKey">
        <template slot-scope="scope">
          <span v-if="scope.row.businessLabelType === '1' && scope.row.businessLabelStringMode === '1'">
            <el-link type="danger" @click="openStringTypeDetail(scope.row)">{{ scope.row.businessLabelKey }}</el-link>
          </span>
          <span v-else>
            {{ scope.row.businessLabelKey}}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center">
        <template slot-scope="scope">
          <el-switch active-value="1" inactive-value="0" v-model="scope.row.businessLabelStatus"
            @change="handleBusinessLabelStatus(scope.row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['quality:businessLabel:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['quality:businessLabel:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body :close-on-click-modal="false">
      <el-form size="small" :inline="true" label-position="top" ref="businessLabelForm" :model="businessLabelForm"
        :rules="businessLabelAddOrUpdateFormRules" label-width="100px" class="businessLabelAddOrUpdateForm">
        <el-row>
          <el-col>
            <el-form-item label="中文名称" prop="businessLabelName">
              <el-input v-model="businessLabelForm.businessLabelName" placeholder="请输入字段中文名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="标签类型" prop="businessLabelType">
              <el-select v-model="businessLabelForm.businessLabelType" placeholder="请选择标签类型" clearable>
                <el-option v-for="dict in dict.type.quality_business_label_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="字段名称" prop="businessLabelKey">
              <el-input v-model="businessLabelForm.businessLabelKey" placeholder="请输入字段名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="businessLabelForm.businessLabelType === '1'">
          <el-col>
            <el-form-item label="字符模式" prop="businessLabelStringMode">
              <el-radio-group v-model="businessLabelForm.businessLabelStringMode" @change="changeBusinessLabelStringMode">
                <el-radio-button label="0">普通字符串</el-radio-button>
                <el-radio-button label="1">label-value</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="businessLabelForm.businessLabelType === '1' && businessLabelForm.businessLabelStringMode === '1'">
          <el-col>
            <el-form-item label="label-value">
              <div v-for="(item, index) in businessLabelForm.businessLabelValue">
                <el-card class="box-card" style="margin-bottom: 10px;">
                  <div class="ruleItemClass">
                    <el-input v-model="item.label" placeholder="请输入label值" style="margin-right: 10px;" />
                    <el-input v-model="item.value" placeholder="请输入value值" style="margin-right: 10px;" />
                    <el-button size="mini" @click="removeKeyValueComboBox(item)" :disabled="index === 0">移除</el-button>
                    <el-button size="mini" @click="addKeyValueComboBox(item)">添加</el-button>
                  </div>

                </el-card>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="详情" :visible.sync="stringTypeDetailVisible" width="40%" append-to-body :close-on-click-modal="false">
      <div>
        <el-card class="box-card">
          <div v-for="(item, index) in activeStringTypeDetail" :key="index"
            style="display: flex; flex-flow: row nowrap;align-items: center;justify-content:center"
            class="stringTypeDetailClass">
            <div style="margin-right: 10px; margin-bottom: 20px; margin-top: 20px;">Label:</div>
            <div style="margin-right: 10px; margin-bottom: 20px; margin-top: 20px;"><el-input disabled
                v-model="item.label" placeholder="请输入Label值" style="margin-right: 10px;" /></div>
            <div style="margin-right: 10px; margin-bottom: 20px; margin-top: 20px;">Value:</div>
            <div style="margin-right: 10px; margin-bottom: 20px; margin-top: 20px;"><el-input disabled
                v-model="item.value" placeholder="请输入Value值" style="margin-right: 10px;" /></div>
          </div>
        </el-card>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getQcBusinessLabelList, addQcBusinessLabel, updateQcBusinessLabel, removeQcBusinessLabel } from "@/api/quality/businessLabel";

export default {
  name: "QualityBusinessLabel",
  dicts: ['quality_business_label_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      businessLabelList: [],
      title: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        businessLabelName: null,
        businessLabelType: null
      },
      businessLabelAddOrUpdateFormRules: {
        businessLabelName: [
          { required: true, message: "字段中文名称不能为空", trigger: "blur" }
        ],
        businessLabelType: [
          { required: true, message: "标签类型不能为空", trigger: "blur" }
        ],
        businessLabelKey: [
          { required: true, message: "字段名称不能为空", trigger: "blur" }
        ]
      },
      businessLabelForm: {},
      stringTypeDetailVisible: false,
      activeStringTypeDetail: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      const queryParams = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize
      };
      const payload = {
        businessLabelName: this.queryParams.businessLabelName,
        businessLabelType: this.queryParams.businessLabelType,
      };
      getQcBusinessLabelList(queryParams, payload).then(response => {
        this.businessLabelList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    cancel() {
      this.open = false;
      this.getList();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleAdd() {
      this.businessLabelForm = {
        businessLabelStringMode: '0',
        businessLabelValue: ''
      }
      this.open = true;
      this.title = "添加业务标签";
    },
    handleUpdate(row) {
      this.businessLabelForm = row;
      if (this.businessLabelForm.businessLabelType === '1' && this.businessLabelForm.businessLabelStringMode === '1' && typeof row.businessLabelValue === 'string') {
        this.businessLabelForm.businessLabelValue = JSON.parse(row.businessLabelValue);
      }
      this.open = true;
      this.title = "修改业务标签";
    },
    submitForm() {
      this.$refs["businessLabelForm"].validate(valid => {
        const businessLabelForm = this.businessLabelForm;
        if (valid) {
          if (this.businessLabelForm.id != null) {
            updateQcBusinessLabel(businessLabelForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQcBusinessLabel(this.businessLabelForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否删除？').then(function () {
        return removeQcBusinessLabel(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    openScoringClassification(row) {
      const businessLabelId = row.id;
      this.$router.push("/quality/businessLabel/scoringClassification/" + businessLabelId);
    },
    handleBusinessLabelStatus(row) {
      this.businessLabelForm = row;
      updateQcBusinessLabel(this.businessLabelForm).then(response => {
        if (row.businessLabelStatus === '0') {
          this.$modal.msgSuccess("禁用成功");
        }
        if (row.businessLabelStatus === '1') {
          this.$modal.msgSuccess("启用成功");
        }
        this.getList();
      });
    },
    openStringTypeDetail(row) {
      this.stringTypeDetailVisible = true;
      let businessLabelValue = row.businessLabelValue;
      if (typeof businessLabelValue === 'string') {
        businessLabelValue = JSON.parse(businessLabelValue);
      }
      this.activeStringTypeDetail = businessLabelValue;
    },
    removeKeyValueComboBox(item) {
      var index = this.businessLabelForm.businessLabelValue.indexOf(item)
      if (index !== -1) {
        this.businessLabelForm.businessLabelValue.splice(index, 1)
      }
    },
    addKeyValueComboBox() {
      this.businessLabelForm.businessLabelValue.push({});
    },
    changeBusinessLabelStringMode(row) {
      if (this.businessLabelForm.businessLabelType === '1' && row === '1') {
        this.businessLabelForm.businessLabelValue = [{}]
      } else {
        this.businessLabelForm.businessLabelValue = ''
      }
    }
  }
};
</script>

<style scoped>
.businessLabelAddOrUpdateForm .el-form-item {
  width: 100%;
}

::v-deep .businessLabelAddOrUpdateForm .el-form-item .el-form-item__label {
  padding: 0px;
}

.businessLabelAddOrUpdateForm .el-select {
  width: 100%;
}

.ruleItemClass {
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: stretch;
}
</style>

<style rel="stylesheet/scss" lang="scss">
.stringTypeDetailClass .el-input.is-disabled .el-input__inner {
  color: black;
  font-weight: bolder;
  border: 3px solid #1f6888;
}
</style>