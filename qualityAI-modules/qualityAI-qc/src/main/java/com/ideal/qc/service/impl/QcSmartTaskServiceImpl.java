package com.ideal.qc.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ideal.common.core.constant.QcConstants;
import com.ideal.common.core.utils.DateUtils;
import com.ideal.common.redis.service.RedisService;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcBizItem;
import com.ideal.qc.domain.QcSmartPlan;
import com.ideal.qc.domain.QcSmartTask;
import com.ideal.qc.domain.QcSmartTaskDetail;
import com.ideal.qc.domain.QcSmartTaskResult;
import com.ideal.qc.mapper.QcBizItemMapper;
import com.ideal.qc.mapper.QcScoringTemplateMapper;
import com.ideal.qc.mapper.QcSmartPlanMapper;
import com.ideal.qc.mapper.QcSmartTaskMapper;

import lombok.extern.slf4j.Slf4j;

import com.ideal.qc.service.IQcSmartTaskService;
import com.ideal.qc.service.IRuleConditionService;
import com.ideal.qc.thread.AsyncUtil;
import com.ideal.qc.utils.AsrUtils;

/**
 * 
 */
@Service
@Slf4j
@EnableAsync
public class QcSmartTaskServiceImpl implements IQcSmartTaskService{
	@Autowired
	private QcSmartTaskMapper qstMapper;
	@Autowired
	private QcBizItemMapper qbiMapper;
	@Autowired
	private QcSmartPlanMapper qspMapper;
	@Autowired
	private QcScoringTemplateMapper qst1Mapper;
	@Autowired
	private IRuleConditionService rcService;
	@Autowired
	private RedisService redisService;
	@Resource
	AsyncUtil asyncUtil;
	@Resource(name="taskExecutor")
	ThreadPoolTaskExecutor executor;
	private SimpleDateFormat sdf_h = new SimpleDateFormat("HH");
	private SimpleDateFormat sdf_ymdh = new SimpleDateFormat("yyyyMMdd_HH");
	/**
     * 查询质检规则列表
     *
     * @param QcRule 质检规则
     * @return 质检规则
     */
    @Override
    public List<QcSmartTask> selectQcSmartTask(JSONObject qst)
    {
        return qstMapper.selectQcSmartTaskList(qst);
    }
    @Override
    public List<QcSmartTaskDetail> selectQcSmartTaskDetail(JSONObject param)
    {
        return qstMapper.selectQcSmartTaskDetailList(param);
    }
    @Override
    public List<QcSmartTaskResult> selectQcSmartTaskResult(QcSmartTaskResult qstr)
    {
        return qstMapper.selectQcSmartTaskResult(qstr);
    }
    @Override
    public List<QcSmartPlan> selectQcSmartPlanList(JSONObject qsp)
    {
        return qspMapper.selectQcSmartPlanList(qsp);
    }
	/**
	 * 按条件批量执行智能质检任务明细
	 * @param tData
	 * @return
	 */
	@Override
	public JSONObject doSmartTaskDetail(JSONObject tData) {
		String taskTimestamp‌‌ = getTaskLockValue();
		log.info(QcConstants.REDIS_LOCK_KEY_SMART_TASK + ":" + taskTimestamp‌‌);
		//设置
		boolean planLock = redisService.getLock(QcConstants.REDIS_LOCK_KEY_SMART_TASK, taskTimestamp‌‌, 1800);
		try {
			if(planLock) {
				JSONObject qsd = new JSONObject();
				qsd.put("status",tData.getIntValue("status"));
				qsd.put("updateTimeBeg",tData.getString("updBegTime"));
				qsd.put("updateTimeEnd",tData.getString("updEndTime"));
				qsd.put("id",tData.getLong("id"));
				qsd.put("smartTaskId",tData.getLong("smartTaskId"));
				//
				//单次定时任务最大处理数据条数，防止出现当前小时跑不完数据的情况或内存溢出的情况
				int maxCount = 5000;
				int execCount = 0;
				int total = qstMapper.selectQcSmartTaskDetailCount(qsd);
				//当待处理的数据量大于单次最大值时，赋值成单次最大值
				total = total>maxCount?maxCount:total;
				log.info("当前待处理的数据量："+total);
				PageHelper.clearPage();
				int offset = 0;
				//平均分配给每个线程
				int coreThreadCount = executor.getCorePoolSize();
				log.info("核心线程数："+coreThreadCount);
				int pgSize = total/coreThreadCount+1;
				PageHelper.offsetPage(offset, pgSize, true);
				int index = 0;
				List<QcSmartTaskDetail> list = qstMapper.selectQcSmartTaskDetailList(qsd);
				while(list.size() != 0 && execCount <maxCount) {
					//index++;
					execCount += list.size();
					offset = index * pgSize;
					//更新数据状态为执行中，防止其他节点跑相同数据
					JSONObject tmp = new JSONObject();
					tmp.put("status",QcConstants.SMART_TASK_DETAIL_STATUS_DOING);
					tmp.put("list", list);
					qstMapper.updateQcSmartTaskDetailStatus(tmp);
					asyncUtil.doSmartTask(this, list, tData);
					PageHelper.offsetPage(offset, pgSize, false);
					list = qstMapper.selectQcSmartTaskDetailList(qsd);
				}
				PageHelper.clearPage();
			}
		}catch(Exception e){
			log.error("执行智能质检失败：",e);
		}finally {
			redisService.unlock(QcConstants.REDIS_LOCK_KEY_SMART_TASK, taskTimestamp‌‌);
		}
		return null;
	}
	@Override
	public void batchDoSmartTaskDetail(List<QcSmartTaskDetail> list,JSONObject tData) {
		log.info("----batchDoSmartTaskDetail begin count:"+list.size()+"----");
		Date start = new Date();
		JSONObject jt = new JSONObject();
		Long currTempId = 0L;
		List<QcSmartTaskDetail> errList = new ArrayList<QcSmartTaskDetail>();
		JSONObject asrConf = AsrUtils.formatAsrLabel(QcConstants.REDIS_ASR_CONF_KEY);
		for(int i=0;i<list.size();i++) {
			try {
				JSONObject tmp = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)));
				tmp.put(QcConstants.REDIS_ASR_CONF_KEY, asrConf);
				if(tmp.containsKey("templateHitRule")) {
					try {
						jt = tmp.getJSONObject("templateHitRule");
					}catch(Exception e) {}
				}
				//缓存的模板为空时，获取最新模板
				if(jt.isEmpty()) {
					if(tmp.getLongValue("templateId") != currTempId) {
						currTempId = tmp.getLongValue("templateId");
						jt = JSONObject.parseObject(JSON.toJSONString(qst1Mapper.getQcScoringTemplate(currTempId)));
					}
				}
				doSmartTaskDetail(tmp,jt);
			}catch(Exception e) {
				log.error("智能质检明细执行失败：",e);
				list.get(i).setStatus(QcConstants.SMART_TASK_DETAIL_STATUS_FAIL);
				errList.add(list.get(i));
			}
		}
		if(errList.size() > 0) {
			qstMapper.batchUpdateQcSmartTaskDetail(errList);
		}
		log.info("----batchDoSmartTaskDetail end 耗时："+(new Date().getTime()-start.getTime())+"----");
	}
	public boolean doSmartTaskDetail(JSONObject param,JSONObject jt) {
		boolean result = true;
//		try {
			rcService.doTemplate(param,jt);
//		}catch(Exception e) {
//			log.error("智能任务跑批异常：",e);
//			result = false;
//		}
		return result;
	}
	/**
	 *执行智能质检计划
	 */
	@Override
	public JSONObject doSmartPlan(JSONObject tData) {
		String planTimestamp‌‌ = getTaskLockValue();
		boolean planLock = redisService.getLock(QcConstants.REDIS_LOCK_KEY_SMART_PLAN, planTimestamp‌‌, 300);
		try {
			if(planLock) {
				//查询当前时点配置的计划列表
				JSONObject qsl = new JSONObject();
				//生效
				qsl.put("status",QcConstants.SMART_STATUS_COMMON_VALID);
				qsl.put("id", tData.getLongValue("id"));
				if(tData.containsKey("planType")) {
					qsl.put("planType",tData.getString("planType"));
				}else {
					log.error("计划执行失败：缺少计划类型");
					return null;
				}
				if(qsl.getIntValue("planType") == QcConstants.SMART_PLAN_TYPE_FIXED_TIME) {
					if(tData.containsKey("planExecTime")) {
						qsl.put("planExecTime",tData.getString("planExecTime"));
					}else {
						qsl.put("planExecTime",sdf_h.format(new Date())+":00:00");
					}
				}else if(qsl.getIntValue("planType") == QcConstants.SMART_PLAN_TYPE_REAL_TIME){
					if(tData.containsKey("datasetPlanId")) {
						qsl.put("datasetPlanId",tData.getString("datasetPlanId"));
					}else {
						log.error("计划执行失败：实时计划缺少数据集计划id");
						return null;
					}
					if(!tData.containsKey("datasetId")) {
						log.error("计划执行失败：实时计划缺少数据集计划id");
						return null;
					}
				}else if(qsl.getIntValue("planType") == QcConstants.SMART_PLAN_TYPE_MT){
					
					if(!tData.containsKey("datasetId")) {
						log.error("计划执行失败：实时计划缺少数据集计划id");
						return null;
					}
				}
				List<QcSmartPlan> qslList = qspMapper.selectQcSmartPlanList(qsl);
				for(int i=0;i<qslList.size();i++) {
					QcSmartPlan qsp = qslList.get(i);
					if(qsl.getIntValue("planType") == QcConstants.SMART_PLAN_TYPE_REAL_TIME) {
						qsp.setDatasetId(tData.getLong("datasetId"));
					}
					try {
						createSmartTask(qsp);
					} catch (Exception e) {
						log.error("创建任务异常：",e);
					}
				}
			}
		}catch(Exception e){
			e.printStackTrace();
		}finally {
			redisService.unlock(QcConstants.REDIS_LOCK_KEY_SMART_PLAN, planTimestamp‌‌);
		}
		
		//查询明细
		return null;
	}
	/**
	 * 创建手动质检任务
	 */
	@Override
	public JSONObject createSmartTask(JSONObject qsp) {
		if(qsp.containsKey("datasetIds")) {
			JSONArray ids = qsp.getJSONArray("datasetIds");
			for(int i=0;i<ids.size();i++) {
				qsp.put("datasetId", ids.get(i));
				try {
					createSmartTask(JSONObject.parseObject(qsp.toString(), QcSmartPlan.class));
				} catch (Exception e) {
					log.error("手动创建智能质检任务异常：",e);
				}
			}
		}else {
			log.error("手动创建智能质检任务异常：缺少数据集id");
		}
		return null;
	}
	/**
	 * 根据计划创建任务
	 * @param qsp
	 * @return
	 * @throws Exception 
	 */
	private JSONObject createSmartTask(QcSmartPlan qsp) throws Exception {
		//生成任务对象
		QcSmartTask qst = new QcSmartTask();
		//设置任务状态：待执行
		qst.setStatus(QcConstants.SMART_TASK_STATUS_DOING);
		//设置计划id
		qst.setPlanId(qsp.getId());
		qst.setPlanName(qsp.getPlanName());
		qst.setTaskType(qsp.getPlanType());
		qst.setDatasetId(qsp.getDatasetId());
		if(QcConstants.SMART_PLAN_TYPE_MT ==qst.getTaskType()) {
			qst.setCreateBy(SecurityUtils.getUsername());
			qst.setTaskName(qsp.getTaskName());
			qst.setPlanExecTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
		}else {
			qst.setCreateBy("system");
			qst.setTaskName(qsp.getPlanName()+"_"+sdf_ymdh.format(new Date()));
			qst.setPlanExecTime(DateUtils.parseDateToStr("yyyy-MM-dd HH", new Date()));

		}
		if(qsp.getDatasetId() != null) {
			qst.setDatasetId(qsp.getDatasetId());
		}
		if(qsp.getScoreTemplate() == null) {
			throw new Exception("模板未配置");
		}
		qst.setTemplateId(qsp.getScoreTemplate());
		String bt = qsp.getDataBegTime();
		if(bt != null) {
			bt = DateUtils.plusDays(bt.startsWith("-")?-1:0).toString()+" "+bt.substring(1);
			qst.setBegTime(bt);
		}
		String et = qsp.getDataEndTime();
		if(et != null) {
			et = DateUtils.plusDays(et.startsWith("-")?-1:0).toString()+" "+et.substring(1);
			qst.setEndTime(et);
		}
		qst.setFilterEmptySession(qsp.getFilterEmptySession());
		List<Map> list1  =qstMapper.selectSemanticsIdsByTempId(qst.getTemplateId());
		qst.setIsSematic(list1.size()>0?QcConstants.IS_SEMATIC_TURE:QcConstants.IS_SEMATIC_FALSE);
		int id = qstMapper.insertQcSmartTask(qst);
		//大模型任务列表
		createSmartTaskDetail(qst,JSONObject.parseObject(qsp.getFollowMatchDetail()),list1);
		/*if(QcConstants.SMART_PLAN_TYPE_MT ==qst.getTaskType()) {
			//立即执行任务
			JSONObject param = new JSONObject();
			param.put("smartTaskId", qst.getId());
			doSmartTaskDetail(param);
			param.put("id", qst.getId());
			//qstMapper.updateQcSmartTaskCount(param);
		}*/
		return null;
	}
	
	
	private int createSmartTaskDetail(QcSmartTask qst,JSONObject busJson,List<Map> list1) {
		if(busJson == null) {
			busJson = new JSONObject();
		}
		JSONObject params = new JSONObject();
		params.put("asrStatus", "done");
		params.put("uBegTime", qst.getBegTime());
		params.put("uEndTime", qst.getEndTime());
		params.put("datasetId", qst.getDatasetId());
		params.put("filterEmpty", qst.getFilterEmptySession());
		//设置公共属性
		QcSmartTaskDetail qsd = new QcSmartTaskDetail();
		//qsd.setStatus(1);
		qsd.setTemplateId(qst.getTemplateId());
		qsd.setSmartTaskId(qst.getId());
		qsd.setCreateBy(qst.getCreateBy());
		//分页处理数据
		int offset = 0;	
		int index = 0;
		int pgSize = 1000;
		PageHelper.offsetPage(offset, pgSize, true);
		List<QcBizItem> qbi = qbiMapper.queryQcDatasetItemAndBus(params);
		
		PageInfo<QcBizItem> pageInfo = new PageInfo<>(qbi);
		long total = pageInfo.getTotal();
		List<QcSmartTaskDetail> qstdList = new ArrayList<QcSmartTaskDetail>();
		String currTempStr = JSON.toJSONString(qst1Mapper.getQcScoringTemplate(qst.getTemplateId()));
		while(qbi.size() != 0) {
			for(int i=0;i<qbi.size();i++) {
				if(!rcService.doFilterBus(busJson, JSONObject.parseObject(JSONObject.toJSONString(qbi.get(i)))).getBooleanValue("result")) {
					continue;
				}
				QcSmartTaskDetail qstd = new QcSmartTaskDetail(qsd);
				qstd.setBizNo(qbi.get(i).getUniqueKey());
				qstd.setTemplateHitRule(currTempStr);
				qstd.setWorkName(qbi.get(i).getAgentName());
				qstd.setWorkNo(qbi.get(i).getAgentId());
				qstd.setSessionLength(qbi.get(i).getCallDurationS());
				qstd.setStatus(list1.size()>0?QcConstants.SMART_TASK_DETAIL_STATUS_AI_DOING:QcConstants.SMART_TASK_DETAIL_STATUS_NEW);
				qstdList.add(qstd);
			}
			if(qstdList.size() != 0) {
				qstMapper.insertQcSmartTaskDetail(qstdList);
				createSemanticsTask(qstdList,list1);
				qstdList.clear();
			}
			index++;
			offset = index * pgSize; 	
			PageHelper.offsetPage(offset, pgSize, false);
			qbi = qbiMapper.queryQcDatasetItemAndBus(params);
		}
		//查询数据集总量
		qst.setSessionCount((int)total);
		PageHelper.clearPage();
		PageHelper.offsetPage(1, 1, true);
		JSONObject queryParam = new JSONObject();
		queryParam.put("smartTaskId",qst.getId());
		PageInfo<QcSmartTaskDetail> pageInfo1 = new PageInfo<>(qstMapper.selectQcSmartTaskDetailList(queryParam));
		qst.setRelSessionCount((int)pageInfo1.getTotal());
		if(total == 0L || pageInfo1.getTotal() == 0L) {
			//会话数量为0，标记为已完成
//			qst.setStatus(QcConstants.SMART_TASK_STATUS_FINISH);
			//删除记录
			qstMapper.deleteQcSmartTaskByIds(new Long[] {qst.getId()});
		}else {
			qst.setUpdateBy("system");
			qstMapper.updateQcSmartTask(qst);
		}
		PageHelper.clearPage();
		return 0;
	}
	private void createSemanticsTask(List<QcSmartTaskDetail> list,List<Map> list2) {
		if(list2 == null || list2.size()==0) {
			return;
		}
		qstMapper.insertQcSmartSemanticsTask(list);
		List<Map> semanticsDetail = new ArrayList<Map>();
		for(int i=0;i<list.size();i++) {
			for(int j=0;j<list2.size();j++) {
				Map map = new HashMap<>();
				map.put("semanticsId", list.get(i).getQcSmartSemanticsTaskId());
				map.put("ruleCondId", list2.get(j).get("qrcIds"));
				map.put("matchRule", list2.get(j).get("semanticsIds"));
				semanticsDetail.add(map);
			}
			if(semanticsDetail.size() >= 1000) {
				qstMapper.insertQcSmartSemanticsDetail(semanticsDetail);
				semanticsDetail = new ArrayList<Map>();
			}
		}
		if(semanticsDetail.size() != 0) {
			qstMapper.insertQcSmartSemanticsDetail(semanticsDetail);
		}
	}
	
	
	@Override
	public void updateSmartDetail(JSONObject param) {
		qstMapper.updateQcSmartTaskDetail(param);
		JSONObject param1 = new JSONObject();
		param1.put("id", param.getIntValue("smartTaskId"));
		qstMapper.updateQcSmartTaskCount(param1);
	}
	@Override
	public int updateSmartPlan(JSONObject param){
		try {
			boolean isInsert = true;
			if(param.containsKey("id") && param.getInteger("id") != null) {
				isInsert = false;
			}
			qspMapper.updateQcSmartPlan(param);
			if(isInsert) {
				qspMapper.updateQcSmartPlan(param);
			}
		}catch(Exception sicvException) {
		}
		
		return 0;
	}
	
	private String getTaskLockValue() {
		return DateUtils.parseDateToStr("yyyy-MM-dd HH", new Date());
	}
	public void stopQcSmartTask(JSONObject tmp) {
		qstMapper.stopQcSmartTask(tmp);
	}
}
