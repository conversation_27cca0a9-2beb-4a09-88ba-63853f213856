package com.ideal.qc.service;

import java.util.List;
import com.ideal.qc.domain.QcDataAnnotation;

/**
 * 质检标注Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
public interface QcDataAnnotationService
{
    /**
     * 查询质检标注
     * 
     * @param id 质检标注主键
     * @return 质检标注
     */
    public QcDataAnnotation selectQcDataAnnotationById(Long id);

    /**
     * 查询质检标注列表
     * 
     * @param QcDataAnnotation 质检标注
     * @return 质检标注集合
     */
    public List<QcDataAnnotation> selectQcDataAnnotationList(QcDataAnnotation QcDataAnnotation);

    /**
     * 新增质检标注
     * 
     * @param QcDataAnnotation 质检标注
     * @return 结果
     */
    public int insertQcDataAnnotation(QcDataAnnotation QcDataAnnotation);

    /**
     * 修改质检标注
     * 
     * @param QcDataAnnotation 质检标注
     * @return 结果
     */
    public int updateQcDataAnnotation(QcDataAnnotation QcDataAnnotation);

    /**
     * 批量删除质检标注
     * 
     * @param ids 需要删除的质检标注主键集合
     * @return 结果
     */
    public int deleteQcDataAnnotationByIds(Long[] ids);

    /**
     * 删除质检标注信息
     * 
     * @param id 质检标注主键
     * @return 结果
     */
    public int deleteQcDataAnnotationById(Long id);
}
