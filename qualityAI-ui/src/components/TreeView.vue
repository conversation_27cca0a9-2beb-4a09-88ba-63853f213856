<template>
  <div className="tree-view">
    <tree-node
      v-for="node in treeData"
      :key="node.id"
      :node="node"
      :default-expand-all="defaultExpandAll"
      @node-click="$emit('node-click', $event)"
    />
  </div>
</template>

<script>
import TreeNode from './TreeNode.vue';

export default {
  name: 'TreeView',
  components: {
    TreeNode
  },
  props: {
    treeData: {
      type: Array,
      required: true
    },
    defaultExpandAll: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style scoped>
.tree-view {
  max-height: 350px;
  overflow-y: auto;
}
</style>
