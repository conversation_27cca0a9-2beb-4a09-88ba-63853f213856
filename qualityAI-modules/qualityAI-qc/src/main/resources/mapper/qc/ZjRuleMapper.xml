<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ZjRuleMapper">

    <resultMap type="ZjRule" id="ZjRuleResult">
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="ruleGroup"    column="rule_group"    />
        <result property="detectionMode"    column="detection_mode"    />
        <result property="detectionContent"    column="detection_content"    />
        <result property="detectionRole"    column="detection_role"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectZjRuleVo">
        select rule_id, rule_name, rule_type, rule_group, detection_mode, detection_content, detection_role, create_time, update_time from zj_rule
    </sql>

    <select id="selectZjRuleList" parameterType="ZjRule" resultMap="ZjRuleResult">
        <include refid="selectZjRuleVo"/>
        <where>
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="ruleType != null  and ruleType != ''"> and rule_type = #{ruleType}</if>
            <if test="ruleGroup != null  and ruleGroup != ''"> and rule_group = #{ruleGroup}</if>
            <if test="detectionMode != null  and detectionMode != ''"> and detection_mode = #{detectionMode}</if>
            <if test="detectionContent != null  and detectionContent != ''"> and detection_content = #{detectionContent}</if>
            <if test="detectionRole != null  and detectionRole != ''"> and detection_role = #{detectionRole}</if>
        </where>
    </select>

    <select id="selectZjRuleByRuleId" parameterType="Long" resultMap="ZjRuleResult">
        <include refid="selectZjRuleVo"/>
        where rule_id = #{ruleId}
    </select>

    <insert id="insertZjRule" parameterType="ZjRule" useGeneratedKeys="true" keyProperty="ruleId">
        insert into zj_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="ruleGroup != null">rule_group,</if>
            <if test="detectionMode != null">detection_mode,</if>
            <if test="detectionContent != null">detection_content,</if>
            <if test="detectionRole != null">detection_role,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="ruleGroup != null">#{ruleGroup},</if>
            <if test="detectionMode != null">#{detectionMode},</if>
            <if test="detectionContent != null">#{detectionContent},</if>
            <if test="detectionRole != null">#{detectionRole},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateZjRule" parameterType="ZjRule">
        update zj_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="ruleGroup != null">rule_group = #{ruleGroup},</if>
            <if test="detectionMode != null">detection_mode = #{detectionMode},</if>
            <if test="detectionContent != null">detection_content = #{detectionContent},</if>
            <if test="detectionRole != null">detection_role = #{detectionRole},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where rule_id = #{ruleId}
    </update>

    <delete id="deleteZjRuleByRuleId" parameterType="Long">
        delete from zj_rule where rule_id = #{ruleId}
    </delete>

    <delete id="deleteZjRuleByRuleIds" parameterType="String">
        delete from zj_rule where rule_id in
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>
</mapper>