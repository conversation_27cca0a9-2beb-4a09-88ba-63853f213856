package com.ideal.qc.service.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import com.ideal.qc.domain.QcDatasetItem;
import com.ideal.qc.domain.ZjData;
import com.ideal.qc.service.IQcDatasetItemService;
import com.ideal.qc.service.IZjDataService;
import com.ideal.qc.service.QcDataAnnotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.QcDataAnnotationMapper;
import com.ideal.qc.domain.QcDataAnnotation;
import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;

import javax.annotation.Resource;

/**
 * 质检标注Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
@Service
public class  QcDataAnnotationServiceImpl implements QcDataAnnotationService
{
    @Resource
    private QcDataAnnotationMapper QcDataAnnotationMapper;

    /*@Autowired
    private IZjDataService zjDataService;*/
    @Autowired
    private IQcDatasetItemService qcDatasetItemService;
    @Value("${file_url}")
    private String file_url;
    /**
     * 查询质检标注
     * 
     * @param id 质检标注主键
     * @return 质检标注
     */
    @Override
    public QcDataAnnotation selectQcDataAnnotationById(Long id)
    {
        return QcDataAnnotationMapper.selectQcDataAnnotationById(id);
    }

    /**
     * 查询质检标注列表
     * 
     * @param QcDataAnnotation 质检标注
     * @return 质检标注
     */
    @Override
    public List<QcDataAnnotation> selectQcDataAnnotationList(QcDataAnnotation QcDataAnnotation)
    {
        return QcDataAnnotationMapper.selectQcDataAnnotationList(QcDataAnnotation);
    }
    private static final String TEMP_DIR = "temp/";
    /**
     * 新增质检标注
     * 
     * @param QcDataAnnotation 质检标注
     * @return 结果
     */
    @Override
    public int insertQcDataAnnotation(QcDataAnnotation QcDataAnnotation)
    {
        QcDatasetItem data = qcDatasetItemService.selectQcDatasetItemById(QcDataAnnotation.getDataId());
        String inputUrl = file_url+data.getFileId();
        // 设置Content-Disposition
        String outputNamePrefix = "clip_";
        long startTime = Long.parseLong(QcDataAnnotation.getStartTime()); // 开始时间(毫秒)
        long endTime = Long.parseLong(QcDataAnnotation.getEndTime()); // 开始时间(毫秒)
        long duration = endTime-startTime;   // 持续时间(毫秒)

        try {
            File clippedAudio = clipAudio(inputUrl, outputNamePrefix, startTime, duration);
            String url = uploadToFTP(clippedAudio);
            System.out.println("音频截取并上传成功!");
            String name = clippedAudio.getName();//去掉.wav
            name = name.substring(0,name.length()-4);
            QcDataAnnotation.setNewUrlName(name);
            QcDataAnnotation.setNewUrl(url);
            QcDataAnnotation.setOldUrl(inputUrl);
        } catch (Exception e) {
            System.err.println("处理失败: " + e.getMessage());
            e.printStackTrace();
        }
        return QcDataAnnotationMapper.insertQcDataAnnotation(QcDataAnnotation);
    }

    /**
     * 修改质检标注
     * 
     * @param QcDataAnnotation 质检标注
     * @return 结果
     */
    @Override
    public int updateQcDataAnnotation(QcDataAnnotation QcDataAnnotation)
    {
        return QcDataAnnotationMapper.updateQcDataAnnotation(QcDataAnnotation);
    }

    /**
     * 批量删除质检标注
     * 
     * @param ids 需要删除的质检标注主键
     * @return 结果
     */
    @Override
    public int deleteQcDataAnnotationByIds(Long[] ids)
    {
        return QcDataAnnotationMapper.deleteQcDataAnnotationByIds(ids);
    }

    /**
     * 删除质检标注信息
     * 
     * @param id 质检标注主键
     * @return 结果
     */
    @Override
    public int deleteQcDataAnnotationById(Long id)
    {
        return QcDataAnnotationMapper.deleteQcDataAnnotationById(id);
    }

    /**
     * 截取音频片段
     * @param inputPath 输入音频文件路径
     * @param outputPrefix 输出文件前缀
     * @param startTime 开始时间(毫秒)
     * @param duration 持续时间(毫秒)
     * @return 截取后的音频文件
     */
    public static File clipAudio(String inputPath, String outputPrefix, long startTime, long duration)
            throws Exception {

        // 创建临时目录
        new File(TEMP_DIR).mkdirs();

        // 生成输出文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String oriPath = TEMP_DIR + "ori_" + timestamp + ".wav";
        String outputPath = TEMP_DIR + outputPrefix + timestamp + ".wav";

        File source = downloadFileFromUrl(inputPath,oriPath);
        File target = new File(outputPath);

        // 音频编码配置
        AudioAttributes audio = new AudioAttributes();
        audio.setCodec("pcm_s16le"); // 16-bit PCM编码
        audio.setBitRate(1411200);   // 16-bit 44.1kHz立体声的比特率
        audio.setChannels(2);        // 立体声
        audio.setSamplingRate(44100); // 44.1kHz采样率

        EncodingAttributes attrs = new EncodingAttributes();
        //attrs.setFormat("wav");     // 输出格式为WAV
        attrs.setAudioAttributes(audio);
        attrs.setOffset(startTime / 1000f); // 转换为秒
        attrs.setDuration(duration / 1000f); // 转换为秒

        // 执行截取
        Encoder encoder = new Encoder();
        encoder.encode(new MultimediaObject(source), target, attrs);

        source.delete();
        return target;
    }
    private static File downloadFileFromUrl(String fileUrl, String localFilePath) throws IOException {
        URL url = new URL(fileUrl);
        try (InputStream in = url.openStream();
             FileOutputStream fos = new FileOutputStream(localFilePath)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        }
        return new File(localFilePath);
    }

    /**
     * 上传文件到FTP服务器
     * @param file 要上传的文件
     */
    public static String uploadToFTP(File file) throws IOException {
        return file.getPath();
       /* FTPClient ftpClient = new FTPClient();

        try {
            // 连接和登录
            ftpClient.connect(FTP_SERVER);
            ftpClient.login(FTP_USER, FTP_PASS);
            ftpClient.enterLocalPassiveMode();
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);

            // 创建目录(如果不存在)
            ftpClient.makeDirectory(FTP_UPLOAD_DIR);

            // 上传文件
            try (FileInputStream inputStream = new FileInputStream(file)) {
                String remoteFile = FTP_UPLOAD_DIR + file.getName();
                boolean success = ftpClient.storeFile(remoteFile, inputStream);

                if (!success) {
                    throw new IOException("文件上传失败");
                }
            }
        } finally {
            try {
                if (ftpClient.isConnected()) {
                    ftpClient.logout();
                    ftpClient.disconnect();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }*/
    }
}
