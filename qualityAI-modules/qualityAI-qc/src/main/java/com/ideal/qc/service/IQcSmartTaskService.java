package com.ideal.qc.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcRule;
import com.ideal.qc.domain.QcSmartPlan;
import com.ideal.qc.domain.QcSmartTask;
import com.ideal.qc.domain.QcSmartTaskDetail;
import com.ideal.qc.domain.QcSmartTaskResult;

public interface IQcSmartTaskService {
	public JSONObject doSmartTaskDetail(JSONObject sData);

	JSONObject doSmartPlan(JSONObject tData);

	/**
	 * 查询质检规则列表
	 *
	 * @param QcRule 质检规则
	 * @return 质检规则
	 */
	List<QcSmartTask> selectQcSmartTask(JSONObject qst);

	List<QcSmartTaskDetail> selectQcSmartTaskDetail(JSONObject param);

	List<QcSmartTaskResult> selectQcSmartTaskResult(QcSmartTaskResult qstr);

	List<QcSmartPlan> selectQcSmartPlanList(JSONObject qsp);

	void updateSmartDetail(JSONObject param);
	int updateSmartPlan(JSONObject param);

	JSONObject createSmartTask(JSONObject qsp);

	void batchDoSmartTaskDetail(List<QcSmartTaskDetail> list, JSONObject tData);
	public void stopQcSmartTask(JSONObject tmp);

}
