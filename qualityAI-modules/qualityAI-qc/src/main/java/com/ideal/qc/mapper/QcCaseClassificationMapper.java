package com.ideal.qc.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcCaseClassification;

import java.util.List;

/**
 * 关键词库分类Mapper接口
 *
 * <AUTHOR>
 * @date 2025/5/20 17:54
 */
public interface QcCaseClassificationMapper {

    /**
     * 查询关键词库分类列表
     *
     * @param payload 需要的一些参数
     * @return 关键词库分类列表
     */
    List<QcCaseClassification> getQcCaseClassificationList(JSONObject payload);

    /**
     * 新增关键词库分类
     *
     * @param payload 需要的一些参数
     */
    void addQcCaseClassification(JSONObject payload);

    /**
     * 修改关键词库分类
     *
     * @param payload 需要的一些参数
     */
    void updateQcCaseClassification(JSONObject payload);

    /**
     * 删除关键词库分类
     *
     * @param id 关键词库分类ID
     */
    void removeQcCaseClassification(Long id);
}
