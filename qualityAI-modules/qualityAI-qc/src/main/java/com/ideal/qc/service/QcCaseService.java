package com.ideal.qc.service;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcCase;
import com.ideal.qc.dto.QcCaseDTO;

import java.util.List;

/**
 * 案例Service接口
 *
 * <AUTHOR>
 * @date 2025/5/20 17:45
 */
public interface QcCaseService {

    /**
     * 查询案例列表
     *
     * @param payload 需要的一些参数
     * @return 案例列表
     */
    List<QcCase> getQcCaseList(JSONObject payload);

    /**
     * 新增案例
     *
     * @param payload 需要的一些参数
     */
    void addQcCase(JSONObject payload);

    /**
     * 修改案例
     *
     * @param payload 需要的一些参数
     */
    void updateQcCase(JSONObject payload);

    /**
     * 查询案列的每个案列状态数量
     *
     * @param payload 需要的一些参数
     * @return 每个案列状态数量
     */
    QcCaseDTO getQcCaseEachCaseStatusQuantity(JSONObject payload);

    /**
     * 查询标记数量根据人工计划ID
     *
     * @param payload 需要的一些参数
     * @return 查询标记数量
     */
    int listCount(JSONObject payload);

    /**
     * 根据案例分类ID查询所有案例
     *
     * @param classificationId 案例分类ID
     * @return 所有案例
     */
    List<QcCase> getQcCaseByCaseClassificationId(Long classificationId);
}
