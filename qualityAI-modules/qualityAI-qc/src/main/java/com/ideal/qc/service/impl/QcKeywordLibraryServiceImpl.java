package com.ideal.qc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.exception.ServiceException;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcKeyword;
import com.ideal.qc.domain.QcKeywordLibrary;
import com.ideal.qc.mapper.QcKeywordLibraryMapper;
import com.ideal.qc.service.QcKeywordLibraryService;
import com.ideal.qc.service.QcKeywordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 关键词库Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/20 17:46
 */
@Service
public class QcKeywordLibraryServiceImpl implements QcKeywordLibraryService {

    @Resource
    private QcKeywordLibraryMapper qcKeywordLibraryMapper;
    @Resource
    private QcKeywordService qcKeywordService;

    /**
     * 查询关键词库列表
     */
    @Override
    public List<QcKeywordLibrary> getQcKeywordLibraryList(JSONObject payload) {
        return qcKeywordLibraryMapper.getQcKeywordLibraryList(payload);
    }

    /**
     * 新增关键词库
     */
    @Override
    public void addQcKeywordLibrary(JSONObject payload) {
        Long keywordLibraryClassificationId = payload.getLong("keywordLibraryClassificationId");
        if (ObjectUtil.isEmpty(keywordLibraryClassificationId)) {
            throw new ServiceException("未选中关键词库分类");
        }
        String keywordLibraryName = payload.getString("keywordLibraryName");
        if (ObjectUtil.isEmpty(keywordLibraryName)) {
            throw new ServiceException("关键词库名称不允许为空");
        }
        payload.put("createTime", DateTime.now());
        payload.put("createBy", SecurityUtils.getUsername());
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        payload.put("tenantId", SecurityUtils.getTenantId());
        qcKeywordLibraryMapper.addQcKeywordLibrary(payload);
    }

    /**
     * 修改关键词库
     */
    @Override
    public void updateQcKeywordLibrary(JSONObject payload) {
        Long keywordLibraryClassificationId = payload.getLong("keywordLibraryClassificationId");
        if (ObjectUtil.isEmpty(keywordLibraryClassificationId)) {
            throw new ServiceException("未选中关键词库分类");
        }
        String keywordLibraryName = payload.getString("keywordLibraryName");
        if (ObjectUtil.isEmpty(keywordLibraryName)) {
            throw new ServiceException("关键词库名称不允许为空");
        }
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        qcKeywordLibraryMapper.updateQcKeywordLibrary(payload);
    }

    /**
     * 删除关键词库
     */
    @Override
    public void removeQcKeywordLibrary(Long id) {
        List<QcKeyword> qcKeywordList = qcKeywordService.getQcKeywordByKeywordIds(id);
        if (CollUtil.isNotEmpty(qcKeywordList)) {
            throw new ServiceException("请先删除与其关联的关键词");
        }
        qcKeywordLibraryMapper.removeQcKeywordLibrary(id);
    }

    /**
     * 查询关键词库列表根据关键词库分类id
     */
    @Override
    public List<QcKeywordLibrary> getQcKeywordLibraryByKeywordLibraryClassificationIds(Long id) {
        return qcKeywordLibraryMapper.getQcKeywordLibraryByKeywordLibraryClassificationIds(id);
    }
}
