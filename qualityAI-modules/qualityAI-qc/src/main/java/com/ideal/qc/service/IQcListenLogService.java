package com.ideal.qc.service;

import java.util.List;
import com.ideal.qc.domain.QcListenLog;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IQcListenLogService 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public QcListenLog selectQcListenLogById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param qcListenLog 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<QcListenLog> selectQcListenLogList(QcListenLog qcListenLog);

    /**
     * 新增【请填写功能名称】
     * 
     * @param qcListenLog 【请填写功能名称】
     * @return 结果
     */
    public int insertQcListenLog(QcListenLog qcListenLog);

    /**
     * 修改【请填写功能名称】
     * 
     * @param qcListenLog 【请填写功能名称】
     * @return 结果
     */
    public int updateQcListenLog(QcListenLog qcListenLog);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteQcListenLogByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteQcListenLogById(Long id);
}
