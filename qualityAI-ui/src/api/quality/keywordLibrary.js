import request from '@/utils/request'

// 查询关键词库列表
export function getQcKeywordLibraryList(params, data) {
  return request({
    url: '/qc/keyword/library/list',
    method: 'post',
    params,
    data
  })
}

// 新增关键词库
export function addQcKeywordLibrary(data) {
  return request({
    url: '/qc/keyword/library/add',
    method: 'post',
    data: data
  })
}

// 修改关键词库
export function updateQcKeywordLibrary(data) {
  return request({
    url: '/qc/keyword/library/update',
    method: 'post',
    data: data
  })
}

// 删除关键词库
export function removeQcKeywordLibrary(id) {
  return request({
    url: '/qc/keyword/library/remove/' + id,
    method: 'post'
  })
}
