<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="问卷编码" prop="qsNo">
        <el-input
          v-model="queryParams.qsNo"
          placeholder="请输入问卷编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="问卷名称" prop="qsName">
        <el-input
          v-model="queryParams.qsName"
          placeholder="请输入问卷名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="isValid">
        <el-select v-model="queryParams.isValid" placeholder="题目状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="租户" prop="tenantCode">
        <el-input
          v-model="queryParams.tenantCode"
          placeholder="请输入租户"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['quality:questionnaire:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['quality:questionnaire:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['quality:questionnaire:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['quality:questionnaire:export']"
        >导出</el-button>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            @click="handleImport"
            v-hasPermi="['quality:questionnaire:import']"
          >导入</el-button>
        </el-col>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="questionnaireList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="ID" align="center" prop="id" />-->
      <el-table-column label="问卷编码" align="left" prop="qsNo" show-overflow-tooltip min-width="50"/>
      <el-table-column label="问卷名称" align="left" prop="qsName" show-overflow-tooltip min-width="90"/>
<!--      <el-table-column label="问卷类型" align="center" prop="qsType" />-->
      <el-table-column label="问卷描述" align="left" prop="qsDes" />
      <el-table-column label="状态" width="55" align="center" prop="isValid">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.isValid"/>
        </template>
      </el-table-column>
      <el-table-column label="租户" width="55" align="center" prop="tenantCode" />
      <el-table-column label="操作" width="155" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleQuestion(scope.row)"
            v-hasPermi="['quality:questionnaire:question']"
          >题目</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['quality:questionnaire:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['quality:questionnaire:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改问卷对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="问卷编码" prop="qsNo">
          <el-input v-model="form.qsNo" placeholder="请输入问卷编码" disabled="true"/>
        </el-form-item>
        <el-form-item label="问卷名称" prop="qsName">
          <el-input v-model="form.qsName" placeholder="请输入问卷名称" />
        </el-form-item>
        <el-form-item label="问卷描述" prop="qsDes">
          <el-input v-model="form.qsDes" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="题目状态" prop="isValid">
          <el-radio-group v-model="form.isValid">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="租户" prop="tenantCode">
          <el-input v-model="form.tenantCode" placeholder="请输入租户" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 题目弹框 -->
    <el-dialog title="题库" :visible.sync="qusOpen" width="900px">
      <el-form :model="formData" :rules="rulesQus" size="small" label-width="100px" v-show="showQusSearch">
        <el-row gutter="10">
          <el-col :span="7.5">
            <el-form-item label="题目类型" prop="qusType">
              <el-select v-model="queryQusParams.qusType" placeholder="请选择题目类型" clearable :style="{width: '100%'}">
                <el-option v-for="(item, index) in qusTypeList" :key="index" :label="item.label"
                           :value="item.value" :disabled="item.disabled"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7.5">
            <el-form-item label="题目名称" prop="qusName">
              <el-input v-model="queryQusParams.qusName" placeholder="请输入题目名称" clearable :style="{width: '100%'}">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label-width="50px" label="" prop="field106">
              <el-button type="primary" size="mini" @click="getQusList">查询</el-button>
              <el-button type="primary" size="mini" @click="getQusMultiAdd">批量添加</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="font-size: 15px; font-weight: bold;color: #2e3676;">题库列表</div>
      <el-table v-loading="loadingQus" :data="questionList" @selection-change="handleSelectionChangeQus">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="题目名称" align="left" prop="qusName" show-overflow-tooltip min-width="90"/>
        <el-table-column label="题目类型" align="center" prop="qusType" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleAddQus(scope.row)"
              v-hasPermi="['questionnaire:question:edit']"
            >添加</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="totalQus>0"
        :total="totalQus"
        :page.sync="queryQusParams.pageNum"
        :limit.sync="queryQusParams.pageSize"
        @pagination="getQusList"
      />
      <div style="font-size: 15px; font-weight: bold;color: #2e3676;">已选择题目</div>
      <el-table :data="questionQsList">
        <el-table-column label="序号" align="center" width="55">
          <template slot-scope="scope">
            {{scope.$index+1}}
          </template>
        </el-table-column>
        <el-table-column label="题目名称" align="left" prop="qusName" show-overflow-tooltip min-width="90"/>
        <el-table-column label="题目类型" align="center" prop="qusType" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleAddDel(scope.$index)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button @click="cancelQus">取消</el-button>
        <el-button type="primary" @click="submitFormQus">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listQuestionnaire, getQuestionnaire, delQuestionnaire, addQuestionnaire, updateQuestionnaire,importTemplate,updateQuestionnaireQus } from "@/api/quality/questionnaire";
import {getToken} from "@/utils/auth";
import { Utils } from '@/api/util/common';
import {listQuestion} from "@/api/quality/question";

export default {
  name: "Questionnaire",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      loadingQus: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      showQusSearch: true,
      qusOpen: false,
      // 总条数
      total: 0,
      totalQus: 0,
      // 问卷表格数据
      questionnaireList: [],
      //题目列表
      questionList: [],
      //已添加题目
      questionQsList: [],
      multipleFlag: false,
      multipleSelect: [],
      qusTypeList: [{"label":'所有',"value":''},{"label":'得分',"value":'得分'},{"label":'单选',"value":'单选'},{"label":'多选',"value":'多选'},{"label":'问答',"value":'问答'}],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        qsNo: null,
        qsName: null,
        qsType: null,
        qsDes: null,
        isValid: null,
        tenantCode: null,
      },
      queryQusParams: {
        pageNum: 1,
        pageSize: 10,
        qusNo: null,
        qusName: null,
        qusType: null,
        tenantCode: null,
      },
      // 表单参数
      form: {},
      formData: {},
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 1,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/qc/questionnaire/importData"
      },
      // 表单校验
      rules: {
      },
      rulesQus:{}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询问卷列表 */
    getList() {
      this.loading = true;
      listQuestionnaire(this.queryParams).then(response => {
        this.questionnaireList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        qsNo: null,
        qsName: null,
        qsType: null,
        qsDes: null,
        isValid: "0",
        tenantCode: '6000',
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    // 表单重置
    resetQus() {
      this.formData = {
        qsId: null,
        qsNo: null,
        qusNo: null,
        qusName: null,
        qusType: null,
        tenantCode: '6000',
      };
      this.resetForm("formData");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加问卷";
      this.form.qsNo = Utils.getUUID()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getQuestionnaire(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改问卷";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateQuestionnaire(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQuestionnaire(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除问卷编号为"' + ids + '"的数据项？').then(function() {
        return delQuestionnaire(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('qc/questionnaire/export', {
        ...this.queryParams
      }, `questionnaire_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport(){
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('qc/questionnaire/importTemplate', {
        ...this.queryParams
      }, `问卷模版.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.data || response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    handleQuestion(row){
      this.resetQus();
      const id = row.id || this.ids;
      this.formData.qsId=id;
      this.formData.qsNo=row.qsNo;
      getQuestionnaire(id).then(response => {
        this.qusOpen = true;
        this.questionQsList = response.data.questionList;
      });
      this.getQusList();
    },

    // 多选框选中数据
    handleSelectionChangeQus(selection) {
      this.ids = selection.map(item => item.id)
      this.multipleFlag = !selection.length
      this.multipleSelect = selection;
    },
    /** 查询问卷列表 */
    getQusList() {
      this.loadingQus = true;
      listQuestion(this.queryQusParams).then(response => {
        this.questionList = response.rows;
        this.totalQus = response.total;
        this.loadingQus = false;
      });
    },
    getQusMultiAdd(){
      this.multipleSelect.forEach((item,index)=>{
        let isExist = false;
        this.questionQsList.forEach((select,selectidx)=>{
          if(select['id']==item['id']){
            isExist = true;
          }
        })
        if(!isExist){
          this.questionQsList.push(item);
        }
      })
    },
    cancelQus() {
      this.qusOpen = false;
      this.resetQus();
    },
    /** 提交按钮 */
    submitFormQus() {
      let id = this.formData.qsId;
      let qsNo = this.formData.qsNo;
      let questionList = this.questionQsList;
      if(questionList.length==0){
        this.$modal.msgError("未添加题目");
        return;
      }
      questionList.forEach((item,index)=>{
        item['qsSeq']=(index+1)
      })
      updateQuestionnaireQus({id:id,qsNo:qsNo,questionList:questionList,tenantCode:this.formData.tenantCode}).then(response => {
        this.$modal.msgSuccess("维护成功");
        this.qusOpen = false;
      });
    },
    handleAddQus(row){
        let isExist = false;
        this.questionQsList.forEach((select,selectidx)=>{
          if(select['id']==row['id']){
            isExist = true;
          }
        })
        if(!isExist){
          this.questionQsList.push(row);
        }
    },
    handleAddDel(index){
      this.questionQsList.splice(index,1);
    }
  }
};
</script>
