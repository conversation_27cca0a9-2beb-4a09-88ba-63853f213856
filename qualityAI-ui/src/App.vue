<template>
  <div id="app">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";

export default {
  name: "App",
  components: { ThemePicker },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  }
};
</script>
<style>
#app .theme-picker {
  display: none;
}
.row{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.col{
  display: flex;
  flex-direction: column;
}
.spaceBet{
  justify-content: space-between;
}
.el-dialog-title.dy-title{
	font-size:large;
	font-weight:900;
	width:250px;
	float:left;
}
.el-divider--horizontal.dy-divider{
	margin-top:40px;
}
.i-or-div{
	width: 100%;
    display: flex;
    justify-content: center;
}
.i-or{
	border-radius: 50%;
    width: 30px;
    height: 30px;
    background-color: #3350eb;
    color: white;
    text-align: center;
    font-style: normal;
	margin-bottom: 10px;
}
.ruleTop.row.spaceBet div,.ruleTop.row.spaceBet i{
	font-weight:800;
	color:#3350eb;
}
.ruleTop.row.spaceBet{
	background-color:#5b94dd;
}
.el-button--text{
	text-decoration: underline;
}
</style>
