package com.ideal.qc.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.common.core.constant.HttpStatus;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.qc.service.ILLMService;



import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import org.springframework.web.client.RestClientException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;


import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.ideal.common.core.web.domain.AjaxResult.*;

@Service
public class LLMServiceImpl implements ILLMService {

    @Value("${deepseek.api.url}")
    private String url;
    @Value("${deepseek.api.model}")
    private String model;

    @Override
    public AjaxResult generateText(String prompt) {
        AjaxResult result=new AjaxResult();
        String responseBody="是";
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json");
            String jsonInputString = "{\"model\": \""+model+"\",\"prompt\":\""+prompt+"\", \"stream\": false}";
            post.setEntity(new StringEntity(jsonInputString, "UTF-8"));
            CloseableHttpResponse response = httpClient.execute(post);
            responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseBody);
            String r=jsonObject.getString("response");
            Pattern pattern = Pattern.compile("<think>[\\s\\S]*?</think>");
            Matcher matcher = pattern.matcher(r);
            r = matcher.replaceAll("");
            System.out.println("Response result2: " + r);
            result.put(CODE_TAG, HttpStatus.SUCCESS);
            result.put(MSG_TAG,"成功");
            result.put(DATA_TAG,r);
        } catch (Exception e) {
            e.printStackTrace();
            result.put(CODE_TAG, HttpStatus.ERROR);
            result.put(MSG_TAG,"失败");
            return result;
        }
        return result;
    }

    @Override
    public AjaxResult generateText(String ruleId,String callId, String ruleContent, String content) {
        if(content==null){
            return AjaxResult.error("质检内容不能为空");
        }
        if(ruleContent==null){
            return AjaxResult.error("规则内容不能为空");
        }
        String prompt="";
        StringBuffer prompt1=new StringBuffer();
        try {
            JSONObject jsonObject1 = JSON.parseObject(content);
            JSONArray sentenceArray = jsonObject1.getJSONArray("sentenceArray");
            for (int i = 0; i < sentenceArray.size(); i++) {
                JSONObject jsonObject2 = sentenceArray.getJSONObject(i);
                String sentence= jsonObject2.getString("sentence");
                String spk= jsonObject2.getString("spk");
                prompt1.append("spk").append(spk).append(":").append(sentence);
            }
            prompt="对话内容："+prompt1+"规则："+ruleContent;
        }catch (Exception e){
            return AjaxResult.error("内容解析错误");
        }

        AjaxResult result=new AjaxResult();
        String responseBody="是";
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json");
            String jsonInputString = "{\"model\": \""+model+"\",\"prompt\":\""+prompt+"\", \"stream\": false}";
            post.setEntity(new StringEntity(jsonInputString, "UTF-8"));
            CloseableHttpResponse response = httpClient.execute(post);
            responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseBody);
            String r=jsonObject.getString("response");
            Pattern pattern = Pattern.compile("<think>[\\s\\S]*?</think>");
            Matcher matcher = pattern.matcher(r);
            r = matcher.replaceAll("");
            System.out.println("Response result2: " + r);
            result.put(CODE_TAG, HttpStatus.SUCCESS);
            result.put(MSG_TAG,"成功");
            result.put(DATA_TAG,r.trim());
            result.put("ruleId",ruleId);
            result.put("callId",callId);
        } catch (Exception e) {
            e.printStackTrace();
            result.put(CODE_TAG, HttpStatus.ERROR);
            result.put(MSG_TAG,"失败");
            result.put("ruleId",ruleId);
            result.put("callId",callId);
            return result;
        }
        return result;
    }


    public static void main(String[] args){
        Long nowTime = System.currentTimeMillis();
        System.out.println("Response Body: " + nowTime);
        String responseBody ="";
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

            String urlString = "http://101.91.200.255:11434/api/generate";

            HttpPost post = new HttpPost(urlString);
            post.setHeader("Content-Type", "application/json");

            String jsonInputString = "{\"model\": \"deepseek-r1:1.5b\",\"prompt\": \"对话内容：spk1:您好，我们这边是第三方市产电源公司的访问员，现在正在进行宽带业务用户满意度调研。想问您几个问题是大约2分钟时间，请问这个移动公司的宽带是您和您的家人3。您好，请问听得到吗？" +
                    "spk0:你好。" +
                    "spk1:哦，我们这边是做调研访问的，就是嗯大约2分钟时间，您看一下有空吗？   规则：前面的对话是一份客服问卷调查。其中文本是通过语音转文字实现的，spk1是客服说的话，spk0是客户的回答，检测这段对话是否存在以下情况，返回是或者否" +
                    "1、客户在开车、开会、聊天等情况下测评；\", \"stream\": false}";
            post.setEntity(new StringEntity(jsonInputString, "UTF-8"));

            try (CloseableHttpResponse response = httpClient.execute(post)) {

                System.out.println("Response Code: " + response.getStatusLine().getStatusCode());
                responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println("Response Body: " + responseBody);
        JSONObject jsonObject = JSON.parseObject(responseBody);
        String result=jsonObject.getString("response");
        System.out.println("Response result: " + result);
        Pattern pattern = Pattern.compile("<think>[\\s\\S]*?</think>");
        Matcher matcher = pattern.matcher(result);
        result = matcher.replaceAll("");
        System.out.println("Response result2: " + result);


    }



}
