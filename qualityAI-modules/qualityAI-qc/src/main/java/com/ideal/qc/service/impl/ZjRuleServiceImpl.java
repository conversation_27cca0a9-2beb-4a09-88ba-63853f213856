package com.ideal.qc.service.impl;

import java.util.List;
import com.ideal.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.ZjRuleMapper;
import com.ideal.qc.domain.ZjRule;
import com.ideal.qc.service.IZjRuleService;

/**
 * 质检规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Service
public class ZjRuleServiceImpl implements IZjRuleService
{
    @Autowired
    private ZjRuleMapper zjRuleMapper;

    /**
     * 查询质检规则
     *
     * @param ruleId 质检规则主键
     * @return 质检规则
     */
    @Override
    public ZjRule selectZjRuleByRuleId(Long ruleId)
    {
        return zjRuleMapper.selectZjRuleByRuleId(ruleId);
    }

    /**
     * 查询质检规则列表
     *
     * @param zjRule 质检规则
     * @return 质检规则
     */
    @Override
    public List<ZjRule> selectZjRuleList(ZjRule zjRule)
    {
        return zjRuleMapper.selectZjRuleList(zjRule);
    }

    /**
     * 新增质检规则
     *
     * @param zjRule 质检规则
     * @return 结果
     */
    @Override
    public int insertZjRule(ZjRule zjRule)
    {
        zjRule.setCreateTime(DateUtils.getNowDate());
        return zjRuleMapper.insertZjRule(zjRule);
    }

    /**
     * 修改质检规则
     *
     * @param zjRule 质检规则
     * @return 结果
     */
    @Override
    public int updateZjRule(ZjRule zjRule)
    {
        zjRule.setUpdateTime(DateUtils.getNowDate());
        return zjRuleMapper.updateZjRule(zjRule);
    }

    /**
     * 批量删除质检规则
     *
     * @param ruleIds 需要删除的质检规则主键
     * @return 结果
     */
    @Override
    public int deleteZjRuleByRuleIds(Long[] ruleIds)
    {
        return zjRuleMapper.deleteZjRuleByRuleIds(ruleIds);
    }

    /**
     * 删除质检规则信息
     *
     * @param ruleId 质检规则主键
     * @return 结果
     */
    @Override
    public int deleteZjRuleByRuleId(Long ruleId)
    {
        return zjRuleMapper.deleteZjRuleByRuleId(ruleId);
    }
}
