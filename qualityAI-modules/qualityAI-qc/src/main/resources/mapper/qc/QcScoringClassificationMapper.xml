<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcScoringClassificationMapper">

    <resultMap id="qcScoringClassificationResultMap" type="QcScoringClassification">
        <result property="id"    column="id"    />
        <result property="scoringTemplateId"    column="scoring_template_id"    />
        <result property="scoringClassificationName"    column="scoring_classification_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <select id="getQcScoringClassificationList" resultMap="qcScoringClassificationResultMap">
        SELECT
            qsc.id,
            qsc.scoring_template_id,
            qsc.scoring_classification_name,
            qsc.create_by,
            qsc.create_time,
            qsc.update_by,
            qsc.update_time,
            qsc.remark,
            qsc.status,
            qsc.is_deleted
        FROM
            qc_scoring_classification qsc
        WHERE
            qsc.scoring_template_id = #{scoringTemplateId}
            AND qsc.is_deleted = '0'
        ORDER BY
            qsc.id ASC
    </select>

    <insert id="addQcScoringClassification">
        INSERT INTO
            qc_scoring_classification
        ( tenant_id, scoring_template_id, scoring_classification_name, create_time, create_by, update_time, update_by, remark )
        VALUES
        ( #{tenantId}, #{scoringTemplateId}, #{scoringClassificationName}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy}, #{remark})
    </insert>

    <update id="updateQcScoringClassification">
        UPDATE qc_scoring_classification
        SET scoring_classification_name = #{scoringClassificationName},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark},
            status = #{status}
        WHERE
            id = #{id}
    </update>

    <update id="removeQcScoringClassification">
        UPDATE qc_scoring_classification
        SET is_deleted = '1'
        WHERE
            id = #{id}
    </update>

    <select id="getQcScoringClassificationListByScoringTemplateIds" resultMap="qcScoringClassificationResultMap">
        SELECT
            qsc.id,
            qsc.scoring_template_id,
            qsc.scoring_classification_name,
            qsc.create_by,
            qsc.create_time,
            qsc.update_by,
            qsc.update_time,
            qsc.remark,
            qsc.status,
            qsc.is_deleted
        FROM
            qc_scoring_classification qsc
        WHERE
            qsc.scoring_template_id IN
        <foreach collection="array" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
            AND qsc.is_deleted = '0'
        ORDER BY
            qsc.id ASC
    </select>
</mapper>