package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcBusinessLabel;
import com.ideal.qc.service.QcBusinessLabelService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 业务标签Controller接口
 *
 * <AUTHOR>
 * @date 2025/5/23 9:23
 */
@RestController
@RequestMapping("/business/label")
public class QcBusinessLabelController extends BaseController {

    @Resource
    private QcBusinessLabelService qcBusinessLabelService;

    /**
     * 查询业务标签列表
     */
    @RequiresPermissions("quality:businessLabel:list")
    @PostMapping("/list")
    public TableDataInfo getQcBusinessLabelList(@RequestBody JSONObject payload) {
        startPage();
        List<QcBusinessLabel> list = qcBusinessLabelService.getQcBusinessLabelList(payload);
        return getDataTable(list);
    }

    /**
     * 新增业务标签
     */
    @RequiresPermissions("quality:businessLabel:add")
    @PostMapping("/add")
    public AjaxResult addQcBusinessLabel(@RequestBody JSONObject payload) {
        qcBusinessLabelService.addQcBusinessLabel(payload);
        return AjaxResult.success();
    }

    /**
     * 修改业务标签
     */
    @RequiresPermissions("quality:businessLabel:edit")
    @PostMapping("/update")
    public AjaxResult updateQcBusinessLabel(@RequestBody JSONObject payload) {
        qcBusinessLabelService.updateQcBusinessLabel(payload);
        return AjaxResult.success();
    }

    /**
     * 删除业务标签
     */
    @RequiresPermissions("quality:businessLabel:remove")
    @PostMapping("/remove/{id}")
    public AjaxResult removeQcBusinessLabel(@PathVariable Long id) {
        qcBusinessLabelService.removeQcBusinessLabel(id);
        return AjaxResult.success();
    }
}
