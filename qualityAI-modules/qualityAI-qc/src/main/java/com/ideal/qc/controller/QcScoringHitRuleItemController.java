package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.qc.domain.QcScoringItem;
import com.ideal.qc.service.QcScoringHitRuleItemService;
import com.ideal.qc.service.QcScoringItemService;
import com.ideal.qc.vo.QcScoringHitRuleItemVo;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 评分规则项Controller接口
 *
 * <AUTHOR>
 * @date 2025/6/5 17:20
 */
@RestController
@RequestMapping("/scoring/hit/rule/item")
public class QcScoringHitRuleItemController extends BaseController {

    @Resource
    private QcScoringHitRuleItemService qcScoringHitRuleItemService;
    @Resource
    private QcScoringItemService qcScoringItemService;

    /**
     * 查询规则列表根据模板ID
     */
    @PostMapping("/by/{scoringTemplateId}")
    public AjaxResult getQcScoringHitRuleItemListByTemplateId(@PathVariable Long scoringTemplateId) {
        List<QcScoringHitRuleItemVo> qcScoringHitRuleItemVoList = qcScoringHitRuleItemService.getQcScoringHitRuleItemListByTemplateId(scoringTemplateId);
        List<QcScoringItem> qcScoringItemList = qcScoringItemService.getQcScoringItemListByTemplateId(scoringTemplateId);
        JSONObject result = new JSONObject();
        result.put("rules", qcScoringHitRuleItemVoList);
        result.put("items", qcScoringItemList);
        return AjaxResult.success(result);
    }

}
