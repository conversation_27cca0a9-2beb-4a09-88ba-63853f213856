<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcDatasetMapper">
    
    <resultMap type="QcDataset" id="QcDatasetResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="dataType"    column="data_type"    />
        <result property="planId"    column="plan_id"    />
        <result property="totalCount"    column="total_count"    />
        <result property="asrProgress"    column="asr_progress"    />
        <result property="bizSampleCols"    column="biz_sample_cols"    />
        <result property="remark"    column="remark"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="processStatus"    column="process_status"    />
        <result property="progress"    column="progress"    />
    </resultMap>

    <sql id="selectQcDatasetVo">
        select id, name, data_type, plan_id, total_count, asr_progress, biz_sample_cols, remark, tenant_id, create_time, update_time, status, process_status, progress,CONCAT(progress,'%') progressStr from qc_dataset
    </sql>

    <select id="selectQcDatasetList" parameterType="QcDataset" resultMap="QcDatasetResult">
        <include refid="selectQcDatasetVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="dataType != null  and dataType != ''"> and data_type = #{dataType}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="totalCount != null "> and total_count = #{totalCount}</if>
            <if test="asrProgress != null "> and asr_progress = #{asrProgress}</if>
            <if test="bizSampleCols != null "> and biz_sample_cols = #{bizSampleCols}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="processStatus != null  and processStatus != ''"> and process_status = #{processStatus}</if>
            <if test="progress != null "> and progress = #{progress}</if>
        </where>
    </select>
    <select id="selectQcDatasetListByMap" parameterType="map" resultMap="QcDatasetResult">
        <include refid="selectQcDatasetVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="dataType != null  and dataType != ''"> and data_type = #{dataType}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="totalCount != null "> and total_count = #{totalCount}</if>
            <if test="asrProgress != null "> and asr_progress = #{asrProgress}</if>
            <if test="bizSampleCols != null "> and biz_sample_cols = #{bizSampleCols}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="processStatus != null  and processStatus != ''"> and process_status = #{processStatus}</if>
            <if test="progress != null "> and progress = #{progress}</if>
            <if test="updateTimeStrs != null and updateTimeStrs.size()>0">and update_time between #{updateTimeStrs[0]} and #{updateTimeStrs[1]}</if>
        </where>
    </select>
    
    <select id="selectQcDatasetById" parameterType="Long" resultMap="QcDatasetResult">
        <include refid="selectQcDatasetVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcDataset" parameterType="QcDataset" useGeneratedKeys="true" keyProperty="id">
        insert into qc_dataset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="dataType != null and dataType != ''">data_type,</if>
            <if test="planId != null">plan_id,</if>
            <if test="totalCount != null">total_count,</if>
            <if test="asrProgress != null">asr_progress,</if>
            <if test="bizSampleCols != null">biz_sample_cols,</if>
            <if test="remark != null">remark,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="progress != null">progress,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="dataType != null and dataType != ''">#{dataType},</if>
            <if test="planId != null">#{planId},</if>
            <if test="totalCount != null">#{totalCount},</if>
            <if test="asrProgress != null">#{asrProgress},</if>
            <if test="bizSampleCols != null">#{bizSampleCols},</if>
            <if test="remark != null">#{remark},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="progress != null">#{progress},</if>
         </trim>
    </insert>

    <update id="updateQcDataset" parameterType="QcDataset">
        update qc_dataset
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="dataType != null and dataType != ''">data_type = #{dataType},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="totalCount != null">total_count = #{totalCount},</if>
            <if test="asrProgress != null">asr_progress = #{asrProgress},</if>
            <if test="bizSampleCols != null">biz_sample_cols = #{bizSampleCols},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="progress != null">progress = #{progress},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcDatasetById" parameterType="Long">
        delete from qc_dataset where id = #{id}
    </delete>

    <delete id="deleteQcDatasetByIds" parameterType="String">
        delete from qc_dataset where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>