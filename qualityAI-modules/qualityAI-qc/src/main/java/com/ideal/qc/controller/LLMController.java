package com.ideal.qc.controller;


import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.qc.service.ILLMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/deepseek")
public class LLMController {

    @Autowired
    private ILLMService iLLMService;

    @GetMapping("/generate")
    public AjaxResult generateText(@RequestParam String prompt) {
        return iLLMService.generateText(prompt);
    }

    @GetMapping("/generate1")
    public AjaxResult generateText1(@RequestParam String prompt) {
        return iLLMService.generateText("1","2","前面的对话是一份客服问卷调查。其中文本是通过语音转文字实现的，spk0是客服说的话，spk1是客户的回答，检测这段对话是否存在以下情况，返回是或者否 1、客户在开车、开会、聊天等情况下测评；","{\"code\":\"0\",\"text\":\" 1:你好\",\"sentenceArray\":[{\"sentence\":\"你好，我们是第3方市场调研公司的务员，现在进行宽带业务，用户满意度调研。呃，想问您几个问题只占您大约二分钟时间，希望得到您的支持。请问这个移动公司宽带是您您家人在使用吗？\",\"bg\":0,\"dB\":\"62.69\",\"db\":\"62.69\",\"speed\":\"5.28\",\"ed\":14389,\"spk\":1},{\"sentence\":\"谢谢大家。\",\"bg\":14920,\"dB\":\"59.29\",\"db\":\"59.29\",\"speed\":\"4.4\",\"ed\":15629,\"spk\":0},{\"sentence\":\"呃，不好意思，很小声，没听清。\",\"bg\":16590,\"dB\":\"62.43\",\"db\":\"62.43\",\"speed\":\"4.74\",\"ed\":18709,\"spk\":1},{\"sentence\":\"是的，好。\",\"bg\":19550,\"dB\":\"66.03\",\"db\":\"66.03\",\"speed\":\"2.07\",\"ed\":20800,\"spk\":0}]}");
    }
}
