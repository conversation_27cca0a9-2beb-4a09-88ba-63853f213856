<template>
  <div class="app-container" style="max-height:1600px;">
    <div :content="title">
		<div class="el-dialog-title dy-title">{{title}}</div>
		<el-button v-if="op=='edit'" class="btn" type="primary" @click="submitForm">保 存</el-button>
		<el-button class="btn" @click="cancel">取 消</el-button>
    </div>
    <el-divider class="dy-divider"></el-divider>
    <el-form ref="form" size="small" :model="form" :rules="rules" label-width="120px" style="max-height:750px;overflow:auto;">
<!--		<el-form-item class="no-label_el-form-item" style="width: 100%">-->
			<el-row :gutter="10" style="width: 100%">
		    	<el-col :span="18">
			      <el-form-item label="规则名称" prop="ruleName">
			        <el-input v-model="form.ruleName" placeholder="请输入规则名称" maxlength="30" :disabled="op == 'view'" show-word-limit/>
			      </el-form-item>
			  	</el-col>
			  	<el-col :span="6">
			      <el-form-item label="规则类型" prop="type">
			        <el-select  v-model="form.ruleClassificate" placeholder="请选择" :disabled="op == 'view'">
			          <el-option v-for="rt in rtList" :label="rt.dictLabel" :value="rt.dictValue" />
			        </el-select>
			      </el-form-item>
			  	</el-col>
			  </el-row>
<!--		  </el-form-item>-->
		<el-form-item label="规则描述" prop="remark">
        	<el-input v-model="form.remark" type="textarea" placeholder="请输入内容" class="" :disabled="op == 'view'" show-word-limit/>
      	</el-form-item>
	  	<filterModal :filterList="form.filterList" :op="op" ref="filterModal"></filterModal>
	  	<hitModal :hitList="form.hitList" :op="op" ref="hitModal"></hitModal>
     </el-form>
  </div>
</template>

<script>
import { queryListByType } from "@/api/system/dict/data";
import {checkFilterForm} from "@/api/quality/smart";
import {getRule,updateRule} from "@/api/quality/rule";
import hitModal from "@/views/quality/ruleConfig/common/hitList.vue";
import filterModal from "@/views/quality/ruleConfig/common/busFilter.vue";
export default {
	components: {
			hitModal,filterModal
		  },
  name: "smartEdit",
  data() {
    return {
      info:{id:""},
	  op:'edit',
	  title:"",
      form: {
        ruleType:'1',
        filterList:[],
        hitList:[],
		deleteList:[]
      },
	  linepage:{
		
	  },
      rules: {
        ruleName: [{ required: true, message: "分类名称不能为空", trigger: "11" }],
		type: [{ required: true, message: "分类名称不能为空", trigger: "11" }],
		hitList: [{ required: true, message: "分类名称不能为空", trigger: "11" }]
      },
      ruleList:[
        {id:1,name:'员工组别'},
        {id:2,name:'坐席工号'}
      ],
	  rtList:[],
      sceneList:[
        {id:1,name:'全会话中'},
        {id:2,name:'会话开始后'},
        {id:3,name:'会话结束前'},
        {id:4,name:'上一个条件之后'},
        {id:5,name:'上一个条件之前'},
        {id:6,name:'上一个条件命中语句'}
      ],
      numList:[
        {id:1,name:'1'},
        {id:2,name:'2'},
        {id:3,name:'3'},
        {id:4,name:'4'},
        {id:5,name:'5'}
      ],
      hitRuleList:[
        {id:1,name:'语义标签'},
        {id:2,name:'关键词'},
        {id:3,name:'关键词重复'},
        {id:4,name:'关键词库'},
        {id:5,name:'正则表达式'},
        {id:6,name:'语速识别'}
      ],		
	  kwList:{
		"1":[
	        {id:1,name:'语义标签'},
	        {id:2,name:'关键词'}
	      ],
		"2":[
          {id:3,name:'关键词重复'},
          {id:4,name:'关键词库'}
		],
  		"3":[
			{id:5,name:'正则表达式'},
			{id:6,name:'语速识别'}
          ]
	  }
    };
  },
  created() {
	this.queryListByType();
  },
  methods: {
	queryListByType(){
		queryListByType	({dictTypeList:["quality_rule_classificate"],status:"0"}).then(response => {
			if(response.data && response.data["quality_rule_classificate"] != null){
				this.rtList = response.data["quality_rule_classificate"];
			}
	      });
	},
	reload(query,op){
		if(op){this.op = op}
		if(query){
			this.info = query;
		}
		if(this.op == 'view'){
			this.title = "查看智能规则";
		}else{
			this.title = this.info.ruleId?'编辑智能规则':'新增智能规则';
		}
		if(!this.info.ruleId || this.info.ruleId ==''){
			this.form={ruleType:'1',filterList:[],hitList:[],deleteList:[]}
			this.form["ruleGroup"] = this.info.ruleGroup;
			return;
		}
		getRule({ruleId:this.info.ruleId,isDetail:true,parentId:"0"}).then(response => {
			for(var i=0;i< response.data.filterList.length;i++){
				response.data.filterList[i].ruleCondDetail = JSON.parse(response.data.filterList[i].ruleCondDetail);
			}
			for(var i=0;i< response.data.hitList.length;i++){
				response.data.hitList[i].ruleCondDetail = JSON.parse(response.data.hitList[i].ruleCondDetail);
			}
			this.form = response.data;
	  	});
		
	},
    // 筛选条件
    addRule(){
      this.form.filterList.push({ruleCondType:1,ruleId:this.info.ruleId,ruleCondDetail:{isAll:'1',isOr:1,options:[{isContain:'1',rule:''}]}})
    },
    cutRule(key,index){
	  if(this.form[key][index].id && this.form[key][index].id != ''){
		if(this.form["deleteList"] || this.form["deleteList"] == null ){
			this.form["deleteList"] = [];
		}
		this.form["deleteList"].push(this.form[key][index]);
	  }
	  this.form[key].splice(index,1);
    },
    addFilOpt(index){
      this.form.filterList[index].ruleCondDetail.options.push({isContain:'1',rule:''})
    },
    cutFilOpt(index,index2){
      this.form.filterList[index].ruleCondDetail.options.splice(index2,1);
    },
    chgHitRule(e,index,index2){
      const obj = this.form.hitList[index].ruleCondDetail.options[index2]
      if(e ==2){
        this.$set(obj,'dynamicTags',[])
      }else{
        if(obj.dynamicTags){
          delete obj.dynamicTags
        }
      }

    },
    cutHitOpt(index,index2){
      this.form.hitList[index].ruleCondDetail.options.splice(index2,1);
    },
    goBack() {
     this.$emit("goList");
    },

    handleInputConfirm(index,index2,inputValue) {
      const obj = this.form.hitList[index].ruleCondDetail.options[index2]
      if (inputValue) {
        obj.dynamicTags.push(inputValue);
        this.$set(obj,'dynamicTags',obj.dynamicTags)
      }
      this.$set(obj,'inputKey','')
    },
    handleClose(index,index2,tag) {
      const dynamicTags = this.form.hitList[index].ruleCondDetail.options[index2].dynamicTags;
      dynamicTags.splice(dynamicTags.indexOf(tag), 1);

    },

    submitForm: function() {
		var tmp = this.form;
		var tip = "";
		tmp.hitList = this.$refs.hitModal.$props.hitList;
		tmp.filterList = this.$refs.filterModal.$props.filterList;
		if(!tmp.ruleName ||tmp.ruleName == ''){
			this.$message({message: '规则名称不能为空',type: 'error'});
			return 
		}
		if(!tmp.ruleClassificate || tmp.ruleClassificate == ''){
			this.$message({message: '规则类型',type: 'error'});
			return 
		}
		tip = checkFilterForm(tmp.filterList,1);
		if(tip){
			this.$message({message: tip,type: 'error'});
			return 
		}
		tip = checkFilterForm(tmp.hitList,2);
		if(tip){
			this.$message({message: tip,type: 'error'});
			return 
		}
		for(var i=0;i< tmp.filterList.length;i++){
			tmp.filterList[i]['ruleOrder'] = i+1;
		}
		for(var i=0;i< tmp.hitList.length;i++){
			tmp.hitList[i]['ruleOrder'] = i+1;
		}
		tmp = JSON.stringify(this.form,	(key, value) => {
		    return value === null ? undefined : value;
		});
		updateRule(tmp).then(response => {
			if(!this.info.ruleId || this.info.ruleId == ''){
				this.info.ruleId = response.data;
			}
			this.$message({message: (!this.form.ruleId || this.form.ruleId == '')?"新增成功":"修改成功",type: 'success'});
			this.$emit("getList");
			this.op = "edit";
		});
    },
    cancel(){
		this.goBack();
		this.op = "edit";
    },
	handleSizeChange(){
		
	}
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.inputWidth{
  width: 450px!important;
}
.selectS{
  width: 80px!important;
  margin-right: 10px;
}
.selectM{
  width: 90px!important;
  margin-right: 10px;
}
.selectL{
  width: 180px!important;
  margin-right: 10px;
}
.ruleBox{
  margin-bottom: 20px;
  .ruleTop{
    background: #ebebeb;
    padding: 2px 15px;
    cursor: pointer;
  }
  .ruleCon{
     padding: 18px 15px;
     font-size: 12px;
     background-color: #fafafa;
  }
  button{
        padding: 9px;
  }
}
.dialog-footer{
  width: 100%;
  margin-top: 40px;
  justify-content: center;

}
.orBt{
  margin-bottom: 15px;
}
.hitBox{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top:20px;
  row-gap: 10px; 
}
.keyInput{
  width: 150px;
  margin-right: 10px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 125px;
  margin-right: 10px;
  vertical-align: bottom;
}
.btn{
	float:right;
	margin-right:5px;
}
.no-label_el-form-item .el-form-item__content{
	margin-left: 0px!important;
}
.no-header-dialog .el-dialog__header {
  display: none;
}
.no-header-dialog .el-dialog__body {
  padding:10px 10px;
}
</style>
