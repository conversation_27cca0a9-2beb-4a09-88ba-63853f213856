package com.ideal.qc.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.common.core.utils.DateUtils;
import com.ideal.common.core.utils.StringUtils;
import com.ideal.qc.domain.QcBizItem;

import java.text.ParseException;
import java.util.*;

/**
 * EasyExcel 4.x 监听器：一行 ⇒ 一个 QcBizItem
 * 表头到“小结备注”列为固定字段；其后的所有列作为动态问卷答案写入 answers_json
 */
public class BizDynamicListener extends AnalysisEventListener<Map<Integer, String>> {

    private Map<Integer, String> headMap;     // 列序号 → 表头
    private int summaryIdx = -1;              // “小结备注”列序号
    private final List<QcBizItem> items = new ArrayList<>();
    private final ObjectMapper mapper = new ObjectMapper();

    /* ---------------- 1. 读取表头，定位“小结备注”列 ---------------- */
    @Override
    public void invokeHeadMap(Map<Integer, String> head, AnalysisContext ctx) {
        this.headMap = head;
        head.forEach((i, t) -> {
            if ("小结备注".equals(t.trim())) summaryIdx = i;
        });
        if (summaryIdx < 0) {
            throw new RuntimeException("未找到『小结备注』列，无法分割固定 / 动态字段！");
        }
    }

    /* ---------------- 2. 处理每一行数据 ---------------- */
    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext ctx) {
        QcBizItem item = new QcBizItem();
        List<Map<String, String>> answerArr = new ArrayList<>();

        /* 固定列映射 */
        headMap.forEach((idx, title) -> {
            String val = data.get(idx);
            if (StringUtils.isBlank(val)) return;

            if (idx < summaryIdx) {                        // --- 固定区 ---
                switch (title.trim()) {
                    case "所属平台":        item.setPlatform(val); break;
                    case "所属项目":        item.setProjectName(val); break;
                    case "指标名称":        item.setMetricName(val); break;
                    case "子指标名称":      item.setSubMetricName(val); break;
                    case "服务中心":        item.setServiceCenter(val); break;
                    case "坐席工号":        item.setAgentId(val); break;
                    case "坐席姓名":        item.setAgentName(val); break;
                    case "答卷开始时间":    item.setAnswerStartAt(parseDateSafe(val, "yyyy/MM/dd HH:mm:ss")); break;
                    case "答卷结束时间":    item.setAnswerEndAt(parseDateSafe(val, "yyyy/MM/dd HH:mm:ss")); break;
                    case "通话时长(s)":    item.setCallDurationS(Long.parseLong(val)); break;
                    case "日期":            item.setRecordDate(parseDateSafe(val, "yyyy/MM/dd")); break;
                    case "答卷编号":        item.setSurveyCode(val); break;
                    case "拨打号码":        item.setCallerPhone(val); break;
                    case "业务类型":        item.setBusinessType(val); break;
                    case "运营商":          item.setCarrier(val); break;
                    case "唯一主键号":      item.setUniqueKey(val); break;
                    case "省份":            item.setProvince(val); break;
                    case "地市":            item.setCity(val); break;
                    case "联系人号码":      item.setContactPhone(val); break;
                    case "区号":            item.setAreaCode(val); break;
                    case "产品类型":        item.setProductType(val); break;
                    case "服务订单编码":    item.setServiceOrderId(val); break;
                    case "服务类型":        item.setServiceType(val); break;
                    case "竣工时间":        item.setFinishTime(parseDateSafe(val, "yyyy/MM/dd HH:mm:ss")); break;
                    case "产品名称":        item.setProductName(val); break;
                    case "销售点名称":      item.setSalesPoint(val); break;
                    case "任务结果":        item.setTaskResult(val); break;
                    case "录音文件":        item.setRecordingFile(val); break;
                    case "来电时间":        item.setCallTime(parseDateSafe(val, "yyyy/MM/dd HH:mm:ss")); break;
                    case "一级质检结果":    item.setQcLvl1Result(val); break;
                    case "一级质检评语":    item.setQcLvl1Comment(val); break;
                    case "一级质检员工号":  item.setQcLvl1EmpId(val); break;
                    case "二级质检结果":    item.setQcLvl2Result(val); break;
                    case "二级质检评语":    item.setQcLvl2Comment(val); break;
                    case "二级质检员工号":  item.setQcLvl2EmpId(val); break;
                    case "小结备注":        item.setSummaryRemark(val); break;
                    // …… 其他固定列如有需要再补
                }
            } else {                                       // --- 动态区 ---
                Map<String, String> obj = new HashMap<>(2);
                obj.put("quesCode", title.trim());
                obj.put("ansValue", val);
                answerArr.add(obj);
            }
        });

        /* 封装 answers_json（JSON array 字符串） */
        try {
            item.setAnswersJson(mapper.writeValueAsString(answerArr));
        } catch (Exception e) {
            throw new RuntimeException("answers_json 序列化失败", e);
        }

        items.add(item);
    }
    /** 空串 / 格式错误 → 返回 null，不抛受检异常 */
    private Date parseDateSafe(String val, String... patterns) {
        if (StringUtils.isBlank(val)) return null;
        try {
            return DateUtils.parseDate(val.trim(), patterns);
        } catch (ParseException e) {
            // 记录日志或收集错误行号；这里简单忽略
            System.err.println("日期解析失败: " + val + ", 错误: " + e.getMessage());
            return null;
        }
    }

    /* ---------------- 3. 解析结束 ---------------- */
    @Override public void doAfterAllAnalysed(AnalysisContext ctx) { }

    public List<QcBizItem> getItems() {
        return items;
    }
}
