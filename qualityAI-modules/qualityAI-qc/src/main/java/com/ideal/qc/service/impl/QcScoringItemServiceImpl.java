package com.ideal.qc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.ideal.common.core.exception.ServiceException;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcScoringHitRuleGroup;
import com.ideal.qc.domain.QcScoringHitRuleItem;
import com.ideal.qc.domain.QcScoringItem;
import com.ideal.qc.dto.QcScoringHitRuleGroupDTO;
import com.ideal.qc.dto.QcScoringItemDTO;
import com.ideal.qc.mapper.QcScoringItemMapper;
import com.ideal.qc.service.QcScoringHitRuleGroupService;
import com.ideal.qc.service.QcScoringHitRuleItemService;
import com.ideal.qc.service.QcScoringItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 评分细则表Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/15 17:38
 */
@Service
public class QcScoringItemServiceImpl implements QcScoringItemService {

    @Resource
    private QcScoringItemMapper qcScoringItemMapper;
    @Resource
    private QcScoringHitRuleGroupService qcScoringHitRuleGroupService;
    @Resource
    private QcScoringHitRuleItemService qcScoringHitRuleItemService;


    /**
     * 查询评分细则列表
     */
    @Override
    public List<QcScoringItem> getQcScoringItemList(JSONObject payload) {
        Long scoringTemplateId = payload.getLong("scoringTemplateId");
        if (ObjectUtil.isEmpty(scoringTemplateId)) {
            throw new ServiceException("未关联任一评分模板");
        }
        return qcScoringItemMapper.getQcScoringItemList(payload);
    }

    /**
     * 查询评分细则详情
     */
    @Override
    public QcScoringItemDTO getQcScoringItem(Long scoringItemId) {
        return qcScoringItemMapper.getQcScoringItem(scoringItemId);
    }

    /**
     * 新增评分细则
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addQcScoringItem(JSONObject payload) {
        Long scoringClassificationId = payload.getLong("scoringClassificationId");
        if (ObjectUtil.isEmpty(scoringClassificationId)) {
            throw new ServiceException("评分分类不允许为空");
        }
        String scoringItemName = payload.getString("scoringItemName");
        if (ObjectUtil.isEmpty(scoringItemName)) {
            throw new ServiceException("评分名称不允许为空");
        }
        Integer hitRuleScore = payload.getInteger("hitRuleScore");
        if (ObjectUtil.isEmpty(hitRuleScore)) {
            throw new ServiceException("命中分数不允许为空");
        }
        // 新增评分细则
        QcScoringItem qcScoringItem = new QcScoringItem();
        qcScoringItem.setScoringClassificationId(scoringClassificationId);
        qcScoringItem.setScoringItemName(scoringItemName);
        qcScoringItem.setScoringItemDescription(payload.getString("scoringItemDescription"));
        qcScoringItem.setHitRuleScore(hitRuleScore);
        qcScoringItem.setCreateBy(SecurityUtils.getUsername());
        qcScoringItem.setCreateTime(new Date());
        qcScoringItem.setUpdateBy(SecurityUtils.getUsername());
        qcScoringItem.setUpdateTime(new Date());
        qcScoringItem.setRemark(payload.getString("remark"));
        qcScoringItem.setTenantId(SecurityUtils.getTenantId());
        qcScoringItem.setTemplateId(payload.getLong("templateId"));
        qcScoringItemMapper.addQcScoringItem(qcScoringItem);
        // 根据评分细则ID新增命中规则组
        Long scoringItemId = qcScoringItem.getId();
        List<QcScoringHitRuleGroupDTO> qcScoringHitRuleGroupDTOList = payload.getList("qcScoringHitRuleGroupDTOList", QcScoringHitRuleGroupDTO.class, JSONReader.Feature.IgnoreCheckClose);
        if (CollUtil.isEmpty(qcScoringHitRuleGroupDTOList)) {
            throw new ServiceException("未设置规则组");
        }
        for (int i = 0; i < qcScoringHitRuleGroupDTOList.size(); i ++) {
            String ruleGroupMode = qcScoringHitRuleGroupDTOList.get(i).getRuleGroupMode();
            if (ObjectUtil.isEmpty(ruleGroupMode)) {
                throw new ServiceException("条件组合" + (i + 1) + "未选择组合模式");
            }
            List<QcScoringHitRuleItem> qcScoringHitRuleItemList = qcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList();
            if (CollUtil.isEmpty(qcScoringHitRuleItemList)) {
                throw new ServiceException("条件组合" + (i + 1) + "未设置规则项");
            }
            for (QcScoringHitRuleItem qcScoringHitRuleItem : qcScoringHitRuleItemList) {
                if (ObjectUtil.isEmpty(qcScoringHitRuleItem.getRuleClassification())) {
                    int ruleClassificationIndex = qcScoringHitRuleItemList.indexOf(qcScoringHitRuleItem);
                    throw new ServiceException("条件组合" + (i + 1) + "第" + (ruleClassificationIndex + 1) + "行规则项未选择规则分类");

                }
                if (ObjectUtil.isEmpty(qcScoringHitRuleItem.getRuleId())) {
                    int ruleIdIndex = qcScoringHitRuleItemList.indexOf(qcScoringHitRuleItem);
                    throw new ServiceException("条件组合" + (i + 1) + "第" + (ruleIdIndex + 1) + "行规则项未选择规则");
                }
            }
            QcScoringHitRuleGroup qcScoringHitRuleGroup = new QcScoringHitRuleGroup();
            qcScoringHitRuleGroup.setScoringItemId(scoringItemId);
            qcScoringHitRuleGroup.setRuleGroupMode(ruleGroupMode);
            qcScoringHitRuleGroup.setCreateBy(SecurityUtils.getUsername());
            qcScoringHitRuleGroup.setCreateTime(new Date());
            qcScoringHitRuleGroup.setUpdateBy(SecurityUtils.getUsername());
            qcScoringHitRuleGroup.setUpdateTime(new Date());
            qcScoringHitRuleGroup.setRemark(qcScoringHitRuleGroupDTOList.get(i).getRemark());
            qcScoringHitRuleGroup.setTenantId(SecurityUtils.getTenantId());
            qcScoringHitRuleGroup.setBusSort(i + 1);
            if (ObjectUtil.isNotEmpty(qcScoringHitRuleGroupDTOList.get(i).getStatus())) {
                qcScoringHitRuleGroup.setStatus(qcScoringHitRuleGroupDTOList.get(i).getStatus());
            } else {
                qcScoringHitRuleGroup.setStatus("1");
            }
            qcScoringHitRuleGroupService.addScoringHitRuleGroup(qcScoringHitRuleGroup);
            // 根据命中规则组ID新增命中规则
            Long scoringHitRuleGroupId = qcScoringHitRuleGroup.getId();
            for (int j = 0; j < qcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().size(); j ++) {
                QcScoringHitRuleItem qcScoringHitRuleItem = new QcScoringHitRuleItem();
                qcScoringHitRuleItem.setScoringHitRuleGroupId(scoringHitRuleGroupId);
                qcScoringHitRuleItem.setRuleClassification(qcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getRuleClassification());
                qcScoringHitRuleItem.setRuleId(qcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getRuleId());
                qcScoringHitRuleItem.setCreateBy(SecurityUtils.getUsername());
                qcScoringHitRuleItem.setCreateTime(new Date());
                qcScoringHitRuleItem.setUpdateBy(SecurityUtils.getUsername());
                qcScoringHitRuleItem.setUpdateTime(new Date());
                qcScoringHitRuleItem.setRemark(qcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getRemark());
                qcScoringHitRuleItem.setTenantId(SecurityUtils.getTenantId());
                qcScoringHitRuleItem.setBusSort(j + 1);
                if (ObjectUtil.isNotEmpty(qcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getStatus())) {
                    qcScoringHitRuleItem.setStatus(qcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getStatus());
                } else {
                    qcScoringHitRuleItem.setStatus("1");
                }
                Long ruleId = qcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getRuleId();
                String ruleClassification = qcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getRuleClassification();
                String ruleName = "";
                if (ruleClassification.equals("1")) {
                    ruleName = qcScoringHitRuleItemService.getRuleNameByRuleId(ruleId);
                } else if (ruleClassification.equals("2")) {
                    ruleName = qcScoringHitRuleItemService.getRuleNameById(ruleId);
                } else if (ruleClassification.equals("3")) {
                    ruleName = qcScoringHitRuleItemService.getRuleNameByRuleId(ruleId);
                }
                qcScoringHitRuleItem.setRuleName(ruleName);
                qcScoringHitRuleItem.setTemplateId(payload.getLong("templateId"));
                qcScoringHitRuleItemService.addScoringHitRuleItem(qcScoringHitRuleItem);
            }
        }
    }

    /**
     * 修改评分细则
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateQcScoringItem(JSONObject payload) {
        Long scoringClassificationId = payload.getLong("scoringClassificationId");
        if (ObjectUtil.isEmpty(scoringClassificationId)) {
            throw new ServiceException("评分分类不允许为空");
        }
        String scoringItemName = payload.getString("scoringItemName");
        if (ObjectUtil.isEmpty(scoringItemName)) {
            throw new ServiceException("评分名称不允许为空");
        }
        Integer hitRuleScore = payload.getInteger("hitRuleScore");
        if (ObjectUtil.isEmpty(hitRuleScore)) {
            throw new ServiceException("命中分数不允许为空");
        }
        // 修改评分细则
        Long id = payload.getLong("id");
        QcScoringItemDTO qcScoringItem = qcScoringItemMapper.getQcScoringItem(id);
        if (null != qcScoringItem) {
            // 修改评分细则
            qcScoringItem.setScoringClassificationId(scoringClassificationId);
            qcScoringItem.setScoringItemName(scoringItemName);
            qcScoringItem.setScoringItemDescription(payload.getString("scoringItemDescription"));
            qcScoringItem.setHitRuleScore(hitRuleScore);
            qcScoringItem.setUpdateBy(SecurityUtils.getUsername());
            qcScoringItem.setUpdateTime(new Date());
            qcScoringItem.setRemark(payload.getString("remark"));
            qcScoringItemMapper.updateQcScoringItem(qcScoringItem);
            // 根据评分细则查询所有规则组id
            List<Long> qcScoringHitRuleGroupIds = qcScoringHitRuleGroupService.getQcScoringHitRuleGroupIds(id);
            List<Long> requireToUpdateScoringHitRuleGroupIds = CollUtil.newArrayList();
            // 修改命中规则组
            List<QcScoringHitRuleGroupDTO> addQcScoringHitRuleGroupDTOList = payload.getList("qcScoringHitRuleGroupDTOList", QcScoringHitRuleGroupDTO.class, JSONReader.Feature.IgnoreCheckClose);
            if (CollUtil.isEmpty(addQcScoringHitRuleGroupDTOList)) {
                throw new ServiceException("未设置规则组");
            }
            for (int i = 0; i < addQcScoringHitRuleGroupDTOList.size(); i ++) {
                QcScoringHitRuleGroup qcScoringHitRuleGroup = new QcScoringHitRuleGroup();
                QcScoringHitRuleGroupDTO qcScoringHitRuleGroupDTO = addQcScoringHitRuleGroupDTOList.get(i);
                String ruleGroupMode = qcScoringHitRuleGroupDTO.getRuleGroupMode();
                if (ObjectUtil.isEmpty(ruleGroupMode)) {
                    throw new ServiceException("条件组合" + (i + 1) + "未选择组合模式");
                }
                List<QcScoringHitRuleItem> qcScoringHitRuleItemList = addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList();
                if (CollUtil.isEmpty(qcScoringHitRuleItemList)) {
                    throw new ServiceException("条件组合" + (i + 1) + "未设置规则项");
                }
                for (QcScoringHitRuleItem qcScoringHitRuleItem : qcScoringHitRuleItemList) {
                    if (ObjectUtil.isEmpty(qcScoringHitRuleItem.getRuleClassification())) {
                        int ruleClassificationIndex = qcScoringHitRuleItemList.indexOf(qcScoringHitRuleItem);
                        throw new ServiceException("条件组合" + (i + 1) + "第" + (ruleClassificationIndex + 1) + "行规则项未选择规则分类");

                    }
                    if (ObjectUtil.isEmpty(qcScoringHitRuleItem.getRuleId())) {
                        int ruleIdIndex = qcScoringHitRuleItemList.indexOf(qcScoringHitRuleItem);
                        throw new ServiceException("条件组合" + (i + 1) + "第" + (ruleIdIndex + 1) + "行规则项未选择规则");
                    }
                }
                qcScoringHitRuleGroup.setId(qcScoringHitRuleGroupDTO.getId());
                qcScoringHitRuleGroup.setScoringItemId(id);
                qcScoringHitRuleGroup.setTenantId(SecurityUtils.getTenantId());
                qcScoringHitRuleGroup.setRuleGroupMode(ruleGroupMode);
                qcScoringHitRuleGroup.setCreateBy(SecurityUtils.getUsername());
                qcScoringHitRuleGroup.setCreateTime(new Date());
                qcScoringHitRuleGroup.setUpdateBy(SecurityUtils.getUsername());
                qcScoringHitRuleGroup.setUpdateTime(new Date());
                qcScoringHitRuleGroup.setRemark(qcScoringHitRuleGroupDTO.getRemark());
                if (ObjectUtil.isNotEmpty(qcScoringHitRuleGroupDTO.getStatus())) {
                    qcScoringHitRuleGroup.setStatus(qcScoringHitRuleGroupDTO.getStatus());
                } else {
                    qcScoringHitRuleGroup.setStatus("1");
                }
                qcScoringHitRuleGroup.setBusSort(i + 1);
                if(qcScoringHitRuleGroupDTO.getId() == null) {
                    qcScoringHitRuleGroupService.addScoringHitRuleGroup(qcScoringHitRuleGroup);
                }else {
                    qcScoringHitRuleGroupService.updateScoringHitRuleGroup(qcScoringHitRuleGroup);
                    requireToUpdateScoringHitRuleGroupIds.add(qcScoringHitRuleGroupDTO.getId());
                }
                // 根据规则组ID查询所有规则项id
                List<Long> qcScoringHitRuleItemIds = qcScoringHitRuleItemService.getQcScoringHitRuleItemIds(qcScoringHitRuleGroupDTO.getId());
                List<Long> requireToUpdateScoringHitRuleItemIds = CollUtil.newArrayList();
                // 修改命中规则
                Long scoringHitRuleGroupId = qcScoringHitRuleGroup.getId();
                for (int j = 0; j < addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().size(); j ++) {
                    QcScoringHitRuleItem qcScoringHitRuleItem = new QcScoringHitRuleItem();
                    qcScoringHitRuleItem.setId(addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getId());
                    qcScoringHitRuleItem.setScoringHitRuleGroupId(scoringHitRuleGroupId);
                    qcScoringHitRuleItem.setRuleClassification(addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getRuleClassification());
                    qcScoringHitRuleItem.setRuleId(addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getRuleId());
                    qcScoringHitRuleItem.setCreateBy(SecurityUtils.getUsername());
                    qcScoringHitRuleItem.setCreateTime(new Date());
                    qcScoringHitRuleItem.setUpdateBy(SecurityUtils.getUsername());
                    qcScoringHitRuleItem.setUpdateTime(new Date());
                    qcScoringHitRuleItem.setRemark(addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getRemark());
                    qcScoringHitRuleItem.setTenantId(SecurityUtils.getTenantId());
                    if (ObjectUtil.isNotEmpty(addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getStatus())) {
                        qcScoringHitRuleItem.setStatus(addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getStatus());
                    } else {
                        qcScoringHitRuleItem.setStatus("1");
                    }
                    qcScoringHitRuleItem.setStatus(addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getStatus());
                    qcScoringHitRuleItem.setBusSort(j + 1);
                    Long ruleId = addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getRuleId();
                    String ruleClassification = addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getRuleClassification();
                    String ruleName = "";
                    if (ruleClassification.equals("1")) {
                        ruleName = qcScoringHitRuleItemService.getRuleNameByRuleId(ruleId);
                    } else if (ruleClassification.equals("2")) {
                        ruleName = qcScoringHitRuleItemService.getRuleNameById(ruleId);
                    } else if (ruleClassification.equals("3")) {
                        ruleName = qcScoringHitRuleItemService.getRuleNameByRuleId(ruleId);
                    }
                    qcScoringHitRuleItem.setRuleName(ruleName);
                    qcScoringHitRuleItem.setTemplateId(payload.getLong("templateId"));
                    if(qcScoringHitRuleItem.getId() == null) {
                        qcScoringHitRuleItemService.addScoringHitRuleItem(qcScoringHitRuleItem);
                    }else {
                        qcScoringHitRuleItemService.updateScoringHitRuleItem(qcScoringHitRuleItem);
                        requireToUpdateScoringHitRuleItemIds.add(addQcScoringHitRuleGroupDTOList.get(i).getQcScoringHitRuleItemList().get(j).getId());
                    }
                }
                // 逻辑批量删除规则项
                qcScoringHitRuleItemIds.removeAll(requireToUpdateScoringHitRuleItemIds);
                if (CollUtil.isNotEmpty(qcScoringHitRuleItemIds)) {
                    qcScoringHitRuleItemService.removeScoringHitRuleItemBatch(qcScoringHitRuleItemIds);
                }
            }
            // 逻辑批量删除规则组
            qcScoringHitRuleGroupIds.removeAll(requireToUpdateScoringHitRuleGroupIds);
            if (CollUtil.isNotEmpty(qcScoringHitRuleGroupIds)) {
                qcScoringHitRuleGroupService.removeScoringHitRuleGroupBatch(qcScoringHitRuleGroupIds);
                // 逻辑批量删除规则组下面所有规则项
                qcScoringHitRuleItemService.removeScoringHitRuleItemBatchByGroupIds(qcScoringHitRuleGroupIds);
            }
        }
    }

    /**
     * 查询评分细则列表根据评分分类id
     */
    @Override
    public List<QcScoringItem> getQcScoringItemByScoringClassificationIds(Long scoringClassificationId) {
        return qcScoringItemMapper.getQcScoringItemByScoringClassificationIds(scoringClassificationId);
    }

    /**
     * 删除评分细则
     */
    @Override
    public void removeQcScoringItem(Long scoringItemId) {
        QcScoringItemDTO qcScoringItem = qcScoringItemMapper.getQcScoringItem(scoringItemId);
        List<Long> qcScoringHitRuleGroupIdList = CollUtil.newArrayList();
        List<Long> qcScoringHitRuleItemIdList = CollUtil.newArrayList();
        List<QcScoringHitRuleGroupDTO> qcScoringHitRuleGroupDTOList = qcScoringItem.getQcScoringHitRuleGroupDTOList();
        if (CollUtil.isNotEmpty(qcScoringHitRuleGroupDTOList)) {
            for (QcScoringHitRuleGroupDTO qcScoringHitRuleGroupDTO : qcScoringHitRuleGroupDTOList) {
                qcScoringHitRuleGroupIdList.add(qcScoringHitRuleGroupDTO.getId());
                List<QcScoringHitRuleItem> qcScoringHitRuleItemList = qcScoringHitRuleGroupDTO.getQcScoringHitRuleItemList();
                if (CollUtil.isNotEmpty(qcScoringHitRuleItemList)) {
                    for (QcScoringHitRuleItem qcScoringHitRuleItem : qcScoringHitRuleItemList) {
                        qcScoringHitRuleItemIdList.add(qcScoringHitRuleItem.getId());
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(qcScoringHitRuleItemIdList)) {
            qcScoringHitRuleItemService.removeScoringHitRuleItemBatch(qcScoringHitRuleItemIdList);
        }
        if (CollUtil.isNotEmpty(qcScoringHitRuleGroupIdList)) {
            qcScoringHitRuleGroupService.removeScoringHitRuleGroupBatch(qcScoringHitRuleGroupIdList);
        }
        qcScoringItemMapper.removeQcScoringItem(scoringItemId);
    }

    /**
     * 修改评分细则状态
     */
    @Override
    public void updateQcScoringItemStatus(JSONObject payload) {
        qcScoringItemMapper.updateQcScoringItemStatus(payload);
    }

    /**
     * 查询细则列表根据模板ID
     */
    @Override
    public List<QcScoringItem> getQcScoringItemListByTemplateId(Long scoringTemplateId) {
        return qcScoringItemMapper.getQcScoringItemListByTemplateId(scoringTemplateId);
    }
}
