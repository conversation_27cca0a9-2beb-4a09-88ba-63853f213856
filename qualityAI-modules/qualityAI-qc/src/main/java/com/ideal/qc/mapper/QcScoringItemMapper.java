package com.ideal.qc.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcScoringItem;
import com.ideal.qc.dto.QcScoringItemDTO;

import java.util.List;

/**
 * 评分细则表Mapper接口
 *
 * <AUTHOR>
 * @date 2025/5/15 17:38
 */
public interface QcScoringItemMapper {

    /**
     * 查询评分细则列表
     *
     * @param payload 需要的一些参数
     * @return 评分细则列表
     */
    List<QcScoringItem> getQcScoringItemList(JSONObject payload);

    /**
     * 查询评分细则详情
     *
     * @param scoringItemId 评分细则ID
     * @return 评分细则详情
     */
    QcScoringItemDTO getQcScoringItem(Long scoringItemId);

    /**
     * 新增评分细则
     *
     * @param qcScoringItem 评分细则实体类
     */
    void addQcScoringItem(QcScoringItem qcScoringItem);

    /**
     * 修改评分细则
     *
     * @param qcScoringItem 评分细则实体类
     */
    void updateQcScoringItem(QcScoringItemDTO qcScoringItem);

    /**
     * 查询评分细则列表根据评分分类id
     *
     * @param scoringClassificationId 评分分类id
     * @return 评分细则列表
     */
    List<QcScoringItem> getQcScoringItemByScoringClassificationIds(Long scoringClassificationId);

    /**
     * 删除评分细则
     *
     * @param scoringItemId 评分细则ID
     */
    void removeQcScoringItem(Long scoringItemId);

    /**
     * 修改评分细则状态
     *
     * @param payload 需要的一些参数
     */
    void updateQcScoringItemStatus(JSONObject payload);

    /**
     * 查询细则列表根据模板ID
     *
     * @param scoringTemplateId 评分模板ID
     * @return 细则列表
     */
    List<QcScoringItem> getQcScoringItemListByTemplateId(Long scoringTemplateId);
}
