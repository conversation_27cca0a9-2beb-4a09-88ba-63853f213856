package com.ideal.system.service;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.system.domain.SysUserUnlock;

import java.util.List;

/**
 * 账户解锁Service接口
 *
 * <AUTHOR>
 * @date 2025/6/27 16:43
 */
public interface ISysUserUnlockService {

    /**
     * 获取已锁定账户列表
     *
     * @param payload 需要的一些参数
     * @return 已锁定账户列表
     */
    List<SysUserUnlock> getUserUnlockList(JSONObject payload);

    /**
     * 账户解锁
     *
     * @param userName 登录名称
     */
    void unlockUser(String userName);
}
