import request from '@/utils/request'

// 查询评分规则模板列表
export function listItem(query) {
  return request({
    url: '/qc/scoreItem/list',
    method: 'get',
    params: query
  })
}

// 查询评分规则模板列表by 评分模板Id
export function listItemByTplId(query) {
  return request({
    url: '/qc/scoreItem/listByTplId/',
    method: 'get',
    params: query
  })
}

export function listPnode(query){
  return request({
    url: '/qc/scoreItem/listPnode/',
    method: 'get',
    params: query
  })
}

// 查询评分规则模板详细
export function getItem(id) {
  return request({
    url: '/qc/scoreItem/' + id,
    method: 'get'
  })
}

// 新增评分规则模板
export function addItem(data) {
  return request({
    url: '/qc/scoreItem',
    method: 'post',
    data: data
  })
}

// 修改评分规则模板
export function updateItem(data) {
  return request({
    url: '/qc/scoreItem',
    method: 'put',
    data: data
  })
}

// 删除评分规则模板
export function delItem(id) {
  return request({
    url: '/qc/scoreItem/' + id,
    method: 'delete'
  })
}
