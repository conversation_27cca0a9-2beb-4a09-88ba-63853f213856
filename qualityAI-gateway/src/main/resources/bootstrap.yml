# Tomcat
server:
  port: 18080

# Spring
spring: 
  application:
    # 应用名称
    name: gateway
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${NACOSURL:127.0.0.1:8848}
        username: nacos
        password: nacos
        # 命名空间 可配置在系统环境变量中
        namespace: ${QC_NACOS_NSP:public}
      config:
        # 配置中心地址
        server-addr: ${NACOSURL:127.0.0.1:8848}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.username}
        # 命名空间 可配置在系统环境变量中
        namespace: ${QC_NACOS_NSP:public}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - redis-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: 127.0.0.1:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: 127.0.0.1:8848
            username: ${spring.cloud.nacos.discovery.username}
            password: ${spring.cloud.nacos.discovery.username}
            dataId: sentinel-gateway
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
