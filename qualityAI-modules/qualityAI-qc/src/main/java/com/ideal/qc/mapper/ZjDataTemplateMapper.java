package com.ideal.qc.mapper;

import com.ideal.qc.domain.ZjDataTemplate;

import java.util.List;

/**
 * 质检源数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface ZjDataTemplateMapper
{
    /**
     * 查询质检源数据
     *
     * @param id 质检源数据主键
     * @return 质检源数据
     */
    public ZjDataTemplate selectZjDataTemplateById(Long id);

    /**
     * 查询质检源数据列表
     *
     * @param zjDataTemplate 质检源数据
     * @return 质检源数据集合
     */
    public List<ZjDataTemplate> selectZjDataTemplateList(ZjDataTemplate zjDataTemplate);

    /**
     * 新增质检源数据
     *
     * @param zjDataTemplate 质检源数据
     * @return 结果
     */
    public int insertZjDataTemplate(ZjDataTemplate zjDataTemplate);

    /**
     * 修改质检源数据
     *
     * @param zjDataTemplate 质检源数据
     * @return 结果
     */
    public int updateZjDataTemplate(ZjDataTemplate zjDataTemplate);

    /**
     * 删除质检源数据
     *
     * @param id 质检源数据主键
     * @return 结果
     */
    public int deleteZjDataTemplateById(Long id);

    /**
     * 批量删除质检源数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZjDataTemplateByIds(Long[] ids);

    public void updateZjDataTemplateStatus(ZjDataTemplate zjDataTemplate);

}
