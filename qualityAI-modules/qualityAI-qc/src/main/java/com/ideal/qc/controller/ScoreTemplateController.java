package com.ideal.qc.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.ScoreTemplate;
import com.ideal.qc.service.IScoreTemplateService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 评分模板Controller
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@RestController
@RequestMapping("/scoretpl")
public class ScoreTemplateController extends BaseController
{
    @Autowired
    private IScoreTemplateService scoreTemplateService;

    /**
     * 查询评分模板列表
     */
    @RequiresPermissions("qc:scoretpl:list")
    @GetMapping("/list")
    public TableDataInfo list(ScoreTemplate scoreTemplate)
    {
        startPage();
        List<ScoreTemplate> list = scoreTemplateService.selectScoreTemplateList(scoreTemplate);
        return getDataTable(list);
    }

    /**
     * 导出评分模板列表
     */
    @RequiresPermissions("qc:scoretpl:export")
    @Log(title = "评分模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScoreTemplate scoreTemplate)
    {
        List<ScoreTemplate> list = scoreTemplateService.selectScoreTemplateList(scoreTemplate);
        ExcelUtil<ScoreTemplate> util = new ExcelUtil<ScoreTemplate>(ScoreTemplate.class);
        util.exportExcel(response, list, "评分模板数据");
    }

    /**
     * 获取评分模板详细信息
     */
    @RequiresPermissions("qc:scoretpl:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(scoreTemplateService.selectScoreTemplateById(id));
    }

    /**
     * 新增评分模板
     */
    @RequiresPermissions("qc:scoretpl:add")
    @Log(title = "评分模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScoreTemplate scoreTemplate)
    {
        return toAjax(scoreTemplateService.insertScoreTemplate(scoreTemplate));
    }

    /**
     * 修改评分模板
     */
    @RequiresPermissions("qc:scoretpl:edit")
    @Log(title = "评分模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScoreTemplate scoreTemplate)
    {
        return toAjax(scoreTemplateService.updateScoreTemplate(scoreTemplate));
    }

    /**
     * 删除评分模板
     */
    @RequiresPermissions("qc:scoretpl:remove")
    @Log(title = "评分模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(scoreTemplateService.deleteScoreTemplateByIds(ids));
    }
}
