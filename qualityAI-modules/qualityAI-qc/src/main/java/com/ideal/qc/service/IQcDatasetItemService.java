package com.ideal.qc.service;

import java.io.File;
import java.util.List;
import com.ideal.qc.domain.QcDatasetItem;

/**
 * 语音数据明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IQcDatasetItemService 
{
    /**
     * 查询语音数据明细
     * 
     * @param id 语音数据明细主键
     * @return 语音数据明细
     */
    public QcDatasetItem selectQcDatasetItemById(Long id);

    /**
     * 查询语音数据明细列表
     * 
     * @param qcDatasetItem 语音数据明细
     * @return 语音数据明细集合
     */
    public List<QcDatasetItem> selectQcDatasetItemList(QcDatasetItem qcDatasetItem);

    /**
     * 新增语音数据明细
     * 
     * @param qcDatasetItem 语音数据明细
     * @return 结果
     */
    public int insertQcDatasetItem(QcDatasetItem qcDatasetItem);

    /**
     * 修改语音数据明细
     * 
     * @param qcDatasetItem 语音数据明细
     * @return 结果
     */
    public int updateQcDatasetItem(QcDatasetItem qcDatasetItem);

    /**
     * 批量删除语音数据明细
     * 
     * @param ids 需要删除的语音数据明细主键集合
     * @return 结果
     */
    public int deleteQcDatasetItemByIds(Long[] ids);

    /**
     * 删除语音数据明细信息
     * 
     * @param id 语音数据明细主键
     * @return 结果
     */
    public int deleteQcDatasetItemById(Long id);

    /**
     * 根据文件ID获取音频文件
     *
     * @param fileId 文件ID
     * @return 音频文件
     */
    public File getAudioFileByFileId(String fileId);
}
