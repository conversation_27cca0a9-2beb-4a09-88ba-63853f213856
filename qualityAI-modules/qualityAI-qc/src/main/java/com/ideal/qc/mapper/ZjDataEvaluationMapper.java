package com.ideal.qc.mapper;

import java.util.List;
import com.ideal.qc.domain.ZjDataEvaluation;

/**
 * 质检评测Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-31
 */
public interface ZjDataEvaluationMapper 
{
    /**
     * 查询质检评测
     * 
     * @param id 质检评测主键
     * @return 质检评测
     */
    public ZjDataEvaluation selectZjDataEvaluationById(Long id);

    /**
     * 查询质检评测列表
     * 
     * @param zjDataEvaluation 质检评测
     * @return 质检评测集合
     */
    public List<ZjDataEvaluation> selectZjDataEvaluationList(ZjDataEvaluation zjDataEvaluation);

    /**
     * 新增质检评测
     * 
     * @param zjDataEvaluation 质检评测
     * @return 结果
     */
    public int insertZjDataEvaluation(ZjDataEvaluation zjDataEvaluation);

    /**
     * 修改质检评测
     * 
     * @param zjDataEvaluation 质检评测
     * @return 结果
     */
    public int updateZjDataEvaluation(ZjDataEvaluation zjDataEvaluation);

    /**
     * 删除质检评测
     * 
     * @param id 质检评测主键
     * @return 结果
     */
    public int deleteZjDataEvaluationById(Long id);

    /**
     * 批量删除质检评测
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZjDataEvaluationByIds(Long[] ids);
}
