export const Utils = {
  /**
   * 生成UUID
   * @returns UUID
   */
  getUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      }
    )
  },

  /**
   * 将列表数据转换成树形结构数据
   * @param {*} list 列表数据  必填
   * @param {*} parentId 父节点ID  选填
   * @returns
   */
  listToTree(list, parentId) {
    list = list || []
    parentId = parentId || 0
    const nodeList = list.filter(v => v.parentId === parentId) || []
    nodeList.forEach((node) => {
      const children = Utils.listToTree(list, Number(node.id))
      if (children.length) {
        node.children = children
      }
    })
    return nodeList
  },


  /**
   * 将树形结构数据转换成列表数据
   * @param {*} treeData 树形结构数据  必填
   * @param {*} list 已有列表数据  选填
   * @param {*} parentId 父节点ID  选填
   * @returns
   */
  treeToList(treeData, list, parentId){
    list = list || []
    if(!Array.isArray(treeData)){
      treeData = [treeData]
    }
    treeData.forEach(n => {
      let node = JSON.parse(JSON.stringify(n))
      let children = n.children
      delete node.children
      node.id = node.id || Utils.getUUID()
      node.parentId = node.parentId || parentId || 0
      list.push(node)
      children && children.length && children.forEach(v => {
        Utils.treeToList(v, list, node.id)
      })
    })
    return list
  },

    /**
   * 通过最后一级id,逐层找到它的父级id，拼接成数组
   * @param {*} list 树形结构数据  必填
   * @param {*} id 最后一级id  必填
   * @returns
   */
  getParentIdList(list, id) {
    if (!list || !id) {
        return ''
    }
    let arr = [];
    let findParent = (data, nodeId, parentId) => {
        for (var i = 0, length = data.length; i < length; i++) {
            let node = data[i];
            if (node.id == nodeId) {
              // arr.unshift(nodeId.toString());
                arr.unshift(node.name);
                if (nodeId == list[0].id) {
                    break
                }
                findParent(list, parentId);
                break
            } else {
                if (node.children) {
                    findParent(node.children, nodeId, node.id);
                }
                continue
            }
        }
        return arr;
    }
    return findParent(list, id);
  },
  // 将秒数转换00:00:00
  getDuration(startTime, endTime) {
    let endTimes = Number(endTime);
    // var currentTime = endTime === 0 ? endTime : (endTime|| new Date().getTime());
    var currentTime = endTimes === 0 ? endTimes : (endTimes|| new Date().getTime());
    var seconds = currentTime - Number(startTime);
    // var hour = parseInt(parseInt(diffTime) / 1000 / 60 / 60 % 60);
    // var minute = parseInt(parseInt(diffTime) / 1000 / 60 % 60);
    // var second = parseInt(parseInt(diffTime) / 1000 % 60);
    // if (hour.toString().length < 2) {
    //   hour = "0" + hour;
    // }
    // if (minute.toString().length < 2) {
    //     minute = "0" + minute;
    // }
    // if (second.toString().length < 2) {
    //     second = "0" + second;
    // }
    // console.log(second,'second---')
    // return hour + ":" + minute + ":" + second;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    const formattedHours = String(hours).padStart(2, '0');
    const formattedMinutes = String(minutes).padStart(2, '0');
    const formattedSeconds = String(remainingSeconds).padStart(2, '0');

    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
  },

  getSensitiveWords(words, type){
    if('phone'===type){
      words = words?words.replace(words.substring(3,7), "****"):words
    }
    return words
  },
  getTel(v){
    if(v!=null&&v!=undefined&&v.length>=3){
      return v.substring(0,3)+"***";
    }else{
      return "";
    }
  },
  getName(v){
    if(v!=null&&v!=undefined&&v.length>=1){
      return v.substring(0,1)+"**";
    }else{
      return "";
    }
  },
  getWorkNo(v) {
    if(v!=null&&v!=undefined&&v.length>=1){
      return v.substring(0,1)+"**";
    }else{
      return "";
    }
  }
}
