# Quality模块设计文档（结构化版本）

## 5.2.1 模块 B1 - 数据集管理模块

对该模块设计的具体描述，如果用ROSE设计，可以导出或复制ROSE的相关用例实现的类图、协作图等UML的表达进行说明，没有使用UML设计的可以采用类似下面的表格说明，但是具体某个功能或方法的设计描述必须包含以下表格涉及的内容。

| 模块名称 | 数据集管理模块（QcDataset） |
|----------|---------------------------|
| 需求编号 | QC-DS-001 |
| 功能描述 | 一般一个模块有多个功能 |
| 非功能需求 | 说明对该程序的非功能需求，包括安全性、易用性和容错机制等要求 |
| 输入 | 对输入进行详细说明，给出对每一个输入项的特性，包括名称、参数名、数据的类型和格式、数据值的有效范围、输入的方式和安全保密条件等等 |
| 输出 | 对输出进行详细说明，给出对每一个输出项的特性，包括名称、参数名、数据的类型和格式、数据值的有效范围、输出的方式、对输出图形及符号的说明和安全保密条件等等 |
| 数据结构和算法 | 用于指导程序的实现，表达该功能设计意图 |
| 异常处理 | 对程序后台报错、数据超出边界值、页面校验等异常情况的处理方式进行说明 |
| 补充说明 | 1.字段要求<br/>2.必填项说明<br/>3.按钮设计<br/>4.快捷键设计<br/>5.提示设计<br/>6.其他逻辑说明<br/>7.翻页设计 |

### 功能详细说明

#### 1. 数据集列表查询功能
- **功能描述**: 支持分页查询数据集列表，包含筛选和排序功能
- **输入参数**: 
  - name: 数据集名称（可选，支持模糊查询）
  - dataType: 数据类型（voice/biz）
  - processStatus: 处理状态筛选
  - pageNum: 页码
  - pageSize: 每页大小
- **输出结果**: 
  - 数据集列表（包含ID、名称、类型、总数、进度等）
  - 分页信息（总数、当前页等）

#### 2. 数据集创建功能
- **功能描述**: 创建新的数据集，支持语音和业务两种类型
- **输入参数**:
  - name: 数据集名称（必填，长度1-255字符）
  - dataType: 数据类型（必选，voice/biz）
  - planId: 来源计划ID（可选）
  - remark: 备注信息（可选）
- **输出结果**: 创建成功的数据集信息

#### 3. 数据集状态管理功能
- **功能描述**: 管理数据集的处理状态和进度更新
- **状态流转**: new → importing → ready → voice_fetch → finished/fail

## 5.2.2 模块 B2 - 业务数据管理模块

| 模块名称 | 业务数据管理模块（QcBizItem） |
|----------|------------------------------|
| 需求编号 | QC-BI-001 |
| 功能描述 | 业务数据明细的增删改查、导入导出、质检流程管理 |
| 非功能需求 | 支持大批量数据导入（万级别），导入成功率>95%，响应时间<3秒，支持并发操作 |
| 输入 | **查询输入**:<br/>- datasetId: 数据集ID（Long类型）<br/>- agentId: 坐席工号（String，支持模糊查询）<br/>- uniqueKey: 唯一主键号（String）<br/>- dateRange: 时间范围（Date数组）<br/>**导入输入**:<br/>- file: Excel文件（MultipartFile，支持.xlsx/.xls格式，最大50MB）<br/>- 动态列数据（Q1-Q11等问卷答案） |
| 输出 | **查询输出**:<br/>- 业务数据列表（包含所有字段信息）<br/>- 分页信息（total, pageNum, pageSize）<br/>**导入输出**:<br/>- 导入结果统计（成功条数、失败条数、错误信息）<br/>**导出输出**:<br/>- Excel文件流（包含动态列展开） |
| 数据结构和算法 | **核心实体**: QcBizItem<br/>**关键字段**: id, dataset_id, unique_key, answers_json<br/>**算法**: 动态Excel解析算法、JSON字段拆分算法、批量插入优化算法 |
| 异常处理 | 1. 文件格式错误：返回"不支持的文件格式"<br/>2. 数据重复：跳过重复记录并记录日志<br/>3. 必填字段缺失：标记错误行并继续处理<br/>4. 数据库连接异常：回滚事务并返回错误信息 |
| 补充说明 | 1.字段要求: unique_key必填且唯一<br/>2.必填项说明: 平台、项目、坐席工号为必填<br/>3.按钮设计: 导入、导出、查看详情、质检按钮<br/>4.快捷键设计: Ctrl+I导入，Ctrl+E导出<br/>5.提示设计: 导入进度提示、成功失败提示<br/>6.其他逻辑说明: 支持动态列扩展<br/>7.翻页设计: 支持前端分页和后端分页 |

### 功能详细说明

#### 1. Excel动态导入功能
- **功能描述**: 支持包含动态列的Excel文件导入，自动解析问卷答案
- **处理流程**: 
  1. 文件上传验证
  2. 动态列头解析
  3. 数据行解析和验证
  4. 批量数据插入
  5. 结果统计返回

#### 2. 动态列导出功能
- **功能描述**: 将JSON格式的答案字段拆分为独立列导出
- **处理逻辑**:
  1. 收集所有动态列名
  2. 构建动态表头
  3. 数据行动态填充
  4. Excel文件生成

## 5.2.3 模块 B3 - 语音数据管理模块

| 模块名称 | 语音数据管理模块（QcDatasetItem） |
|----------|----------------------------------|
| 需求编号 | QC-DI-001 |
| 功能描述 | 语音文件存储、ASR识别、音频播放、标注管理 |
| 非功能需求 | 支持多种音频格式（wav/mp3/m4a），文件大小限制100MB，ASR识别准确率>90%，音频加载时间<2秒 |
| 输入 | **文件上传**:<br/>- audioFile: 音频文件（MultipartFile）<br/>- bizNo: 业务编号（String，关联业务数据）<br/>- callTime: 通话时间（Date）<br/>**ASR处理**:<br/>- fileId: FastDFS文件ID<br/>- asrConfig: 识别配置参数 |
| 输出 | **查询输出**:<br/>- 语音数据列表（文件信息、识别状态、结果）<br/>- 音频文件流（支持断点续传）<br/>**ASR输出**:<br/>- 识别文本结果<br/>- 识别状态更新<br/>- 时间戳信息 |
| 数据结构和算法 | **核心实体**: QcDatasetItem<br/>**关键字段**: file_id, asr_result, asr_status<br/>**算法**: 音频文件分片上传、ASR异步处理、断点续传算法 |
| 异常处理 | 1. 文件上传失败：重试机制，最多3次<br/>2. ASR识别失败：标记失败状态，支持重新识别<br/>3. 文件损坏：返回错误信息并记录日志<br/>4. 网络中断：支持断点续传 |
| 补充说明 | 1.字段要求: file_id必填，biz_no关联业务数据<br/>2.必填项说明: 数据集ID、文件ID为必填<br/>3.按钮设计: 播放、暂停、下载、重新识别<br/>4.快捷键设计: 空格键播放/暂停<br/>5.提示设计: 上传进度、识别进度提示<br/>6.其他逻辑说明: 支持音频波形显示<br/>7.翻页设计: 虚拟滚动优化大列表 |

### 功能详细说明

#### 1. 音频文件上传功能
- **功能描述**: 支持音频文件上传到FastDFS存储系统
- **处理流程**:
  1. 文件格式验证
  2. 文件大小检查
  3. FastDFS上传
  4. 数据库记录创建

#### 2. ASR语音识别功能
- **功能描述**: 异步处理音频文件的语音识别
- **处理流程**:
  1. 任务队列调度
  2. 音频预处理
  3. ASR服务调用
  4. 结果存储更新

## 5.2.4 模块 B4 - 质检流程管理模块

| 模块名称 | 质检流程管理模块（QcQualityCheck） |
|----------|-----------------------------------|
| 需求编号 | QC-QC-001 |
| 功能描述 | 智能质检、人工质检、复检流程、结果管理 |
| 非功能需求 | 支持并发质检操作，质检结果实时保存，支持质检模板配置，质检效率提升50% |
| 输入 | **质检输入**:<br/>- bizNo: 业务编号（String）<br/>- templateId: 质检模板ID（Long）<br/>- qualityLevel: 质检等级（String）<br/>- scores: 评分项目（Map<String, Integer>）<br/>- comment: 质检评语（String，最大1000字符） |
| 输出 | **质检输出**:<br/>- 质检结果详情<br/>- 评分统计信息<br/>- 质检报告<br/>- 历史质检记录 |
| 数据结构和算法 | **核心实体**: QcManualTaskResult, QcScoringTemplate<br/>**算法**: 智能评分算法、质检规则引擎、统计分析算法 |
| 异常处理 | 1. 模板不存在：返回模板错误信息<br/>2. 评分超出范围：自动修正并提示<br/>3. 保存失败：本地缓存并重试<br/>4. 并发冲突：乐观锁处理 |
| 补充说明 | 1.字段要求: 质检等级必选，评语必填<br/>2.必填项说明: 所有评分项目必须完成<br/>3.按钮设计: 保存草稿、提交质检、返回修改<br/>4.快捷键设计: Ctrl+S保存，Ctrl+Enter提交<br/>5.提示设计: 自动保存提示、提交确认<br/>6.其他逻辑说明: 支持质检模板切换<br/>7.翻页设计: 质检历史分页展示 |

### 功能详细说明

#### 1. 人工质检功能
- **功能描述**: 提供完整的人工质检界面和流程
- **核心组件**:
  - 音频播放器（支持波形显示）
  - 质检表单（动态加载模板）
  - 评分组件（支持多种评分方式）
  - 标注功能（时间点标注）

#### 2. 智能质检功能
- **功能描述**: 基于AI算法的自动质检
- **处理流程**:
  1. 文本分析
  2. 关键词匹配
  3. 情感分析
  4. 自动评分
  5. 结果输出

## 5.2.5 模块 B5 - 系统配置管理模块

| 模块名称 | 系统配置管理模块（QcSystemConfig） |
|----------|-----------------------------------|
| 需求编号 | QC-SC-001 |
| 功能描述 | 质检模板管理、评分规则配置、系统参数设置 |
| 非功能需求 | 配置实时生效，支持配置版本管理，配置变更审计 |
| 输入 | **模板配置**:<br/>- templateName: 模板名称<br/>- scoreItems: 评分项目配置<br/>- rules: 质检规则配置<br/>**系统参数**:<br/>- paramKey: 参数键<br/>- paramValue: 参数值 |
| 输出 | **配置输出**:<br/>- 模板列表<br/>- 规则配置<br/>- 参数设置<br/>- 变更历史 |
| 数据结构和算法 | **核心实体**: QcScoringTemplate, QcScoringItem<br/>**算法**: 配置热更新算法、规则引擎 |
| 异常处理 | 1. 配置格式错误：验证并提示<br/>2. 配置冲突：版本控制处理<br/>3. 权限不足：返回权限错误 |
| 补充说明 | 1.字段要求: 模板名称唯一<br/>2.必填项说明: 模板名称、评分项目必填<br/>3.按钮设计: 新增、编辑、删除、复制<br/>4.提示设计: 配置保存成功提示<br/>5.其他逻辑说明: 支持模板导入导出 |

## 5.2.6 模块 B6 - 文件存储管理模块

| 模块名称 | 文件存储管理模块（FileStorageManager） |
|----------|---------------------------------------|
| 需求编号 | QC-FS-001 |
| 功能描述 | FastDFS文件上传下载、文件访问控制、存储空间管理 |
| 非功能需求 | 文件上传成功率>99%，下载速度>10MB/s，支持断点续传，存储容量可扩展 |
| 输入 | **文件上传**:<br/>- file: 文件对象（MultipartFile，最大100MB）<br/>- fileType: 文件类型（audio/document/image）<br/>- metadata: 文件元数据（Map<String, String>）<br/>**文件下载**:<br/>- fileId: 文件ID（String）<br/>- range: 请求范围（String，支持断点续传） |
| 输出 | **上传输出**:<br/>- fileId: FastDFS文件ID<br/>- fileUrl: 访问URL<br/>- fileSize: 文件大小<br/>**下载输出**:<br/>- 文件流（InputStream）<br/>- 文件信息（FileInfo） |
| 数据结构和算法 | **核心组件**: FastDFS客户端、文件分片算法<br/>**数据结构**: 文件元数据表、访问日志表<br/>**算法**: 分片上传、断点续传、负载均衡 |
| 异常处理 | 1. 网络中断：自动重试，最多3次<br/>2. 存储空间不足：返回空间不足错误<br/>3. 文件损坏：校验失败重新上传<br/>4. 访问权限：返回403错误 |
| 补充说明 | 1.字段要求: 文件类型必须在允许范围内<br/>2.必填项说明: 文件对象必填<br/>3.按钮设计: 上传、下载、删除、预览<br/>4.提示设计: 上传进度条、下载进度<br/>5.其他逻辑说明: 支持文件预览<br/>6.安全设计: 文件类型检查、病毒扫描 |

### 功能详细说明

#### 1. 分片上传功能
- **功能描述**: 大文件分片上传，支持断点续传
- **处理流程**:
  1. 文件分片（默认2MB每片）
  2. 并行上传分片
  3. 分片合并
  4. 完整性校验

#### 2. 文件访问控制
- **功能描述**: 基于权限的文件访问控制
- **控制策略**:
  - 租户隔离
  - 角色权限验证
  - 访问日志记录

## 5.2.7 模块 B7 - 数据统计分析模块

| 模块名称 | 数据统计分析模块（QcDataAnalysis） |
|----------|-----------------------------------|
| 需求编号 | QC-DA-001 |
| 功能描述 | 质检数据统计、趋势分析、报表生成、数据可视化 |
| 非功能需求 | 统计查询响应时间<5秒，支持实时数据更新，报表导出格式多样化 |
| 输入 | **统计查询**:<br/>- dateRange: 时间范围（Date[]）<br/>- agentIds: 坐席ID列表（List<String>）<br/>- qualityLevels: 质检等级（List<String>）<br/>- datasetIds: 数据集ID（List<Long>）<br/>**报表生成**:<br/>- reportType: 报表类型（daily/weekly/monthly）<br/>- templateId: 报表模板ID |
| 输出 | **统计输出**:<br/>- 质检统计数据（Map<String, Object>）<br/>- 趋势图数据（ChartData）<br/>- 排行榜数据（List<RankingItem>）<br/>**报表输出**:<br/>- Excel报表文件<br/>- PDF报表文件<br/>- 图表图片 |
| 数据结构和算法 | **核心算法**: 数据聚合算法、趋势分析算法、排名算法<br/>**数据结构**: 统计结果缓存、报表模板配置<br/>**优化**: SQL优化、缓存策略、分页查询 |
| 异常处理 | 1. 数据量过大：分批处理并提示<br/>2. 查询超时：返回超时错误<br/>3. 内存不足：使用流式处理<br/>4. 报表生成失败：重试并记录日志 |
| 补充说明 | 1.字段要求: 时间范围必填<br/>2.必填项说明: 统计维度至少选择一个<br/>3.按钮设计: 查询、导出、刷新、重置<br/>4.图表设计: 柱状图、折线图、饼图<br/>5.提示设计: 数据加载提示、导出成功提示<br/>6.其他逻辑说明: 支持数据钻取<br/>7.缓存设计: 统计结果缓存30分钟 |

### 功能详细说明

#### 1. 实时统计功能
- **功能描述**: 提供实时的质检数据统计
- **统计维度**:
  - 按时间统计（日/周/月）
  - 按坐席统计
  - 按质检等级统计
  - 按数据集统计

#### 2. 趋势分析功能
- **功能描述**: 分析质检数据的变化趋势
- **分析内容**:
  - 质检通过率趋势
  - 坐席表现趋势
  - 问题分布趋势

## 5.2.8 模块 B8 - 任务调度管理模块

| 模块名称 | 任务调度管理模块（QcJobScheduler） |
|----------|-----------------------------------|
| 需求编号 | QC-JS-001 |
| 功能描述 | ASR任务调度、数据同步任务、定时统计任务、系统维护任务 |
| 非功能需求 | 任务执行成功率>95%，支持任务重试，任务监控实时性，支持集群部署 |
| 输入 | **任务配置**:<br/>- jobName: 任务名称（String）<br/>- cronExpression: Cron表达式（String）<br/>- jobClass: 任务执行类（String）<br/>- jobData: 任务参数（Map<String, Object>）<br/>**任务控制**:<br/>- jobId: 任务ID<br/>- action: 操作类型（start/stop/pause/resume） |
| 输出 | **任务状态**:<br/>- 任务列表（List<JobInfo>）<br/>- 执行历史（List<JobExecution>）<br/>- 任务日志（List<JobLog>）<br/>**监控数据**:<br/>- 任务执行统计<br/>- 性能指标 |
| 数据结构和算法 | **核心框架**: Quartz调度框架<br/>**数据结构**: 任务配置表、执行历史表、日志表<br/>**算法**: 任务分发算法、负载均衡算法、故障转移 |
| 异常处理 | 1. 任务执行失败：自动重试，最多3次<br/>2. 节点宕机：任务转移到其他节点<br/>3. 资源不足：任务排队等待<br/>4. 配置错误：任务暂停并告警 |
| 补充说明 | 1.字段要求: Cron表达式格式正确<br/>2.必填项说明: 任务名称、执行类必填<br/>3.按钮设计: 启动、停止、暂停、恢复、删除<br/>4.监控设计: 实时状态监控、执行日志<br/>5.提示设计: 操作确认提示、状态变更通知<br/>6.其他逻辑说明: 支持任务依赖关系<br/>7.集群设计: 支持多节点部署 |

### 功能详细说明

#### 1. ASR任务调度
- **功能描述**: 自动调度语音识别任务
- **调度策略**:
  - 按优先级调度
  - 负载均衡分配
  - 失败重试机制

#### 2. 数据同步任务
- **功能描述**: 定时同步外部数据
- **同步内容**:
  - 业务数据同步
  - 用户信息同步
  - 配置信息同步

## 5.2.9 模块 B9 - 权限管理模块

| 模块名称 | 权限管理模块（QcPermissionManager） |
|----------|-------------------------------------|
| 需求编号 | QC-PM-001 |
| 功能描述 | 用户权限控制、角色管理、数据权限、操作审计 |
| 非功能需求 | 权限验证响应时间<100ms，支持细粒度权限控制，审计日志完整性100% |
| 输入 | **权限验证**:<br/>- userId: 用户ID（Long）<br/>- resource: 资源路径（String）<br/>- action: 操作类型（String）<br/>**角色管理**:<br/>- roleName: 角色名称<br/>- permissions: 权限列表<br/>- description: 角色描述 |
| 输出 | **验证结果**:<br/>- 权限验证结果（Boolean）<br/>- 权限详情（PermissionInfo）<br/>**管理输出**:<br/>- 角色列表<br/>- 权限树<br/>- 审计日志 |
| 数据结构和算法 | **核心模型**: RBAC权限模型<br/>**数据结构**: 用户表、角色表、权限表、关联表<br/>**算法**: 权限继承算法、缓存算法 |
| 异常处理 | 1. 权限不足：返回403错误<br/>2. 用户不存在：返回用户错误<br/>3. 角色冲突：提示冲突信息<br/>4. 系统权限：特殊处理 |
| 补充说明 | 1.字段要求: 用户ID必须有效<br/>2.必填项说明: 资源路径、操作类型必填<br/>3.按钮设计: 授权、撤权、查看详情<br/>4.安全设计: 权限最小化原则<br/>5.提示设计: 权限变更通知<br/>6.其他逻辑说明: 支持临时权限<br/>7.审计设计: 所有权限操作记录 |

### 功能详细说明

#### 1. 细粒度权限控制
- **功能描述**: 支持到字段级别的权限控制
- **控制层级**:
  - 模块级权限
  - 功能级权限
  - 数据级权限
  - 字段级权限

#### 2. 多租户权限隔离
- **功能描述**: 基于租户的数据隔离
- **隔离策略**:
  - 数据查询自动过滤
  - 操作权限租户限制
  - 配置信息隔离

## 5.2.10 模块 B10 - 系统监控模块

| 模块名称 | 系统监控模块（QcSystemMonitor） |
|----------|--------------------------------|
| 需求编号 | QC-SM-001 |
| 功能描述 | 系统性能监控、业务指标监控、告警管理、健康检查 |
| 非功能需求 | 监控数据实时性<1秒，告警响应时间<30秒，监控覆盖率100% |
| 输入 | **监控配置**:<br/>- metricName: 指标名称<br/>- threshold: 阈值设置<br/>- alertRule: 告警规则<br/>**查询参数**:<br/>- timeRange: 时间范围<br/>- metricType: 指标类型 |
| 输出 | **监控数据**:<br/>- 性能指标（CPU、内存、磁盘）<br/>- 业务指标（导入成功率、质检完成率）<br/>- 告警信息（AlertInfo）<br/>**健康状态**:<br/>- 系统健康度<br/>- 服务状态列表 |
| 数据结构和算法 | **监控框架**: Micrometer + Prometheus<br/>**数据结构**: 指标数据表、告警配置表<br/>**算法**: 异常检测算法、趋势预测算法 |
| 异常处理 | 1. 监控数据丢失：补偿机制<br/>2. 告警风暴：告警聚合<br/>3. 存储空间不足：数据清理<br/>4. 网络异常：本地缓存 |
| 补充说明 | 1.字段要求: 指标名称唯一<br/>2.必填项说明: 阈值设置必填<br/>3.按钮设计: 查看详情、设置告警、导出数据<br/>4.图表设计: 实时监控图表<br/>5.提示设计: 告警通知、状态变更<br/>6.其他逻辑说明: 支持自定义指标<br/>7.告警设计: 多渠道告警（邮件、短信、钉钉） |

### 功能详细说明

#### 1. 实时性能监控
- **功能描述**: 监控系统关键性能指标
- **监控指标**:
  - JVM内存使用率
  - CPU使用率
  - 数据库连接池
  - 接口响应时间

#### 2. 业务指标监控
- **功能描述**: 监控业务关键指标
- **业务指标**:
  - 数据导入成功率
  - ASR处理成功率
  - 质检完成率
  - 用户活跃度

---

*本文档版本：v2.0*
*最后更新：2025-01-07*
*文档维护：开发团队*
