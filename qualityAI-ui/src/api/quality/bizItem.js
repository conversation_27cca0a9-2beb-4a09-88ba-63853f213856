import request from '@/utils/request'

// 查询业务数据明细列表
export function listBizItem(query) {
  return request({
    url: '/qc/bizItem/list',
    method: 'get',
    params: query
  })
}

// 查询业务数据明细详细
export function getBizItem(id) {
  return request({
    url: '/qc/bizItem/' + id,
    method: 'get'
  })
}

// 新增业务数据明细
export function addBizItem(data) {
  return request({
    url: '/qc/bizItem',
    method: 'post',
    data: data
  })
}

// 修改业务数据明细
export function updateBizItem(data) {
  return request({
    url: '/qc/bizItem',
    method: 'put',
    data: data
  })
}

// 删除业务数据明细
export function delBizItem(id) {
  return request({
    url: '/qc/bizItem/' + id,
    method: 'delete'
  })
}

// 导入业务数据明细
export function importData(data) {
  return request({
    url: '/qc/bizItem/importData',
    method: 'post',
    data: data
  })
}

// 导出业务数据明细（拆分动态列）
export function exportWithDynamicColumns(query) {
  return request({
    url: '/qc/bizItem/exportWithDynamicColumns',
    method: 'post',
    params: query,
    responseType: 'blob'
  })
}
