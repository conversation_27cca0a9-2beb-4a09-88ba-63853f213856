package com.ideal.qc.service.impl;

import java.util.List;
import java.util.Map;

import com.ideal.common.core.utils.DateUtils;
import com.ideal.qc.domain.ZjDataAiresult;
import com.ideal.qc.domain.ZjDataReviewData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.ZjDataReviewMapper;
import com.ideal.qc.domain.ZjDataReview;
import com.ideal.qc.service.IZjDataReviewService;

/**
 * 人工复检Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@Service
public class ZjDataReviewServiceImpl implements IZjDataReviewService 
{
    @Autowired
    private ZjDataReviewMapper zjDataReviewMapper;

    /**
     * 查询人工复检
     * 
     * @param id 人工复检主键
     * @return 人工复检
     */
    @Override
    public ZjDataReview selectZjDataReviewById(Long id)
    {
        return zjDataReviewMapper.selectZjDataReviewById(id);
    }

    /**
     * 查询人工复检列表
     * 
     * @param zjDataReview 人工复检
     * @return 人工复检
     */
    @Override
    public List<ZjDataReview> selectZjDataReviewList(ZjDataReview zjDataReview)
    {
        return zjDataReviewMapper.selectZjDataReviewList(zjDataReview);
    }

    /**
     * 新增人工复检
     * 
     * @param zjDataReview 人工复检
     * @return 结果
     */
    @Override
    public int insertZjDataReview(ZjDataReview zjDataReview)
    {
        zjDataReview.setCreateTime(DateUtils.getNowDate());
        return zjDataReviewMapper.insertZjDataReview(zjDataReview);
    }

    /**
     * 修改人工复检
     * 
     * @param zjDataReview 人工复检
     * @return 结果
     */
    @Override
    public int updateZjDataReview(ZjDataReview zjDataReview)
    {
        zjDataReview.setUpdateTime(DateUtils.getNowDate());
        return zjDataReviewMapper.updateZjDataReview(zjDataReview);
    }

    /**
     * 批量删除人工复检
     * 
     * @param ids 需要删除的人工复检主键
     * @return 结果
     */
    @Override
    public int deleteZjDataReviewByIds(Long[] ids)
    {
        return zjDataReviewMapper.deleteZjDataReviewByIds(ids);
    }

    /**
     * 删除人工复检信息
     * 
     * @param id 人工复检主键
     * @return 结果
     */
    @Override
    public int deleteZjDataReviewById(Long id)
    {
        return zjDataReviewMapper.deleteZjDataReviewById(id);
    }

    @Override
    public List<ZjDataReviewData> selectReviewData(ZjDataReviewData zjDataReviewData) {
        return zjDataReviewMapper.selectReviewData(zjDataReviewData);
    }

    @Override
    public List<ZjDataAiresult> selectAiResultData(ZjDataReviewData zjDataReviewData) {
        return zjDataReviewMapper.selectAiResultData(zjDataReviewData);
    }

    @Override
    public List<ZjDataAiresult> selectAiTemplateData(ZjDataReviewData zjDataReviewData) {
        return zjDataReviewMapper.selectAiTemplateData(zjDataReviewData);
    }

    @Override
    public void completeZjDataReview(Map<String,Object> map) {
        zjDataReviewMapper.completeZjDataReview(map);
    }

    @Override
    public List<ZjDataReview> getReviewListByDataId(Long dataId) {
        return zjDataReviewMapper.getReviewListByDataId(dataId);
    }
}
