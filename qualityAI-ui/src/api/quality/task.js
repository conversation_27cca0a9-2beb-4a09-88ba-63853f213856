import request from '@/utils/request'

// 查询质检规则列表
export function listTask(query) {
  return request({
    url: '/qc/task/list',
    method: 'get',
    params: query
  })
}

// 查询质检规则详细
export function getTask(taskId) {
  return request({
    url: '/qc/task/' + taskId,
    method: 'get'
  })
}

// 新增质检规则
export function addTask(data) {
  return request({
    url: '/qc/task',
    method: 'post',
    data: data
  })
}

// 修改质检规则
export function updateTask(data) {
  return request({
    url: '/qc/task',
    method: 'put',
    data: data
  })
}

// 删除质检规则
export function delTask(taskId) {
  return request({
    url: '/qc/task/' + taskId,
    method: 'delete'
  })
}

// 启动质检规则
export function startTask(data) {
  return request({
    url: '/qc/task/start',
    method: 'post',
    data: data
  })
}
