<template>
  <div class="app-container">
    <el-row :gutter="40">
      <el-col :span="4" style="border-right:1px solid #F2F6FC;padding:0;">
        <div class="sortItem row spaceBet" @click="handleKeywordLibraryClassification()">
          <div>全部分类</div>
          <i class="el-icon-plus" @click="handleAdd"></i>
        </div>
        <div class="sortChild">
          <div v-for="(item, index) in keywordLibraryClassificationList" :key="index"
            class="sortItem row spaceBet childItem"
            :class="{ 'active-item': activeKeywordLibraryClassification === item }"
            @click="handleKeywordLibraryClassification(item)">
            <div>{{ item.keywordLibraryClassificationName }}</div>
            <el-dropdown size="mini" @command="(command) => handleCommand(command, item)">
              <i class="el-icon-more"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="handleReset" icon="el-icon-refresh-right">重命名</el-dropdown-item>
                <el-dropdown-item command="handleDelete" icon="el-icon-delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-col>

      <el-col :span="20">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px"
          class="row spaceBet" style="align-items:flex-start;">
          <el-form-item label="" prop="keywordLibraryName">
            <el-input v-model="queryParams.keywordLibraryName" placeholder="词库名称" clearable style="width: 240px">
              <el-button slot="append" icon="el-icon-search" @click="getKeywordLibraryList"></el-button>
            </el-input>
          </el-form-item>
          <el-row :gutter="10">
            <el-col :span="1.5">
              <el-button v-hasPermi="['quality:keywordLibrary:add']" type="primary" plain icon="el-icon-plus"
                size="small" @click="addKeywordLibrary">新增关键词库</el-button>
            </el-col>
          </el-row>
        </el-form>
        <el-table v-loading="loading" :data="keywordLibraryList">
          <el-table-column label="词库名称" align="center" prop="keywordLibraryName" />
          <el-table-column label="归属分类" align="center" prop="keywordLibraryClassificationName" />
          <el-table-column label="关键词数" align="center" prop="keywordNumber" />
          <el-table-column label="是否启用" align="center">
            <template slot-scope="scope">
              <el-switch active-value="1" inactive-value="0" v-model="scope.row.status"
                @change="handleKeywordLibraryStatus(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="editKeywordLibrary(scope.row)"
                style="margin-right:10px;">修改</el-button>
              <el-popconfirm title="你确定要删除吗？" @confirm="delKeywordLibrary(scope.row.id)">
                <el-button slot="reference" size="mini" type="text" icon="el-icon-delete"
                  style="margin-right:10px;">删除</el-button>
              </el-popconfirm>
              <el-button size="mini" type="text" @click="openKeyword(scope.row)">更多<i
                  class="el-icon-d-arrow-right"></i></el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getKeywordLibraryList" />
      </el-col>
    </el-row>

    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="keywordLibraryClassificationAddOrUpdateForm" :inline="true" label-position="top"
        :model="keywordLibraryClassificationAddOrUpdateForm" :rules="KeywordLibraryClassificationAddOrUpdateFormRules"
        label-width="160px" class="keywordLibraryClassificationAddOrUpdateForm">
        <el-form-item label="关键词库分类名称" prop="keywordLibraryClassificationName">
          <el-input v-model="keywordLibraryClassificationAddOrUpdateForm.keywordLibraryClassificationName"
            placeholder="请输入关键词库分类名称" maxlength="30" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitKeywordLibraryClassificationAddOrUpdateForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="keywordLibraryTitle" :visible.sync="keywordLibraryDialog" width="40%" append-to-body :close-on-click-modal="false">
      <el-form size="small" :inline="true" label-position="top" ref="keywordLibraryForm" :model="keywordLibraryForm"
        :rules="keywordLibraryFormRules" label-width="100px" class="keywordLibraryForm">
        <el-form-item label="词库分类" prop="keywordLibraryClassificationId">
          <el-select v-model="keywordLibraryForm.keywordLibraryClassificationId" placeholder="请选择词库分类" clearable>
            <el-option v-for="item in keywordLibraryClassificationList" :key="item.id"
              :label="item.keywordLibraryClassificationName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="词库名称" prop="keywordLibraryName">
          <el-input v-model="keywordLibraryForm.keywordLibraryName" placeholder="请输入词库名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="subKeywordLibraryForm">保 存</el-button>
        <el-button @click="cancelKeywordLibraryForm">取 消</el-button>
      </div>
    </el-dialog>

    <el-drawer title="关键词管理" :visible.sync="keywordVisible" direction="btt" size="50%" :wrapperClosable="false">
      <el-card class="box-card" shadow="never">
        <div style="text-align: right; margin-bottom: 10px;">
          <el-button v-hasPermi="['quality:keyword:add']" type="primary" plain icon="el-icon-plus" size="mini"
            @click="addKeyword">新增关键词</el-button>
        </div>
        <el-table border size="mini" :data="keywordList" style="width: 100%;" v-loading="keywordLoading">
          <el-table-column property="keywordName" label="关键词" align="center"></el-table-column>
          <el-table-column property="keywordLibraryClassificationName" label="归属分类" align="center"></el-table-column>
          <el-table-column property="keywordLibraryName" label="归属词库" align="center"></el-table-column>
          <el-table-column label="是否启用" align="center">
            <template slot-scope="scope">
              <el-switch active-value="1" inactive-value="0" v-model="scope.row.status"
                @change="handleKeywordStatus(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="editKeyword(scope.row)"
                style="margin-right:10px;">修改</el-button>
              <el-popconfirm title="你确定要删除吗？" @confirm="delKeyword(scope.row.id)">
                <el-button slot="reference" size="mini" type="text" icon="el-icon-delete">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="keywordTotal > 0" :total="keywordTotal" :page.sync="queryKeywordParams.pageNum"
          :limit.sync="queryKeywordParams.pageSize" @pagination="getKeywordList" />
      </el-card>
    </el-drawer>

    <el-dialog :title="keywordTitle" :visible.sync="keywordDialog" width="40%" append-to-body :close-on-click-modal="false">
      <el-form size="small" :inline="true" label-position="top" ref="keywordForm" :model="keywordForm"
        :rules="keywordFormRules" label-width="100px" class="keywordForm">
        <el-form-item label="关键词名称" prop="keywordName">
          <el-input v-model="keywordForm.keywordName" placeholder="请输入关键词名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="subKeywordForm">保 存</el-button>
        <el-button @click="cancelKeywordForm">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { getQcKeywordLibraryClassificationList, addQcKeywordLibraryClassification, updateQcKeywordLibraryClassification, removeQcKeywordLibraryClassification } from "@/api/quality/keywordLibraryClassification"
import { getQcKeywordLibraryList, addQcKeywordLibrary, updateQcKeywordLibrary, removeQcKeywordLibrary } from "@/api/quality/keywordLibrary"
import { getQcKeywordList, addQcKeyword, updateQcKeyword, removeQcKeyword } from "@/api/quality/keyword"

export default {
  name: "KeywordLibrary",
  dicts: ['quality_rule_classification'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      open: false,
      // 表单参数
      form: {},
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      queryKeywordParams: {
        pageNum: 1,
        pageSize: 10,
      },
      keywordTotal: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keywordLibraryName: ''
      },
      keywordLibraryClassificationList: [],
      keywordLibraryForm: {
        qcScoringHitRuleGroupDTOList: [
          {
            qcScoringHitRuleItemList: [{}]
          }
        ]
      },
      keywordLibraryDialog: false,
      keywordLibraryTitle: '',
      keywordLibraryFormRules: {
        keywordLibraryName: [
          { required: true, message: "词库名称不能为空", trigger: "blur" }
        ],
        keywordLibraryClassificationId: [
          { required: true, message: "词库分类不能为空", trigger: "change" }
        ]
      },
      KeywordLibraryClassificationAddOrUpdateFormRules: {
        keywordLibraryClassificationName: [
          { required: true, message: "关键词库分类名称不能为空", trigger: "blur" }
        ],
      },
      keywordFormRules: {
        keywordName: [
          { required: true, message: "关键词名称不能为空", trigger: "blur" }
        ]
      },
      keywordLibraryClassificationAddOrUpdateForm: {
        keywordLibraryClassificationName: ''
      },
      keywordLibraryList: null,
      rule_group_mode: [
        { label: "全部", value: "1" },
        { label: "任一", value: "2" }
      ],
      activeKeywordLibraryClassification: {},
      keywordVisible: false,
      keywordList: [],
      keywordLoading: false,
      keywordNameVisible: false,
      keywordForm: {},
      keywordTitle: '',
      keywordDialog: false,
      activeKeywordLibraryId: null
    };
  },
  created() {
    this.getKeywordLibraryList();
    this.getQcKeywordLibraryClassificationList();
  },
  methods: {
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleReset":
          this.handleUpdate(row);
          break;
        case "handleDelete":
          this.handleDelete(row);
          break;
        default:
          break;
      }
    },
    getQcKeywordLibraryClassificationList() {
      this.keywordLibraryClassificationList = [];
      getQcKeywordLibraryClassificationList({}).then(response => {
        this.keywordLibraryClassificationList = response.data;
      });
    },
    submitKeywordLibraryClassificationAddOrUpdateForm: function () {
      this.$refs["keywordLibraryClassificationAddOrUpdateForm"].validate(valid => {
        if (valid) {
          if (this.keywordLibraryClassificationAddOrUpdateForm.id != undefined) {
            updateQcKeywordLibraryClassification(this.keywordLibraryClassificationAddOrUpdateForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getQcKeywordLibraryClassificationList();
              this.getKeywordLibraryList();
            });
          } else {
            addQcKeywordLibraryClassification(this.keywordLibraryClassificationAddOrUpdateForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getQcKeywordLibraryClassificationList();
              this.getKeywordLibraryList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      const keywordLibraryClassificationId = row.id;
      this.$modal.confirm('确定删除该分类吗').then(function () {
        return removeQcKeywordLibraryClassification(keywordLibraryClassificationId);
      }).then(() => {
        this.getQcKeywordLibraryClassificationList();
        this.getKeywordLibraryList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加关键词库分类";
    },
    handleUpdate(row) {
      this.reset();
      let keywordLibraryClassificationAddOrUpdateForm = JSON.parse(JSON.stringify(row))
      this.keywordLibraryClassificationAddOrUpdateForm = keywordLibraryClassificationAddOrUpdateForm
      this.open = true;
      this.title = "修改关键词库分类";
    },
    delKeywordLibrary(id) {
      removeQcKeywordLibrary(id).then(response => {
        this.getKeywordLibraryList();
      });
    },
    getKeywordLibraryList() {
      this.loading = true;
      const queryParams = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize
      };
      const payload = {
        keywordLibraryName: this.queryParams.keywordLibraryName,
        keywordLibraryClassificationId: this.activeKeywordLibraryClassification.id
      };

      getQcKeywordLibraryList(queryParams, payload).then(response => {
        this.keywordLibraryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.keywordLibraryClassificationAddOrUpdateForm = {};
      this.resetForm("keywordLibraryClassificationAddOrUpdateForm");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getKeywordLibraryList();
    },
    addKeywordLibrary() {
      this.resetKeywordLibrary();
      this.keywordLibraryDialog = true;
      if (this.activeKeywordLibraryClassification) {
        this.keywordLibraryForm.keywordLibraryClassificationId = this.activeKeywordLibraryClassification.id;
      }
      this.keywordLibraryTitle = "添加关键词库";
    },
    editKeywordLibrary(row) {
      this.keywordLibraryForm = row;
      this.keywordLibraryDialog = true;
      this.keywordLibraryTitle = "修改关键词库";
    },
    resetKeywordLibrary() {
      this.keywordLibraryForm = {};
      this.resetForm("keywordLibraryForm");
    },
    subKeywordLibraryForm() {
      this.$refs["keywordLibraryForm"].validate(valid => {
        if (valid) {
          if (this.keywordLibraryForm.id != undefined) {
            updateQcKeywordLibrary(this.keywordLibraryForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.keywordLibraryDialog = false;
              this.getKeywordLibraryList();
            });
          } else {
            addQcKeywordLibrary(this.keywordLibraryForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.keywordLibraryDialog = false;
              this.getKeywordLibraryList();
            });
          }
        }
      });
    },
    handleKeywordLibraryClassification(row) {
      if (row) {
        this.activeKeywordLibraryClassification = row;
      } else {
        this.activeKeywordLibraryClassification = {};
      }
      this.getKeywordLibraryList();
    },
    cancelKeywordLibraryForm() {
      this.keywordLibraryDialog = false;
      this.getKeywordLibraryList();
    },
    openKeyword(row) {
      this.keywordVisible = true;
      this.activeKeywordLibraryId = row.id;
      this.getKeywordList();
    },
    getKeywordList() {
      this.keywordLoading = true;
      const queryKeywordParams = {
        pageNum: this.queryKeywordParams.pageNum,
        pageSize: this.queryKeywordParams.pageSize
      };
      const payload = {
        keywordName: this.queryKeywordParams.keywordName,
        keywordLibraryId: this.activeKeywordLibraryId
      };

      if (payload.keywordLibraryId) {
        getQcKeywordList(queryKeywordParams, payload).then(response => {
          this.keywordList = response.rows;
          this.keywordTotal = response.total;
          this.keywordLoading = false;
        });
      } else {
        this.keywordList = [];
        this.keywordTotal = 0;
        this.keywordLoading = false;
      }
    },
    editKeyword(row) {
      this.keywordForm = row;
      this.keywordDialog = true;
      this.keywordTitle = "修改关键词";
    },
    delKeyword(id) {
      removeQcKeyword(id).then(response => {
        this.getKeywordList();
      });
    },
    addKeyword() {
      this.keywordForm = {};
      this.keywordDialog = true;
      this.keywordTitle = "添加关键词";
    },
    subKeywordForm() {
      this.$refs["keywordForm"].validate(valid => {
        if (valid) {
          this.keywordForm.keywordLibraryId = this.activeKeywordLibraryId;
          console.log(this.keywordForm.keywordLibraryClassificationId)
          if (this.keywordForm.id != undefined) {
            updateQcKeyword(this.keywordForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.keywordDialog = false;
              this.getKeywordList();
            });
          } else {
            addQcKeyword(this.keywordForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.keywordDialog = false;
              this.getKeywordList();
            });
          }
        }
      });
    },
    cancelKeywordForm() {
      this.keywordDialog = false;
      this.getKeywordList();
    },
    handleKeywordLibraryStatus(row) {
      this.keywordLibraryForm = row;
      updateQcKeywordLibrary(this.keywordLibraryForm).then(response => {
        if (row.status === '0') {
          this.$modal.msgSuccess("禁用成功");
        }
        if (row.status === '1') {
          this.$modal.msgSuccess("启用成功");
        }
        this.getKeywordLibraryList();
      });
    },
    handleKeywordStatus(row) {
      this.keywordForm = row;
      updateQcKeyword(this.keywordForm).then(response => {
        if (row.status === '0') {
          this.$modal.msgSuccess("禁用成功");
        }
        if (row.status === '1') {
          this.$modal.msgSuccess("启用成功");
        }
        this.getKeywordList();
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.sortItem {
  border-left: 2px solid white;
  font-size: 13px;
  padding: 8px 14px;
  cursor: pointer;
}

.sortItem:hover {
  border-left: 2px solid #409EFF;
  background: #ecf5ff;
}

div.sortItem.row.spaceBet.childItem.active-item {
  border-left: 2px solid #409EFF;
  background: #ecf5ff;
}

.childItem {
  padding-left: 30px;
}
</style>

<style scoped>
.keywordLibraryClassificationAddOrUpdateForm .el-form-item {
  width: 100%;
}

::v-deep .keywordLibraryClassificationAddOrUpdateForm .el-form-item .el-form-item__label {
  padding: 0px;
}

.keywordLibraryClassificationAddOrUpdateForm .el-select {
  width: 100%;
}

.keywordLibraryForm .el-form-item {
  width: 100%;
}

.keywordLibraryForm .el-select {
  width: 100%;
}

::v-deep .keywordLibraryForm .el-form-item .el-form-item__label {
  padding: 0px;
}

.keywordLibraryForm .scoringSelect .el-select {
  width: 100%;
}

.keywordForm .el-form-item {
  width: 100%;
}

::v-deep .keywordForm .el-form-item .el-form-item__label {
  padding: 0px;
}

.keywordForm .scoringSelect .el-select {
  width: 100%;
}
</style>