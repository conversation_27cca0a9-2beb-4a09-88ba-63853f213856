<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcBizAnswerMapper">
    
    <resultMap type="QcBizAnswer" id="QcBizAnswerResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="quesCode"    column="ques_code"    />
        <result property="ansValue"    column="ans_value"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectQcBizAnswerVo">
        select id, item_id, ques_code, ans_value, tenant_id, create_by, create_time, update_by, update_time, remark from qc_biz_answer
    </sql>

    <select id="selectQcBizAnswerList" parameterType="QcBizAnswer" resultMap="QcBizAnswerResult">
        <include refid="selectQcBizAnswerVo"/>
        <where>  
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="quesCode != null  and quesCode != ''"> and ques_code = #{quesCode}</if>
            <if test="ansValue != null  and ansValue != ''"> and ans_value = #{ansValue}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
        </where>
    </select>
    
    <select id="selectQcBizAnswerById" parameterType="Long" resultMap="QcBizAnswerResult">
        <include refid="selectQcBizAnswerVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcBizAnswer" parameterType="QcBizAnswer" useGeneratedKeys="true" keyProperty="id">
        insert into qc_biz_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="quesCode != null and quesCode != ''">ques_code,</if>
            <if test="ansValue != null">ans_value,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="quesCode != null and quesCode != ''">#{quesCode},</if>
            <if test="ansValue != null">#{ansValue},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            #{createTime}
         </trim>
    </insert>

    <update id="updateQcBizAnswer" parameterType="QcBizAnswer">
        update qc_biz_answer
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="quesCode != null and quesCode != ''">ques_code = #{quesCode},</if>
            <if test="ansValue != null">ans_value = #{ansValue},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcBizAnswerById" parameterType="Long">
        delete from qc_biz_answer where id = #{id}
    </delete>

    <delete id="deleteQcBizAnswerByIds" parameterType="String">
        delete from qc_biz_answer where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>