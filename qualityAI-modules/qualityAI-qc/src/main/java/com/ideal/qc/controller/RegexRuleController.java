package com.ideal.qc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.qc.domain.Question;
import com.ideal.qc.domain.ScoreItem;
import com.ideal.qc.domain.ZjRule;
import com.ideal.qc.service.IQualityInspectionService;
import com.ideal.qc.service.IScoreItemService;
import com.ideal.qc.service.IZjRuleService;
import io.swagger.v3.core.util.Json;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 题目信息操作处理
 * 
 * <AUTHOR>
@RestController
@RequestMapping("/regexRule")
public class RegexRuleController extends BaseController
{
    @Autowired
    private IZjRuleService zjRuleService;

    @Autowired
    private IScoreItemService scoreItemService;

    @Autowired
    private IQualityInspectionService qualityInspectionService;
    /**
     * 执行正则表达式规则
     */
    @PostMapping("/regex")
    public AjaxResult regex(Long scoreItemId, JSONObject content, Long callId)
    {
        if (scoreItemId==null){
            return AjaxResult.error("规则ID不能为空");
        }
        if (content==null){
            return AjaxResult.error("质检内容不能为空");
        }
        if (callId==null){
            return AjaxResult.error("callId不能为空");
        }
        ScoreItem scoreItem=scoreItemService.selectScoreItemById(scoreItemId);

        ZjRule zjRule=zjRuleService.selectZjRuleByRuleId(scoreItem.getRuleId());


        Boolean reslut=qualityInspectionService.performQualityCheck(content.toString(), zjRule.getDetectionContent());

        JSONObject result=new JSONObject();
        result.put("result",reslut);
        result.put("callId",callId);
        result.put("nature",scoreItem.getNature());
        result.put("ruleId",zjRule.getRuleId());
        result.put("scoreItemId",scoreItemId);
        return AjaxResult.success(result);


    }

}
