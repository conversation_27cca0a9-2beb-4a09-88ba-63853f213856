import request from '@/utils/request'

// 查询分类列表
export function classifyList(query) {
  return request({
    url: '/qc/manual/conf/classify/list',
    method: 'get',
    params: query
  })
}



// 新增分类
export function addClassify(data) {
  return request({
    url: '/qc/manual/conf/classify/add',
    method: 'post',
    data: data
  })
}

// 修改分类名称
export function updateClassify(data) {
  return request({
    url: '/qc/manual/conf/classify/edit',
    method: 'post',
    data: data
  })
}

// 删除质检规则
export function delClassify(classifyId) {
  return request({
    url: '/qc/manual/conf/classify/del/' + classifyId,
    method: 'get'
  })
}
