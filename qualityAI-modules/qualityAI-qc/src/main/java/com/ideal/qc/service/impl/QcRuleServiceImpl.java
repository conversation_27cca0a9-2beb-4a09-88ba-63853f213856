package com.ideal.qc.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.utils.DateUtils;
import com.ideal.common.redis.service.RedisService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.QcRuleMapper;
import com.ideal.qc.domain.QcRule;
import com.ideal.qc.domain.QcRuleCond;
import com.ideal.qc.service.IQcRuleService;

/**
 * 质检规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Service
public class QcRuleServiceImpl implements IQcRuleService
{
    @Autowired
    private QcRuleMapper QcRuleMapper;
    @Autowired
	private RedisService redisService;
    /**
     * 查询质检规则
     *
     * @param ruleId 质检规则主键
     * @return 质检规则
     */
    @Override
    public QcRule selectQcRuleByRuleId(Long ruleId)
    {
        return QcRuleMapper.selectQcRuleByRuleId(ruleId);
    }
    /**
     * 查询质检规则
     *
     * @param ruleId 质检规则主键
     * @return 质检规则
     */
    @Override
    public QcRule selectQcRule(QcRule QcRule)
    {
    	QcRule result = QcRuleMapper.selectQcRule(QcRule);
    	Map dRes = new HashMap();
    	if(QcRule.isDetail() && result != null) {
    		QcRuleCond qrc = new QcRuleCond();
    		qrc.setRuleId(QcRule.getRuleId());
    		qrc.setRuleCondType(1);
    		result.setFilterList(QcRuleMapper.selectQcRuleCondList(qrc));
    		qrc.setRuleCondType(2);
    		result.setHitList(QcRuleMapper.selectQcRuleCondList(qrc));
    		if("3".equals(result.getRuleType())) {
    			JSONObject tmp = new JSONObject();
    			tmp.put("parentId", result.getRuleId());
    			result.setSubList(QcRuleMapper.selectSubQcRuleList(tmp));
    		}
    	}
        return result;
    }

    /**
     * 查询质检规则列表
     *
     * @param QcRule 质检规则
     * @return 质检规则
     */
    @Override
    public List<QcRule> selectQcRuleList(JSONObject QcRule)
    {
        return QcRuleMapper.selectQcRuleList(QcRule);
    }

    /**
     * 新增质检规则
     *
     * @param QcRule 质检规则
     * @return 结果
     */
    @Override
    public int insertQcRule(QcRule qcRule)
    {
    	int result = 0;
    	qcRule.setCreateTime(DateUtils.getNowDate());
        result = QcRuleMapper.insertQcRule(qcRule);
        result = QcRuleMapper.insertQcRuleCond(qcRule);
        this.reloadRules(new Object[] {qcRule.getRuleId()});
        return result;
    }

    /**
     * 修改质检规则
     *
     * @param QcRule 质检规则
     * @return 结果
     */
    @Override
    public Long updateQcRule(QcRule qcRule)
    {
    	qcRule.setUpdateTime(DateUtils.getNowDate());
        Long result = 0L;
        if(qcRule.getRuleId() != null) {
        	QcRuleMapper.updateQcRule(qcRule);
        }else {
        	QcRuleMapper.insertQcRule(qcRule);
        	QcRuleMapper.insertQcRuleCond(qcRule);
    		result = qcRule.getRuleId();
        }
        this.reloadRules(new Object[] {qcRule.getRuleId(),qcRule.getParentId()});
        return result;
    }

    @PostConstruct
    @Override
    public void reloadRules() {
    	reloadRules(null);
    }
    public void reloadRules(Object[] ids) {
    	JSONObject qcRule = new JSONObject();
//    	qcRule.put("status",1);
    	qcRule.put("parentId",-1L);
    	qcRule.put("ids", ids);
    	JSONObject ret = new JSONObject();
    	List<QcRule> list = QcRuleMapper.selectQcRuleList(qcRule);
    	for(int i=0;i<list.size();i++) {
    		QcRuleCond qrc = new QcRuleCond();
    		qrc.setRuleId(list.get(i).getRuleId());
    		qrc.setRuleCondType(1);
    		list.get(i).setFilterList(QcRuleMapper.selectQcRuleCondList(qrc));
    		qrc.setRuleCondType(2);
    		list.get(i).setHitList(QcRuleMapper.selectQcRuleCondList(qrc));
    		list.get(i).setCreateBy(null);
    		list.get(i).setCreateTime(null);
    		list.get(i).setUpdateBy(null);
    		list.get(i).setUpdateTime(null);
    		list.get(i).setRemark(null);
    		if("3".equals(list.get(i).getRuleType())) {
    			JSONObject tmp = new JSONObject();
    			tmp.put("parentId", list.get(i).getRuleId());
    			list.get(i).setSubList(QcRuleMapper.selectSubQcRuleList(tmp));
    		}
    		if(ids != null) {
    			if(list.get(i).getStatus() == 1) {
    				redisService.setCacheMapValue("qcRule", list.get(i).getRuleId()+"",JSON.toJSONString(list.get(i)));
    			}else {
    				redisService.deleteCacheMapValue("qcRule", list.get(i).getRuleId()+"");
    			}
    			continue;
    		}
    		ret.put(list.get(i).getRuleId()+"", JSON.toJSONString(list.get(i)));
    	}
    	if(ids == null) {
    		redisService.setCacheMap("qcRule", ret);
    	}
    }
	@Override
	public List selectQcRuleRecording(JSONObject param) {
		return 	QcRuleMapper.selectQcRuleRecording(param);
	}
	
	@Override
	public List queryKeyWordLibList() {
		return QcRuleMapper.queryKeyWordLibList();
	}
}
