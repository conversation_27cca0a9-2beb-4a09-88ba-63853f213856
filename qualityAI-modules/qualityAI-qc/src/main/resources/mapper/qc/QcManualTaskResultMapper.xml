<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcManualTaskResultMapper">
    
    <resultMap type="com.ideal.qc.domain.QcManualTaskResult" id="QcManualTaskResultResult">
        <result property="id"    column="id"    />
        <result property="taskDetailId"    column="task_detail_id"    />
        <result property="itemName"    column="item_name"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="smartResult"    column="smart_result"    />
        <result property="recheckResult"    column="recheck_result"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="itemResult"    column="item_result"    />
    </resultMap>
    <sql id="selectQcManualTaskResultVo">
        select id, task_detail_id, item_name, rule_id, smart_result, recheck_result,create_time, create_by, update_time, update_by, rule_name, item_id,
               (SELECT d.scoring_template_name FROM qc_manual_task_detail t left join qc_scoring_template d on d.id=t.template_id WHERE t.id=task_detail_id) templateName,
               item_result
        from qc_manual_task_result q
    </sql>

    <select id="getRulesFromManualResult" resultMap="QcManualTaskResultResult">
        select
        distinct rule_id, rule_name
        from
        qc_manual_task_result;
    </select>

    <select id="getItemsFromManualResult" resultMap="QcManualTaskResultResult">
        select  distinct
        item_id,item_name
        from
        qc_manual_task_result
    </select>

    <select id="selectQcManualTaskResultList" parameterType="QcManualTaskResult" resultMap="QcManualTaskResultResult">
        select id, task_detail_id, item_name, rule_id, smart_result, recheck_result,create_time, create_by, update_time, update_by, rule_name, item_id,
        (SELECT d.scoring_template_name FROM qc_manual_task_detail t left join qc_scoring_template d on d.id=t.template_id WHERE t.id=task_detail_id) templateName,
        item_result
        from qc_manual_task_result q where EXISTS(SELECT * FROM qc_manual_task_detail t WHERE t.id=q.task_detail_id AND t.biz_no=#{bizNo})
    </select>
    
    <select id="selectQcManualTaskResultById" parameterType="Long" resultMap="QcManualTaskResultResult">
        <include refid="selectQcManualTaskResultVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcManualTaskResult" parameterType="QcManualTaskResult">
        insert into qc_manual_task_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskDetailId != null">task_detail_id,</if>
            <if test="ruleItemId != null">rule_item_id,</if>
            <if test="itemName != null">item_name,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="smartResult != null">smart_result,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="ruleName != null">rule_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskDetailId != null">#{taskDetailId},</if>
            <if test="ruleItemId != null">#{ruleItemId},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="smartResult != null">#{smartResult},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="ruleName != null">#{ruleName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="bizNo != null">#{bizNo},</if>
         </trim>
    </insert>

    <delete id="deleteQcManualTaskResultById" parameterType="Long">
        delete from qc_manual_task_result where id = #{id}
    </delete>

    <delete id="deleteQcManualTaskResultByIds" parameterType="String">
        delete from qc_manual_task_result where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>