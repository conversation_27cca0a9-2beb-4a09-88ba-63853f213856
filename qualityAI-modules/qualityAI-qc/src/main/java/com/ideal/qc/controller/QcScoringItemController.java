package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcScoringItem;
import com.ideal.qc.dto.QcScoringItemDTO;
import com.ideal.qc.service.QcScoringItemService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 评分细则表Controller接口
 *
 * <AUTHOR>
 * @date 2025/5/15 17:37
 */
@RestController
@RequestMapping("/scoring/item")
public class QcScoringItemController extends BaseController {

    @Resource
    private QcScoringItemService qcScoringItemService;

    /**
     * 查询评分细则列表
     */
    @RequiresPermissions("quality:scoringItem:list")
    @PostMapping("/list")
    public TableDataInfo getQcScoringItemList(@RequestBody JSONObject payload) {
        startPage();
        List<QcScoringItem> list = qcScoringItemService.getQcScoringItemList(payload);
        return getDataTable(list);
    }

    /**
     * 查询评分细则详情
     */
    @RequiresPermissions("quality:scoringItem:query")
    @PostMapping("/query/{scoringItemId}")
    public AjaxResult getQcScoringItem(@PathVariable("scoringItemId") Long scoringItemId) {
        QcScoringItemDTO result = qcScoringItemService.getQcScoringItem(scoringItemId);
        return AjaxResult.success(result);
    }

    /**
     * 新增评分细则
     */
    @RequiresPermissions("quality:scoringItem:add")
    @PostMapping("/add")
    public AjaxResult addQcScoringItem(@RequestBody JSONObject payload) {
        qcScoringItemService.addQcScoringItem(payload);
        return AjaxResult.success();
    }

    /**
     * 修改评分细则
     */
    @RequiresPermissions("quality:scoringItem:edit")
    @PostMapping("/update")
    public AjaxResult updateQcScoringItem(@RequestBody JSONObject payload) {
        qcScoringItemService.updateQcScoringItem(payload);
        return AjaxResult.success();
    }

    /**
     * 删除评分细则
     */
    @RequiresPermissions("quality:scoringItem:remove")
    @PostMapping("/remove/{scoringItemId}")
    public AjaxResult removeQcScoringItem(@PathVariable("scoringItemId") Long scoringItemId) {
        qcScoringItemService.removeQcScoringItem(scoringItemId);
        return AjaxResult.success();
    }

    /**
     * 修改评分细则状态
     */
    @RequiresPermissions("quality:scoringItem:edit")
    @PostMapping("/update/status")
    public AjaxResult updateQcScoringItemStatus(@RequestBody JSONObject payload) {
        qcScoringItemService.updateQcScoringItemStatus(payload);
        return AjaxResult.success();
    }
}
