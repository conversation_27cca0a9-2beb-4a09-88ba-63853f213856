<template>
  <el-card>
    <div ref="waveform_Ref" style="cursor: pointer"></div>
    <!-- 时间信息 -->
    <div class="btBox row">
        <div class="timeBox">{{ formatTime(currentTime) }}/{{ formatTime(duration) }}</div>
        <img v-if="!playing" src="../assets/images/stop.png" @click="playMusic" class="icon" alt="" />
        <img v-if="playing" src="../assets/images/play.png" @click="playMusic" class="icon" alt="" />
        <!-- 音量 -->
        <el-popover placement="top-start" trigger="click">
            <div class="block" style="width: 100px">
                <el-slider
                v-model="volume"
                :min="0"
                :max="1"
                :step="0.1"
                :format-tooltip="formatVolume"
                @change="changeVolume"
                style="width: 125px"
            ></el-slider>
            </div>
            <img slot="reference" class="voiceImg" src="../assets/images/voice.png" alt="" />
        </el-popover>

        <!-- 倍速 -->
        <el-select v-model="playbackRate"  @change="changeSpeed" size="mini" style="width: 100px">
            <el-option
                v-for="rate in speedOptions"
                :key="rate.value"
                :label="rate.label"
                :value="rate.value"
            ></el-option>
        </el-select>
    </div>


  </el-card>
</template>
 
<script>
import WaveSurfer from "wavesurfer.js";
 
export default {
  name: "WaveSurferPlayer",
  props: {
    audioSrc: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      wavesurfer: null,
      playing: false,
      duration: 0,
      currentTime: 0, // 当前播放时间

      volume: 0.8,
      playbackRate: 1,
      speedOptions: [
        { value: 0.5, label: '0.5x' },
        { value: 0.75, label: '0.75x' },
        { value: 1, label: '1x (正常)' },
        { value: 1.25, label: '1.25x' },
        { value: 1.5, label: '1.5x' },
        { value: 2, label: '2x' }
      ]

    };
  },
  mounted() {
    this.$nextTick(() => {
      this.wavesurfer = WaveSurfer.create({
        // 波形图的容器
        container: this.$refs.waveform_Ref,
        // 已播放波形的颜色
        progressColor: "#67C23A",
        // 未播放波形的颜色
        waveColor: "lightgrey",
        // 波形图的高度，单位为px
        // height: 10,
        // 是否显示滚动条，默认为false
        scrollParent: true,
        // 波形的振幅（高度），默认为1
        // barHeight: 0.8,
        // 波形条的圆角
        // barRadius: 2,
        // 波形条的宽度
        // barWidth: 1,
        // 波形条间的间距
        // barGap: 3
        // 播放进度光标条的颜色
        cursorColor: "red",
        // 播放进度光标条的宽度，默认为1
        // cursorWidth: 10,
        // 播放进度颜色
        // progressColor: "blue",
        //  波形容器的背景颜色
        // backgroundColor: "yellow",
        // 音频的播放速度
        // audioRate: "1",
        // （与区域插件一起使用）启用所选区域的循环
        // loopSelection:false
      });
      this.wavesurfer.on("error", (error) => {
        console.error("音频加载失败:", error);
        this.$message({
          type: "error",
          message: "音频加载失败，请检查文件路径或网络连接",
        });
      });
      this.wavesurfer.load(this.audioSrc);
      // 监听结束事件
      this.wavesurfer.on("finish", () => {
        this.playing = false;
      });
      // 监听播放进度变化（自然播放时更新）
      this.wavesurfer.on("audioprocess", (time) => {
        this.currentTime = time;
        this.$emit("time-update", this.currentTime);
      });
      // 监听用户停止拖动进度条，`seeking`事件会显示用户跳转到的时间点。
      this.wavesurfer.on("seeking", (time) => {
        this.currentTime = time;
        this.$emit("time-update", this.currentTime);
      });
      // 监听暂停事件
      this.wavesurfer.on("pause", () => {
        this.playing = false;
      });
      // 监听音频加载完成事件
      this.wavesurfer.on("ready", () => {
        this.duration = this.wavesurfer.getDuration();
      });
    });
  },
  methods: {
    // 格式化时间（秒 -> 分:秒）
    formatTime(time) {
      const minutes = Math.floor(time / 60);
      const seconds = Math.floor(time % 60);
      return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    },
    playMusic() {
      this.wavesurfer.playPause.bind(this.wavesurfer)();
      this.playing = !this.playing;
    },

    changeVolume() {
      this.wavesurfer.setVolume(this.volume);
    },
    changeSpeed() {
      this.wavesurfer.setPlaybackRate(Number(this.playbackRate));
    },
    formatVolume(value) {
      return `${Math.round(value * 100)}%`;
    },
    seekTo(time) {
      console.log(time,'time--')
      if (this.wavesurfer) {
        const duration = this.wavesurfer.getDuration();
        let ms = time /1000/ duration
        this.wavesurfer.seekTo(ms);
      }
     
    },
  },
  beforeDestroy() {
    // 销毁 Wavesurfer 实例
    if (this.wavesurfer) {
      this.wavesurfer.destroy();
    }
  },
};
</script>
<style lang="scss" scoped>
.timeBox {
  color: #666;
  margin-right: 20px;
}
.btBox{
    justify-content: center;
    .icon{
        width:35px;
        height: 35px;
        cursor: pointer;
        margin-right: 15px;
      
    }
}
.voiceImg{
    width:20px;
    height: 20px;
    cursor: pointer;
    margin-right: 10px;
    margin-top: 5px;
}
</style>