<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="所属平台" prop="platform">
        <el-input
          v-model="queryParams.platform"
          placeholder="请输入所属平台"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属项目" prop="project">
        <el-input
          v-model="queryParams.project"
          placeholder="请输入所属项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="指标名称" prop="targetName">
        <el-input
          v-model="queryParams.targetName"
          placeholder="请输入指标名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务中心" prop="serviceCenter">
        <el-input
          v-model="queryParams.serviceCenter"
          placeholder="请输入服务中心"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="坐席工号" prop="workNo">
        <el-input
          v-model="queryParams.workNo"
          placeholder="请输入坐席工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="坐席姓名" prop="workName">
        <el-input
          v-model="queryParams.workName"
          placeholder="请输入坐席姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-input
          v-model="queryParams.startTime"
          placeholder="请输入开始时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-input
          v-model="queryParams.endTime"
          placeholder="请输入结束时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="答卷编号" prop="qsNo">
        <el-input
          v-model="queryParams.qsNo"
          placeholder="请输入答卷编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电话" prop="telephone">
        <el-input
          v-model="queryParams.telephone"
          placeholder="请输入电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主键号" prop="onlyNo">
        <el-input
          v-model="queryParams.onlyNo"
          placeholder="请输入主键号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="省份" prop="province">
        <el-input
          v-model="queryParams.province"
          placeholder="请输入省份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入地市"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系号码" prop="contactPhone">
        <el-input
          v-model="queryParams.contactPhone"
          placeholder="请输入联系人号码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区号" prop="area">
        <el-input
          v-model="queryParams.area"
          placeholder="请输入区号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务单号" prop="serviceOrderNo">
        <el-input
          v-model="queryParams.serviceOrderNo"
          placeholder="请输入服务单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售点" prop="saleName">
        <el-input
          v-model="queryParams.saleName"
          placeholder="请输入销售点名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测评月份" prop="testMonth">
        <el-input
          v-model="queryParams.testMonth"
          placeholder="请输入测评月份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="呼叫结果" prop="callResult">
        <el-input
          v-model="queryParams.callResult"
          placeholder="请输入呼叫结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务结果" prop="taskResult">
        <el-input
          v-model="queryParams.taskResult"
          placeholder="请输入任务结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:data:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:data:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:data:remove']"
        >删除</el-button>
      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:data:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:data:edit']"
          >查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="所属平台" align="center" prop="platform"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="所属项目" align="center" prop="project"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="指标名称" align="center" prop="targetName"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="子指标名称" align="center" prop="targetSubName" show-overflow-tooltip min-width="90" />
      <el-table-column label="服务中心" align="center" prop="serviceCenter" show-overflow-tooltip min-width="90" />
      <el-table-column label="坐席工号" align="center" prop="agentId" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getName(scope.row.agentId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="坐席姓名" align="center" prop="agentName" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getName(scope.row.agentName) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="答卷开始时间" align="center" prop="startTime"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="答卷结束时间" align="center" prop="endTime"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="通话时长(s)" align="center" prop="longTime" show-overflow-tooltip min-width="90" />
      <el-table-column label="日期" align="center" prop="dataDate" show-overflow-tooltip min-width="90" />
      <el-table-column label="答卷编号" align="center" prop="qsNo"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="电话" align="center" prop="callerPhone" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getTel(scope.row.callerPhone) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center" prop="businessType"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="运营商" align="center" prop="carrier" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getName(scope.row.carrier) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="唯一主键号" align="center" prop="onlyNo" show-overflow-tooltip min-width="90" />
      <el-table-column label="省份" align="center" prop="province"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="地市" align="center" prop="city" show-overflow-tooltip min-width="90" />
      <el-table-column label="联系人号码" align="center" prop="contactPhone" show-overflow-tooltip width="100">
        <template slot-scope="scope">
          <span>{{ getTel(scope.row.contactPhone) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="区号" align="center" prop="area" show-overflow-tooltip min-width="90" />
      <el-table-column label="产品类型" align="center" prop="productType"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="服务订单编号" align="center" prop="serviceOrderNo"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="服务类型" align="center" prop="serviceType"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="竣工时间" align="center" prop="completionTime"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="产品名称" align="center" prop="productName"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="销售点名称" align="center" prop="saleName"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="序号" align="center" prop="seqNo" show-overflow-tooltip min-width="90" />
      <el-table-column label="测评月份" align="center" prop="testMonth" show-overflow-tooltip min-width="90" />
      <el-table-column label="呼叫结果" align="center" prop="callResult"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="任务结果" align="center" prop="taskResult"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="录音文件" align="center" prop="audioUrl" show-overflow-tooltip min-width="90" />
      <el-table-column label="来电时间" align="center" prop="incomingTime" show-overflow-tooltip min-width="90" />
      <el-table-column label="质检状态" align="center" prop="zjStatus" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改质检源数据对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="85%" append-to-body>
      <el-form ref="form" :model="form" size="small" :inline="true" :rules="rules" label-width="80px">
        <div class="video-container" style="padding-bottom: 10px;">
          <video
            controls
            :src="form.audioUrl"
            width="100%"
            height="auto"
            preload="auto"
            poster="封面图片URL(可选)"
            style="height: 40px;"
          >
            您的浏览器不支持HTML5视频标签
          </video>
        </div>
        <el-form-item label="所属平台" prop="platform">
          <el-input v-model="form.platform" placeholder="请输入所属平台" />
        </el-form-item>
        <el-form-item label="所属项目" prop="project">
          <el-input v-model="form.project" placeholder="请输入所属项目" />
        </el-form-item>
        <el-form-item label="指标名称" prop="targetName">
          <el-input v-model="form.targetName" placeholder="请输入指标名称" />
        </el-form-item>
        <el-form-item label="子指标" prop="targetSubName">
          <el-input v-model="form.targetSubName" placeholder="请输入子指标" />
        </el-form-item>
        <el-form-item label="服务中心" prop="serviceCenter">
          <el-input v-model="form.serviceCenter" placeholder="请输入服务中心" />
        </el-form-item>
        <el-form-item label="坐席工号" prop="workNo">
          <el-input v-model="form.workNo" placeholder="请输入坐席工号" />
        </el-form-item>
        <el-form-item label="坐席姓名" prop="workName">
          <el-input v-model="form.workName" placeholder="请输入坐席姓名" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-input v-model="form.startTime" placeholder="请输入答卷开始时间" />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-input v-model="form.endTime" placeholder="请输入答卷结束时间" />
        </el-form-item>
        <el-form-item label="通话时长" prop="longTime">
          <el-input v-model="form.longTime" placeholder="请输入通话时长(s)" />
        </el-form-item>
        <el-form-item label="日期" prop="dataDate">
          <el-input v-model="form.dataDate" placeholder="请输入日期" />
        </el-form-item>
        <el-form-item label="答卷编号" prop="qsNo">
          <el-input v-model="form.qsNo" placeholder="请输入答卷编号" />
        </el-form-item>
        <el-form-item label="电话" prop="telephone">
          <el-input v-model="form.telephone" placeholder="请输入电话" />
        </el-form-item>
        <el-form-item label="运营商" prop="serviceProvider">
          <el-input v-model="form.serviceProvider" placeholder="请输入运营商" />
        </el-form-item>
        <el-form-item label="主键号" prop="onlyNo">
          <el-input v-model="form.onlyNo" placeholder="请输入唯一主键号" />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="form.province" placeholder="请输入省份" />
        </el-form-item>
        <el-form-item label="地市" prop="city">
          <el-input v-model="form.city" placeholder="请输入地市" />
        </el-form-item>
        <el-form-item label="联系号码" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系人号码" />
        </el-form-item>
        <el-form-item label="区号" prop="area">
          <el-input v-model="form.area" placeholder="请输入区号" />
        </el-form-item>
        <el-form-item label="服务单号" prop="serviceOrderNo">
          <el-input v-model="form.serviceOrderNo" placeholder="请输入服务订单编号" />
        </el-form-item>
        <el-form-item label="竣工时间" prop="completionTime">
          <el-input v-model="form.completionTime" placeholder="请输入竣工时间" />
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="销售名称" prop="saleName">
          <el-input v-model="form.saleName" placeholder="请输入销售点名称" />
        </el-form-item>
        <el-form-item label="序号" prop="seqNo">
          <el-input v-model="form.seqNo" placeholder="请输入序号" />
        </el-form-item>
        <el-form-item label="测评月份" prop="testMonth">
          <el-input v-model="form.testMonth" placeholder="请输入测评月份" />
        </el-form-item>
        <el-form-item label="呼叫结果" prop="callResult">
          <el-input v-model="form.callResult" placeholder="请输入呼叫结果" />
        </el-form-item>
        <el-form-item label="任务结果" prop="taskResult">
          <el-input v-model="form.taskResult" placeholder="请输入任务结果" />
        </el-form-item>
        <el-form-item label="来电时间" prop="incomingTime">
          <el-input v-model="form.incomingTime" placeholder="请输入来电时间" />
        </el-form-item>
      </el-form>
      <div style="padding: 5px; font-weight: bold; color: rgb(110, 42, 10);  padding-bottom: 10px;  border-bottom: 1px solid #ccc;">评测列表</div>
      <el-table v-loading="loading" :data="evaluationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="left" prop="seq" width="55"/>
        <el-table-column label="问题" align="left" prop="question"  show-overflow-tooltip min-width="90"/>
        <el-table-column label="回答" align="left" prop="answer"  width="200"/>
      </el-table>
      <div slot="footer" class="dialog-footer">
       <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import { listData, getData, delData, addData, updateData } from "@/api/quality/data";
import {Utils} from "@/api/util/common";

export default {
  name: "Data",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 质检源数据表格数据
      dataList: [],
      // 质检评测表格数据
      evaluationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platform: null,
        project: null,
        targetName: null,
        targetSubName: null,
        serviceCenter: null,
        workNo: null,
        workName: null,
        startTime: null,
        endTime: null,
        longTime: null,
        dataDate: null,
        qsNo: null,
        telephone: null,
        businessType: null,
        serviceProvider: null,
        onlyNo: null,
        province: null,
        city: null,
        contactPhone: null,
        area: null,
        productType: null,
        serviceOrderNo: null,
        serviceType: null,
        completionTime: null,
        productName: null,
        saleName: null,
        seqNo: null,
        testMonth: null,
        callResult: null,
        taskResult: null,
        audioUrl: null,
        incomingTime: null,
        zjStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getTel(v){
      return Utils.getTel(v)
    },
    getName(v){
      return Utils.getName(v)
    },
    /** 查询质检源数据列表 */
    getList() {
      this.loading = true;
      listData(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    handleDetail(row){
      this.$router.push({ path: '/quality-check/index', query: { id: row.id,isEdit:0}})
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        platform: null,
        project: null,
        targetName: null,
        targetSubName: null,
        serviceCenter: null,
        workNo: null,
        workName: null,
        startTime: null,
        endTime: null,
        longTime: null,
        dataDate: null,
        qsNo: null,
        telephone: null,
        businessType: null,
        serviceProvider: null,
        onlyNo: null,
        province: null,
        city: null,
        contactPhone: null,
        area: null,
        productType: null,
        serviceOrderNo: null,
        serviceType: null,
        completionTime: null,
        productName: null,
        saleName: null,
        seqNo: null,
        testMonth: null,
        callResult: null,
        taskResult: null,
        audioUrl: null,
        incomingTime: null,
        zjStatus: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加质检源数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getData(id).then(response => {
        this.form = response.data;
        this.evaluationList = response.data.list;
        this.open = true;
        this.title = "查看质检源数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateData(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addData(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除质检源数据编号为"' + ids + '"的数据项？').then(function() {
        return delData(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/data/export', {
        ...this.queryParams
      }, `data_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
