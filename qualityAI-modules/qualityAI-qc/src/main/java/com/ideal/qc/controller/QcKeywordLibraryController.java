package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcKeywordLibrary;
import com.ideal.qc.service.QcKeywordLibraryService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 关键词库Controller接口
 *
 * <AUTHOR>
 * @date 2025/5/20 17:38
 */
@RestController
@RequestMapping("/keyword/library")
public class QcKeywordLibraryController extends BaseController {

    @Resource
    private QcKeywordLibraryService qcKeywordLibraryService;

    /**
     * 查询关键词库列表
     */
    @RequiresPermissions("quality:keywordLibrary:list")
    @PostMapping("/list")
    public TableDataInfo getQcKeywordLibraryList(@RequestBody JSONObject payload) {
        startPage();
        List<QcKeywordLibrary> list = qcKeywordLibraryService.getQcKeywordLibraryList(payload);
        return getDataTable(list);
    }

    /**
     * 新增关键词库
     */
    @RequiresPermissions("quality:keywordLibrary:add")
    @PostMapping("/add")
    public AjaxResult addQcKeywordLibrary(@RequestBody JSONObject payload) {
        qcKeywordLibraryService.addQcKeywordLibrary(payload);
        return AjaxResult.success();
    }

    /**
     * 修改关键词库
     */
    @RequiresPermissions("quality:keywordLibrary:edit")
    @PostMapping("/update")
    public AjaxResult updateQcKeywordLibrary(@RequestBody JSONObject payload) {
        qcKeywordLibraryService.updateQcKeywordLibrary(payload);
        return AjaxResult.success();
    }

    /**
     * 删除关键词库
     */
    @RequiresPermissions("quality:keywordLibrary:remove")
    @PostMapping("/remove/{id}")
    public AjaxResult removeQcKeywordLibrary(@PathVariable Long id) {
        qcKeywordLibraryService.removeQcKeywordLibrary(id);
        return AjaxResult.success();
    }
}
