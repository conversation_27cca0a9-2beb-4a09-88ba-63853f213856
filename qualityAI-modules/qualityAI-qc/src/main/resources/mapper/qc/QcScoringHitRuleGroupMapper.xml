<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcScoringHitRuleGroupMapper">

    <resultMap id="qcScoringHitRuleGroupResultMap" type="QcScoringHitRuleGroup">
        <result property="id"    column="id"    />
        <result property="scoringItemId"    column="scoring_item_id"    />
        <result property="ruleGroupMode" column="rule_group_mode" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="status" column="status" />
    </resultMap>
    <insert id="addScoringHitRuleGroup" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
            qc_scoring_hit_rule_group
        ( tenant_id, scoring_item_id, rule_group_mode, status, create_time, create_by, remark, bus_sort)
        VALUES
        ( #{tenantId}, #{scoringItemId}, #{ruleGroupMode}, #{status}, #{createTime}, #{createBy}, #{remark}, #{busSort})
    </insert>

    <update id="removeScoringHitRuleGroupBatch">
        UPDATE qc_scoring_hit_rule_group SET
            is_deleted = '1'
        WHERE id IN
        <foreach item="item" collection="qcScoringHitRuleGroupIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getQcScoringHitRuleGroupIds" resultType="java.lang.Long">
        SELECT
            qshrg.id
        FROM
            qc_scoring_hit_rule_group qshrg
        WHERE
            qshrg.scoring_item_id = #{scoringItemId}
          AND qshrg.is_deleted = '0'
        ORDER BY
            qshrg.id ASC
    </select>

    <update id="updateScoringHitRuleGroup">
        UPDATE qc_scoring_hit_rule_group SET
             rule_group_mode = #{ruleGroupMode},
             update_by = #{updateBy},
             update_time = #{updateTime},
             remark = #{remark},
             bus_sort = #{busSort},
             status = #{status}
        WHERE
            id = #{id}
    </update>
</mapper>