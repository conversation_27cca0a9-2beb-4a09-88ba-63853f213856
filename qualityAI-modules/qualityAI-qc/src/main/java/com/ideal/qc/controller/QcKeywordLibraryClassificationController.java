package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcKeywordLibraryClassification;
import com.ideal.qc.service.QcKeywordLibraryClassificationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 关键词库分类Controller接口
 *
 * <AUTHOR>
 * @date 2025/5/20 17:38
 */
@RestController
@RequestMapping("/keyword/library/classification")
public class QcKeywordLibraryClassificationController extends BaseController {

    @Resource
    private QcKeywordLibraryClassificationService qcKeywordLibraryClassificationService;

    /**
     * 查询关键词库分类列表
     */
    @RequiresPermissions("quality:keywordLibraryClassification:query")
    @PostMapping("/list")
    public AjaxResult getQcKeywordLibraryClassificationList(@RequestBody JSONObject payload) {
        List<QcKeywordLibraryClassification> result = qcKeywordLibraryClassificationService.getQcKeywordLibraryClassificationList(payload);
        return AjaxResult.success(result);
    }

    /**
     * 新增关键词库分类
     */
    @RequiresPermissions("quality:keywordLibraryClassification:add")
    @PostMapping("/add")
    public AjaxResult addQcKeywordLibraryClassification(@RequestBody JSONObject payload) {
        qcKeywordLibraryClassificationService.addQcKeywordLibraryClassification(payload);
        return AjaxResult.success();
    }

    /**
     * 修改关键词库分类
     */
    @RequiresPermissions("quality:keywordLibraryClassification:edit")
    @PostMapping("/update")
    public AjaxResult updateQcKeywordLibraryClassification(@RequestBody JSONObject payload) {
        qcKeywordLibraryClassificationService.updateQcKeywordLibraryClassification(payload);
        return AjaxResult.success();
    }

    /**
     * 删除关键词库分类
     */
    @RequiresPermissions("quality:keywordLibraryClassification:remove")
    @PostMapping("/remove/{id}")
    public AjaxResult removeQcKeywordLibraryClassification(@PathVariable Long id) {
        qcKeywordLibraryClassificationService.removeQcKeywordLibraryClassification(id);
        return AjaxResult.success();
    }
}
