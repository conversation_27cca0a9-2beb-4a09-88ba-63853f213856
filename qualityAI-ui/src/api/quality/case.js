import request from '@/utils/request'

// 查询案例列表
export function getQcCaseList(params, data) {
  return request({
    url: '/qc/case/list',
    method: 'post',
    params,
    data
  })
}

// 新增案例
export function addQcCase(data) {
  return request({
    url: '/qc/case/add',
    method: 'post',
    data: data
  })
}

// 修改案例
export function updateQcCase(data) {
  return request({
    url: '/qc/case/update',
    method: 'post',
    data: data
  })
}

// 查询案列的每个案列状态数量
export function getQcCaseEachCaseStatusQuantity(data) {
  return request({
    url: '/qc/case/each/caseStatus/quantity',
    method: 'post',
    data: data
  })
}