<template>
  <div class="app-container">
    <el-row :gutter="40">
      <!--部门数据-->
      <el-col :span="4" style="border-right:1px solid #F2F6FC;padding:0;">
        <div class="sortItem row spaceBet" @click="handleAllClassify">
          <div style="font-weight: 900;">全部分类</div>
          <i class="el-icon-plus" @click="handleAdd"></i>
        </div>
        <div class="sortChild">
          <div v-for="(item, index) in classifyList" :key="index" class="sortItem row spaceBet childItem"
            :class="{ active: item.classifyId === queryParams.classifyId }" @click="clickClassify(item)">
            <div>{{ item.classifyName }}</div>
            <el-dropdown size="mini" @command="(command) => handleCommand(command, item)">
              <i class="el-icon-more"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="handleReset" icon="el-icon-refresh-right">重命名</el-dropdown-item>
                <!-- <el-dropdown-item command="handleDelete" icon="el-icon-delete">删除</el-dropdown-item> -->
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px"
          class="row spaceBet" style="align-items:flex-start;">
          <el-form-item label="" prop="content">
            <el-input v-model="queryParams.content" placeholder="搜索规则名称" clearable style="width: 240px"
              @keyup.enter="handleQuery()">
              <!-- <el-button slot="append" icon="el-icon-search" @click="handleQuery()"></el-button> -->
            </el-input>
          </el-form-item>
          <el-form-item label="" prop="ruleId">
            <el-input v-model="queryParams.ruleId" placeholder="搜索规则id" clearable style="width: 240px"
              @keyup.enter="handleQuery()">
              <!-- <el-button slot="append" icon="el-icon-search" @click="handleQuery()"></el-button> -->
            </el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" clearable style="width: 240px">
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
          </el-form-item>

          <el-row :gutter="10">
            <!-- <el-col :span="1.5">
              <el-button type="info" plain icon="el-icon-upload2" size="small" @click="handleImport">导入</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="info" plain icon="el-icon-download" size="small" @click="handleExport">导出</el-button>
            </el-col> -->
            <el-col :span="1.5">
              <el-button type="primary" plain icon="el-icon-plus" size="small"
                @click="editRule(undefined)">新建规则</el-button>
            </el-col>
          </el-row>
        </el-form>
        <el-table :data="ruleList">
          <el-table-column label="规则ID" align="left" key="id" prop="id" />
          <el-table-column label="规则名称" align="left" key="ruleName" prop="ruleName" :show-overflow-tooltip="true" width="300"/>
          <el-table-column label="规则分组" align="left" key="ruleGroup" prop="ruleGroup" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              {{ computeGroup(scope.row.ruleGroup) }}
            </template>
          </el-table-column>
          <el-table-column label="规则类型" align="let" key="ruleType" prop="ruleType" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              {{ computeType(scope.row.ruleType) }}
            </template>
          </el-table-column>

          <el-table-column label="状态" align="left" key="status" prop="status" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <div :style="`color:${scope.row.status==1?'green':'red'}`">{{ scope.row.status == '1' ? '启用' : '禁用' }}</div>
            </template>
          </el-table-column>


          <el-table-column label="更新人" align="left" key="updateBy" prop="updateBy" :show-overflow-tooltip="true" />
          <el-table-column label="更新时间" align="left" key="updateTime" prop="updateTime" width="200" />
          <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="editRule(scope.row,'edit')" v-if="scope.row.status == '2'" style="margin-right:10px;">修改</el-button>
				<el-button size="mini" type="text" @click="editRule(scope.row,'view')" v-if="scope.row.status != '2'" style="margin-right:10px;">查看</el-button>
              <el-popconfirm :title="scope.row.status == 2 ? '你确定要启用吗？' : '你确定要禁用吗？'"
                @confirm="updateStatus(scope.row)">
                <el-button slot="reference" size="mini" type="text" :style="`color:${scope.row.status==2?'#1890ff':'red'}`">{{ scope.row.status == 2 ? '启用' : '禁用'
                  }}</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 添加或修改分类对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="classifyForm" :model="classifyForm" :rules="classifyRules" label-width="80px">
        <el-form-item label="分类名称" prop="classifyName">
          <el-input v-model="classifyForm.classifyName" placeholder="请输入分类名称" maxlength="30" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <!-- <el-button v-if="!form.id" type="primary">保存并新建下一个</el-button> -->
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 新建规则 -->
    <!--    <el-dialog :title="ruleTitle" :visible.sync="ruleDialog" width="500px" append-to-body>-->
    <!--      <el-form ref="ruleForm" :model="ruleForm" :rules="formRules" label-width="80px">-->
    <!--        <el-form-item label="规则名称" prop="ruleName">-->
    <!--          <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称" maxlength="30" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="规则分组" prop="ruleGroup">-->
    <!--          <el-select v-model="ruleForm.ruleGroup" placeholder="请选择" style="width:100%;">-->
    <!--            <el-option v-for="item in ruleGroups" :key="item.value" :label="item.label" :value="item.value"></el-option>-->
    <!--          </el-select>-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="规则类型" prop="ruleType">-->
    <!--          <el-select v-model="ruleForm.ruleType" placeholder="请选择" style="width:100%;">-->
    <!--            <el-option v-for="item in ruleTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>-->
    <!--          </el-select>-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="规则描述" prop="ruleDes">-->
    <!--          <el-input v-model="ruleForm.ruleDes" type="textarea" placeholder="请输入内容"></el-input>-->
    <!--        </el-form-item>-->

    <!--      </el-form>-->
    <!--      <div slot="footer" class="dialog-footer">-->
    <!--        <el-button type="primary" @click="subRuleForm">保 存</el-button>-->
    <!--        <el-button @click="ruleDialog = false">取 消</el-button>-->
    <!--      </div>-->
    <!--    </el-dialog>-->
    <el-dialog title="" :visible.sync="ruleDialog" width="500px" :close-on-press-escape="false" :show-close="false"
      :close-on-click-modal="false" custom-class="no-header-dialog" height="860px">
      <edit @getList="getList" ref="editRule"></edit>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRule, addRule, updateRule, delRule } from "@/api/quality/manualRule";
import { classifyList, addClassify, updateClassify, delClassify } from "@/api/quality/manualRuleClassify";
import { getToken } from "@/utils/auth";
import edit from './man/edit.vue'
export default {
  name: "User",
  components: {
    edit
  },
  props: {
    op: String
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      ruleList: null,
      // 弹出层标题
      title: "",
      open: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        content: null,
        ruleId: null,
        classifyId: null,
        status: null
      },
      // 分类表单参数
      classifyForm: {},
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/qc/manual/conf/importData/"
      },

      // 表单校验
      classifyRules: {
        classifyName: [
          { required: true, message: "分类名称不能为空", trigger: "blur" },
          { min: 2, max: 20, message: '分类名称长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],

      },
      classifyList: [],
      ruleForm: {},
      ruleDialog: false,
      ruleTitle: '',
      formRules: {
        ruleName: [
          { required: true, message: "规则名称不能为空", trigger: "blur" }
        ],
        ruleGroup: [
          { required: true, message: "规则分组不能为空", trigger: "change" }
        ],
        ruleType: [
          { required: true, message: "规则类型不能为空", trigger: "change" }
        ],
      },
      ruleGroups: [
        { label: '人工加分项', value: '1' },
        { label: '人工减分项', value: '2' }
      ],
      ruleTypes: [
        { label: '普通规则', value: '1' },
        { label: '致命规则', value: '2' }
      ],
      classifyFlag: false
    };
  },
  watch: {

  },
  created() {
    console.log(this.$route.path, 'path---')
    this.getClassifyList()
    this.getList();
  },


  methods: {

    computeGroup(val) {
      const items = this.ruleGroups.filter(item => item.value == val)
      if (items.length > 0) {
        return items[0].label
      } else {
        return val
      }
    },

    computeType(val) {
      const items = this.ruleTypes.filter(item => item.value == val)
      if (items.length > 0) {
        return items[0].label
      } else {
        return val
      }
    },


    handleAllClassify() {

      console.log('点击了全部分类')
      this.queryParams.classifyId = null
      this.getList()

    },

    //点击分类
    clickClassify(row) {
      console.log('点击了分类')
      this.classifyFlag = true
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.queryParams.classifyId = row.classifyId
      console.log('classifyRow', row)
      this.getList()
    },

    // 取消按钮
    cancel() {
      this.open = false;
      // this.reset();
    },
    // 表单重置
    reset() {
      this.classifyForm = {};
      this.resetForm("classifyForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleReset":
          this.handleUpdate(row);
          break;
        case "handleDelete":
          this.handleDelete(row);
          break;
        default:
          break;
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // this.handleQuery();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      let form = JSON.parse(JSON.stringify(row))
      this.classifyForm = form
      this.open = true;
      this.title = "修改分类名称";
      console.log('classifyForm', this.classifyForm)
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["classifyForm"].validate(valid => {
        if (valid) {
          if (this.classifyForm.classifyId != undefined) {
            updateClassify(this.classifyForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getClassifyList();
            });
          } else {
            addClassify(this.classifyForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getClassifyList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal.confirm('确定删除该分类吗').then(function () {
        return delClassify(row.classifyId);
      }).then(response => {
        if (response.code == 200) {
          this.getClassifyList();
          this.$modal.msgSuccess("删除成功");
        } else {
          this.$modal.notifyError(response.msg);
        }
      }).catch(() => { });
    },


    //查询分类列表
    getClassifyList() {
      classifyList().then(response => {
        this.classifyList = response.data
      }
      )
    },




    /**查询人工规则列表 */
    getList() {
      this.ruleDialog = false;
      this.loading = true;
      listRule(this.queryParams).then(response => {
        this.ruleList = response.rows;
        this.total = response.total;
      }
      ).finally(v => { this.loading = false; });
    },


    //启用禁用
    updateStatus(row) {
      row.status = (row.status == 1 ? 2 : 1)
      updateRule(row).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("更新成功");
          this.getList();
        }
      }).catch(error => {
        this.getList();
      });
    },

    //删除人工规则
    delRow(id) {
      delRule(id).then(res => {
        if (res.code == 200) {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }
      })
    },

    /** 新增人工规则 */
    addRule() {
      if (!this.queryParams.classifyId) {
        this.$message.error('请先选择分类');
        return
      }
      this.resetRule();
      this.ruleDialog = true;
      this.ruleTitle = "添加人工规则";
      this.ruleForm.classifyId = this.queryParams.classifyId
      console.log('ruleForm', this.ruleForm)
    },
    /** 修改人工规则 */
    editRule(row,op) {
      var tmp;
      if (!row && !this.queryParams.classifyId) {
        this.$message.error('请先选择分类');
        return
      }
      if (row) {
        tmp = row
      } else {
        tmp = { classifyId: this.queryParams.classifyId }
      }
      this.ruleDialog = true;
      this.$nextTick(() => {
        this.$refs.editRule.reload(tmp,op);
      })
    },
    resetRule() {
      this.ruleForm = {};
      this.resetForm("ruleForm");
    },

    //  提交新增/修改人工规则
    subRuleForm: function () {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          if (this.ruleForm.id != undefined) {
            updateRule(this.ruleForm).then(res => {
              if (res.code == 200) {
                this.$modal.msgSuccess("修改成功");
                this.ruleDialog = false;
                this.getList();
              }
            });
          } else {
            addRule(this.ruleForm).then(res => {
              if (res.code == 200) {
                this.$modal.msgSuccess("新增成功");
                this.ruleDialog = false;
                this.getList();
              }

            });
          }
        }
      });
    },


    /** 导出按钮操作 */
    handleExport() {
      this.download('qc/manual/conf/export', { ...this.queryParams }, `conf_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "导入规则";
      this.upload.open = true;
      this.upload.url = process.env.VUE_APP_BASE_API + "/qc/manual/conf/importData/" + "?classifyId=" + this.queryParams.classifyId

    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('qc/manual/conf/importTemplate', {
      }, `rule_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.sortItem {
  border-left: 2px solid white;
  font-size: 13px;
  padding: 8px 14px;
  cursor: pointer;
}

.sortItem:hover {
  border-left: 2px solid #409EFF;
  background: #ecf5ff;
}

.childItem {
  padding-left: 30px;
}


.active {
  background: #ecf5ff;
  border-left: 2px solid rgb(0, 255, 64);
}

.no-header-dialog .el-dialog__header {
  display: none;
}

.no-header-dialog .el-dialog__body {
  padding: 10px 10px;
}
.app-container{
	padding:5px;
}
</style>
