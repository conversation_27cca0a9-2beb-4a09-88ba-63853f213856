package com.ideal.qc.controller;

import com.alibaba.fastjson.JSONArray;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.Question;
import com.ideal.qc.domain.QuestionAnswer;
import com.ideal.qc.service.IQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 题目信息操作处理
 * 
 * <AUTHOR>
@RestController
@RequestMapping("/question")
public class Question<PERSON>ontroller extends BaseController
{
    @Autowired
    private IQuestionService questionService;

    /**
     * 获取题目列表
     */
    @RequiresPermissions("quality:question:list")
    @GetMapping("/list")
    public TableDataInfo list(Question question)
    {
        startPage();
        List<Question> list = questionService.selectQusList(question);
        return getDataTable(list);
    }

    @Log(title = "题目管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("quality:question:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, Question question)
    {
        List<Question> list = questionService.selectQusList(question);
        ExcelUtil<Question> util = new ExcelUtil<Question>(Question.class);
        util.exportExcel(response, list, "题目数据");
    }

    /**
     * 根据题目编号获取详细信息
     */
    @RequiresPermissions("quality:question:query")
    @GetMapping(value = "/{questionId}")
    public AjaxResult getInfo(@PathVariable Long questionId)
    {
        return success(questionService.selectQusById(questionId));
    }

    /**
     * 新增题目
     */
    @RequiresPermissions("quality:question:add")
    @Log(title = "题目管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody Question question)
    {
        if (!questionService.checkQusNameUnique(question))
        {
            return error("新增题目'" + question.getQusName() + "'失败，题目名称已存在");
        }
        else if (!questionService.checkQusCodeUnique(question))
        {
            return error("新增题目'" + question.getQusName() + "'失败，题目编码已存在");
        }
        question.setCreateBy(SecurityUtils.getUsername());
        questionService.insertQus(question);
        //新增细项
        List<QuestionAnswer> options = question.getOptions();
        for(int i = 0;i<options.size();i++){
            QuestionAnswer qa = options.get(i);
            qa.setQuestionId(question.getId());
            questionService.insertQusAnswer(qa);
        }
        return toAjax(1);
    }

    /**
     * 修改题目
     */
    @RequiresPermissions("quality:question:edit")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody Question question)
    {
        if (!questionService.checkQusNameUnique(question))
        {
            return error("修改题目'" + question.getQusName() + "'失败，题目名称已存在");
        }
        else if (!questionService.checkQusCodeUnique(question))
        {
            return error("修改题目'" + question.getQusName() + "'失败，题目编码已存在");
        }
        question.setUpdateBy(SecurityUtils.getUsername());
        questionService.updateQus(question);
        questionService.delQusAnswer(question.getId());
        //新增细项
        List<QuestionAnswer> options = question.getOptions();
        for(int i = 0;i<options.size();i++){
            QuestionAnswer qa = options.get(i);
            qa.setQuestionId(question.getId());
            questionService.insertQusAnswer(qa);
        }
        return toAjax(1);
    }

    /**
     * 删除题目
     */
    @RequiresPermissions("quality:question:remove")
    @Log(title = "题目管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable Long[] questionIds)
    {
        return toAjax(questionService.deleteQusByIds(questionIds));
    }

    /**
     * 获取题目选择框列表
     */
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        List<Question> questions = questionService.selectQusAll();
        return success(questions);
    }
}
