import request from '@/utils/request'

// 查询问卷列表
export function listQuestionnaire(query) {
  return request({
    url: '/qc/questionnaire/list',
    method: 'get',
    params: query
  })
}

// 查询问卷详细
export function getQuestionnaire(id) {
  return request({
    url: '/qc/questionnaire/' + id,
    method: 'get'
  })
}

// 新增问卷
export function addQuestionnaire(data) {
  return request({
    url: '/qc/questionnaire',
    method: 'post',
    data: data
  })
}
// 维护题目
export function updateQuestionnaireQus(data) {
  return request({
    url: '/qc/questionnaire/updateQuestionnaireQus',
    method: 'post',
    data: data
  })
}

// 修改问卷
export function updateQuestionnaire(data) {
  return request({
    url: '/qc/questionnaire',
    method: 'put',
    data: data
  })
}

// 删除问卷
export function delQuestionnaire(id) {
  return request({
    url: '/qc/questionnaire/' + id,
    method: 'delete'
  })
}
export function importTemplate() {
  return request({
    url: '/qc/questionnaire/importTemplate',
    method: 'get'
  })
}
