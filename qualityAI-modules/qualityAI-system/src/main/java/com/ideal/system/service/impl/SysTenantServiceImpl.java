package com.ideal.system.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.ideal.common.core.utils.DateUtils;
import com.ideal.system.domain.SysTenantMenu;
import com.ideal.system.domain.SysTenantPermi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.system.mapper.SysTenantMapper;
import com.ideal.system.domain.SysTenant;
import com.ideal.system.service.ISysTenantService;

import javax.annotation.Resource;

/**
 * 租户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Service
public class SysTenantServiceImpl implements ISysTenantService
{
    @Resource
    private SysTenantMapper sysTenantMapper;

    /**
     * 查询租户信息
     *
     * @param tenantId 租户信息主键
     * @return 租户信息
     */
    @Override
    public SysTenant selectSysTenantByTenantId(Long tenantId)
    {
        return sysTenantMapper.selectSysTenantByTenantId(tenantId);
    }

    /**
     * 查询租户信息列表
     *
     * @param sysTenant 租户信息
     * @return 租户信息
     */
    @Override
    public List<SysTenant> selectSysTenantList(SysTenant sysTenant)
    {
        return sysTenantMapper.selectSysTenantList(sysTenant);
    }

    /**
     * 新增租户信息
     *
     * @param sysTenant 租户信息
     * @return 结果
     */
    @Override
    public int insertSysTenant(SysTenant sysTenant)
    {
        sysTenant.setCreateTime(DateUtils.getNowDate());
        return sysTenantMapper.insertSysTenant(sysTenant);
    }

    /**
     * 修改租户信息
     *
     * @param sysTenant 租户信息
     * @return 结果
     */
    @Override
    public int updateSysTenant(SysTenant sysTenant)
    {
        sysTenant.setUpdateTime(DateUtils.getNowDate());
        return sysTenantMapper.updateSysTenant(sysTenant);
    }

    /**
     * 批量删除租户信息
     *
     * @param tenantIds 需要删除的租户信息主键
     * @return 结果
     */
    @Override
    public int deleteSysTenantByTenantIds(Long[] tenantIds)
    {
        return sysTenantMapper.deleteSysTenantByTenantIds(tenantIds);
    }

    /**
     * 删除租户信息信息
     *
     * @param tenantId 租户信息主键
     * @return 结果
     */
    @Override
    public int deleteSysTenantByTenantId(Long tenantId)
    {
        return sysTenantMapper.deleteSysTenantByTenantId(tenantId);
    }

    /**
     * 查询租户菜单列表
     *
     * @param tenantId 租户信息
     * @return 租户菜单列表集合
     */
    @Override
    public SysTenantPermi getPermiByTenantId(Long tenantId) {
        List<SysTenantMenu> menus = sysTenantMapper.selectMenus(tenantId);
        List<Long> checkedMenuIds = sysTenantMapper.selectMenuIdsByTenant(tenantId);
        SysTenantPermi sysTenantPermi = new SysTenantPermi();
        sysTenantPermi.setMenus(menus);
        sysTenantPermi.setCheckedMenuIds(checkedMenuIds);
        return sysTenantPermi;
    }

}
