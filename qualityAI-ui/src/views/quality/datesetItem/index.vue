<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="唯一编号" prop="bizNo">
        <el-input
          v-model="queryParams.bizNo"
          placeholder="请输入唯一编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据集" prop="datasetName">
        <el-input
          v-model="queryParams.datasetName"
          placeholder="请输入数据集"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="时长(秒)" prop="durationSec">
        <el-input
          v-model="queryParams.durationSec"
          placeholder="请输入时长(秒)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="通话时间" prop="callTime">
        <el-date-picker clearable
          v-model="queryParams.callTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择通话时间">
        </el-date-picker>
      </el-form-item>
<!--      <el-form-item label="租户id" prop="tenantId">
        <el-input
          v-model="queryParams.tenantId"
          placeholder="请输入租户id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['quality:datesetItem:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="datesetItemList" @selection-change="handleSelectionChange">
<!--      <el-table-column type="selection" width="55" align="center" />-->
<!--      <el-table-column label="id" align="center" prop="id" />-->
      <el-table-column label="数据集" align="center" prop="datasetName" />
      <el-table-column label="唯一编号" align="center" prop="bizNo"  show-overflow-tooltip/>
      <el-table-column label="时长(秒)" align="center" prop="durationSec"  show-overflow-tooltip/>
      <el-table-column label="通话时间" align="center" prop="callTime" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.callTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="语音识别状态" align="center" prop="asrStatus"  show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag :type="getAsrStatusLabel(scope.row.asrStatus)">
            {{ getAsrStatusLabel(scope.row.asrStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="语音识别结果" align="center" prop="asrResult"  show-overflow-tooltip/>
      <el-table-column label="租户" align="center" prop="tenantId"  show-overflow-tooltip/>
      <el-table-column label="文件路径" align="center" prop="fileId"  show-overflow-tooltip/>
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['quality:datesetItem:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['quality:datesetItem:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改语音数据明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="${comment}" prop="datasetId">
          <el-input v-model="form.datasetId" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="FastDFS 路径" prop="fileId">
          <el-input v-model="form.fileId" placeholder="请输入FastDFS 路径" />
        </el-form-item>
        <el-form-item label="时长(秒)" prop="durationSec">
          <el-input v-model="form.durationSec" placeholder="请输入时长(秒)" />
        </el-form-item>
        <el-form-item label="通话时间" prop="callTime">
          <el-date-picker clearable
            v-model="form.callTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择通话时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="语音返回" prop="asrResult">
          <el-input v-model="form.asrResult" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="租户id" prop="tenantId">
          <el-input v-model="form.tenantId" placeholder="请输入租户id" />
        </el-form-item>
        <el-form-item label="唯一编号" prop="bizNo">
          <el-input v-model="form.bizNo" placeholder="请输入唯一编号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDatesetItem, getDatesetItem, delDatesetItem, addDatesetItem, updateDatesetItem } from "@/api/quality/datesetItem";

export default {
  name: "DatesetItem",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 语音数据明细表格数据
      datesetItemList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        datasetId: null,
        fileId: null,
        durationSec: null,
        callTime: null,
        asrStatus: null,
        asrResult: null,
        tenantId: null,
        bizNo: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        datasetId: [
          { required: true, message: "$comment不能为空", trigger: "blur" }
        ],
        bizNo: [
          { required: true, message: "唯一编号不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询语音数据明细列表 */
    getList() {
      this.loading = true;
      listDatesetItem(this.queryParams).then(response => {
        this.datesetItemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getAsrStatusLabel(v){
      if(v=="wait"){
        return "新建";
      }else if(v=="run"){
        return "运行";
      }else if(v=="done"){
        return "完成";
      }else if(v=="fail"){
        return "失败";
      }else{
        return "";
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        datasetId: null,
        fileId: null,
        durationSec: null,
        callTime: null,
        asrStatus: null,
        asrResult: null,
        tenantId: null,
        createTime: null,
        bizNo: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加语音数据明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDatesetItem(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改语音数据明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDatesetItem(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDatesetItem(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除语音数据明细编号为"' + ids + '"的数据项？').then(function() {
        return delDatesetItem(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('qc/datesetItem/export', {
        ...this.queryParams
      }, `datesetItem_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
