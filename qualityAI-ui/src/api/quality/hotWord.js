import request from '@/utils/request'

// 查询ASR热词配置列表
export function listHotWord(query) {
  return request({
    url: '/qc/hotWord/list',
    method: 'get',
    params: query
  })
}

// 查询ASR热词配置树结构
export function treeHotWord(query) {
  return request({
    url: '/qc/hotWord/tree',
    method: 'get',
    params: query
  })
}

// 查询ASR热词配置详细
export function getHotWord(id) {
  return request({
    url: '/qc/hotWord/' + id,
    method: 'get'
  })
}

// 新增ASR热词配置
export function addHotWord(data) {
  return request({
    url: '/qc/hotWord',
    method: 'post',
    data: data
  })
}

// 修改ASR热词配置
export function updateHotWord(data) {
  return request({
    url: '/qc/hotWord',
    method: 'put',
    data: data
  })
}

// 删除ASR热词配置
export function delHotWord(id) {
  return request({
    url: '/qc/hotWord/' + id,
    method: 'delete'
  })
}

// 获取启用的热词字符串
export function getEnabledHotWords() {
  return request({
    url: '/qc/hotWord/enabled',
    method: 'get'
  })
}

// 校验热词名称
export function checkWordNameUnique(data) {
  return request({
    url: '/qc/hotWord/checkWordNameUnique',
    method: 'post',
    data: data
  })
}
