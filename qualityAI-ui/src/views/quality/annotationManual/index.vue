<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="88px">
      <el-form-item label="唯一主键号" prop="uniqueKey">
        <el-input
          v-model="queryParams.uniqueKey"
          placeholder="请输入唯一主键号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属平台" prop="platform">
        <el-input
          v-model="queryParams.platform"
          placeholder="请输入所属平台"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属项目" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入所属项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="指标名称" prop="metricName">
        <el-input
          v-model="queryParams.metricName"
          placeholder="请输入指标名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="子指标名称" prop="subMetricName">
        <el-input
          v-model="queryParams.subMetricName"
          placeholder="请输入子指标名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务中心" prop="serviceCenter">
        <el-input
          v-model="queryParams.serviceCenter"
          placeholder="请输入服务中心"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item label="坐席工号" prop="agentId">
        <el-input
          v-model="queryParams.agentId"
          placeholder="请输入坐席工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="坐席姓名" prop="agentName">
        <el-input
          v-model="queryParams.agentName"
          placeholder="请输入坐席姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="答卷开始时间" prop="answerStartAt">
        <el-date-picker clearable
                        v-model="queryParams.answerStartAt"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择答卷开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="答卷结束时间" prop="answerEndAt">
        <el-date-picker clearable
                        v-model="queryParams.answerEndAt"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择答卷结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="通话时长(秒)" prop="callDurationS">
        <el-input
          v-model="queryParams.callDurationS"
          placeholder="请输入通话时长(秒)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="日期" prop="recordDate">
        <el-date-picker clearable
                        v-model="queryParams.recordDate"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="答卷编号" prop="surveyCode">
        <el-input
          v-model="queryParams.surveyCode"
          placeholder="请输入答卷编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电话" prop="callerPhone">
        <el-input
          v-model="queryParams.callerPhone"
          placeholder="请输入电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="运营商" prop="carrier">
        <el-input
          v-model="queryParams.carrier"
          placeholder="请输入运营商"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="唯一主键号" prop="uniqueKey">
        <el-input
          v-model="queryParams.uniqueKey"
          placeholder="请输入唯一主键号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="省份" prop="province">
        <el-input
          v-model="queryParams.province"
          placeholder="请输入省份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入地市"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系人号码" prop="contactPhone">
        <el-input
          v-model="queryParams.contactPhone"
          placeholder="请输入联系人号码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区号" prop="areaCode">
        <el-input
          v-model="queryParams.areaCode"
          placeholder="请输入区号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务订单编码" prop="serviceOrderId">
        <el-input
          v-model="queryParams.serviceOrderId"
          placeholder="请输入服务订单编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="竣工时间" prop="finishTime">
        <el-date-picker clearable
                        v-model="queryParams.finishTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择竣工时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售点名称" prop="salesPoint">
        <el-input
          v-model="queryParams.salesPoint"
          placeholder="请输入销售点名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务结果" prop="taskResult">
        <el-input
          v-model="queryParams.taskResult"
          placeholder="请输入任务结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="来电时间" prop="callTime">
        <el-date-picker clearable
                        v-model="queryParams.callTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择来电时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="一级质检结果" prop="qcLvl1Result">
        <el-input
          v-model="queryParams.qcLvl1Result"
          placeholder="请输入一级质检结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级质检评语" prop="qcLvl1Comment">
        <el-input
          v-model="queryParams.qcLvl1Comment"
          placeholder="请输入一级质检评语"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级质检员工号" prop="qcLvl1EmpId">
        <el-input
          v-model="queryParams.qcLvl1EmpId"
          placeholder="请输入一级质检员工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="二级质检结果" prop="qcLvl2Result">
        <el-input
          v-model="queryParams.qcLvl2Result"
          placeholder="请输入二级质检结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="二级质检评语" prop="qcLvl2Comment">
        <el-input
          v-model="queryParams.qcLvl2Comment"
          placeholder="请输入二级质检评语"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="二级质检员工号" prop="qcLvl2EmpId">
        <el-input
          v-model="queryParams.qcLvl2EmpId"
          placeholder="请输入二级质检员工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="小结备注" prop="summaryRemark">
        <el-input
          v-model="queryParams.summaryRemark"
          placeholder="请输入小结备注"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="多租户" prop="tenantId">
        <el-input
          v-model="queryParams.tenantId"
          placeholder="请输入多租户"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="primary"-->
      <!--          plain-->
      <!--          icon="el-icon-plus"-->
      <!--          size="mini"-->
      <!--          @click="handleAdd"-->
      <!--          v-hasPermi="['quality:bizItem:add']"-->
      <!--        >新增</el-button>-->
      <!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="success"-->
      <!--          plain-->
      <!--          icon="el-icon-edit"-->
      <!--          size="mini"-->
      <!--          :disabled="single"-->
      <!--          @click="handleUpdate"-->
      <!--          v-hasPermi="['quality:bizItem:edit']"-->
      <!--        >修改</el-button>-->
      <!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="danger"-->
      <!--          plain-->
      <!--          icon="el-icon-delete"-->
      <!--          size="mini"-->
      <!--          :disabled="multiple"-->
      <!--          @click="handleDelete"-->
      <!--          v-hasPermi="['quality:bizItem:remove']"-->
      <!--        >删除</el-button>-->
      <!--      </el-col>-->
<!--      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['quality:bizItem:export']"
        >导出</el-button>
      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bizItemList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:bizItem:edit']"
          >标注</el-button>
        </template>
      </el-table-column>
<!--      <el-table-column label="所属平台" align="center" prop="platform" show-overflow-tooltip/>-->
      <el-table-column label="所属项目" align="center" prop="projectName" show-overflow-tooltip/>
      <el-table-column label="指标名称" align="center" prop="metricName" show-overflow-tooltip/>
      <el-table-column label="子指标名称" align="center" prop="subMetricName" show-overflow-tooltip/>
      <el-table-column label="服务中心" align="center" prop="serviceCenter" show-overflow-tooltip/>
      <el-table-column label="坐席工号" align="center" prop="agentId" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getName(scope.row.agentId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="坐席姓名" align="center" prop="agentName" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getName(scope.row.agentName) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="答卷开始时间" align="center" prop="answerStartAt" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.answerStartAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="答卷结束时间" align="center" prop="answerEndAt" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.answerEndAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="通话时长(秒)" align="center" prop="callDurationS" show-overflow-tooltip/>
      <el-table-column label="日期" align="center" prop="recordDate" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.recordDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="答卷编号" align="center" prop="surveyCode" show-overflow-tooltip/>
      <el-table-column label="电话" align="center" prop="callerPhone" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getTel(scope.row.callerPhone) }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="业务类型" align="center" prop="businessType" />
      <el-table-column label="运营商" align="center" prop="carrier" />
      <el-table-column label="唯一主键号" align="center" prop="uniqueKey" show-overflow-tooltip />
      <el-table-column label="省份" align="center" prop="province" />
      <el-table-column label="地市" align="center" prop="city" />
      <el-table-column label="联系人号码" align="center" prop="contactPhone" />
      <el-table-column label="区号" align="center" prop="areaCode" />
      <el-table-column label="产品类型" align="center" prop="productType" />
      <el-table-column label="服务订单编码" align="center" prop="serviceOrderId" />
      <el-table-column label="服务类型" align="center" prop="serviceType" />
      <el-table-column label="竣工时间" align="center" prop="finishTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.finishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="销售点名称" align="center" prop="salesPoint" />
      <el-table-column label="任务结果" align="center" prop="taskResult" />
      <el-table-column label="来电时间" align="center" prop="callTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.callTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="一级质检结果" align="center" prop="qcLvl1Result" />
      <el-table-column label="一级质检评语" align="center" prop="qcLvl1Comment" />
      <el-table-column label="一级质检员工号" align="center" prop="qcLvl1EmpId" />
      <el-table-column label="二级质检结果" align="center" prop="qcLvl2Result" />
      <el-table-column label="二级质检评语" align="center" prop="qcLvl2Comment" />
      <el-table-column label="二级质检员工号" align="center" prop="qcLvl2EmpId" />
      <el-table-column label="小结备注" align="center" prop="summaryRemark" />
      &lt;!&ndash;      <el-table-column label="动态问卷答案 (Q1-Q11, 服务质量多选等)" align="center" prop="answersJson" />&ndash;&gt;
      <el-table-column label="多租户" align="center" prop="tenantId" />-->

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改业务数据明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="所属数据集 qc_dataset.id" prop="datasetId">
          <el-input v-model="form.datasetId" placeholder="请输入所属数据集 qc_dataset.id" />
        </el-form-item>
        <el-form-item label="所属平台" prop="platform">
          <el-input v-model="form.platform" placeholder="请输入所属平台" />
        </el-form-item>
        <el-form-item label="所属项目" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入所属项目" />
        </el-form-item>
        <el-form-item label="指标名称" prop="metricName">
          <el-input v-model="form.metricName" placeholder="请输入指标名称" />
        </el-form-item>
        <el-form-item label="子指标名称" prop="subMetricName">
          <el-input v-model="form.subMetricName" placeholder="请输入子指标名称" />
        </el-form-item>
        <el-form-item label="服务中心" prop="serviceCenter">
          <el-input v-model="form.serviceCenter" placeholder="请输入服务中心" />
        </el-form-item>
        <el-form-item label="坐席工号" prop="agentId">
          <el-input v-model="form.agentId" placeholder="请输入坐席工号" />
        </el-form-item>
        <el-form-item label="坐席姓名" prop="agentName">
          <el-input v-model="form.agentName" placeholder="请输入坐席姓名" />
        </el-form-item>
        <el-form-item label="答卷开始时间" prop="answerStartAt">
          <el-date-picker clearable
                          v-model="form.answerStartAt"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择答卷开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="答卷结束时间" prop="answerEndAt">
          <el-date-picker clearable
                          v-model="form.answerEndAt"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择答卷结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="通话时长(秒)" prop="callDurationS">
          <el-input v-model="form.callDurationS" placeholder="请输入通话时长(秒)" />
        </el-form-item>
        <el-form-item label="日期" prop="recordDate">
          <el-date-picker clearable
                          v-model="form.recordDate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="答卷编号" prop="surveyCode">
          <el-input v-model="form.surveyCode" placeholder="请输入答卷编号" />
        </el-form-item>
        <el-form-item label="电话" prop="callerPhone">
          <el-input v-model="form.callerPhone" placeholder="请输入电话" />
        </el-form-item>
        <el-form-item label="运营商" prop="carrier">
          <el-input v-model="form.carrier" placeholder="请输入运营商" />
        </el-form-item>
        <el-form-item label="唯一主键号" prop="uniqueKey">
          <el-input v-model="form.uniqueKey" placeholder="请输入唯一主键号" />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="form.province" placeholder="请输入省份" />
        </el-form-item>
        <el-form-item label="地市" prop="city">
          <el-input v-model="form.city" placeholder="请输入地市" />
        </el-form-item>
        <el-form-item label="联系人号码" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系人号码" />
        </el-form-item>
        <el-form-item label="区号" prop="areaCode">
          <el-input v-model="form.areaCode" placeholder="请输入区号" />
        </el-form-item>
        <el-form-item label="服务订单编码" prop="serviceOrderId">
          <el-input v-model="form.serviceOrderId" placeholder="请输入服务订单编码" />
        </el-form-item>
        <el-form-item label="竣工时间" prop="finishTime">
          <el-date-picker clearable
                          v-model="form.finishTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择竣工时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="销售点名称" prop="salesPoint">
          <el-input v-model="form.salesPoint" placeholder="请输入销售点名称" />
        </el-form-item>
        <el-form-item label="任务结果" prop="taskResult">
          <el-input v-model="form.taskResult" placeholder="请输入任务结果" />
        </el-form-item>
        <el-form-item label="录音 FastDFS file_id" prop="recordingFile">
          <file-upload v-model="form.recordingFile"/>
        </el-form-item>
        <el-form-item label="来电时间" prop="callTime">
          <el-date-picker clearable
                          v-model="form.callTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择来电时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="一级质检结果" prop="qcLvl1Result">
          <el-input v-model="form.qcLvl1Result" placeholder="请输入一级质检结果" />
        </el-form-item>
        <el-form-item label="一级质检评语" prop="qcLvl1Comment">
          <el-input v-model="form.qcLvl1Comment" placeholder="请输入一级质检评语" />
        </el-form-item>
        <el-form-item label="一级质检员工号" prop="qcLvl1EmpId">
          <el-input v-model="form.qcLvl1EmpId" placeholder="请输入一级质检员工号" />
        </el-form-item>
        <el-form-item label="二级质检结果" prop="qcLvl2Result">
          <el-input v-model="form.qcLvl2Result" placeholder="请输入二级质检结果" />
        </el-form-item>
        <el-form-item label="二级质检评语" prop="qcLvl2Comment">
          <el-input v-model="form.qcLvl2Comment" placeholder="请输入二级质检评语" />
        </el-form-item>
        <el-form-item label="二级质检员工号" prop="qcLvl2EmpId">
          <el-input v-model="form.qcLvl2EmpId" placeholder="请输入二级质检员工号" />
        </el-form-item>
        <el-form-item label="小结备注" prop="summaryRemark">
          <el-input v-model="form.summaryRemark" placeholder="请输入小结备注" />
        </el-form-item>
        <el-form-item label="多租户" prop="tenantId">
          <el-input v-model="form.tenantId" placeholder="请输入多租户" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBizItem, getBizItem, delBizItem, addBizItem, updateBizItem } from "@/api/quality/bizItem";
import {Utils} from "@/api/util/common";

export default {
  name: "BizItem",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 业务数据明细表格数据
      bizItemList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        datasetId: null,
        platform: null,
        projectName: null,
        metricName: null,
        subMetricName: null,
        serviceCenter: null,
        agentId: null,
        agentName: null,
        answerStartAt: null,
        answerEndAt: null,
        callDurationS: null,
        recordDate: null,
        surveyCode: null,
        callerPhone: null,
        businessType: null,
        carrier: null,
        uniqueKey: null,
        province: null,
        city: null,
        contactPhone: null,
        areaCode: null,
        productType: null,
        serviceOrderId: null,
        serviceType: null,
        finishTime: null,
        productName: null,
        salesPoint: null,
        taskResult: null,
        recordingFile: null,
        callTime: null,
        qcLvl1Result: null,
        qcLvl1Comment: null,
        qcLvl1EmpId: null,
        qcLvl2Result: null,
        qcLvl2Comment: null,
        qcLvl2EmpId: null,
        summaryRemark: null,
        answersJson: null,
        tenantId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        datasetId: [
          { required: true, message: "所属数据集 qc_dataset.id不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getTel(v){
      return Utils.getTel(v)
    },
    getName(v){
      return Utils.getName(v)
    },
    /** 查询业务数据明细列表 */
    getList() {
      this.loading = true;
      listBizItem(this.queryParams).then(response => {
        this.bizItemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleDetail(row){
      //this.$router.push({ path: '/quality-detail/index', query: {bizNo:row.uniqueKey,type:"3"}}) //type,1为人工质检，2为人工复检,3为详情
      this.$router.push({ path: '/quality-annotation-check/annotation', query: {bizNo:row.uniqueKey,type:"3"}})
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        datasetId: null,
        platform: null,
        projectName: null,
        metricName: null,
        subMetricName: null,
        serviceCenter: null,
        agentId: null,
        agentName: null,
        answerStartAt: null,
        answerEndAt: null,
        callDurationS: null,
        recordDate: null,
        surveyCode: null,
        callerPhone: null,
        businessType: null,
        carrier: null,
        uniqueKey: null,
        province: null,
        city: null,
        contactPhone: null,
        areaCode: null,
        productType: null,
        serviceOrderId: null,
        serviceType: null,
        finishTime: null,
        productName: null,
        salesPoint: null,
        taskResult: null,
        recordingFile: null,
        callTime: null,
        qcLvl1Result: null,
        qcLvl1Comment: null,
        qcLvl1EmpId: null,
        qcLvl2Result: null,
        qcLvl2Comment: null,
        qcLvl2EmpId: null,
        summaryRemark: null,
        answersJson: null,
        tenantId: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加业务数据明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getBizItem(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改业务数据明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateBizItem(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBizItem(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除业务数据明细编号为"' + ids + '"的数据项？').then(function() {
        return delBizItem(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('qc/bizItem/export', {
        ...this.queryParams
      }, `bizItem_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style>
.el-table .cell {
  white-space: nowrap;
}
</style>
