package com.ideal.qc.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.exception.ServiceException;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcKeyword;
import com.ideal.qc.mapper.QcKeywordMapper;
import com.ideal.qc.service.QcKeywordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 关键词Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/20 17:46
 */
@Service
public class QcKeywordServiceImpl implements QcKeywordService {

    @Resource
    private QcKeywordMapper qcKeywordMapper;

    /**
     * 查询关键词列表
     */
    @Override
    public List<QcKeyword> getQcKeywordList(JSONObject payload) {
        Long keywordLibraryId = payload.getLong("keywordLibraryId");
        if (ObjectUtil.isEmpty(keywordLibraryId)) {
            throw new ServiceException("未选中关键词库");
        }
        return qcKeywordMapper.getQcKeywordList(payload);
    }

    /**
     * 新增关键词
     */
    @Override
    public void addQcKeyword(JSONObject payload) {
        Long keywordLibraryId = payload.getLong("keywordLibraryId");
        if (ObjectUtil.isEmpty(keywordLibraryId)) {
            throw new ServiceException("未选中关键词库");
        }
        String keywordName = payload.getString("keywordName");
        if (ObjectUtil.isEmpty(keywordName)) {
            throw new ServiceException("关键词名称不允许为空");
        }
        payload.put("createTime", DateTime.now());
        payload.put("createBy", SecurityUtils.getUsername());
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        payload.put("tenantId", SecurityUtils.getTenantId());
        qcKeywordMapper.addQcKeyword(payload);
    }

    /**
     * 修改关键词
     */
    @Override
    public void updateQcKeyword(JSONObject payload) {
        Long keywordLibraryId = payload.getLong("keywordLibraryId");
        if (ObjectUtil.isEmpty(keywordLibraryId)) {
            throw new ServiceException("未选中关键词库");
        }
        String keywordName = payload.getString("keywordName");
        if (ObjectUtil.isEmpty(keywordName)) {
            throw new ServiceException("关键词名称不允许为空");
        }
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        qcKeywordMapper.updateQcKeyword(payload);
    }

    /**
     * 删除关键词
     */
    @Override
    public void removeQcKeyword(Long id) {
        qcKeywordMapper.removeQcKeyword(id);
    }

    /**
     * 查询关键词列表根据关键词库id
     */
    @Override
    public List<QcKeyword> getQcKeywordByKeywordIds(Long id) {
        return qcKeywordMapper.getQcKeywordByKeywordIds(id);
    }
}
