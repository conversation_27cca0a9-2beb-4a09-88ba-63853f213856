package com.ideal.qc.service.impl;

import java.util.List;
import com.ideal.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.ZjDataAiresultMapper;
import com.ideal.qc.domain.ZjDataAiresult;
import com.ideal.qc.service.IZjDataAiresultService;

/**
 * AI质检结果Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@Service
public class ZjDataAiresultServiceImpl implements IZjDataAiresultService 
{
    @Autowired
    private ZjDataAiresultMapper zjDataAiresultMapper;

    /**
     * 查询AI质检结果
     * 
     * @param id AI质检结果主键
     * @return AI质检结果
     */
    @Override
    public ZjDataAiresult selectZjDataAiresultById(Long id)
    {
        return zjDataAiresultMapper.selectZjDataAiresultById(id);
    }

    /**
     * 查询AI质检结果列表
     * 
     * @param zjDataAiresult AI质检结果
     * @return AI质检结果
     */
    @Override
    public List<ZjDataAiresult> selectZjDataAiresultList(ZjDataAiresult zjDataAiresult)
    {
        return zjDataAiresultMapper.selectZjDataAiresultList(zjDataAiresult);
    }

    /**
     * 新增AI质检结果
     * 
     * @param zjDataAiresult AI质检结果
     * @return 结果
     */
    @Override
    public int insertZjDataAiresult(ZjDataAiresult zjDataAiresult)
    {
        zjDataAiresult.setCreateTime(DateUtils.getNowDate());
        return zjDataAiresultMapper.insertZjDataAiresult(zjDataAiresult);
    }

    /**
     * 修改AI质检结果
     * 
     * @param zjDataAiresult AI质检结果
     * @return 结果
     */
    @Override
    public int updateZjDataAiresult(ZjDataAiresult zjDataAiresult)
    {
        return zjDataAiresultMapper.updateZjDataAiresult(zjDataAiresult);
    }

    /**
     * 批量删除AI质检结果
     * 
     * @param ids 需要删除的AI质检结果主键
     * @return 结果
     */
    @Override
    public int deleteZjDataAiresultByIds(Long[] ids)
    {
        return zjDataAiresultMapper.deleteZjDataAiresultByIds(ids);
    }

    /**
     * 删除AI质检结果信息
     * 
     * @param id AI质检结果主键
     * @return 结果
     */
    @Override
    public int deleteZjDataAiresultById(Long id)
    {
        return zjDataAiresultMapper.deleteZjDataAiresultById(id);
    }
}
