package com.ideal.qc.service;

import java.util.List;
import java.util.Map;

import com.ideal.qc.domain.ZjDataAiresult;
import com.ideal.qc.domain.ZjDataReview;
import com.ideal.qc.domain.ZjDataReviewData;

/**
 * 人工复检Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
public interface IZjDataReviewService 
{
    /**
     * 查询人工复检
     * 
     * @param id 人工复检主键
     * @return 人工复检
     */
    public ZjDataReview selectZjDataReviewById(Long id);

    /**
     * 查询人工复检列表
     * 
     * @param zjDataReview 人工复检
     * @return 人工复检集合
     */
    public List<ZjDataReview> selectZjDataReviewList(ZjDataReview zjDataReview);

    /**
     * 新增人工复检
     * 
     * @param zjDataReview 人工复检
     * @return 结果
     */
    public int insertZjDataReview(ZjDataReview zjDataReview);

    /**
     * 修改人工复检
     * 
     * @param zjDataReview 人工复检
     * @return 结果
     */
    public int updateZjDataReview(ZjDataReview zjDataReview);

    /**
     * 批量删除人工复检
     * 
     * @param ids 需要删除的人工复检主键集合
     * @return 结果
     */
    public int deleteZjDataReviewByIds(Long[] ids);

    /**
     * 删除人工复检信息
     * 
     * @param id 人工复检主键
     * @return 结果
     */
    public int deleteZjDataReviewById(Long id);

    List<ZjDataReviewData> selectReviewData(ZjDataReviewData zjDataReviewData);

    List<ZjDataAiresult> selectAiResultData(ZjDataReviewData zjDataReviewData);

    List<ZjDataAiresult> selectAiTemplateData(ZjDataReviewData zjDataReviewData);

    void completeZjDataReview(Map<String,Object> map);

    List<ZjDataReview> getReviewListByDataId(Long dataId);
}
