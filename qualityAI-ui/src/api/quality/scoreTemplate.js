import request from '@/utils/request'

// 查询评分模板列表
export function listScoretpl(query) {
  return request({
    url: '/qc/scoretpl/list',
    method: 'get',
    params: query
  })
}

// 查询评分模板详细
export function getScoretpl(id) {
  return request({
    url: '/qc/scoretpl/' + id,
    method: 'get'
  })
}

// 新增评分模板
export function addScoretpl(data) {
  return request({
    url: '/qc/scoretpl',
    method: 'post',
    data: data
  })
}

// 修改评分模板
export function updateScoretpl(data) {
  return request({
    url: '/qc/scoretpl',
    method: 'put',
    data: data
  })
}

// 删除评分模板
export function delScoretpl(id) {
  return request({
    url: '/qc/scoretpl/' + id,
    method: 'delete'
  })
}
