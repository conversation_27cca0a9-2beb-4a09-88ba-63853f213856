<template>
  <div class="app-container" style="max-height:1060px;overflow:auto">
    <div>
		<div class="el-dialog-title dy-title">{{title}}</div>
		<el-button v-if="op=='edit'" class="btn" type="primary" @click="subRuleForm">保 存</el-button>
		<el-button class="btn" @click="cancel">取 消</el-button>
    </div>
    <el-divider class="dy-divider"></el-divider>
	<el-form ref="ruleForm" :model="ruleForm" :rules="formRules" label-width="80px">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称" maxlength="30" :disabled="op == 'view'"/>
        </el-form-item>
        <el-form-item label="规则分组" prop="ruleGroup">
          <el-select v-model="ruleForm.ruleGroup" placeholder="请选择" style="width:100%;" :disabled="op == 'view'">
            <el-option v-for="item in ruleGroups" :key="item.value" :label="item.label" :value="item.value" ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规则类型" prop="ruleType">
          <el-select v-model="ruleForm.ruleType" placeholder="请选择" style="width:100%;" :disabled="op == 'view'">
            <el-option v-for="item in ruleTypes" :key="item.value" :label="item.label" :value="item.value" ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规则描述" prop="ruleDes">
          <el-input v-model="ruleForm.ruleDes" type="textarea" placeholder="请输入内容" :disabled="op == 'view'"></el-input>
        </el-form-item>
      </el-form>
  </div>
</template>

<script>
import { listRule, addRule, updateRule } from "@/api/quality/manualRule";
export default {
	name: "manEdit",
	data() {
    	return {
			info:{id:""},
			op:'edit',
			title:"",
			ruleForm: {},
			formRules: {
				ruleName: [
					{ required: true, message: "规则名称不能为空", trigger: "blur" }
				],
				ruleGroup: [
					{ required: true, message: "规则分组不能为空", trigger: "change" }
				],
				ruleType: [
					{ required: true, message: "规则类型不能为空", trigger: "change" }
				],
			},
			ruleGroups: [
				{ label: '人工加分项', value: '1' },
				{ label: '人工减分项', value: '2' }
			],
			ruleTypes: [
	        	{ label: '普通规则', value: '1' },
	        	{ label: '致命规则', value: '2' }
			],
		};
	},
	created() {
	},
	methods: {
		reload(query,op){
			this.ruleForm = {};
			this.$refs.ruleForm.resetFields();
			this.info = {};
			if(op){this.op = op}
			if(query){
				this.info = query;
			}
			if(this.op == 'view'){
				this.title = "查看人工规则";
			}else{
				this.title = this.info.id?'编辑人工规则':'新增人工规则';
			}
			if(!this.info.id || this.info.id ==''){
				this.ruleForm['classifyId'] = this.info.classifyId;
				return;
			}
			listRule({ruleId:this.info.id}).then(response => {
			        this.ruleForm = response.rows[0];
		      	}
		    );

		},
	//  提交新增/修改人工规则
		subRuleForm: function () {
			this.$refs["ruleForm"].validate(valid => {
	        	if (valid) {
					if (this.ruleForm.id != undefined) {
	            		updateRule(this.ruleForm).then(res => {
							if (res.code == 200) {
				                this.$modal.msgSuccess("修改成功");
				                this.goBack();
							}
						});
					} else {
	            		addRule(this.ruleForm).then(res => {
							if (res.code == 200) {
				                this.$modal.msgSuccess("新增成功");
				                this.goBack();
							}

						});
					}
				}
			});
		},
		goBack(){
			this.$emit("getList");
			this.op = 'edit';
		},
		cancel(){
			this.goBack();
			this.op = 'edit';
		}
	}
};
</script>
