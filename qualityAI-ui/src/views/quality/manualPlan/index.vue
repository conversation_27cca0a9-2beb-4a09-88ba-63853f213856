<template>



    <div class="app-container">

        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">

            <el-form-item label="创建时间">
                <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" size="small"
                    class="date-range-picker" clearable></el-date-picker>
            </el-form-item>
            <el-form-item label="计划名称" prop="">
                <el-input v-model="queryParams.planName" placeholder="请输入计划名称" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <!-- <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button> -->
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini"
                    @click="addTimingPlan('1')">新建定时计划</el-button>
            </el-col>
        </el-row>

        <el-table :data="planList">
            <el-table-column label="计划名称" align="center" prop="planName" />
            <el-table-column label="计划类型" align="center" prop="planType">
                <template slot-scope="scope">
                    {{ scope.row.planType == '1' ? '语音任务 | 定时分配' : '' }}
                </template>
            </el-table-column>
            <el-table-column label="计划状态" align="center" prop="status">
                <template slot-scope="scope">
                    {{ scope.row.status == '0' ? '禁用' : '启用' }}
                </template>
            </el-table-column>

            <el-table-column label="计划创建时间" align="center" prop="createTime" />
            <el-table-column label="最近一次运行时间" align="center" prop="updateTime" />
            <el-table-column label="创建任务包含的数据数量" align="center" prop="dataCount" />
            <el-table-column label="已选座席数量" align="center" prop="seatCount" />

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
                <template slot-scope="scope" v-if="scope.row.userId !== 1">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="editPlan(scope.row)"
                        v-if="scope.row.status == '0'" style="margin-right:10px;">修改</el-button>
                    <el-popconfirm :title="scope.row.status == 0 ? '你确定要启用吗？' : '你确定要禁用吗？'"
                        @confirm="updateStatus(scope.row)">
                        <el-button slot="reference" size="mini" type="text">{{ scope.row.status == 0 ? '启用' :
                            '禁用' }}</el-button>
                    </el-popconfirm>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-if="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getManualPlanList" />


        <!-- 新建定时计划 -->
        <el-dialog :title="dialogFlag == 'add' ? '新建定时计划' : '修改定时计划'" :visible.sync="dialogVisible" width="800px" :close-on-click-modal="false">

            <el-form :model="planForm" label-width="100px" :rules="planFormRules" ref="planFormRef"
                label-position="top">
                <!-- <el-form-item> -->
                <div class="section-title">基础设置</div>
                <!-- </el-form-item> -->

                <!-- 计划名称 -->
                <el-form-item label="计划名称:" prop="planName" class="form-item">
                    <el-input v-model="planForm.planName" placeholder="" clearable></el-input>
                </el-form-item>

                <!-- 计划类型 -->
                <el-form-item label="计划类型:" prop="planType" class="form-item">
                    <el-select v-model="planForm.planType" placeholder="请选择计划类型" class="type-select" >
                        <el-option label="语音任务 | 定时分配" value="1"></el-option>
                    </el-select>
                </el-form-item>

                <!-- 检查类型 -->
                <el-form-item label="人工质检类型:" prop="manualType" class="form-item" >
                    <el-select v-model="planForm.manualType" class="type-select" :disabled="dialogFlag == 'edit'" @change="changeManualType">
                        <el-option label="人工抽检" value="1"></el-option>
                        <el-option label="人工复检" value="2"></el-option>
                    </el-select>
                </el-form-item>

                <!-- 评分模板 -->
                <el-form-item label="评分模板:" prop="templateId" v-show="planForm.manualType == '1'">
                    <el-select v-model="planForm.templateId" placeholder="请选择模板">
                        <el-option v-for="item in templateIdOptions" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>

                <!-- <el-form-item> -->
                <div class="section-title">数据范围</div>
                <!-- </el-form-item> -->

                <!-- 数据范围 -->
                <el-form-item label="时间范围:" prop="dataRange" class="compact-form-item">
                    <div class="data-range-container">
                        <span class="inline-label">抽取分配前</span>
                        <el-select v-model="planForm.dataRange" class="compact-select" placeholder="请选择时间范围"
                            style="width: 150px;" clearable>
                            <el-option v-for="item in dataRangeOptions" :key="item.value" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                        <span class="inline-label">内的数据</span>
                    </div>
                </el-form-item>

                <!-- <el-form-item label="时间范围:" prop="dataRange" class="compact-form-item"
                    v-show="planForm.manualType == '2'">
                    <div class="data-range-container">
                        <span class="inline-label">抽取分配前</span>
                        <el-select v-model="planForm.dataRange" class="compact-select" placeholder="请选择时间范围"
                            style="width: 150px;" clearable>
                            <el-option v-for="item in dataRangeOptions" :key="item.value" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                        <span class="inline-label">内的机检任务</span>
                    </div>
                </el-form-item> -->


                      <!-- 人工质检抽检数量 -->
                <el-form-item label="抽检数量:"  v-show="planForm.manualType == '1'">
                    <div class="session-extract-container">
                        <div class="extract-line">
                            <el-select v-model="planForm.extractType" class="compact-select type-select" clearable>
                                <el-option label="按条数" value="count"></el-option>
                                <el-option label="按百分比" value="percent"></el-option>
                            </el-select>
                            <span class="extract-label">抽取</span>
                            <el-input v-model="planForm.extractValue" class="value-input" type="number" clearable>
                                <template #suffix >
                                    <span class="percentage-suffix">
                                        <span v-if="planForm.extractType === 'percent'">%</span>
                                        <span v-if="planForm.extractType === 'count'">条</span>
                                    </span>
                                </template>
                            </el-input>
                            
                        </div>
                    </div>
                </el-form-item>

                <!-- 机检计划 -->
                <el-form-item label="机检计划:" prop="smartPlan" v-show="planForm.manualType == '2'">
                    <el-select v-model="planForm.smartPlan" placeholder="请选择计划" @change="handleSmartPlan" >
                        <el-option v-for="item in smartPlanOptions" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>


                <!-- 命中规则 -->
                <el-form-item label="命中规则:" v-show="planForm.manualType == '2'">
                    <el-select v-model="planForm.hitRules" multiple @change="handleHitRules">
                        <el-option :key="'all'"  :label="'全选'" :value="allValue"></el-option>
                        <el-option v-for="item in ruleOptions" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>


                <!-- <el-form-item> -->
                <div class="section-title">任务分配</div>
                <!-- </el-form-item> -->

                <!-- 分配时间 -->
                <el-form-item label="分配时间:" prop="allocateTime">
                    <el-checkbox-group v-model="planForm.weekDays">
                        <el-checkbox v-for="day in weekDaysOptions" :key="day.value" :label="day.value">{{
                            day.label }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>


                <!-- 分配质检员 -->
                <el-form-item label="质检员:" prop="inspectors">
                    <el-select v-model="planForm.inspectors" multiple @change="handleInspectorChange">
                        <el-option v-for="item in inspectorOptions" :key="item.userName" :label="item.nickName"
                            :value="item.userName"></el-option>
                    </el-select>
                    <div class="selected-count">已选择 {{ planForm.inspectors.length }} 项</div>
                </el-form-item>

                <!-- 调整质检员比例 -->
                <el-form-item label="调整质检员比例:" :class="{ 'error': totalRatio !== 100 }">
                    <!-- <div class="ratio-switch">
                        <el-switch v-model="showRatioAdjustment"></el-switch>
                        <span class="switch-label">比例调整</span>
                    </div> -->

                    <div v-for="(item, index) in dynamicRatios" :key="index" class="ratio-item">
                        <span class="name">{{ showNickName(item.name) }}</span>
                        <el-input v-model="item.ratio" type="number" :min="0" :max="100" class="ratio-input"
                            @change="handleRatioChange">
                            <template #suffix>
                                <span class="percentage-suffix">%</span>
                            </template>
                        </el-input>
                    </div>
                    <div class="ratio-total">
                        当前分配比例合计：{{ totalRatio }}%
                        <span v-if="totalRatio !== 100" class="error-msg">（比例总和需等于100%）</span>
                    </div>
                </el-form-item>
            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>

import { manualListPlan, addPlan, smartPlanList, getSmartPlanById, getAllRulesById, getAllQcTemplate, getAllUser, updatePlanStatus, updatePlan } from "@/api/quality/manualPlan";

export default {
    name: 'ManualPlan',
    data() {
        return {
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                planName: null,
                dateRange: ['', '']
            },
            // 列表总条数
            total: 3,
            planList: [],    //计划列表
            //新建计划
            dialogVisible: false,
            dialogFlag: '',
            //新建计划表单字段
            planForm: {
                planName: '',
                planType: '1',
                //人工类型 (1:人工抽检 2:人工复检)
                manualType: '1',
                dataRange: '',
                extractType: '', //抽取方式
                extractValue: null,//抽取数量
                smartPlan: null,
                templateId: null,
                hitRules: [],
                weekDays: [],
                inspectors: [],
            },

            //是否分配质检员比例
            showRatioAdjustment: false,
            //质检员比例
            dynamicRatios: [],
            //评分模板
            templateIdOptions: [],
            //数据范围
            dataRangeOptions: [
                { label: '1小时', value: '1' },
                { label: '2小时', value: '2' },
                { label: '4小时', value: '3' },
                { label: '8小时', value: '4' },
                { label: '24小时', value: '5' },
                { label: '7天', value: '6' },
            ],
            //机检计划
            smartPlanOptions: [],
            //命中规则
            ruleOptions: [],
            allValue:'all', //全选命中规则
            //分配时间
            weekDaysOptions: [
                { label: '周一', value: '1' },
                { label: '周二', value: '2' },
                { label: '周三', value: '3' },
                { label: '周四', value: '4' },
                { label: '周五', value: '5' },
                { label: '周六', value: '6' },
                { label: '周日', value: '7' },
            ],
            //质检员
            inspectorOptions: [],
            planFormRules: {}
        }
    },
    computed: {
        totalRatio() {
            return Number(this.dynamicRatios.reduce((sum, item) => sum + Number(item.ratio || 0), 0).toFixed(2))
        }
    },

    watch: {

    },

    created() {
        this.getManualPlanList()
        this.smartPlanList()
        this.getAllQcTemplate()
        this.getAllUser()
    },

    methods: {

        //启用/禁用计划
        updateStatus(row) {
            console.log('row', row)
            row.status = (row.status == 0 ? 1 : 0)
            updatePlanStatus(row).then(res => {
                if (res.code == 200) {
                    this.$modal.msgSuccess("更新成功");
                    this.getManualPlanList();
                }
            }).catch(error => {
                this.getManualPlanList();
            });
        },

        //新建计划时清空表单相关字段
        addTimingPlan() {
            this.dialogFlag = 'add'
            this.dialogVisible = true
            this.planForm.planName = ''
            this.planForm.manualType = '1'
            this.planForm.dataRange = ''
            this.planForm.extractType = ''
            this.planForm.extractValue = null
            this.planForm.smartPlan = null
            this.planForm.templateId = null
            this.ruleOptions = []
            this.planForm.hitRules = []
            this.planForm.weekDays = []
            this.planForm.inspectors = []
            this.dynamicRatios = []
        },

        //切换人工质检类型时，将相关数据清空，防止页面展示出现错误
        changeManualType() {
            console.log('切换了质检类型')
            this.planForm.smartPlan = null
            this.planForm.templateId = null
            this.ruleOptions = []
            this.planForm.hitRules = []
        },


        //修改任务
        editPlan(row) {
            this.dialogFlag = 'edit'
            console.log('row', row)
            this.dialogVisible = true
            this.planForm.id = row.id
            this.planForm.planName = row.planName
            this.planForm.manualType = row.manualType
            this.planForm.dataRange = row.dataRange
            this.planForm.extractType = row.extractType
            this.planForm.extractValue = row.extractValue
            this.planForm.templateId = row.templateId
            this.planForm.smartPlan = row.smartPlan
            this.planForm.weekDays = JSON.parse(row.weekDays)
            const parsedData = JSON.parse(row.inspectorRate)
            this.planForm.inspectors = parsedData.map(item => item.name)
            this.dynamicRatios = JSON.parse(row.inspectorRate)
            //如果是人工复检且选择了机检计划，则先查出该计划对应的模板下的规则列表，再赋值当前任务所选的命中规则，保证页面镇正确回显
            if (row.manualType == 2 && row.smartPlan && row.templateId) {
                this.getRuleOptionsAsync(row.templateId, row.hitRules)
            }
            console.log('planForm', this.planForm)
        },

        async getRuleOptionsAsync(id, hitRules) {
            const response = await getAllRulesById(id)
            const data = response.data
            this.ruleOptions = data.rules.map(item => ({
                label: item.name,
                value: item.id
            }));
            this.planForm.hitRules = JSON.parse(hitRules)
        },

        //获取智能质检计划列表
        smartPlanList() {
            smartPlanList({}).then(response => {
                const data = response.data
                this.smartPlanOptions = data.map(item => ({
                    label: item.planName,
                    value: item.id
                }));
                console.log('smartPlanOptions', this.smartPlanOptions)
            }
            );
        },

        //选择智能质检计划时，查询对应的模板id
        handleSmartPlan(value) {
            this.ruleOptions = []
            this.planForm.hitRules = []
            this.planForm.templateId = null


            console.log('value', value)
            if (value) {
                getSmartPlanById(value).then(response => {
                    this.planForm.templateId = response.data.scoreTemplate
                    //根据模板id查询对应的规则
                    this.getAllRulesById(this.planForm.templateId)
                }
                )
            }
        },

        //根据模板id查询所有的规则
        getAllRulesById(id) {
            getAllRulesById(id).then(response => {
                const data = response.data
                this.ruleOptions = data.rules.map(item => ({
                    label: item.name,
                    value: item.id
                }));
                console.log('ruleOptions', this.ruleOptions)
            }
            )
        },

         //选择命中规则
        handleHitRules(value){
             console.log('selectedValue',value)
            // 判断是否选择了全选选项
            if (value.includes(this.allValue)) {
                // 过滤掉全选选项，添加所有实际选项
                this.planForm.hitRules = this.ruleOptions.map(item => item.value);
            } 
             console.log('ruleOptions',this.planForm.hitRules)
        },

        //查询所有状态正常的模板
        getAllQcTemplate() {
            getAllQcTemplate().then(response => {
                const data = response.data
                this.templateIdOptions = data.map(item => ({
                    label: item.scoringTemplateName,
                    value: item.id
                }));
                console.log('templateIdOptions', this.templateIdOptions)
            }
            )
        },


        //获取所有的质检员
        getAllUser() {
            getAllUser().then(response => {
                const data = response.data
                this.inspectorOptions = data.map(item => ({
                    userName: item.userName,
                    nickName: item.nickName
                }));
                console.log('inspectorOptions', this.inspectorOptions)
            }
            )
        },

        //获取人工质检计划列表
        getManualPlanList() {
            manualListPlan(this.queryParams).then(response => {
                this.planList = response.rows;
                this.total = response.total;
                this.loading = false;
            }
            );
        },


        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getManualPlanList();
        },

        showNickName(val) {
            const inspector = this.inspectorOptions.find(item => item.userName === val);
            return inspector ? inspector.nickName : '未找到';
        },

        updateDynamicRatios(inspectors) {
            const count = inspectors.length
            const avgRatio = count > 0 ? (100 / count).toFixed(2) : 0
            this.dynamicRatios = inspectors.map(name => ({
                name,
                ratio: avgRatio
            }))
        },
        handleRatioChange() {
            const total = this.dynamicRatios.reduce((sum, item) => sum + Number(item.ratio || 0), 0)
            if (total !== 100) {
                const remaining = 100 - total
                // 可添加自动分配剩余比例的逻辑
            }
        },
        handleInspectorChange() {
            this.updateDynamicRatios(this.planForm.inspectors)
        },

        //提交新增计划
        submitForm() {

            if (!this.planForm.planName) {
                this.$message.error('请填写计划名称')
                return
            }

            //判断人工抽检
            if (this.planForm.manualType == 1) {
                if (!this.planForm.templateId) {
                    this.$message.error('请选择评分模板')
                    return
                }
                  if (!this.planForm.extractType || !this.planForm.extractValue ) {
                    this.$message.error('请选择抽取数量')
                    return
                }
                 if (!(this.planForm.extractValue > 0)) {
                    this.$message.error('抽取数量不得小于0')
                    return
                }
                if(this.planForm.extractType == 'percent' && this.planForm.extractValue > 100){
                    this.$message.error('百分比不得大于100')
                    return
                }
            }


             if (!this.planForm.dataRange) {
                this.$message.error('请选择时间范围')
                return
            }

            //判断人工复检
            if (this.planForm.manualType == 2) {
                if (!this.planForm.smartPlan) {
                    this.$message.error('请选择机检计划')
                    return
                }
            }
            
           

            if (!(this.planForm.weekDays.length > 0)) {
                this.$message.error('请选择分配时间')
                return
            }

            if (!(this.planForm.inspectors.length > 0)) {
                this.$message.error('请选择质检员')
                return
            }
            if (!(this.totalRatio == 100)) {
                this.$message.error('质检员比例总和需等于100%')
                return
            }

            var payload = this.planForm
            payload.inspectorRate = this.dynamicRatios
            console.log('payload', payload)
            if (this.dialogFlag == 'add') {
                addPlan(payload).then(response => {
                    if (response.code == 200) {
                        this.$modal.msgSuccess("新增成功");
                        this.dialogVisible = false;
                        this.getManualPlanList()
                    }

                });
            } else {
                updatePlan(payload).then(response => {
                    if (response.code == 200) {
                        this.$modal.msgSuccess("修改成功");
                        this.dialogVisible = false;
                        this.getManualPlanList()
                    }
                });
            }


        }
    }
}
</script>

<style scoped>
.flex-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.data-range-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.inline-label {
    font-size: 14px;
    color: #606266;
}

.compact-select {
    width: 120px;
}

.compact-select ::v-deep .el-input__inner {
    height: 32px;
    line-height: 32px;
}

.session-extract-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.extract-line {
    display: flex;
    align-items: center;
    gap: 8px;
}

.value-input {
    width: 120px;
}

.value-input ::v-deep input::-webkit-outer-spin-button,
.value-input ::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.value-input ::v-deep input[type="number"] {
    -moz-appearance: textfield;
}

.percentage-suffix {
    color: #606266;
    padding-right: 8px;
}

.ratio-switch {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.ratio-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 20px;
}

.ratio-input {
    width: 120px;
}

.error-msg {
    color: #f56c6c;
}

.selected-count {
    color: #909399;
    font-size: 12px;
    margin-top: 5px;
}


/* demo */
.complete-time-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.number-input {
    width: 100px;
}

.unit-select {
    width: 120px;
}

.prefix-text,
.suffix-text,
.unit-text {
    color: #606266;
    font-size: 14px;
    white-space: nowrap;
}

/* 调整输入框按钮样式 */
.number-input ::v-deep .el-input-number__decrease,
.number-input ::v-deep .el-input-number__increase {
    background: #f5f7fa;
    height: 14px;
    line-height: 14px;
}

.number-input ::v-deep .el-input__inner {
    padding: 0 35px;
    text-align: center;
}


.section-title {
    font-size: 18px;
    font-weight: bold;
    /* 下划线样式，可按需调整 */
    border-bottom: 1px solid #ccc;
    padding-bottom: 8px;
    margin-bottom: 10px;
    margin-top: 40px;
}
</style>
