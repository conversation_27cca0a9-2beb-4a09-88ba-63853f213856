<template>
  <div class="app-container">
    <div class="app-main">
      <el-page-header @back="goBack" :content="isEdit?'人工复检':'录音详情'">
      </el-page-header>
      <el-divider class="topLine"></el-divider>

      <WaveSurferPlayer  ref="audioPlayer" :audioSrc="audioUrl"
      @time-update="handleTimeUpdate"
      @duration="handleDurationUpdate"/>

      <div class="conMain row">
        <div class="leftMain">
          <!--{{currentTime}}-->
          <el-card>
            <div slot="header" class="card-tit">
              <span>智能转译文本</span>
            </div>
            <editable-text-annotation
              @child-to-parent="handleChildData"
              :parentData="parentData"
              ref="annotator"
            >
            <div class="card-body chatMain" v-if="msgList.length">
              <div :class="!item.spk?'chatBox chatMy':'chatBox'" v-for="(item,index) in msgList" :key="index" ref="itemRefs">

                <img v-if="item.spk" src="@/assets/images/agent.png" mode="" />
                <div v-if="!item.spk" class="chatTime row">
                  <!-- {{convertHours(item.ed-item.bg)}} {{item.bg}}-{{item.ed}}-->
                  <div>{{convertHours(item.ed-item.bg)}}</div><img class="icon" src="@/assets/images/triangle.png" />
                </div>

                <div :style="{textAlign:(!item.spk?'right':'left')}" class="msg" :class="{'blueText': isActive(item) }" @click="jumpToTime(item.bg,item.ed,$event)">
                    <span v-html="highlightTitle(item.text)" @contextmenu.prevent="selectAllText($event,item.bg,item.ed)" class="selectable-text"></span>
                </div>

                <div v-if="item.spk" class="chatTime row">
                  <div>{{convertHours(item.ed-item.bg)}}</div><img class="icon" src="@/assets/images/triangle.png" />
                </div>

                <img v-if="!item.spk" src="@/assets/images/user.png" mode="" />
              </div>
            </div>
            </editable-text-annotation>
          </el-card>

          <el-card style="margin-top:10px;">
            <div slot="header" class="card-tit">
              <span>录音信息</span>
            </div>
            <el-descriptions class="card-body" title="" :column="3" >
              <el-descriptions-item label="所属平台">{{info.platform}}</el-descriptions-item>
              <el-descriptions-item label="所属项目">{{info.project}}</el-descriptions-item>
              <el-descriptions-item label="指标名称">{{info.targetName}}</el-descriptions-item>
              <el-descriptions-item label="子指标">{{info.targetSubName}}</el-descriptions-item>
              <el-descriptions-item label="服务中心">{{info.serviceCenter}}</el-descriptions-item>
              <el-descriptions-item label="坐席工号">{{info.workNo}}</el-descriptions-item>
              <el-descriptions-item label="坐席姓名">{{info.workName}}</el-descriptions-item>
              <el-descriptions-item label="开始时间">{{info.startTime}}</el-descriptions-item>
              <el-descriptions-item label="结束时间">{{info.longTime}}</el-descriptions-item>
              <el-descriptions-item label="通话时长">{{info.longTime}}</el-descriptions-item>
              <el-descriptions-item label="日期">{{info.dataDate}}</el-descriptions-item>
              <el-descriptions-item label="答卷编号">{{info.qsNo}}</el-descriptions-item>
              <el-descriptions-item label="电话">{{info.telephone}}</el-descriptions-item>
              <el-descriptions-item label="运营商">{{info.serviceProvider}}</el-descriptions-item>
              <el-descriptions-item label="主键号">{{info.onlyNo}}</el-descriptions-item>
              <el-descriptions-item label="省份">{{info.province}}</el-descriptions-item>
              <el-descriptions-item label="地市">{{info.city}}</el-descriptions-item>
              <el-descriptions-item label="联系号码">{{info.contactPhone}}</el-descriptions-item>
              <el-descriptions-item label="区号">{{info.area}}</el-descriptions-item>
              <el-descriptions-item label="服务单号">{{info.serviceOrderNo}}</el-descriptions-item>
              <el-descriptions-item label="竣工时间">{{info.completionTime}}</el-descriptions-item>
              <el-descriptions-item label="产品名称">{{info.productName}}</el-descriptions-item>
              <el-descriptions-item label="销售名称">{{info.saleName}}</el-descriptions-item>
              <el-descriptions-item label="序号">{{info.seqNo}}</el-descriptions-item>
              <el-descriptions-item label="测评月份">{{info.testMonth}}</el-descriptions-item>
              <el-descriptions-item label="呼叫结果">{{info.callResult}}</el-descriptions-item>
              <el-descriptions-item label="任务结果">{{info.taskResult}}</el-descriptions-item>
              <el-descriptions-item label="来电时间">{{info.incomingTime}}</el-descriptions-item>
            </el-descriptions>
          </el-card>

        </div>


        <div class="rightMain">
          <div class="tab-buttons">
            <button
              v-for="(tab, index) in tabs"
              :key="index"
              @click="currentTab = index"
              :class="{ active: currentTab === index }"
            >
              {{ tab.title }}
            </button>
          </div>
          <div class="tab-content">
            <div v-if="currentTab === 0">
              <el-card>
                <div slot="header" class="card-tit">
                  <span>评测信息</span>
                </div>
                <div class="card-body">
                  <div class="ruleBox">
                    <div v-for="(item,index) in scoreList" :key="index">
                      <div class="row">
                         <el-tooltip  effect="dark"  placement="top">
                            <div slot="content" class="toolBox">{{item.question}}</div>
                            <div class="rule-tit">{{index+1}}.{{item.question}}</div>
                          </el-tooltip>
                        <div>{{item.answer}}分</div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
            <div v-else-if="currentTab === 1">
              <div class="an-container">
                <el-card shadow="hover" class="annotation-list-card">
                  <div slot="header">
                    <span>标注列表 (共{{annotations.length}}条)</span>
                    <div class="card-actions" style="float: right;padding-right: 20px;">
                      <el-button
                        type="primary"
                        size="small"
                        @click="exportAnnotations"
                      >导出标注</el-button>
                    </div>
                  </div>
                  <el-table
                    :data="annotations"
                    border
                    style="width: 100%"
                    @row-click="handleRowClick"
                  >
                    <el-table-column prop="type" label="类型" width="120">
                      <template slot-scope="scope">
                        <el-tag :type="getTagType(scope.row.type)">
                          {{ getTypeLabel(scope.row.type) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="originalText" label="原始文本" width="200">
                      <template slot-scope="scope">
                        <div class="text-truncate" :title="scope.row.originalText">
                          {{ scope.row.originalText }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="correctText" label="修正文本">
                      <template slot-scope="scope">
                        <div class="text-truncate" :title="scope.row.correctText">
                          {{ scope.row.correctText }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
                    <el-table-column label="操作" width="150">
                      <template slot-scope="scope">
                        <el-button
                          size="mini"
                          @click.stop="editAnnotation(scope.row)"
                        >编辑</el-button>
                        <el-button
                          size="mini"
                          type="danger"
                          @click.stop="deleteAnnotation(scope.row)"
                        >删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
              </div>
            </div>
          </div>
          <el-card style="margin:10px 0;">
            <div slot="header" class="card-tit">
              <div class="row spaceBet">
                <div class="row">
                  <div>智能质检结果<span v-if="tableData.length">（模板:{{tableData[0].templateName}}）</span></div>
                  <el-button type="primary" size="mini" @click="drawer = true" style="margin-left:10px;">查看质检模板</el-button>
                </div>
                <div v-if="tableData.length">质检结果：<span class="redText">{{tableData[0].aiResult}}</span></div>
              </div>
            </div>
            <div class="card-body">
              <el-table :data="tableData" size="small">
                <el-table-column label="评分项" prop="parentName" show-overflow-tooltip align="center">
                </el-table-column>
                <el-table-column label="规则名称" prop="itemName"  show-overflow-tooltip align="center">
                </el-table-column>
                <el-table-column label="优先级" width="90" prop="priority" align="center">
                </el-table-column>
                <el-table-column label="智能结果" width="90" prop="aiResult" align="center">
                </el-table-column>
              </el-table>
            </div>
          </el-card>

          <el-card>
            <div slot="header" class="card-tit">
              <span>人工复检</span>
            </div>
            <div class="card-body">

              <div v-if="isEdit" class="row selBox">
                <div class="label">选择质检规则</div>
                 <el-cascader
                  size="mini"
                  v-model="ruleVal"
                  :options="treeList"
                  :props="{ value: 'id', label: 'itemName',emitPath:false}">
                  </el-cascader>
                <el-button @click="addRule" class="addBt" type="primary" size="mini">+</el-button>
              </div>

              <el-table :data="manCheckData" size="small">
                <el-table-column label="评分项" prop="parentName" show-overflow-tooltip align="center">
                </el-table-column>
                <el-table-column label="规则名称" prop="itemName" show-overflow-tooltip  align="center">
                </el-table-column>
                <el-table-column label="优先级" width="80" prop="priority" align="center">
                </el-table-column>
                <el-table-column label="智能结果" width="90" prop="aiResult" align="center">
                </el-table-column>

                <el-table-column v-if="!isEdit" label="复检结果" prop="reviewResult" align="center" width="105">
                </el-table-column>

                <el-table-column v-if="isEdit" label="复检结果" align="center" width="105">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.reviewResult"  size="mini" placeholder="请选择">
                      <el-option
                      v-for="(item,index) in reResultList"
                      :key="index"
                      :label="item"
                      :value="index"
                    ></el-option>
                  </el-select>
                  </template>
                </el-table-column>
                <el-table-column v-if="isEdit" label="操作" align="center" width="60">
                  <template slot-scope="scope">
                    <el-popconfirm  @confirm="delRule(scope.$index)" title="你确定要删除吗？">
                      <el-button  slot="reference" size="mini" type="text">删除</el-button>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>

              <div  v-if="isEdit" class="footBt row">
                <el-button type="primary" size="mini" @click="submitRule">提 交</el-button>
                <el-button size="mini" @click="goBack" style="margin-left:30px;">返 回</el-button>
              </div>
            </div>
          </el-card>

        </div>
      </div>

    </div>

    <el-drawer
      title="质检模板"
      :visible.sync="drawer"
      size="564px"
      >
      <div class="drawerBox">
        <el-table
          :data="qualTempData"
          row-key="id"
          :default-expand-all="isExpandAll"
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}" >
          <el-table-column label="评分项名称" align="center" prop="itemName" width="200"/>
          <el-table-column label="评分方式" align="center" prop="scoreWay" />
          <el-table-column label="致命性" align="center" prop="nature" />
          <el-table-column label="分数" align="center" prop="scoreNum" />
          <el-table-column label="规则模型" align="center" prop="ruleId" />
        </el-table>
      </div>
    </el-drawer>
  </div>

</template>

<script>
import { reviewInfo,aiResList,tempList,addReview,soundInfo,reviewDetail } from "@/api/quality/review";
import {listItem} from "@/api/quality/scoreItem";
import WaveSurferPlayer from '@/components/WaveSurferPlayer'
import { Utils } from '@/api/util/common';
import {listAnnotation, delAnnotation, listAllAnnotation} from "@/api/quality/annotation";
import EditableTextAnnotation  from '@/components/AdvancedTextAnnotation'
export default {
  name: "History",
  components:{
    WaveSurferPlayer,
    EditableTextAnnotation
  },
  data() {
    return {
      currentTab: 0,
      tabs: [
        { title: '评测信息' },
        { title: '标注列表' }
      ],
      annotations: [],
      parentData: {
        dataId: "",
        oldUrl: "",
        newUrl: "",
        newUrlName: "",
        startTime: "",
        endTime: ""},
      msgList:[],
      scoreList:[],
      tableData:[],
      ruleVal:'',
      ruleList:[],
      treeList:[],
      ruleIndex:null,
      manCheckData:[],
      qualTempData:[],// 评分规则模板表格数据
      reResultList:['0','1','2','3','4','5','6','7','8','9','10','废卷','待修正'],
      drawer:false,
      audioUrl: require('../../../assets/images/test2.wav'),
      // audioUrl:'http://downsc.chinaz.net/Files/DownLoad/sound1/201906/11582.mp3'
      info:{},

      currentTime: 0,
      duration: 0,
      keyWords:[],
      // 是否展开，默认全部折叠
      isExpandAll: false,
      isEdit:1,//1带编辑的，0仅查看

    };
  },
  created() {
    let id = this.$route.query.id
    this.isEdit = Number(this.$route.query.isEdit)
    this.getInfo(id);
  },
  watch: {
  },
  methods: {
    handleChildData(data){
      this.annotations=data;
    },
    selectAllText(event,startTime,endTime) {
      this.parentData.startTime=startTime;
      this.parentData.endTime=endTime;
    },
    // 导出标注
    exportAnnotations() {
      this.download('qc/api/audio/exportSamples', {
        dataId:this.info.id
      }, `audio_samples_${new Date().getTime()}.zip`)
    },

    // 清空所有标注
    clearAllAnnotations() {
      if (this.annotations.length === 0) return

      this.$confirm('确定要清空所有标注吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.annotations = []
        this.$message.success('已清空所有标注')
      }).catch(() => {})
    },

    // 表格行点击
    handleRowClick(row) {
      this.$refs.annotator.showAnnotationDetail(row)
    },

    // 编辑标注
    getAnnotation(dataId) {
      listAllAnnotation({dataId:dataId}).then(response => {
        this.annotations = response.data;
        this.parentData.annotations=this.annotations;
        this.getVoice()
      });
    },
    // 编辑标注
    editAnnotation(row) {
      this.$refs.annotator.editCurrentAnnotation(row)
    },

    // 删除标注
    deleteAnnotation(row) {
      this.$confirm('确定要删除此标注吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delAnnotation(row.id).then(response => {
          this.annotations = this.annotations.filter(a => a.id !== row.id)
          this.$message.success('标注已删除')
        });
      }).catch(() => {})
    },

    // 获取标签类型
    getTagType(type) {
      switch(type) {
        case 'correction': return 'danger'
        case 'highlight': return 'warning'
        case 'question': return 'primary'
        case 'comment': return 'success'
        default: return 'info'
      }
    },

    // 获取类型标签
    getTypeLabel(type) {
      const types = [
        { value: 'correction', label: '文本修正' },
        { value: 'highlight', label: '重点标注' },
        { value: 'question', label: '疑问点' },
        { value: 'comment', label: '评注' }
      ]
      const item = types.find(t => t.value === type)
      return item ? item.label : '未知类型'
    },
    handleTimeUpdate(timeInMs) {
      this.currentTime = Math.round(timeInMs*1000);
      this.highlightAndScroll();
    },

    handleDurationUpdate(durationInMs) {
      this.duration = durationInMs;
    },
    isActive(item) {
      return this.currentTime >= item.bg && this.currentTime < item.ed;
    },
    jumpToTime(startTime,endTime,event) {
      this.currentTime = startTime
      this.$refs.audioPlayer.seekTo(startTime);
      if (event.target.classList.contains('span_c')) {
        const vValue = event.target.getAttribute("_v");
        this.showAnnotation(vValue);
      }
    },
    //滚动到高亮位置
    highlightAndScroll() {
      const highlightedIndex = this.msgList.findIndex(item => this.isActive(item));
      if (highlightedIndex !== -1) {
        const itemElement = this.$refs.itemRefs[highlightedIndex];
        if (itemElement) {
          itemElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }
    },
    //匹配关键词
    highlightTitle(msg) {
      let highlightedText = msg;
      let i = 0;
      this.annotations.forEach(key => {
        if (msg.includes(key.originalText)) {
          highlightedText = highlightedText.replace(
            new RegExp(key.originalText, 'g'),
            `<span style="color: #F56C6C;border: 2px dashed blue;" class="span_c" _v="${i}">${key.originalText}</span>`
          );
        }
        i++;
      });
      return highlightedText;
      /*let highlightedText = msg;
      this.keyWords.forEach(key => {
        if (msg.includes(key)) {
          highlightedText = highlightedText.replace(
            new RegExp(key, 'g'),
            `<span style="color: #F56C6C;">${key}</span>`
          );
        }
      });
      return highlightedText;*/
    },
    showAnnotation(idx){
      let tmp = this.annotations[parseInt(idx)]
      this.$refs.annotator.showAnnotationDetail(tmp)
    },
    //录音信息
    getInfo(id) {
      reviewInfo(id).then(res => {
        this.info = res.data
        this.scoreList = res.data.list
        this.parentData.dataId=this.info.id;
        this.getAnnotation(id);
        this.getAiResList();
        this.getVoice()
        this.getListByScoretplId()

        if(this.isEdit){//复检
          this.getTempList()
        }else{
          this.getManCheckList()
        }
      });
    },
    getAiResList(){
      if(!this.info.templateId) return
      aiResList({dataNo:this.info.onlyNo,templateId:this.info.templateId}).then(res => {
        this.tableData = res.data
      });
    },
    getTempList(){
      if(!this.info.templateId) return
      tempList({dataNo:this.info.onlyNo,templateId:this.info.templateId}).then(res => {
        this.ruleList = res.data
        this.treeList = Utils.listToTree(res.data)
      });
    },
    getVoice(){
      soundInfo(this.info.onlyNo).then(res => {
        // this.audioUrl = res.data.videoUrl
        this.msgList = res.data.msgList
        this.keyWords = res.data.keys
        // console.log(this.msgList,'this.msgList----')
      });
    },
    getManCheckList(){
      reviewDetail(this.info.id).then(res => {
        this.manCheckData = res.data
      });
    },
     /** 查询评分规则模板列表 */
    getListByScoretplId() {
      listItem({scoretplId: this.info.templateId}).then(response => {
        this.qualTempData = response.data;
      });
    },
    goBack() {
      this.$store.dispatch("tagsView/delView", this.$route); //关闭当前页
      // this.$router.replace({ path: "/data/quality"}); // 要打开的页面
      this.$router.back()
    },
    changeRule(){

    },
    submitRule(){
      if(!this.manCheckData.length){
        this.$message.warning('请至少添加一条规则！')
      }else{
      //  const hasNullId = this.manCheckData.some(item => item.reviewResult === null);
      //  if(hasNullId){
      //   this.$message.warning('请填写完整复检结果！')
      //  }else{
        const newArr = this.manCheckData.map(item=>{
          let obj = {
            id:this.id,
            dataId:this.dataId,
            templateId:item.templateId,
            ruleId:item.ruleId,
            aiResult:item.aiResult,
            reviewResult:item.reviewResult

          }
          return obj
        })
        addReview(newArr).then(res => {
          if(res.code == 200){
            this.$message.success('添加成功')
            this.goBack()
          }
        })
       }

      // }
    },
    addRule(){
      if(this.ruleVal==''){
         this.$message.warning('请选择规则！')
      }else{
        const info = this.ruleList.find(item=>item.id == this.ruleVal)
        // console.log(info,'info--')
        this.manCheckData.unshift({...info,reviewResult:null})
      }
    },
    delRule(index){
      this.manCheckData.splice(index,1);
    }


  }
}
</script>
<style lang="scss" scoped>

.app-container{
  background: #eff1f4;
  padding: 24px;
  min-height: calc(100vh - 50px);
}
.app-main{
  background: rgb(255, 255, 255);
  min-height: calc(-98px + 100vh);
  border-radius: 4px;
  padding: 20px;
}
.blueText{
  color: #409EFF!important;
}
.redText{
  color: #F56C6C!important;
}
.topLine{
  margin: 15px 0;
}
.conMain{
  width: 100%;
  align-items: flex-start;
  margin-top: 10px;
}
.leftMain{
  padding-right: 10px;
  // border: 1px solid red;
  width: 50%;
}
.chatMain{
  //  border: 1px solid red;
  height: 400px;
  // height: calc(100vh - 280px);
  overflow: auto;
}
.rightMain{
  width: 50%;
}

.card-tit{
  font-size: 14px;
}
.card-body{
  font-size: 13px;
}
.ruleBox{
  display: grid;
  grid-template-columns: repeat(2,1fr);
  grid-column-gap: 20px;
  grid-row-gap: 18px;
  justify-content: space-between;
}
.rule-tit{
  width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
}


.resBt{
  margin: 0 20px;
}
.selBox{
  margin-bottom: 10px;
  .label{
    font-size: 13px;
    margin-right: 10px;
  }
  .addBt{
    margin-left: 10px;
  }
}
.footBt{
  justify-content: center;
  margin: 10px 0;
}


.chatBox{
	display: flex;
	flex-direction: row;
	margin-bottom: 10px;
  font-size: 13px;
  // border: 1px solid blue;
	img{
		width: 40px;
		height: 40px;
	}
	.msg{
    // display: inline-block;
		border-radius: 5px;
		background-color: #f5f5f5;
		padding: 8px 12px;
		max-width: 360px;
		word-wrap: break-word;
		word-break: break-all;
    margin: 0 10px;
    position: relative;
    text-align: left;
    color: #606266;
    line-height: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
	}


}
.chatTime{
  color: #999;
  font-size:12px;
  cursor: pointer;
  .icon{
    width: 20px;
    height: 20px;
  }
}
.chatMy{
	justify-content: flex-end;
}

.msg-item.chat-mine {
	text-align: right;
	padding-left: 0;
	padding-right: 30px;

	.chat-user {
	  left: auto;
	  right: 3px;

	  cite {
		left: auto;
		right: 30px;
		text-align: right;

		i {
		  padding-left: 0;
		  // padding-right: 15px;
		}
	  }
	}

	.chat-text {
	  margin-left: 0;
	  text-align: left;
	  background-color: #cad5e5;
	  color: #000;
	}

	.chat-text::after {
	  top: 15px;
	  left: auto;
	  right: -10px;
	  border-top: 5upx solid transparent;
	  border-left: 10upx solid #cad5e5;
	  border-bottom: 5upx solid transparent;
	  border-right: unset;
	}

	.chat-system {
	  margin-left: 15px;
	}
}
.drawerBox{
  padding: 0 20px;
}
.toolBox{
  max-width: 600px;
}
.annotation-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tab-buttons button {
  padding: 8px 16px;
  margin-right: 5px;
  background: #f0f0f0;
  border: none;
  cursor: pointer;
}

.tab-buttons button.active {
  background: #ddd;
  font-weight: bold;
  color: #2c86c9;
}
</style>
