-- ----------------------------
-- Table structure for qc_hot_word
-- ----------------------------
DROP TABLE IF EXISTS `qc_hot_word`;
CREATE TABLE `qc_hot_word` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父级ID，0表示根节点',
  `word_name` varchar(100) NOT NULL COMMENT '热词名称',
  `word_type` varchar(20) NOT NULL DEFAULT 'word' COMMENT '热词类型：category-分类，word-热词',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `order_num` int(11) NOT NULL DEFAULT '0' COMMENT '排序号',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '多租户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_word_type` (`word_type`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ASR热词配置表';

-- ----------------------------
-- Records of qc_hot_word
-- ----------------------------
INSERT INTO `qc_hot_word` VALUES 
(1, 0, '评分相关', 'category', 1, 1, 1, 'admin', NOW(), NULL, NULL, '评分相关热词分类'),
(2, 1, '一分', 'word', 1, 1, 1, 'admin', NOW(), NULL, NULL, NULL),
(3, 1, '二分', 'word', 1, 2, 1, 'admin', NOW(), NULL, NULL, NULL),
(4, 1, '三分', 'word', 1, 3, 1, 'admin', NOW(), NULL, NULL, NULL),
(5, 1, '四分', 'word', 1, 4, 1, 'admin', NOW(), NULL, NULL, NULL),
(6, 1, '五分', 'word', 1, 5, 1, 'admin', NOW(), NULL, NULL, NULL),
(7, 1, '六分', 'word', 1, 6, 1, 'admin', NOW(), NULL, NULL, NULL),
(8, 1, '七分', 'word', 1, 7, 1, 'admin', NOW(), NULL, NULL, NULL),
(9, 1, '八分', 'word', 1, 8, 1, 'admin', NOW(), NULL, NULL, NULL),
(10, 1, '九分', 'word', 1, 9, 1, 'admin', NOW(), NULL, NULL, NULL),
(11, 1, '十分', 'word', 1, 10, 1, 'admin', NOW(), NULL, NULL, NULL),
(12, 0, '问题相关', 'category', 1, 2, 1, 'admin', NOW(), NULL, NULL, '问题相关热词分类'),
(13, 12, '哪些方面', 'word', 1, 1, 1, 'admin', NOW(), NULL, NULL, NULL),
(14, 12, '哪一些方面', 'word', 1, 2, 1, 'admin', NOW(), NULL, NULL, NULL);

-- ----------------------------
-- Menu SQL for qc_hot_word
-- ----------------------------
-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ASR热词配置', '2000', '1', 'hotWord', 'quality/hotWord/index', 1, 0, 'C', '0', '0', 'quality:hotWord:list', 'tree', 'admin', sysdate(), '', null, 'ASR热词配置菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ASR热词配置查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'quality:hotWord:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ASR热词配置新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'quality:hotWord:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ASR热词配置修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'quality:hotWord:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ASR热词配置删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'quality:hotWord:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ASR热词配置导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'quality:hotWord:export',       '#', 'admin', sysdate(), '', null, '');
