package com.ideal.qc.service;

import com.ideal.qc.domain.QcScoringHitRuleItem;
import com.ideal.qc.vo.QcScoringHitRuleItemVo;

import java.util.List;

/**
 * 评分细则(项)规则组关联规则表Service接口
 *
 * <AUTHOR>
 * @date 2025/5/15 17:38
 */
public interface QcScoringHitRuleItemService {

    /**
     * 新增评分细则命中规则项
     *
     * @param qcScoringHitRuleItem 评分细则命中规则项实体
     */
    void addScoringHitRuleItem(QcScoringHitRuleItem qcScoringHitRuleItem);

    /**
     * 批量删除评分细则命中规则项
     *
     * @param qcScoringHitRuleItemIdList 评分细则命中规则项ID列表
     */
    void removeScoringHitRuleItemBatch(List<Long> qcScoringHitRuleItemIdList);

    /**
     * 修改评分细则命中规则项
     *
     * @param qcScoringHitRuleItem 评分细则命中规则项实体
     */
    void updateScoringHitRuleItem(QcScoringHitRuleItem qcScoringHitRuleItem);

    /**
     * 根据规则组ID查询所有规则项id
     *
     * @param scoringHitRuleGroupId 规则组ID
     * @return 所有规则项id
     */
    List<Long> getQcScoringHitRuleItemIds(Long scoringHitRuleGroupId);

    /**
     * 批量删除评分细则命中规则项根据规则组ID
     *
     * @param qcScoringHitRuleGroupIds 规则组IDs
     */
    void removeScoringHitRuleItemBatchByGroupIds(List<Long> qcScoringHitRuleGroupIds);

    /**
     * 根据规则ID获取规则名称
     *
     * @param ruleId 规则ID
     * @return 规则名称
     */
    String getRuleNameByRuleId(Long ruleId);

    /**
     * 根据规则ID获取规则名称
     *
     * @param ruleId 规则ID
     * @return 规则名称
     */
    String getRuleNameById(Long ruleId);

    /**
     * 查询规则列表根据模板ID
     *
     * @param scoringTemplateId 模板ID
     * @return 规则列表
     */
    List<QcScoringHitRuleItemVo> getQcScoringHitRuleItemListByTemplateId(Long scoringTemplateId);
}
