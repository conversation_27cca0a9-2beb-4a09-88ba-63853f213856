<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="来源计划id" prop="planId">
        <el-input
          v-model="queryParams.planId"
          placeholder="请输入来源计划id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="租户id" prop="tenantId">
        <el-input
          v-model="queryParams.tenantId"
          placeholder="租户id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['quality:dataset:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="datasetList" @selection-change="handleSelectionChange">
<!--      <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="类型" align="center" prop="dataType" >
        <template slot-scope="scope">
            {{ getDateType(scope.row.dataType) }}
        </template>
      </el-table-column>
<!--      <el-table-column label="来源计划id" align="center" prop="planId" />-->
      <el-table-column label="总数" align="center" prop="totalCount" />
      <el-table-column label="语音已转写条数" align="center" prop="asrProgress" />
      <el-table-column label="业务问卷动态列数" align="center" prop="bizSampleCols" />
      <el-table-column label="状态" align="center" prop="status" >
        <template slot-scope="scope">
            {{ getStatusLabel(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column label="处理状态" align="center" prop="processStatus" >
        <template slot-scope="scope">
          <el-tag :type="getProcessStatusLabel(scope.row.processStatus)">
            {{ getProcessStatusLabel(scope.row.processStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="完成百分比(0‑100)" align="center" prop="progress" >
        <template slot-scope="scope">
            {{scope.row.progress+'%' }}
        </template>
      </el-table-column>
      <el-table-column label="租户" align="center" prop="tenantId" />
      <el-table-column label="备注" align="center" prop="remark" />
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['quality:dataset:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['quality:dataset:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据集对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="来源计划ID" prop="planId">
          <el-input v-model="form.planId" placeholder="请输入来源计划ID" />
        </el-form-item>
        <el-form-item label="总数" prop="totalCount">
          <el-input v-model="form.totalCount" placeholder="请输入总数" />
        </el-form-item>
        <el-form-item label="语音已转写条数" prop="asrProgress">
          <el-input v-model="form.asrProgress" placeholder="请输入语音已转写条数" />
        </el-form-item>
        <el-form-item label="业务问卷动态列数" prop="bizSampleCols">
          <el-input v-model="form.bizSampleCols" placeholder="请输入业务问卷动态列数" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="租户id" prop="tenantId">
          <el-input v-model="form.tenantId" placeholder="请输入租户id" />
        </el-form-item>
        <el-form-item label="完成百分比(0‑100)" prop="progress">
          <el-input v-model="form.progress" placeholder="请输入完成百分比(0‑100)" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDataset, getDataset, delDataset, addDataset, updateDataset } from "@/api/quality/dataset";
import {Utils} from "@/api/util/common";

export default {
  name: "Dataset",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据集表格数据
      datasetList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        dataType: null,
        planId: null,
        totalCount: null,
        asrProgress: null,
        bizSampleCols: null,
        tenantId: null,
        status: null,
        processStatus: null,
        progress: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "$comment不能为空", trigger: "blur" }
        ],
        dataType: [
          { required: true, message: "'voice','biz'不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getTel(v){
      return Utils.getTel(v)
    },
    getName(v){
      return Utils.getName(v)
    },
    /** 查询数据集列表 */
    getList() {
      this.loading = true;
      listDataset(this.queryParams).then(response => {
        this.datasetList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    getDateType(v){
      if(v=="voice"){
        return "语音";
      }else{
        return "";
      }
    },
    getProcessStatusLabel(v){
      if(v=="new"){
        return "新建";
      }else if(v=="importing"){
        return "正在导入";
      }else if(v=="ready"){
        return "准备";
      }else if(v=="voice_fetch"){
        return "语音同步";
      }else if(v=="finished"){
        return "已完成";
      }else if(v=="fail"){
        return "失败";
      }else{
        return "";
      }
    },
    getStatusLabel(v){
      if(v=="now"){
        return "新建";
      }else if(v=="done"){
        return "完成";
      }else{
        return "";
      }
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        dataType: null,
        planId: null,
        totalCount: null,
        asrProgress: null,
        bizSampleCols: null,
        remark: null,
        tenantId: null,
        createTime: null,
        updateTime: null,
        status: null,
        processStatus: null,
        progress: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据集";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDataset(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改数据集";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDataset(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDataset(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除数据集编号为"' + ids + '"的数据项？').then(function() {
        return delDataset(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('qc/dataset/export', {
        ...this.queryParams
      }, `dataset_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
