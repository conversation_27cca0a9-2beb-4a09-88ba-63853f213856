package com.ideal.qc.controller;

import java.io.*;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcDatasetItem;
import com.ideal.qc.service.IQcDatasetItemService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 语音数据明细Controller
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/datesetItem")
public class QcDatasetItemController extends BaseController
{
    @Autowired
    private IQcDatasetItemService qcDatasetItemService;

    /**
     * 查询语音数据明细列表
     */
    @RequiresPermissions("quality:datesetItem:list")
    @GetMapping("/list")
    public TableDataInfo list(QcDatasetItem qcDatasetItem)
    {
        startPage();
        List<QcDatasetItem> list = qcDatasetItemService.selectQcDatasetItemList(qcDatasetItem);
        return getDataTable(list);
    }

    /**
     * 导出语音数据明细列表
     */
    @RequiresPermissions("quality:datesetItem:export")
    @Log(title = "语音数据明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QcDatasetItem qcDatasetItem)
    {
        List<QcDatasetItem> list = qcDatasetItemService.selectQcDatasetItemList(qcDatasetItem);
        ExcelUtil<QcDatasetItem> util = new ExcelUtil<QcDatasetItem>(QcDatasetItem.class);
        util.exportExcel(response, list, "语音数据明细数据");
    }

    /**
     * 获取语音数据明细详细信息
     */
    @RequiresPermissions("quality:datesetItem:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(qcDatasetItemService.selectQcDatasetItemById(id));
    }

    /**
     * 新增语音数据明细
     */
    @RequiresPermissions("quality:datesetItem:add")
    @Log(title = "语音数据明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QcDatasetItem qcDatasetItem)
    {
        return toAjax(qcDatasetItemService.insertQcDatasetItem(qcDatasetItem));
    }

    /**
     * 修改语音数据明细
     */
    @RequiresPermissions("quality:datesetItem:edit")
    @Log(title = "语音数据明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QcDatasetItem qcDatasetItem)
    {
        return toAjax(qcDatasetItemService.updateQcDatasetItem(qcDatasetItem));
    }

    /**
     * 删除语音数据明细
     */
    @RequiresPermissions("quality:datesetItem:remove")
    @Log(title = "语音数据明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(qcDatasetItemService.deleteQcDatasetItemByIds(ids));
    }

    @GetMapping("/getFileByFileId")
    public void getFileByFileId(@RequestParam("fileId") String fileId,
                               @RequestHeader(value = "Range", required = false) String range,
                               HttpServletResponse response) throws IOException {
        File audioFile = qcDatasetItemService.getAudioFileByFileId(fileId);
        if (audioFile != null && audioFile.exists()) {
            long fileLength = audioFile.length();
            response.setContentType("audio/wav");
            response.setHeader("Accept-Ranges", "bytes");
            response.setHeader("Content-Length", String.valueOf(fileLength));
            
            if (range != null) {
                // 处理范围请求，支持拖动进度条
                String[] ranges = range.replace("bytes=", "").split("-");
                long start = Long.parseLong(ranges[0]);
                long end = ranges.length > 1 && !ranges[1].isEmpty() ? 
                          Long.parseLong(ranges[1]) : fileLength - 1;
                
                response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
                response.setHeader("Content-Range", "bytes " + start + "-" + end + "/" + fileLength);
                response.setHeader("Content-Length", String.valueOf(end - start + 1));
                
                try (RandomAccessFile raf = new RandomAccessFile(audioFile, "r")) {
                    raf.seek(start);
                    byte[] buffer = new byte[1024];
                    long remaining = end - start + 1;
                    while (remaining > 0) {
                        int bytesRead = raf.read(buffer, 0, (int) Math.min(buffer.length, remaining));
                        if (bytesRead == -1) break;
                        response.getOutputStream().write(buffer, 0, bytesRead);
                        remaining -= bytesRead;
                    }
                }
            } else {
                // 完整文件下载
                try (FileInputStream fis = new FileInputStream(audioFile);
                     OutputStream os = response.getOutputStream()) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        os.write(buffer, 0, bytesRead);
                    }
                }
            }
        }
    }


}
