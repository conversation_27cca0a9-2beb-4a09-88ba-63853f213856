package com.ideal.qc.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.stream.Collectors;

import com.ideal.common.core.utils.DateUtils;
import com.ideal.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.QcHotWordMapper;
import com.ideal.qc.domain.QcHotWord;
import com.ideal.qc.service.IQcHotWordService;

/**
 * ASR热词配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
public class QcHotWordServiceImpl implements IQcHotWordService 
{
    @Autowired
    private QcHotWordMapper qcHotWordMapper;

    /**
     * 查询ASR热词配置
     * 
     * @param id ASR热词配置主键
     * @return ASR热词配置
     */
    @Override
    public QcHotWord selectQcHotWordById(Long id)
    {
        return qcHotWordMapper.selectQcHotWordById(id);
    }

    /**
     * 查询ASR热词配置列表
     * 
     * @param qcHotWord ASR热词配置
     * @return ASR热词配置
     */
    @Override
    public List<QcHotWord> selectQcHotWordList(QcHotWord qcHotWord)
    {
        return qcHotWordMapper.selectQcHotWordList(qcHotWord);
    }

    /**
     * 查询ASR热词配置树结构
     * 
     * @param qcHotWord ASR热词配置
     * @return ASR热词配置树结构
     */
    @Override
    public List<QcHotWord> selectQcHotWordTree(QcHotWord qcHotWord)
    {
        List<QcHotWord> hotWords = qcHotWordMapper.selectQcHotWordTree(qcHotWord);
        return buildTree(hotWords);
    }

    /**
     * 新增ASR热词配置
     * 
     * @param qcHotWord ASR热词配置
     * @return 结果
     */
    @Override
    public int insertQcHotWord(QcHotWord qcHotWord)
    {
        qcHotWord.setCreateTime(DateUtils.getNowDate());
        return qcHotWordMapper.insertQcHotWord(qcHotWord);
    }

    /**
     * 修改ASR热词配置
     * 
     * @param qcHotWord ASR热词配置
     * @return 结果
     */
    @Override
    public int updateQcHotWord(QcHotWord qcHotWord)
    {
        qcHotWord.setUpdateTime(DateUtils.getNowDate());
        return qcHotWordMapper.updateQcHotWord(qcHotWord);
    }

    /**
     * 批量删除ASR热词配置
     * 
     * @param ids 需要删除的ASR热词配置主键
     * @return 结果
     */
    @Override
    public int deleteQcHotWordByIds(Long[] ids)
    {
        return qcHotWordMapper.deleteQcHotWordByIds(ids);
    }

    /**
     * 删除ASR热词配置信息
     * 
     * @param id ASR热词配置主键
     * @return 结果
     */
    @Override
    public int deleteQcHotWordById(Long id)
    {
        return qcHotWordMapper.deleteQcHotWordById(id);
    }

    /**
     * 获取启用的热词字符串（用于ASR调用）
     * 
     * @return 热词字符串，以分号分隔
     */
    @Override
    public String getEnabledHotWordsString()
    {
        List<String> hotWords = qcHotWordMapper.selectEnabledHotWords();
        if (hotWords == null || hotWords.isEmpty()) {
            // 返回默认热词
            return "一分;二分;三分;四分;五分;六分;七分;八分;九分;十分;哪些方面;哪一些方面";
        }
        return String.join(";", hotWords);
    }

    /**
     * 校验热词名称是否唯一
     *
     * @param qcHotWord 热词信息
     * @return 结果
     */
    @Override
    public String checkWordNameUnique(QcHotWord qcHotWord)
    {
        Long id = StringUtils.isNull(qcHotWord.getId()) ? -1L : qcHotWord.getId();
        int count = qcHotWordMapper.checkWordNameUnique(qcHotWord.getWordName(), qcHotWord.getParentId(), id);
        if (count > 0)
        {
            return "热词名称已存在";
        }
        return "0";
    }

    /**
     * 构建树结构
     * 
     * @param hotWords 热词列表
     * @return 树结构
     */
    @Override
    public List<QcHotWord> buildTree(List<QcHotWord> hotWords)
    {
        List<QcHotWord> returnList = new ArrayList<QcHotWord>();
        List<Long> tempList = new ArrayList<Long>();
        for (QcHotWord hotWord : hotWords)
        {
            tempList.add(hotWord.getId());
        }
        for (Iterator<QcHotWord> iterator = hotWords.iterator(); iterator.hasNext();)
        {
            QcHotWord hotWord = (QcHotWord) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(hotWord.getParentId()))
            {
                recursionFn(hotWords, hotWord);
                returnList.add(hotWord);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = hotWords;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<QcHotWord> list, QcHotWord t)
    {
        // 得到子节点列表
        List<QcHotWord> childList = getChildList(list, t);
        t.setChildren(childList);
        for (QcHotWord tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<QcHotWord> getChildList(List<QcHotWord> list, QcHotWord t)
    {
        List<QcHotWord> tlist = new ArrayList<QcHotWord>();
        Iterator<QcHotWord> it = list.iterator();
        while (it.hasNext())
        {
            QcHotWord n = (QcHotWord) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<QcHotWord> list, QcHotWord t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
