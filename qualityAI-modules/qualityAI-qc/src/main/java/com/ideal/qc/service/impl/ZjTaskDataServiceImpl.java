package com.ideal.qc.service.impl;

import com.ideal.qc.domain.ZjTaskData;
import com.ideal.qc.mapper.ZjTaskDataMapper;
import com.ideal.qc.service.IZjTaskDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 质检源数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Service
public class ZjTaskDataServiceImpl implements IZjTaskDataService
{
    @Autowired
    private ZjTaskDataMapper zjTaskDataMapper;
    /**
     * 查询质检源数据
     *
     * @param id 质检源数据主键
     * @return 质检源数据
     */
    @Override
    public ZjTaskData selectZjTaskDataById(Long id)
    {
        return zjTaskDataMapper.selectZjTaskDataById(id);
    }

    /**
     * 查询质检源数据列表
     *
     * @param zjData 质检源数据
     * @return 质检源数据
     */
    @Override
    public List<ZjTaskData> selectZjTaskDataList(ZjTaskData zjTaskData)
    {
        return zjTaskDataMapper.selectZjTaskDataList(zjTaskData);
    }

    @Override
    public String getVideoText(String onlyNo) {
        return zjTaskDataMapper.getVideoText(onlyNo);
    }

    @Override
    public List<String> getVideoTextMatchKey(String onlyNo) {
        return zjTaskDataMapper.getVideoTextMatchKey(onlyNo);
    }
}
