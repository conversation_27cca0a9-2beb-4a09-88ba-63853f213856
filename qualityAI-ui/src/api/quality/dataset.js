import request from '@/utils/request'

// 查询数据集列表
export function listDataset(query) {
  return request({
    url: '/qc/dataset/list',
    method: 'get',
    params: query
  })
}
// 查询数据集列表
export function listDatasetByMap(query,data) {
  return request({
    url: '/qc/dataset/list/byMap',
    method: 'post',
    params: query,
	data:data
  })
}
// 查询数据集详细
export function getDataset(id) {
  return request({
    url: '/qc/dataset/' + id,
    method: 'get'
  })
}

// 新增数据集
export function addDataset(data) {
  return request({
    url: '/qc/dataset',
    method: 'post',
    data: data
  })
}

// 修改数据集
export function updateDataset(data) {
  return request({
    url: '/qc/dataset',
    method: 'put',
    data: data
  })
}

// 删除数据集
export function delDataset(id) {
  return request({
    url: '/qc/dataset/' + id,
    method: 'delete'
  })
}
