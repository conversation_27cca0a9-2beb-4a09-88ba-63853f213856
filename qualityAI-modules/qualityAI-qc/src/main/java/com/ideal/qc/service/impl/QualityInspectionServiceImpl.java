package com.ideal.qc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ideal.qc.domain.ScoreItem;
import com.ideal.qc.domain.ZjRule;
import com.ideal.qc.service.IQualityInspectionService;
import com.ideal.qc.service.IScoreItemService;
import com.ideal.qc.service.IZjRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

@Service
public class QualityInspectionServiceImpl implements IQualityInspectionService {

    @Autowired
    private IZjRuleService zjRuleService;

    @Autowired
    private IScoreItemService scoreItemService;

    // 缓存编译后的 Pattern 对象，避免重复编译相同规则
    private final ConcurrentHashMap<String, Pattern> patternCache = new ConcurrentHashMap<>();

    /**
     * 根据 ASR 文本和原始自定义规则做质检匹配
     * 预处理时移除文本中的所有空白字符
     * 采用 matcher.matches() 进行整体匹配（因为生成的正则表达式内部已加锚定）
     */
    @Override
    public boolean performQualityCheck(String asrText, String rawPattern) {
        Pattern compiledPattern = getOrCompilePattern(rawPattern);
        // 移除所有空白字符，确保匹配时不受间隔空格影响
        String processedAsrText = asrText.replaceAll("\\s+", "");
        boolean match = compiledPattern.matcher(processedAsrText).matches();
        if (match) {
            // 如果整体匹配成功，进一步逐条检查打印命中的规则部分
            List<String> altMatches = getMatchedAlternatives(rawPattern, processedAsrText);
            System.out.println("命中规则部分：" + altMatches);
        }
        return match;
    }

    private Pattern getOrCompilePattern(String rawPattern) {
        return patternCache.computeIfAbsent(rawPattern, key -> {
            String processedPattern = processPattern(key);
            // 调试时可打印生成的正则表达式：System.out.println("生成的正则：" + processedPattern);
            return Pattern.compile(processedPattern);
        });
    }

    /**
     * 质检规则匹配
     * 此方法调用外部服务获得规则，主要展示正则表达式逻辑转换和匹配打印
     */
    @Override
    public JSONObject regexRule(Long scoreItemId, JSONObject content, Long callId) {
        ScoreItem scoreItem = scoreItemService.selectScoreItemById(scoreItemId);
        ZjRule zjRule = zjRuleService.selectZjRuleByRuleId(scoreItem.getRuleId());
        Boolean resultMatch = performQualityCheck(content.toString(), zjRule.getDetectionContent());
        JSONObject result = new JSONObject();
        result.put("result", resultMatch);
        result.put("callId", callId);
        result.put("nature", scoreItem.getNature());
        result.put("ruleId", zjRule.getRuleId());
        result.put("scoreItemId", scoreItemId);
        return result;
    }

    /**
     * 将自定义规则转换为 Java 正则表达式
     * 支持的操作符包括：
     *  & ：逻辑与，要求左右表达式都出现
     *  | ：逻辑或，要求至少匹配一边
     *  ! ：逻辑非，要求其后的内容不出现
     *  # ：逻辑近运算，要求各关键词按顺序出现，各关键词之间允许有最多指定字符（默认20）
     */
    private String processPattern(String raw) {
        // 利用内部解析器对原始规则进行解析，构造语法树
        Parser parser = new Parser(raw);
        Node node = parser.parseExpression();
        // 根据语法树生成最终正则表达式
        return buildFinalRegex(node);
    }

    /**
     * 根据解析树生成最终正则表达式
     * 若为 And 或 Not 运算，则采用多个 lookahead 预查；否则采用简单模式匹配
     */
    private String buildFinalRegex(Node node) {
        if (node instanceof AndNode || node instanceof NotNode) {
            StringBuilder sb = new StringBuilder();
            sb.append("(?s)^"); // DOTALL 模式及匹配起始
            if (node instanceof AndNode) {
                AndNode andNode = (AndNode) node;
                for (Node child : andNode.children) {
                    if (child instanceof NotNode) {
                        sb.append(child.build());
                    } else {
                        sb.append("(?=.*(?:").append(child.build()).append("))");
                    }
                }
            } else {
                sb.append(node.build());
            }
            sb.append(".*$");
            return sb.toString();
        } else {
            return "(?s)^.*(?:" + node.build() + ").*$";
        }
    }

    /**
     * 将包含 '#' 的逻辑近运算表达式转换为正则表达式
     * 例如： "不打扰#稍后" 转为 "不打扰" + ".{0,20}" + "稍后"，若最后一段为数字则用作间隔限制
     */
    private String convertCustomSequence(String group) {
        String[] parts = group.split("#");
        int maxDistance = 20; // 默认最大间隔字数
        if (parts.length > 1 && parts[parts.length - 1].matches("\\d+")) {
            maxDistance = Integer.parseInt(parts[parts.length - 1]);
            parts = Arrays.copyOf(parts, parts.length - 1);
        }
        StringBuilder regex = new StringBuilder();
        for (int i = 0; i < parts.length; i++) {
            regex.append(Pattern.quote(parts[i]));
            if (i != parts.length - 1) {
                regex.append(".{0,").append(maxDistance).append("}");
            }
        }
        return regex.toString();
    }

    /**
     * 辅助方法：将数据库中原始规则按 "或" 分组拆分，并针对每个分组单独测试是否匹配
     * 成功匹配的部分会打印输出
     */
    private List<String> getMatchedAlternatives(String rawPattern, String processedAsrText) {
        List<String> matches = new ArrayList<>();
        // 假设原始规则格式为：(规则1)|(规则2)|...|(规则N)
        String trimmed = rawPattern;
        if (trimmed.startsWith("(") && trimmed.endsWith(")")) {
            trimmed = trimmed.substring(1, trimmed.length() - 1);
        }
        // 按照 ")|(" 分割
        String[] alternatives = trimmed.split("\\)\\|\\(");
        for (String alternative : alternatives) {
            // 对单条 alternative 单独调用 processPattern 转换为正则表达式，并编译（同样包含锚定）
            String altRegex = processPattern(alternative);
            Pattern altPattern = Pattern.compile(altRegex);
            if (altPattern.matcher(processedAsrText).matches()) {
                matches.add(alternative);
            }
        }
        return matches;
    }

    // ================== 内部解析器及语法树节点定义 ==================

    /**
     * Node 接口：所有语法树节点均实现此接口，生成不含全局锚定的正则表达式片段
     */
    private interface Node {
        String build();
    }

    /**
     * 文字节点：保存文字常量
     * 如文字中含有 '#' 则调用 convertCustomSequence 处理近运算
     */
    private class LiteralNode implements Node {
        private String text;
        public LiteralNode(String text) {
            this.text = text;
        }
        @Override
        public String build() {
            if (text.contains("#")) {
                return convertCustomSequence(text);
            } else {
                return Pattern.quote(text);
            }
        }
    }

    /**
     * 逻辑与节点：要求所有子表达式匹配（顺序不限）
     * 生成时采用多个 lookahead 预查的方式拼接
     */
    private class AndNode implements Node {
        private List<Node> children = new ArrayList<>();
        public AndNode(Node left, Node right) {
            children.add(left);
            children.add(right);
        }
        public void add(Node node) {
            children.add(node);
        }
        @Override
        public String build() {
            StringBuilder sb = new StringBuilder();
            for (Node child : children) {
                if (child instanceof NotNode) {
                    sb.append(child.build());
                } else {
                    sb.append("(?=.*(?:").append(child.build()).append("))");
                }
            }
            return sb.toString();
        }
    }

    /**
     * 逻辑或节点：只需其中一个子表达式匹配
     * 生成时构造一个非捕获组，内部使用 "|" 链接
     */
    private class OrNode implements Node {
        private List<Node> children = new ArrayList<>();
        public OrNode(Node left, Node right) {
            children.add(left);
            children.add(right);
        }
        public void add(Node node) {
            children.add(node);
        }
        @Override
        public String build() {
            StringBuilder sb = new StringBuilder();
            sb.append("(?:" );
            for (int i = 0; i < children.size(); i++) {
                sb.append(children.get(i).build());
                if (i < children.size() - 1) {
                    sb.append("|");
                }
            }
            sb.append(")");
            return sb.toString();
        }
    }

    /**
     * 逻辑非节点：要求其子表达式不出现
     * 使用负向预查实现
     */
    private class NotNode implements Node {
        private Node child;
        public NotNode(Node child) {
            this.child = child;
        }
        @Override
        public String build() {
            return "(?!.*(?:" + child.build() + "))";
        }
    }

    /**
     * 简单的递归下降解析器
     * 支持运算符：! (最高优先级)、& (其次) 和 | (最低)
     * 允许 literal 内出现 '#' 近运算表达式
     * 支持的特殊字符： ( ) & | !
     */
    private class Parser {
        private String input;
        private int pos;
        public Parser(String input) {
            this.input = input;
            this.pos = 0;
        }
        // 解析 OR 表达式
        public Node parseExpression() {
            Node node = parseAnd();
            while (true) {
                skipWhitespace();
                if (peek() == '|') {
                    consume(); // 消耗 '|'
                    Node right = parseAnd();
                    node = mergeOr(node, right);
                } else {
                    break;
                }
            }
            return node;
        }
        // 解析 AND 表达式
        private Node parseAnd() {
            Node node = parseUnary();
            while (true) {
                skipWhitespace();
                if (peek() == '&') {
                    consume(); // 消耗 '&'
                    Node right = parseUnary();
                    node = mergeAnd(node, right);
                } else {
                    break;
                }
            }
            return node;
        }
        // 解析一元表达式，支持逻辑非 "!"
        private Node parseUnary() {
            skipWhitespace();
            if (peek() == '!') {
                consume(); // 消耗 '!'
                Node child = parseUnary();
                return new NotNode(child);
            } else {
                return parsePrimary();
            }
        }
        // 解析基本单元：括号中的表达式或文字
        private Node parsePrimary() {
            skipWhitespace();
            char current = peek();
            if (current == '(') {
                consume(); // 消耗 '('
                Node node = parseExpression();
                skipWhitespace();
                if (peek() == ')') {
                    consume(); // 消耗 ')'
                }
                return node;
            } else {
                return parseLiteral();
            }
        }
        // 解析文字，直到遇到运算符或括号结束
        private Node parseLiteral() {
            skipWhitespace();
            StringBuilder sb = new StringBuilder();
            while (!isEnd() && !isOperator(peek())) {
                sb.append(consume());
            }
            return new LiteralNode(sb.toString());
        }
        private boolean isOperator(char c) {
            return c == '|' || c == '&' || c == '!' || c == '(' || c == ')';
        }
        private void skipWhitespace() {
            while (!isEnd() && Character.isWhitespace(peek())) {
                consume();
            }
        }
        private char peek() {
            return isEnd() ? '\0' : input.charAt(pos);
        }
        private char consume() {
            return input.charAt(pos++);
        }
        private boolean isEnd() {
            return pos >= input.length();
        }
        // 合并节点为 OR 节点
        private Node mergeOr(Node left, Node right) {
            if (left instanceof OrNode) {
                ((OrNode) left).add(right);
                return left;
            } else {
                return new OrNode(left, right);
            }
        }
        // 合并节点为 AND 节点
        private Node mergeAnd(Node left, Node right) {
            if (left instanceof AndNode) {
                ((AndNode) left).add(right);
                return left;
            } else {
                return new AndNode(left, right);
            }
        }
    }

    // ================== 测试入口 ==================

    /**
     * 测试 main 方法
     * 此处构造了数据库中的配置规则，并对示例 ASR 文本进行匹配，并打印出匹配到的规则部分
     */
    public static void main(String[] args) {
        QualityInspectionServiceImpl service = new QualityInspectionServiceImpl();

        // 数据库中配置的规则字符串（多个 or 分组，每个分组用 () 包裹）
        String dbPattern =
                "(不打扰#稍后)|(不打扰#生活愉快)|(不打扰#愉快#再见)|(不打扰#再见)|" +
                        "(不耽误#生活愉快)|(打扰#感谢)|(打扰#感谢#再见)|(打扰#接听#再见)|" +
                        "(打扰#您#愉快)|(打扰#生活#愉快#15)|(打扰#生活#再见)|(打扰#生活愉快)|" +
                        "(打扰#谢#接听)|(打扰#愉快)|(打扰#愉快#再见)|(打扰#再见)|" +
                        "(打扰#祝#生活)|(打扰#祝#愉快)|(打扰#祝#再见)|(打扰#祝您)|" +
                        "(打扰#祝您#挂机#15)|(打扰#祝您#再见)|(打扰您#拜拜)|(打扰您#接听)|" +
                        "(打扰您#再见)|(耽误#愉快)|(耽误#祝您生活)|(非常#您#生活)|" +
                        "(非常#祝#愉快)|(感谢#接听#再见#15)|(感谢#接听#祝)|(感谢#来电#愉快)|" +
                        "(感谢#来电#再见)|(感谢#来电#祝您)|(感谢#理解#祝)|(感谢#致电#愉快)|" +
                        "(感谢#致电#祝)|(接听#生活#再见)|(接听#生活愉快)|(来电#愉快#再见)|" +
                        "(岚图#生活愉快)|(岚图#祝您#愉快)|(忙#不打扰您)|(您#生活愉快)|" +
                        "(您先#打扰#再见)|(稍#微信#祝您)|(微信#祝您#愉快)|(微信聊#祝您)|" +
                        "(先忙#打扰您)|(谢#接听#生活)|(谢#理解#愉快)|(谢#理解#祝您)|" +
                        "(谢#愉快#再见)|(谢#致#祝您)|(有#需要#再#再见)|" +
                        "(愉快#您先挂机)|(愉快#再见)|(致电#祝#生活)|(祝#请#挂机)|" +
                        "(祝#生活愉快)|(祝您#车愉快)|(祝您#先挂机)|!(祝您#愉快#再见)";

        // 示例 ASR 文本（例如中包含："打扰", "感谢", "再见"）
        String testText = "{\"code\":\"0\",\"text\":\" 1:你好，我们是第3方市场调研，公司的访问员正在进行宽带业务，用户满满意度调研。想要问您几个问题，只占用你大约一分钟的时间，希望得到你支持。 1:你好，请问听得到吗？ 0:啊，看到你说。 1:好吧，请问这个移动公司宽带是您和您家人在使用吗？ 1:哎，你好。 0:我没听清。 1:呃，请问这个移动公司宽带是您和您家人在使用吗？ 0:对呀。 1:好的，您对移动公司的宽带上网质量满意吗？可以打九分，请您用一到十分之间任意一个整数来评价很满意为十分，很不满意为一分。 0:马云啊。 1:呃，那女士一到十分之间，您觉得可以打九分呢？ 0:你慢点说，听不清。 1:就是您对移动公司的宽带上网质量满意吗？一到十分之间，您觉得可以打九分呢？很满意为十分，很不满意就为一分啊。 0:很很满意。 1:呃，那您可以打七分呢，女士。 0:打十分呗。 1:好的，十分，您认为移动公司宽带业务的营销活动，自费套餐等公示和宣传是否准确，客观清晰易懂，一到十分期间给到九分呢？ 0:打十分。 1:好的，十分，您认为移动公司宽带业务的套餐设计在网合约业务或套餐变更退订等规则是否合理一到十分之间可以打七分呢？ 0:和林。 0:十分。 1:哦，好的，十分。您认为移动公司宽带业务在咨询查询告知提醒账单信息方面是否清晰明白，一到十分期间可以打七分呢？ 0:打十分。 1:考的十分，请问近三分月内，您是否去过移动公司的营业厅？ 0:没呀，咋的呀。 1:呃，好的呃，请问近三分月内，您是否拨打过移动公司的10086客服热线？ 0:打过了。 1:好的，您对客服您对移动公司客服热线服务是否满意，一到十分期间可以打七分呢？ 0:十分。 1:好的，十分，请问近三分月内，您是否使用过移动公司的手机营业厅？网上营业厅或微信公众号小程序等。 0:没了。 1:哦，好的，您对移动公司安装或维修服务满意吗？一到十分期间可以打七分呢。 0:十分。 1:好的，十分总体来看，结合您的使用体验。您对移动公司的宽带业务满意吗？一到十分之间能打九分呢。 0:你说你还有你还有没玩没？ 1:呃呃，女士，您刚才打的是九分呢？ 0:十分呢啊好。 1:好的，十分就剩二分道题了。女士很快的，您会向别人推荐移动公司宽带业务吗？可能是越大，打分越高，肯定会为十分，肯定不会为一分一到十分之间，请您请请您评价。 1:还担心你吗？ 0:一分。 1:哦，好的，一分近三分月内，您是否向移动公司投诉过宽带业务？ 0:没投诉。 1:好的，您认为移动公司宽带业务哪些方面需要改进？ 0:没有。 1:网络质量，资费套餐服务质量这些方面您觉得需要改进的吗？ 0:网络。 1:呃，网络的话具体是什么问题呢？ 0:网络不好啊。 1:哦，网网络质量的话，具体是哪些方面不好呢？ 0:是上个5就掉线了。 1:是上网速度慢，还是上网稳定性差，或者说是线路故障情况多，还是号线到网速度度差哦，好的，那还有其他方面的问题吗？ 0:没有。 1:好的，那我的回访就到这里，我们是第三分市场调研公司，祝您生活愉快再见。\",\"wavCid\":\"B00400610f9eaf6d4d4493793f8731bdca1a7ac_1.wav\",\"sentenceArray\":[{\"sentence\":\"你好，我们是第3方市场调研，公司的访问员正在进行宽带业务，用户满满意度调研。想要问您几个问题，只占用你大约一分钟的时间，希望得到你支持。\",\"bg\":0,\"dB\":\"61.84\",\"db\":\"61.84\",\"speed\":\"5.76\",\"ed\":10399,\"spk\":1},{\"sentence\":\"你好，请问听得到吗？\",\"bg\":10880,\"dB\":\"62.54\",\"db\":\"62.54\",\"speed\":\"4.1\",\"ed\":12630,\"spk\":1},{\"sentence\":\"啊，看到你说。\",\"bg\":12630,\"dB\":\"60.01\",\"db\":\"60.01\",\"speed\":\"4.17\",\"ed\":13630,\"spk\":0},{\"sentence\":\"好吧，请问这个移动公司宽带是您和您家人在使用吗？\",\"bg\":13630,\"dB\":\"64.01\",\"db\":\"64.01\",\"speed\":\"5.92\",\"ed\":17149,\"spk\":1},{\"sentence\":\"哎，你好。\",\"bg\":20410,\"dB\":\"61.59\",\"db\":\"61.59\",\"speed\":\"2.07\",\"ed\":21660,\"spk\":1},{\"sentence\":\"我没听清。\",\"bg\":21660,\"dB\":\"69.26\",\"db\":\"69.26\",\"speed\":\"3.34\",\"ed\":22660,\"spk\":0},{\"sentence\":\"呃，请问这个移动公司宽带是您和您家人在使用吗？\",\"bg\":22660,\"dB\":\"62.72\",\"db\":\"62.72\",\"speed\":\"4.47\",\"ed\":27160,\"spk\":1},{\"sentence\":\"对呀。\",\"bg\":27160,\"dB\":\"72.1\",\"db\":\"72.1\",\"speed\":\"2.86\",\"ed\":27660,\"spk\":0},{\"sentence\":\"好的，您对移动公司的宽带上网质量满意吗？可以打九分，请您用一到十分之间任意一个整数来评价很满意为十分，很不满意为一分。\",\"bg\":27660,\"dB\":\"62.65\",\"db\":\"62.65\",\"speed\":\"5.78\",\"ed\":36799,\"spk\":1},{\"sentence\":\"马云啊。\",\"bg\":37320,\"dB\":\"68.39\",\"db\":\"68.39\",\"speed\":\"2.07\",\"ed\":38570,\"spk\":0},{\"sentence\":\"呃，那女士一到十分之间，您觉得可以打九分呢？\",\"bg\":38570,\"dB\":\"62.11\",\"db\":\"62.11\",\"speed\":\"5.66\",\"ed\":41729,\"spk\":1},{\"sentence\":\"你慢点说，听不清。\",\"bg\":43270,\"dB\":\"72.45\",\"db\":\"72.45\",\"speed\":\"3.87\",\"ed\":44879,\"spk\":0},{\"sentence\":\"就是您对移动公司的宽带上网质量满意吗？一到十分之间，您觉得可以打九分呢？很满意为十分，很不满意就为一分啊。\",\"bg\":45360,\"dB\":\"62.6\",\"db\":\"62.6\",\"speed\":\"5.36\",\"ed\":54110,\"spk\":1},{\"sentence\":\"很很满意。\",\"bg\":54110,\"dB\":\"69.16\",\"db\":\"69.16\",\"speed\":\"2.25\",\"ed\":55689,\"spk\":0},{\"sentence\":\"呃，那您可以打七分呢，女士。\",\"bg\":56170,\"dB\":\"62.35\",\"db\":\"62.35\",\"speed\":\"4.78\",\"ed\":58269,\"spk\":1},{\"sentence\":\"打十分呗。\",\"bg\":58760,\"dB\":\"67.48\",\"db\":\"67.48\",\"speed\":\"2.76\",\"ed\":60010,\"spk\":0},{\"sentence\":\"好的，十分，您认为移动公司宽带业务的营销活动，自费套餐等公示和宣传是否准确，客观清晰易懂，一到十分期间给到九分呢？\",\"bg\":60010,\"dB\":\"62.78\",\"db\":\"62.78\",\"speed\":\"5.58\",\"ed\":68949,\"spk\":1},{\"sentence\":\"打十分。\",\"bg\":70790,\"dB\":\"62.68\",\"db\":\"62.68\",\"speed\":\"2.07\",\"ed\":72040,\"spk\":0},{\"sentence\":\"好的，十分，您认为移动公司宽带业务的套餐设计在网合约业务或套餐变更退订等规则是否合理一到十分之间可以打七分呢？\",\"bg\":72040,\"dB\":\"62.22\",\"db\":\"62.22\",\"speed\":\"5.52\",\"ed\":81259,\"spk\":1},{\"sentence\":\"和林。\",\"bg\":82580,\"dB\":\"67.17\",\"db\":\"67.17\",\"speed\":\"1.96\",\"ed\":83399,\"spk\":0},{\"sentence\":\"十分。\",\"bg\":84030,\"dB\":\"64.99\",\"db\":\"64.99\",\"speed\":\"1.98\",\"ed\":84839,\"spk\":0},{\"sentence\":\"哦，好的，十分。您认为移动公司宽带业务在咨询查询告知提醒账单信息方面是否清晰明白，一到十分期间可以打七分呢？\",\"bg\":85720,\"dB\":\"62.27\",\"db\":\"62.27\",\"speed\":\"5.74\",\"ed\":94059,\"spk\":1},{\"sentence\":\"打十分。\",\"bg\":96440,\"dB\":\"64.84\",\"db\":\"64.84\",\"speed\":\"2.07\",\"ed\":97690,\"spk\":0},{\"sentence\":\"考的十分，请问近三分月内，您是否去过移动公司的营业厅？\",\"bg\":97690,\"dB\":\"63.05\",\"db\":\"63.05\",\"speed\":\"6.08\",\"ed\":101439,\"spk\":1},{\"sentence\":\"没呀，咋的呀。\",\"bg\":101920,\"dB\":\"64.09\",\"db\":\"64.09\",\"speed\":\"2.57\",\"ed\":103670,\"spk\":0},{\"sentence\":\"呃，好的呃，请问近三分月内，您是否拨打过移动公司的10086客服热线？\",\"bg\":103670,\"dB\":\"62.56\",\"db\":\"62.56\",\"speed\":\"6.21\",\"ed\":108459,\"spk\":1},{\"sentence\":\"打过了。\",\"bg\":109430,\"dB\":\"67.82\",\"db\":\"67.82\",\"speed\":\"3.16\",\"ed\":110180,\"spk\":0},{\"sentence\":\"好的，您对客服您对移动公司客服热线服务是否满意，一到十分期间可以打七分呢？\",\"bg\":110180,\"dB\":\"62.37\",\"db\":\"62.37\",\"speed\":\"5.95\",\"ed\":115699,\"spk\":1},{\"sentence\":\"十分。\",\"bg\":118070,\"dB\":\"65.92\",\"db\":\"65.92\",\"speed\":\"2.11\",\"ed\":118820,\"spk\":0},{\"sentence\":\"好的，十分，请问近三分月内，您是否使用过移动公司的手机营业厅？网上营业厅或微信公众号小程序等。\",\"bg\":118820,\"dB\":\"62.2\",\"db\":\"62.2\",\"speed\":\"6.17\",\"ed\":125429,\"spk\":1},{\"sentence\":\"没了。\",\"bg\":127690,\"dB\":\"66.88\",\"db\":\"66.88\",\"speed\":\"1.92\",\"ed\":128529,\"spk\":0},{\"sentence\":\"哦，好的，您对移动公司安装或维修服务满意吗？一到十分期间可以打七分呢。\",\"bg\":129240,\"dB\":\"62.72\",\"db\":\"62.72\",\"speed\":\"5.88\",\"ed\":134309,\"spk\":1},{\"sentence\":\"十分。\",\"bg\":135750,\"dB\":\"66.86\",\"db\":\"66.86\",\"speed\":\"2.11\",\"ed\":136500,\"spk\":0},{\"sentence\":\"好的，十分总体来看，结合您的使用体验。您对移动公司的宽带业务满意吗？一到十分之间能打九分呢。\",\"bg\":136500,\"dB\":\"62.11\",\"db\":\"62.11\",\"speed\":\"6.05\",\"ed\":143079,\"spk\":1},{\"sentence\":\"你说你还有你还有没玩没？\",\"bg\":144460,\"dB\":\"66.32\",\"db\":\"66.32\",\"speed\":\"3.97\",\"ed\":147029,\"spk\":0},{\"sentence\":\"呃呃，女士，您刚才打的是九分呢？\",\"bg\":147510,\"dB\":\"62.9\",\"db\":\"62.9\",\"speed\":\"4.93\",\"ed\":149949,\"spk\":1},{\"sentence\":\"十分呢啊好。\",\"bg\":150430,\"dB\":\"67.65\",\"db\":\"67.65\",\"speed\":\"3.45\",\"ed\":151680,\"spk\":0},{\"sentence\":\"好的，十分就剩二分道题了。女士很快的，您会向别人推荐移动公司宽带业务吗？可能是越大，打分越高，肯定会为十分，肯定不会为一分一到十分之间，请您请请您评价。\",\"bg\":151680,\"dB\":\"61.8\",\"db\":\"61.8\",\"speed\":\"5.89\",\"ed\":162849,\"spk\":1},{\"sentence\":\"还担心你吗？\",\"bg\":165560,\"dB\":\"43.9\",\"db\":\"43.9\",\"speed\":\"5.27\",\"ed\":166310,\"spk\":1},{\"sentence\":\"一分。\",\"bg\":166310,\"dB\":\"67.44\",\"db\":\"67.44\",\"speed\":\"1.67\",\"ed\":167310,\"spk\":0},{\"sentence\":\"哦，好的，一分近三分月内，您是否向移动公司投诉过宽带业务？\",\"bg\":167310,\"dB\":\"63.21\",\"db\":\"63.21\",\"speed\":\"5.32\",\"ed\":171810,\"spk\":1},{\"sentence\":\"没投诉。\",\"bg\":171810,\"dB\":\"63.51\",\"db\":\"63.51\",\"speed\":\"2.5\",\"ed\":172810,\"spk\":0},{\"sentence\":\"好的，您认为移动公司宽带业务哪些方面需要改进？\",\"bg\":172810,\"dB\":\"63.14\",\"db\":\"63.14\",\"speed\":\"5.77\",\"ed\":176249,\"spk\":1},{\"sentence\":\"没有。\",\"bg\":178550,\"dB\":\"59.93\",\"db\":\"59.93\",\"speed\":\"2.11\",\"ed\":179300,\"spk\":0},{\"sentence\":\"网络质量，资费套餐服务质量这些方面您觉得需要改进的吗？\",\"bg\":179300,\"dB\":\"62.15\",\"db\":\"62.15\",\"speed\":\"6.17\",\"ed\":183149,\"spk\":1},{\"sentence\":\"网络。\",\"bg\":186940,\"dB\":\"68.87\",\"db\":\"68.87\",\"speed\":\"1.63\",\"ed\":187969,\"spk\":0},{\"sentence\":\"呃，网络的话具体是什么问题呢？\",\"bg\":188760,\"dB\":\"63.23\",\"db\":\"63.23\",\"speed\":\"5.31\",\"ed\":191009,\"spk\":1},{\"sentence\":\"网络不好啊。\",\"bg\":192750,\"dB\":\"68.91\",\"db\":\"68.91\",\"speed\":\"3.13\",\"ed\":194149,\"spk\":0},{\"sentence\":\"哦，网网络质量的话，具体是哪些方面不好呢？\",\"bg\":195120,\"dB\":\"62.92\",\"db\":\"62.92\",\"speed\":\"4.18\",\"ed\":199229,\"spk\":1},{\"sentence\":\"是上个5就掉线了。\",\"bg\":199710,\"dB\":\"71.42\",\"db\":\"71.42\",\"speed\":\"3.27\",\"ed\":201960,\"spk\":0},{\"sentence\":\"是上网速度慢，还是上网稳定性差，或者说是线路故障情况多，还是号线到网速度度差哦，好的，那还有其他方面的问题吗？\",\"bg\":201960,\"dB\":\"63.65\",\"db\":\"63.65\",\"speed\":\"5.05\",\"ed\":211460,\"spk\":1},{\"sentence\":\"没有。\",\"bg\":211460,\"dB\":\"63.67\",\"db\":\"63.67\",\"speed\":\"2.86\",\"ed\":211960,\"spk\":0},{\"sentence\":\"好的，那我的回访就到这里，我们是第三分市场调研公司，祝您生活愉快再见。\",\"bg\":211960,\"dB\":\"61.98\",\"db\":\"61.98\",\"speed\":\"6.39\",\"ed\":216609,\"spk\":1}]}\n";

        // 调用 performQualityCheck，并打印出最终匹配结果
        boolean match = service.performQualityCheck(testText, dbPattern);
        System.out.println("整体匹配结果：" + match);
    }
}
