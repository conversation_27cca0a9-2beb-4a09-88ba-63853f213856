<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="题目编码" prop="qusNo">
        <el-input
          v-model="queryParams.qusNo"
          placeholder="请输入题目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="题目名称" prop="qusName">
        <el-input
          v-model="queryParams.qusName"
          placeholder="请输入题目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="题目类型" prop="qusType">
        <el-select v-model="queryParams.qusType" placeholder="请选择题目类型" clearable :style="{width: '100%'}">
          <el-option v-for="(item, index) in qusTypeList" :key="index" :label="item.label"
                     :value="item.value" :disabled="item.disabled"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="isValid">
        <el-select v-model="queryParams.isValid" placeholder="题目状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['questionnaire:question:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['questionnaire:question:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['questionnaire:question:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['questionnaire:question:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="qusList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="题目编码" align="left" prop="qusNo" show-overflow-tooltip min-width="50"/>
      <el-table-column label="题目名称" align="left" prop="qusName" show-overflow-tooltip min-width="90"/>
      <el-table-column label="题目类型" width="105" align="center" prop="qusType" />
      <el-table-column label="状态" width="55" align="center" prop="isValid">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.isValid"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['questionnaire:question:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['questionnaire:question:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改题目对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="题目名称" prop="qusName">
          <el-input v-model="form.qusName" type="textarea" placeholder="请输入题目名称"
                    :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"/>
        </el-form-item>
        <el-form-item label="题目编码" prop="qusNo">
          <el-input v-model="form.qusNo" placeholder="请输入编码名称" disabled="true"/>
        </el-form-item>
        <el-form-item label="题目类型" prop="qusType">
          <el-radio-group v-model="form.qusType" size="medium">
            <el-radio v-for="(item, index) in typeOptions" :key="index" :label="item.value"
                      :disabled="item.disabled" @change="handleRadioChange">{{item.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="题目状态" prop="isValid">
          <el-radio-group v-model="form.isValid">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="得分配置" v-show="dfshow">
          <el-form-item label="最小值" prop="minV" style="margin-bottom: 10px;">
            <el-input-number v-model="form.minV" placeholder="请输入最小得分" />
          </el-form-item>
          <el-form-item label="最大值" prop="maxV">
            <el-input-number v-model="form.maxV" placeholder="请输入最大得分" />
          </el-form-item>
        </el-form-item>
        <el-form-item label="选项配置" prop="options" v-show="selectshow">
          <div class="row" v-for="(item,index) in form.options" :key="index" style="margin-bottom: 10px;">
            <el-input v-model="item.answer" style="width: 200px;" show-word-limit maxlength="10"></el-input>
            <el-button  type="success" icon="el-icon-plus" @click="addOption" size="mini"
                       style="margin-left: 10px;"></el-button>
            <el-button v-if="index>0" type="danger" icon="el-icon-minus" size="mini"
                       @click="cutOption(index)" style="margin-left: 10px;"></el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listQuestion, getQuestion, delQuestion, addQuestion, updateQuestion } from "@/api/quality/question";
import { Utils } from '@/api/util/common';
export default {
  name: "Question",
  dicts: ['sys_normal_disable'],
  data() {
    var validators = (rule,value,callback) =>{
      let qusType = this.$refs.form._props.model.qusType;
      let options = this.$refs.form._props.model.options
      let arr = []
      options.forEach((item)=>{
        if(item.answer !=''){
          arr.push(item.answer)
        }
      })
      if(arr.length == 0&&(qusType=="单选" || qusType=="多选")){
        callback(new Error('请至少输入一个配置选项'))
      }else{
        callback();
      }
    }
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 题目表格数据
      qusList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示得分
      dfshow: false,
      // 是否显示单选或多选
      selectshow: false,
      qusTypeList: [{"label":'所有',"value":''},{"label":'得分',"value":'得分'},{"label":'单选',"value":'单选'},{"label":'多选',"value":'多选'},{"label":'问答',"value":'问答'}],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        qusNo: undefined,
        qusName: undefined,
        isValid: undefined
      },
      // 表单参数
      form: {
        qusType:"",
        qusName:"",
        options:[{}]
      },
      // 表单校验
      rules: {
        qusName: [
          { required: true, message: "题目名称不能为空", trigger: "blur" }
        ],
        qusNo: [
          { required: true, message: "题目编码不能为空", trigger: "blur" }
        ],
        qusType: [{
          required: true,
          message: '题目类型不能为空',
          trigger: 'change'
        }],
        options: [
          { required: true, validator:validators, trigger: 'blur' }
        ],
      },
      typeOptions: [{
        "label": "得分",
        "value": "得分"
      }, {
        "label": "单选",
        "value": "单选"
      }, {
        "label": "多选",
        "value": "多选"
      }, {
        "label": "问答",
        "value": "问答"
      }],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询题目列表 */
    getList() {
      this.loading = true;
      listQuestion(this.queryParams).then(response => {
        this.qusList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        qusNo: undefined,
        qusName: undefined,
        qusType: "",
        isValid: "0",
        options:[{}]
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.dfshow =  false;
      this.selectshow =  false;
      this.title = "添加题目";
      this.form.qusNo = Utils.getUUID()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getQuestion(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改题目";
        let qusType = this.form.qusType;
        this.dfshow =  false;
        this.selectshow =  false;
        if(qusType=="得分"){
          this.dfshow =  true;
        }else if(qusType=="单选"||qusType=="多选"){
          this.selectshow =  true;
        };
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      if(this.form.options&&(this.form.qusType=="单选"||this.form.qusType=="多选")){
        this.form.options.forEach((item,index)=>{
          item['seq']=index
        })
      }else{
        this.form.options = [];
      }
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateQuestion(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQuestion(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除题目编号为"' + ids + '"的数据项？').then(function() {
        return delQuestion(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('qc/question/export', {
        ...this.queryParams
      }, `question_${new Date().getTime()}.xlsx`)
    },
    handleRadioChange(value) {
      console.log('Radio value changed:', value);
      this.dfshow =  false;
      this.selectshow =  false;
      // 在这里处理点击事件
      if(value=="得分"){
        this.dfshow =  true;
      }else if(value=="单选"||value=="多选"){
        this.selectshow =  true;
        this.form['options'] = [{answer:''}]
      };
    },
    addOption(){
      this.form['options'].push({answer:''})
      console.log(this.form.options,'9999999999')
    },
    cutOption(index){
      this.form['options'].splice(index,1);
    }
  }
};
</script>
