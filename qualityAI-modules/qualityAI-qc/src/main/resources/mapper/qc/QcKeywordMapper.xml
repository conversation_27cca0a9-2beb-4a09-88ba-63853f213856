<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcKeywordMapper">

    <resultMap id="qcKeywordResultMap" type="QcKeyword">
        <result property="id"    column="id"    />
        <result property="keywordLibraryId" column="keyword_library_id" />
        <result property="keywordName" column="keyword_name" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="keywordLibraryName" column="keyword_library_name" />
        <result property="keywordLibraryClassificationName" column="keyword_library_classification_name" />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <select id="getQcKeywordList" resultMap="qcKeywordResultMap">
        SELECT
            qk.id,
            qk.keyword_library_id,
            qk.keyword_name,
            qk.create_by,
            qk.create_time,
            qk.update_by,
            qk.update_time,
            qk.remark,
            qk.status,
            qk.is_deleted,
            qkl.keyword_library_name,
            qklc.keyword_library_classification_name
        FROM
            qc_keyword qk
        LEFT JOIN
            qc_keyword_library qkl ON qkl.id = qk.keyword_library_id
        LEFT JOIN
            qc_keyword_library_classification qklc ON qklc.id = qkl.keyword_library_classification_id
        WHERE
            qk.keyword_library_id = #{keywordLibraryId}
            AND qk.is_deleted = '0'
        <if test="keywordName != null and keywordName != ''">
            AND qk.keyword_name LIKE concat('%', #{keywordName}, '%')
        </if>
        ORDER BY
            qk.id ASC
    </select>

    <insert id="addQcKeyword">
        INSERT INTO
            qc_keyword
        ( tenant_id, keyword_library_id, keyword_name, create_time, create_by, update_time, update_by, remark )
        VALUES
        ( #{tenantId}, #{keywordLibraryId}, #{keywordName}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy}, #{remark})
    </insert>

    <update id="updateQcKeyword">
        UPDATE qc_keyword
        SET keyword_name = #{keywordName},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark},
            status = #{status}
        WHERE
            id = #{id}
    </update>

    <update id="removeQcKeyword">
        UPDATE qc_keyword SET
            is_deleted = '1'
        WHERE
            id = #{id}
    </update>

    <select id="getQcKeywordByKeywordIds" resultMap="qcKeywordResultMap">
        SELECT
            qk.id,
            qk.keyword_library_id,
            qk.keyword_name,
            qk.create_by,
            qk.create_time,
            qk.update_by,
            qk.update_time,
            qk.remark,
            qk.status,
            qk.is_deleted
        FROM
            qc_keyword qk
        WHERE
            qk.keyword_library_id = #{keywordLibraryId}
            AND qk.is_deleted = '0'
        ORDER BY
            qk.id ASC
    </select>
</mapper>