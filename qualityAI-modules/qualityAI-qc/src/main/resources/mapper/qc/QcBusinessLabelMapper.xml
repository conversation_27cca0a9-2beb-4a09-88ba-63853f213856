<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcBusinessLabelMapper">

    <resultMap id="qcBusinessLabelResultMap" type="QcBusinessLabel">
        <result property="id"    column="id"    />
        <result property="businessLabelType" column="business_label_type" />
        <result property="businessLabelName" column="business_label_name" />
        <result property="businessLabelKey" column="business_label_key" />
        <result property="businessLabelValue" column="business_label_value" />
        <result property="businessLabelStatus" column="business_label_status" />
        <result property="businessLabelStringMode" column="business_label_string_mode" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <select id="getQcBusinessLabelList" resultMap="qcBusinessLabelResultMap">
        SELECT
            qbl.id,
            qbl.business_label_type,
            qbl.business_label_name,
            qbl.business_label_key,
            qbl.business_label_value,
            qbl.business_label_status,
            qbl.business_label_string_mode,
            qbl.create_by,
            qbl.create_time,
            qbl.update_by,
            qbl.update_time,
            qbl.remark
        FROM
            qc_business_label qbl
        WHERE
            qbl.is_deleted = '0'
            <if test="businessLabelName != null and businessLabelName != ''">
                AND qbl.business_label_name LIKE concat('%', #{businessLabelName}, '%')
            </if>
            <if test="businessLabelType != null and businessLabelType != ''">
                AND qbl.business_label_type = #{businessLabelType}
            </if>
        ORDER BY
            qbl.id ASC
    </select>

    <insert id="addQcBusinessLabel">
        INSERT INTO
            qc_business_label
        ( tenant_id, business_label_type, business_label_name, business_label_key, business_label_value, business_label_string_mode, create_time, create_by, update_time, update_by, remark )
        VALUES
        ( #{tenantId}, #{businessLabelType}, #{businessLabelName}, #{businessLabelKey}, #{businessLabelValue}, #{businessLabelStringMode}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy}, #{remark})
    </insert>

    <update id="updateQcBusinessLabel">
        UPDATE qc_business_label
        SET business_label_type = #{businessLabelType},
            business_label_name = #{businessLabelName},
            business_label_key = #{businessLabelKey},
            business_label_value = #{businessLabelValue},
            business_label_status = #{businessLabelStatus},
            business_label_string_mode = #{businessLabelStringMode},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark}
        WHERE
            id = #{id}
    </update>

    <update id="removeQcBusinessLabel">
        UPDATE qc_business_label
        SET is_deleted = '1'
        WHERE
            id = #{id}
    </update>
</mapper>