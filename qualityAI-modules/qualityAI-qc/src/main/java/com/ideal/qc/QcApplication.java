package com.ideal.qc;

import com.ideal.common.security.annotation.EnableCustomConfig;
import com.ideal.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableCustomConfig
@EnableRyFeignClients
@EnableScheduling
@SpringBootApplication
@EnableAsync
public class QcApplication {
    public static void main(String[] args) {
        SpringApplication.run(QcApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ qc 系统模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}