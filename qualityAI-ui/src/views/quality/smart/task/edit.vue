<template>
    <div class="app-container">
		<div content="新增手动任务" style="margin-bottom:10px;">
			<div class="el-dialog-title dy-title">新建手动任务</div>
			<el-button class="btn" type="primary" @click="submit" :loading="submiting">保 存</el-button>
			<el-button class="btn" @click="close">取 消</el-button>
	    </div>
		<el-divider class="dy-divider"></el-divider>
        <el-form :model="form" ref="queryForm" size="small" :inline="true" label-width="90px" :rules="rules">
<!--			<el-form-item label="" class="no-label_el-form-item" style="width: 100%">-->
			<el-row :gutter="10" style="width: 100%">
				<el-col :span="8">
					<el-form-item label="任务名称" prop="taskName">
		                <el-input v-model="form.taskName" placeholder="请输入任务名称" clearable show-word-limit max-length="30"/>
		            </el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="评分模板" prop="template">
						<el-select v-model="form.scoreTemplate" >
							<el-option
						      v-for="itemt in tempList"
						      :key="itemt.dictValue"
						      :label="itemt.dictLabel"
						      :value="itemt.dictValue"
							  :disabled="itemt.status==0">
						    </el-option>
						</el-select>
		            </el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="过滤空会话" prop="">
						<el-select v-model="form.filterEmptySession" >
							<el-option :key="1" label="是" :value="1"></el-option>
							<el-option :key="2" label="否" :value="2"></el-option>
						</el-select>
		            </el-form-item>
				</el-col>
			</el-row>
            <el-form-item label="数据集" style="width: 100%" prop="dataset">
				<el-row style="width: 100%">
					<el-col>
						<el-date-picker :default-time="['00:00:00', '23:59:59']" type="datetimerange" v-model="dsl.data.updateTimeStrs" value-format="yyyy-MM-dd HH:mm:ss" @change="getDsList"/>
						<el-select v-model="form.datasetIds" style="margin:5px 5px;" multiple>
							<el-option v-for="item in dsl.list" :key="item.id" :label="item.name" :value="item.id" :disabled="item.processStatus!='finished'"> </el-option>
			              	<el-pagination
				                @size-change="getDsList"
				                @current-change="getDsList"
				                :current-page.sync="dsl.query.pageNum"
				                :page-size="dsl.query.pageSize"
				                layout="total, prev, pager, next"
				                :total="dsl.query.total"
			              	>
			          	  	</el-pagination>
						</el-select>
					</el-col>
				</el-row>
            </el-form-item>
			<div id="planForm">
				<filterModal :filterList="filterList" type="flData" ref="filterModal"></filterModal>
			</div>
        </el-form>
    </div>
</template>

<script>

import { createTask,checkFilterForm} from "@/api/quality/smart";
import {listDatasetByMap} from "@/api/quality/dataset";
import filterModal from "@/views/quality/ruleConfig/common/busFilter";
export default {
	name:"planEdit",
	components:{
		filterModal
	},
    data() {
        return {
			form:{planType:3,filterEmptySession:2,status:1},
			submiting:false,
			dsList:[],
			tempList:[],
			filterList:[],
			dsl:{
				list:[],
				query:{
					pageNum: 1,
			  	    pageSize: 10,
					total:0,
				},
				data:{
					
				}
			},
			rules:{
				taskName: [{ required: true, message: '请输入任务名称', trigger: '11' }],
             	dataset: [{ required: true, message: '请选择数据集', trigger: '11' }],
             	template: [{ required: true, message: '请选择模板', trigger: '11' }],
			}
			
        }
    },
    computed: {
    },
    watch: {
    },

  	created() {
  	},

    methods: {
		reload(row,list){
			this.tempList = list;
		},
		close(){
			this.form = {planType:3,filterEmptySession:2,status:1}
			this.dsList = [];
			this.filterList=[];
			this.$emit('goList',true,'task');
			
		},
		getDsList(){
			listDatasetByMap(this.dsl.query,this.dsl.data).then(response=>{
				this.dsl.list = response.rows;
				this.dsl.query.total = response.total;
			});
		},
		submit(){
			var tmp = this.form;
			if(!tmp.datasetIds || tmp.datasetIds.length<1){
				this.$message({message: '未选择数据集',type: 'error'});
				return;
			}
			if(!tmp.taskName || tmp.taskName == ''){
				this.$message({message: '未填写任务名称',type: 'error'});
				return;
			}
			if(!tmp.scoreTemplate || tmp.scoreTemplate == ''){
				this.$message({message: '未选择评分模板',type: 'error'});
				return;
			}
			var tip = checkFilterForm(this.filterList,1);
			if(tip){
				this.$message({message: tip,type: 'error'});
				return 
			}
			if(this.filterList.length>0){
				this.form["followMatchDetail"] = JSON.stringify(this.filterList[0].ruleCondDetail);
			}else{
				this.form["followMatchDetail"] = null;
			}
			this.submiting = true;
			createTask(this.form).then(response=>{
				//.reload({id:response.data});
				if(this.form.id){
					this.$message({message: "修改成功",type: 'success'});
				}else{
					this.$message({message: "新增成功",type: 'success'});
				}
				this.close();
			}).finally(v=>{
				this.submiting = false;
			});
		}
    }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.inputWidth{
  width: 450px!important;
}
.selectS{
  width: 80px!important;
  margin-right: 10px;
}
.selectM{
  width: 90px!important;
  margin-right: 10px;
}
.selectL{
  width: 180px!important;
  margin-right: 10px;
}
.ruleBox{
  margin-bottom: 20px;
  .ruleTop{
    background: #ebebeb;
    padding: 2px 15px;
    cursor: pointer;
  }
  .ruleCon{
     padding: 18px 15px;
     font-size: 12px;
     background-color: #fafafa;
  }
  button{
        padding: 9px;
  }
}
.dialog-footer{
  width: 100%;
  margin-top: 40px;
  justify-content: center;

}
.orBt{
  margin-bottom: 15px;
}
.hitBox{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top:20px;
  row-gap: 10px; 
}
.keyInput{
  width: 150px;
  margin-right: 10px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 125px;
  margin-right: 10px;
  vertical-align: bottom;
}
.btn{
	float:right;
	margin-right:5px;
}
.no-label_el-form-item .el-form-item__content{
	margin-left: 0px!important;
}
#planForm .el-form-item__content{
	width:90%;
}
.el-form--inline .el-form-item {
    display: inline-block;
    margin-right: 0px!important;
    vertical-align: top;
}
</style>
