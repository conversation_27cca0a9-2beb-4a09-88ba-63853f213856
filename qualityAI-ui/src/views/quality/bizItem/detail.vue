<template>
  <div class="app-container">
    <div class="app-main">
      <el-page-header @back="goBack" :content="'录音详情'">
      </el-page-header>
      <el-divider class="topLine"></el-divider>
      <!-- <WaveSurferPlayer  ref="audioPlayer" :audioSrc="audioUrl"
      @time-update="handleTimeUpdate"
      @duration="handleDurationUpdate"/> -->

      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane v-for="(item,index) in videoList" :key="index" :label="'录音'+(index+1)" :name="index+''">
          <WaveSurferPlayer
          ref="audioPlayer"
          :audioSrc="item"
          @time-update="(time) => handleTimeUpdate(time, index)"
          @duration="handleDurationUpdate"/>
        </el-tab-pane>
      </el-tabs>


      <div class="conMain row">
        <div class="leftMain">
          <!--{{currentTime}}-->
          <el-card>
            <div slot="header" class="card-tit">
              <span>智能转译文本</span>
            </div>
            <editable-text-annotation
              @child-to-parent="handleChildData"
              :parentData="parentData"
              ref="annotator"
            >
            <div class="card-body chatMain" v-if="msgList.length">
              <div :class="!item.spk?'chatBox chatMy':'chatBox'" v-for="(item,index) in msgList" :key="index" ref="itemRefs">

                <img v-if="item.spk" src="@/assets/images/agent.png" mode="" />
                <div v-if="!item.spk" class="chatTime row">
                  <!-- {{convertHours(item.ed-item.bg)}} {{item.bg}}-{{item.ed}}-->
                  <div>{{convertHours(item.ed-item.bg)}}</div><img class="icon" src="@/assets/images/triangle.png" />
                </div>

                <div :style="{textAlign:(!item.spk?'left':'left')}" class="msg" :class="{'blueText': isActive(item) }" @click="jumpToTime(item.bg,item.ed,$event)">
                    <span v-html="highlightTitle(item.text)" @contextmenu.prevent="selectAllText($event,item.bg,item.ed)" class="selectable-text"></span>
                </div>

                <div v-if="item.spk" class="chatTime row">
                  <div>{{convertHours(item.ed-item.bg)}}</div><img class="icon" src="@/assets/images/triangle.png" />
                </div>

                <img v-if="!item.spk" src="@/assets/images/user.png" mode="" />
              </div>
            </div>
            </editable-text-annotation>
          </el-card>

          <el-card style="margin-top:10px;">
            <div slot="header" class="card-tit">
              <span>录音信息</span>
            </div>
            <el-descriptions class="card-body" title="" :column="1" >
              <el-descriptions-item label="唯一主键" >{{info.uniqueKey}}</el-descriptions-item>
            </el-descriptions>
            <el-descriptions class="card-body" title="" :column="3" >
              <el-descriptions-item label="所属平台">{{info.platform}}</el-descriptions-item>
              <el-descriptions-item label="所属项目">{{info.projectName}}</el-descriptions-item>
              <el-descriptions-item label="指标名称">{{info.metricName}}</el-descriptions-item>
              <el-descriptions-item label="子指标">{{info.subMetricName}}</el-descriptions-item>
              <el-descriptions-item label="服务中心">{{info.serviceCenter}}</el-descriptions-item>
              <el-descriptions-item label="坐席工号">{{getName(info.agentId)}}</el-descriptions-item>
              <el-descriptions-item label="坐席姓名">{{getName(info.agentName)}}</el-descriptions-item>
              <el-descriptions-item label="开始时间">{{info.answerStartAt}}</el-descriptions-item>
              <el-descriptions-item label="结束时间">{{info.answerEndAt}}</el-descriptions-item>
              <el-descriptions-item label="通话时长">{{info.callDurationS}}</el-descriptions-item>
              <el-descriptions-item label="日期">{{info.recordDate}}</el-descriptions-item>
              <el-descriptions-item label="电话">{{info.callerPhone}}</el-descriptions-item>
<!--              <el-descriptions-item label="答卷编号">{{info.surveyCode}}</el-descriptions-item>
              <el-descriptions-item label="运营商">{{getName(info.carrier)}}</el-descriptions-item>
              <el-descriptions-item label="省份">{{info.province}}</el-descriptions-item>
              <el-descriptions-item label="地市">{{info.city}}</el-descriptions-item>
              <el-descriptions-item label="联系号码">
                {{getTel(info.contactPhone)}}
              </el-descriptions-item>
              <el-descriptions-item label="区号">{{info.areaCode}}</el-descriptions-item>
              <el-descriptions-item label="服务单号">{{info.serviceOrderId}}</el-descriptions-item>
              <el-descriptions-item label="竣工时间">{{info.finishTime}}</el-descriptions-item>
              <el-descriptions-item label="产品名称">{{info.productName}}</el-descriptions-item>
              <el-descriptions-item label="销售名称">{{getName(info.saleName)}}</el-descriptions-item>
              <el-descriptions-item label="任务结果">{{info.taskResult}}</el-descriptions-item>
              <el-descriptions-item label="一级质检评语">{{info.qcLvl1Result}}</el-descriptions-item>
              <el-descriptions-item label="一级质检员工号">{{getName(info.qcLvl1Comment)}}</el-descriptions-item>
              <el-descriptions-item label="二级质检结果">{{info.qcLvl2Result}}</el-descriptions-item>
              <el-descriptions-item label="二级质检评语">{{info.qcLvl2Comment}}</el-descriptions-item>
              <el-descriptions-item label="二级质检员工号">{{getName(info.qcLvl2EmpId)}}</el-descriptions-item>
              <el-descriptions-item label="来电时间">{{info.callTime}}</el-descriptions-item>-->
            </el-descriptions>
          </el-card>
        </div>


        <div class="rightMain">
          <div class="tab-buttons">
            <button
              v-for="(tab, index) in tabs"
              :key="index"
              @click="currentTab = index"
              :class="{ active: currentTab === index }"
            >
              {{ tab.title }}
            </button>
          </div>
          <div class="tab-content">
            <div v-if="currentTab === 0">
              <el-card>
                <div slot="header" class="card-tit">
                  <span>评测信息</span>
                </div>
                <div class="card-body" style="max-height: 400px;overflow: auto">
                  <div class="ruleBox">
                    <div v-for="(item,index) in scoreList" :key="index">
                      <div class="row">
                        <el-tooltip  effect="dark"  placement="top">
                          <div slot="content" class="toolBox">{{item.quesCode}}</div>
                          <div class="rule-tit">{{index+1}}.{{item.quesCode}}</div>
                        </el-tooltip>
                        <div>{{item.ansValue}}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
            <div v-else-if="currentTab === 1">
              <div class="an-container">
                <el-card shadow="hover" class="annotation-list-card">
                  <div slot="header">
                    <span>标注列表 (共{{annotations.length}}条)</span>
                    <div class="card-actions" style="float: right;padding-right: 20px;">
                      <el-button
                        type="primary"
                        size="small"
                        @click="exportAnnotations"
                      >导出标注</el-button>
                    </div>
                  </div>
                  <el-table
                    :data="annotations"
                    border
                    style="width: 100%"
                    @row-click="handleRowClick"
                    height="300"
                  >
                    <el-table-column prop="type" label="类型" width="120">
                      <template slot-scope="scope">
                        <el-tag :type="getTagType(scope.row.type)">
                          {{ getTypeLabel(scope.row.type) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="originalText" label="原始文本" width="200">
                      <template slot-scope="scope">
                        <div class="text-truncate" :title="scope.row.originalText">
                          {{ scope.row.originalText }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="correctText" label="修正文本">
                      <template slot-scope="scope">
                        <div class="text-truncate" :title="scope.row.correctText">
                          {{ scope.row.correctText }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
                    <el-table-column label="操作" width="150">
                      <template slot-scope="scope">
                        <el-button
                          size="mini"
                          @click.stop="editAnnotation(scope.row)"
                        >编辑</el-button>
                        <el-button
                          size="mini"
                          type="danger"
                          @click.stop="deleteAnnotation(scope.row)"
                        >删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
              </div>
            </div>
            <div v-else-if="currentTab === 2">
              <el-card>
                <div slot="header" class="card-tit">
                  <span>收听记录</span>
                </div>
                <div class="card-body" style="max-height: 400px;overflow: auto">
                  <el-table :data="listenList" size="small" height="300">
                    <el-table-column label="收听时长" prop="listenLong" show-overflow-tooltip align="center">
                    </el-table-column>
                    <el-table-column label="收听人" prop="createBy" show-overflow-tooltip  align="center">
                    </el-table-column>
                    <el-table-column label="收听时间" prop="createTime" align="center">
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </div>
          </div>
<!--          <el-card style="margin:10px 0;">
            <div slot="header" class="card-tit">
              <div class="row spaceBet">
                <div class="row">
                  <div>AI质检模版详情</div>
                  <el-button type="primary" size="mini" @click="handleTemp" style="margin-left:10px;">查看</el-button>
                </div>
&lt;!&ndash;                <div v-if="tableData.length">质检结果：<span class="redText">{{tableData[0].aiResult}}</span></div>&ndash;&gt;
              </div>
            </div>
          </el-card>-->
          <el-card style="margin:10px 0;">
            <div slot="header" class="card-tit">
              <div class="row spaceBet">
                <div class="row">
                  <div>人工质检模版详情</div>
                </div>
                <!--                <div v-if="tableData.length">质检结果：<span class="redText">{{tableData[0].aiResult}}</span></div>-->
              </div>
            </div>
            <div class="card-body"  style="max-height: 400px;overflow: auto">
              <el-table :data="manCheckData" size="small" height="300">
                <el-table-column label="模版名称" prop="templateName" show-overflow-tooltip align="center" fixed>
                </el-table-column>
<!--                <el-table-column label="评分项" prop="itemName" show-overflow-tooltip align="center">
                </el-table-column>
                <el-table-column label="规则组" prop="ruleGroupName" show-overflow-tooltip  align="center">
                </el-table-column>-->
                <el-table-column label="规则" prop="ruleName" show-overflow-tooltip  align="center">
                </el-table-column>
                <!--                <el-table-column label="智能结果" width="90" prop="aiResult" align="center">
                                </el-table-column>-->
                <el-table-column label="规则结果" prop="recheckResult" align="center" width="105">
<!--                  <template #default="{row}">
                    {{ formatResult(row.recheckResult) }}
                  </template>-->
                </el-table-column>
                <el-table-column label="评分项结果" prop="itemResult" align="center" width="105">
                  <template #default="{row}">
                    {{ formatItemResult(row.itemResult) }}
                  </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="createTime" show-overflow-tooltip  align="center">
                </el-table-column>
              </el-table>
            </div>
          </el-card>
          <el-card style="margin:10px 0;">
            <div slot="header" class="card-tit">
              <div class="row spaceBet">
                <div class="row">
                  <div>人工复检模版详情</div>
                </div>
                <!--                <div v-if="tableData.length">质检结果：<span class="redText">{{tableData[0].aiResult}}</span></div>-->
              </div>
            </div>
            <div class="card-body" style="max-height: 400px;overflow: auto">
              <el-table :data="manCheckAIData" size="small" height="300">
                <el-table-column label="模版名称" prop="templateName" show-overflow-tooltip align="center" fixed>
                </el-table-column>
<!--                <el-table-column label="评分项" prop="itemName" show-overflow-tooltip align="center">
                </el-table-column>
                <el-table-column label="规则组" prop="ruleGroupName" show-overflow-tooltip  align="center">
                </el-table-column>-->
                <el-table-column label="规则" prop="ruleName" show-overflow-tooltip  align="center">
                </el-table-column>
                <el-table-column label="智能结果" width="90" prop="smartResult" align="center">
                  <template #default="{row}">
                    {{ formatResult(row.smartResult) }}
                  </template>
                </el-table-column>
                <el-table-column label="复检结果" prop="recheckResult" align="center" width="105">
<!--                  <template #default="{row}">
                    {{ formatResult(row.recheckResult) }}
                  </template>-->
                </el-table-column>
                <el-table-column label="评分项结果" prop="itemResult" align="center" width="105">
                  <template #default="{row}">
                    {{ formatItemResult(row.itemResult) }}
                  </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="createTime" show-overflow-tooltip  align="center">
                </el-table-column>
              </el-table>
            </div>
          </el-card>
          <el-card>
            <div slot="header" class="card-tit">
              <span>人工质检结果</span>
            </div>
            <div class="card-body">
              <el-table :data="resultCheckData" size="small">
                <el-table-column label="质检类型" width="90" prop="qcType" show-overflow-tooltip align="center" fixed>
                  <template #default="{row}">
                    {{ formatType(row.qcType) }}
                  </template>
                </el-table-column>
                <el-table-column label="模版名称" width="150" prop="templateName" show-overflow-tooltip  align="center" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="质检结果" width="120" prop="qcResult" align="center" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="废卷点" width="120" prop="invalidDesc" align="center" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="质检内容" width="100" prop="remark" align="center" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="智能结果" width="100" prop="aiResult" align="center" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="智能结果是否正确" width="130" prop="aiIsRight" align="center">
                  <template #default="{row}">
                    {{ formatStatus(row.status) }}
                  </template>
                </el-table-column>
                <el-table-column label="创建时间" width="150" prop="createTime" align="center" show-overflow-tooltip>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </div>
      </div>

    </div>

    <el-dialog :title="conf.view.title" :visible.sync="conf.view.open" width="1600px"height="800px" append-to-body :close-on-click-modal="false">
      <div v-if="conf.view.open">
        <tempViewModal :viewParam="conf.view.param" ref="tempViewModal"></tempViewModal>
      </div>

    </el-dialog>
  </div>

</template>

<script>
import { reviewInfo,aiResList,tempFirstList,tempAIList,soundInfo,reviewDetail,getManualResult,
  getListenList,
  addListen,
  getSmartCheckList } from "@/api/quality/detail";
import {listItem} from "@/api/quality/scoreItem";
import WaveSurferPlayer from '@/components/WaveSurferPlayer'
import { Utils } from '@/api/util/common';
import {listAnnotation, delAnnotation, listAllAnnotation} from "@/api/quality/annotation";
import EditableTextAnnotation  from '@/components/AdvancedTextAnnotation'
import tempViewModal from "@/views/quality/smart/view/template2";
export default {
  name: "History",
  components:{
    WaveSurferPlayer,
    EditableTextAnnotation,
    tempViewModal,
  },
  data() {
    return {
      currentTab: 0,
      conf:{
        view:{
          title:"",
          open:false,
          param:{tempId:0,taskDetailId:0,op:'view'}
        }
      },
      tabs: [
        { title: '评测信息' },
        { title: '标注列表' },
        { title: '收听记录' }
      ],
      aiIsRightArray:[{"label":"是","value":"1"},{"label":"否","value":"0"}],
      qcResultArray:[{"label":"废卷（模糊打分）"},{"label":"需修改(内容修改)"},{"label":"废卷（关键性差错）"},{"label":"需注意"},{"label":"废卷（打分错误)"},
      {"label":"需修改(打分错误)"},
      {"label":"废卷（无效、敷衍打分）"},
      {"label":"废卷（漏题打分）"},
      {"label":"废卷（矛盾打分）"},
      {"label":"废卷（甄别错误）"},
      {"label":"废卷（抢先打分）"},
      {"label":"废卷"}],
      annotations: [],
      parentData: {
        dataId: "",
        oldUrl: "",
        newUrl: "",
        newUrlName: "",
        startTime: "",
        endTime: ""},
      msgList:[],
      scoreList:[],
      tableData:[],
      ruleVal:'',
      ruleList:[],
      treeList:[],
      ruleIndex:null,
      resultCheckData:[],//人工质检结果数据
      manCheckData:[],//人工质检规则数据
      manCheckAIData:[],//人工复检规则数据
      qualTempData:[],// 评分规则模板表格数据
      reResultList:['0','1','2','3','4','5','6','7','8','9','10','废卷','待修正'],
      drawer:false,
      // audioUrl: require('../../../assets/images/test2.wav'),
      // audioUrl:'',
      info:{},
      bizNo:"",
      bizType:"",
      isShow: false,
      currentTime: 0,
      duration: 0,
      keyWords:[],
      // 是否展开，默认全部折叠
      isExpandAll: false,
      isEdit:1,//1带编辑的，0仅查看
      videoList: [],
      txtList:[],
      activeName: '0',
      form: {},
      currDatasetItemId:'',
      manualTaskDetailId:0,
      manualTempId:0,
      maxTimes: [], // 存储每个音频的最大播放时间
      currentTimes: [], // 存储每个音频的当前播放时间
      listenList:[]
    };
  },
  created() {
    let bizNo = this.$route.query.bizNo
    let bizType = this.$route.query.type
    this.bizNo = bizNo;
    this.bizType = bizType;
    reviewInfo(bizNo,bizType,0).then(res => { //人工复检
      this.info = res.data
      this.isShow = true
      let answersJson = this.info.answersJson
      if (answersJson != null && answersJson != "") {
        this.scoreList = JSON.parse(answersJson);
      }
      this.getVoice(bizNo);
      this.getTempList();
      this.getManCheckList();
      this.getResultCheckList();
      this.getListenList();
      this.getSmartCheckList();
    })

  },
  watch: {
  },
  beforeDestroy(){
      this.calculateTotalMaxTime();
  },

  methods: {
    getTel(v){
      return Utils.getTel(v)
    },
    getName(v){
      return Utils.getName(v)
    },
    getListenList(){
      getListenList({bizNo:this.bizNo}).then(response => {
        this.listenList = response.data;
      });
    },
    formatMilliseconds(ms) {
      // 1. 将毫秒转为秒
      const totalSeconds = Math.floor(ms / 1000);

      // 2. 计算小时、分钟、秒
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      // 3. 格式化为 2 位数（补零）
      const pad = (num) => num.toString().padStart(2, '0');

      return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
    },
    handleTemp(){
      if(this.info.taskDetailId==null){
        this.$message.warning("无AI任务模版信息");
          return;
      }
      this.conf.view.open=true;
      this.conf.view.title="AI模版规则";
      this.conf.view.param.tempId = this.info.tempId
      this.conf.view.param.taskDetailId = this.info.taskDetailId
    },
    handleTempManual(){
      if(this.info.manualTaskDetailId==null){
        this.$message.warning("无人工质检任务模版信息");
        return;
      }
      this.conf.view.open=true;
      this.conf.view.title="人工模版规则";
      this.conf.view.param.tempId = this.info.manualTempId
      this.conf.view.param.taskDetailId = this.info.manualTaskDetailId
    },
    handleClick(tab, event) {
      let index = Number(tab.index)
      this.msgList = []
      this.msgList = this.txtList[index].msgList.list
      this.currDatasetItemId = this.txtList[index].msgList.datasetItemId;
      this.parentData.dataId=this.currDatasetItemId;
      this.getAnnotation(this.currDatasetItemId);
      this.$refs.audioPlayer[index].seekTo(0);
    },
    formatStatus(status) {
      if(status=='1'){
        return "是";
      }else if(status=='0'){
        return "否";
      }else{
        return "";
      }
    },
    formatItemResult(status) {
      if(status=='1'){
        return "命中";
      }else if(status=='2'){
        return "不命中";
      }else{
        return "";
      }
    },
    formatType(type) {
      return type === '1' ? '人工质检' : '人工复检'
    },
    formatResult(type) {
      return type === '1' ? '命中' : ''
    },
    handleChildData(data){
      this.annotations=data;
    },
    selectAllText(event,startTime,endTime) {
      this.parentData.startTime=startTime;
      this.parentData.endTime=endTime;
    },
    // 导出标注
    exportAnnotations() {
      this.download('qc/api/audio/exportSamples', {
        dataId:this.currDatasetItemId
      }, `audio_samples_${new Date().getTime()}.zip`)
    },

    // 清空所有标注
    clearAllAnnotations() {
      if (this.annotations.length === 0) return

      this.$confirm('确定要清空所有标注吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.annotations = []
        this.$message.success('已清空所有标注')
      }).catch(() => {})
    },

    // 表格行点击
    handleRowClick(row) {
      this.$refs.annotator.showAnnotationDetail(row)
    },

    // 编辑标注
    getAnnotation(dataId) {
      listAllAnnotation({dataId:dataId}).then(response => {
        this.annotations = response.data;
        this.parentData.annotations=this.annotations;
      });
    },
    // 编辑标注
    editAnnotation(row) {
      this.$refs.annotator.editCurrentAnnotation(row)
    },

    // 删除标注
    deleteAnnotation(row) {
      this.$confirm('确定要删除此标注吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delAnnotation(row.id).then(response => {
          this.annotations = this.annotations.filter(a => a.id !== row.id)
          this.parentData.annotations=this.annotations;
          this.$message.success('标注已删除')
        });
      }).catch(() => {})
    },

    // 获取标签类型
    getTagType(type) {
      switch(type) {
        case 'correction': return 'danger'
        case 'highlight': return 'warning'
        case 'question': return 'primary'
        case 'comment': return 'success'
        default: return 'info'
      }
    },

    // 获取类型标签
    getTypeLabel(type) {
      const types = [
        { value: 'correction', label: '文本修正' },
        { value: 'highlight', label: '重点标注' },
        { value: 'question', label: '疑问点' },
        { value: 'comment', label: '评注' }
      ]
      const item = types.find(t => t.value === type)
      return item ? item.label : '未知类型'
    },
    //获取当前播放时长
    handleTimeUpdate(timeInMs, index) {
      this.currentTime = Math.round(timeInMs*1000);
      this.highlightAndScroll();

      const timeInSeconds = Math.round(timeInMs * 1000);
      this.$set(this.currentTimes, index, timeInSeconds);
      // 更新最大播放时间
      if (!this.maxTimes[index] || timeInSeconds > this.maxTimes[index]) {
        this.$set(this.maxTimes, index, timeInSeconds);
      }
    },
    calculateTotalMaxTime() {
      const total = this.maxTimes.reduce((sum, time) => sum + (time || 0), 0);
      console.log('每段录音的最大播放时间:', this.maxTimes);
      console.log('总播放时长:', total);
      if(total > 0){
        // 这里可以发送到服务器或存储到Vuex/store
        let listenLong =this.formatMilliseconds(total)
        console.log(listenLong,'总时长----------'); // 输出 "00:01:47"
        let json = {listenLong:listenLong,bizNo:this.bizNo}
        addListen(json).then(res => {
          console.log('addListen保存播放时长:', res);
        });
      }
      return total;
    },


    handleDurationUpdate(durationInMs) {
      this.duration = durationInMs;
    },
    isActive(item) {
      return this.currentTime >= item.bg && this.currentTime < item.ed;
    },
    jumpToTime(startTime,endTime,event) {
      this.currentTime = startTime
      // this.$refs.audioPlayer.seekTo(startTime);
      this.$refs.audioPlayer[Number(this.activeName)].seekTo(startTime);
      if (event.target.classList.contains('span_c')) {
        const vValue = event.target.getAttribute("_v");
        this.showAnnotation(vValue);
      }
    },
    //滚动到高亮位置
    highlightAndScroll() {
      const highlightedIndex = this.msgList.findIndex(item => this.isActive(item));
      if (highlightedIndex !== -1) {
        const itemElement = this.$refs.itemRefs[highlightedIndex];
        if (itemElement) {
          itemElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }
    },
    //匹配关键词
    highlightTitle(msg) {
      let highlightedText = msg;
      let i = 0;
      this.annotations.forEach(key => {
        if (msg.includes(key.originalText)) {
          highlightedText = highlightedText.replace(
            new RegExp(key.originalText, 'g'),
            `<span style="color: #F56C6C;border: 2px dashed blue;" class="span_c" _v="${i}">${key.originalText}</span>`
          );
        }
        i++;
      });
      return highlightedText;
      /*let highlightedText = msg;
      this.keyWords.forEach(key => {
        if (msg.includes(key)) {
          highlightedText = highlightedText.replace(
            new RegExp(key, 'g'),
            `<span style="color: #F56C6C;">${key}</span>`
          );
        }
      });
      return highlightedText;*/
    },
    showAnnotation(idx){
      let tmp = this.annotations[parseInt(idx)]
      this.$refs.annotator.showAnnotationDetail(tmp)
    },
    getAiResList(){
      if(!this.info.templateId) return
      aiResList({dataNo:this.info.onlyNo,templateId:this.info.templateId}).then(res => {
        this.tableData = res.data
      });
    },
    getTempList(){
      /*tempFirstList(this.manualDetailId).then(res => {
        let json = JSON.parse(res.msg);
        let array = [];
        array.push(json);
        this.ruleList = array;
        this.treeList = array;
      });*/
      /*if(this.bizType=="1"){//人工质检
        tempFirstList(this.bizNo).then(res => {
          let json = JSON.parse(res.msg);
          let array = [];
          array.push(json);
          this.ruleList = array;
          this.treeList = array;
          //this.treeList = Utils.listToTree(res.data)
        });
      }else{ //人工复检
        tempAIList(this.bizNo).then(res => {
          let json = JSON.parse(res.msg);
          let array = [];
          array.push(json);
          this.ruleList = array;
          this.treeList = array;
        });
      }*/
    },
    getVoice(bizNo){
      soundInfo(bizNo).then(res => {
        //let videoList = res.data.videoList
        // let txtList = res.data.txtList
        // console.log("1--------"+JSON.stringify(videoList))
        // console.log("2--------"+JSON.stringify(txtList))

        this.videoList = res.data.videoList
        this.txtList = res.data.txtList
        this.msgList = res.data.txtList[0].msgList.list
        this.currDatasetItemId = res.data.txtList[0].msgList.datasetItemId;
        this.parentData.dataId=this.currDatasetItemId;
        this.getAnnotation(this.currDatasetItemId);
        console.log(this.txtList,'this.txtList---')
        this.keyWords = res.data.keys
      });
    },
    getManCheckList(){
      getManualResult(this.bizNo).then(res => {
        this.manCheckData = res.data
      });
    },
    getSmartCheckList(){
      getSmartCheckList(this.bizNo).then(res => {
        this.manCheckAIData = res.data
      });
    },
    getResultCheckList(){
      reviewDetail(this.bizNo).then(res => {
        this.resultCheckData = res.data
      });
    },
     /** 查询评分规则模板列表 */
    getListByScoretplId() {
      listItem({scoretplId: this.info.templateId}).then(response => {
        this.qualTempData = response.data;
      });
    },
    goBack() {
      this.$store.dispatch("tagsView/delView", this.$route); //关闭当前页
      // this.$router.replace({ path: "/data/quality"}); // 要打开的页面
      this.$router.back()
    },
  }
}
</script>
<style lang="scss" scoped>

.app-container{
  background: #eff1f4;
  padding: 24px;
  min-height: calc(100vh - 50px);
}
.app-main{
  background: rgb(255, 255, 255);
  min-height: calc(-98px + 100vh);
  border-radius: 4px;
  padding: 20px;
}
.blueText{
  color: #409EFF!important;
}
.redText{
  color: #F56C6C!important;
}
.topLine{
  margin: 15px 0;
}
.conMain{
  width: 100%;
  align-items: flex-start;
  margin-top: 10px;
}
.leftMain{
  padding-right: 10px;
  // border: 1px solid red;
  width: 50%;
}
.chatMain{
  //  border: 1px solid red;
  height: 400px;
  // height: calc(100vh - 280px);
  overflow: auto;
}
.rightMain{
  width: 50%;
}

.card-tit{
  font-size: 14px;
}
.card-body{
  font-size: 13px;
}
.ruleBox{
  display: grid;
  grid-template-columns: repeat(2,1fr);
  grid-column-gap: 20px;
  grid-row-gap: 18px;
  justify-content: space-between;
}
.rule-tit{
  width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
}


.resBt{
  margin: 0 20px;
}
.selBox{
  margin-bottom: 10px;
  .label{
    font-size: 13px;
    margin-right: 10px;
  }
  .addBt{
    margin-left: 10px;
  }
}
.footBt{
  justify-content: center;
  margin: 10px 0;
}


.chatBox{
	display: flex;
	flex-direction: row;
	margin-bottom: 10px;
  font-size: 13px;
  // border: 1px solid blue;
	img{
		width: 40px;
		height: 40px;
	}
	.msg{
    // display: inline-block;
		border-radius: 5px;
		background-color: #f5f5f5;
		padding: 8px 12px;
		max-width: 360px;
		word-wrap: break-word;
		word-break: break-all;
    margin: 0 10px;
    position: relative;
    text-align: left;
    color: #606266;
    line-height: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
	}


}
.chatTime{
  color: #999;
  font-size:12px;
  cursor: pointer;
  .icon{
    width: 20px;
    height: 20px;
  }
}
.chatMy{
	justify-content: flex-end;
}

.msg-item.chat-mine {
	text-align: right;
	padding-left: 0;
	padding-right: 30px;

	.chat-user {
	  left: auto;
	  right: 3px;

	  cite {
		left: auto;
		right: 30px;
		text-align: right;

		i {
		  padding-left: 0;
		  // padding-right: 15px;
		}
	  }
	}

	.chat-text {
	  margin-left: 0;
	  text-align: left;
	  background-color: #cad5e5;
	  color: #000;
	}

	.chat-text::after {
	  top: 15px;
	  left: auto;
	  right: -10px;
	  border-top: 5upx solid transparent;
	  border-left: 10upx solid #cad5e5;
	  border-bottom: 5upx solid transparent;
	  border-right: unset;
	}

	.chat-system {
	  margin-left: 15px;
	}
}
.drawerBox{
  padding: 0 20px;
}
.toolBox{
  max-width: 600px;
}
.annotation-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tab-buttons button {
  padding: 8px 16px;
  margin-right: 5px;
  background: #f0f0f0;
  border: none;
  cursor: pointer;
}

.tab-buttons button.active {
  background: #ddd;
  font-weight: bold;
  color: #2c86c9;
}
</style>
