<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ZjDataReviewMapper">
    
    <resultMap type="com.ideal.qc.domain.ZjDataReview" id="ZjDataReviewResult">
        <result property="id"    column="id"    />
        <result property="dataId"    column="data_id"    />
        <result property="templateId"    column="template_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemName"    column="itemName"    />
        <result property="aiResult"    column="ai_result"    />
        <result property="reviewResult"    column="review_result"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectZjDataReviewVo">
        select id, data_id, template_id, item_id,
            (select item_name from zj_score_item t where s.item_id=t.id) itemName,
               ai_result, review_result, create_by, create_time, update_by, update_time from zj_data_review s
    </sql>

    <select id="selectZjDataReviewList" parameterType="ZjDataReview" resultMap="ZjDataReviewResult">
        <include refid="selectZjDataReviewVo"/>
        <where>  
            <if test="dataId != null  and dataId != ''"> and data_id = #{dataId}</if>
            <if test="templateId != null "> and template_id = #{templateId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="reviewResult != null  and reviewResult != ''"> and review_result = #{reviewResult}</if>
        </where>
    </select>
    
    <select id="selectZjDataReviewById" parameterType="Long" resultMap="ZjDataReviewResult">
        <include refid="selectZjDataReviewVo"/>
        where id = #{id}
    </select>

    <insert id="insertZjDataReview" parameterType="ZjDataReview" useGeneratedKeys="true" keyProperty="id">
        insert into zj_data_review
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataId != null">data_id,</if>
            <if test="templateId != null">template_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="aiResult != null">ai_result,</if>
            <if test="reviewResult != null">review_result,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataId != null">#{dataId},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="aiResult != null">#{aiResult},</if>
            <if test="reviewResult != null">#{reviewResult},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateZjDataReview" parameterType="ZjDataReview">
        update zj_data_review
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="aiResult != null">ai_result = #{aiResult},</if>
            <if test="reviewResult != null">review_result = #{reviewResult},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZjDataReviewById" parameterType="Long">
        delete from zj_data_review where id = #{id}
    </delete>

    <delete id="deleteZjDataReviewByIds" parameterType="String">
        delete from zj_data_review where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectReviewData" resultMap="ZjDataReviewDataResult">
        select  t.id,j.id dataId, platform, project, target_name, target_sub_name,
                service_center, work_no, work_name, start_time, end_time, long_time, data_date, qs_no, telephone, business_type,
                service_provider, j.only_no, province, city, contact_phone, area, product_type, service_order_no, service_type, completion_time, product_name, sale_name, seq_no,
                test_month, call_result, task_result, audio_url, incoming_time,
                (select GROUP_CONCAT(ai_result,',') from zj_data_airesult a where a.data_no=j.only_no) aiResult,
                (SELECT tpl_name FROM zj_score_template a WHERE a.id=t.zj_template_id) templateName,
            t.replay_check_time firstCheckTime,
            '' asrBy,
            t.zj_template_id
        from zj_data j
            left join zj_data_template t on j.id=t.data_id
        where t.zj_status='1'
        <if test="aiResult != null  and aiResult != ''"> and exists(select * from zj_data_airesult a where ai_result = #{aiResult} and a.data_no=j.only_no)</if>
        <if test="dataNo != null  and dataNo != ''"> and j.only_no=#{dataNo}</if>

    </select>
    <resultMap type="com.ideal.qc.domain.ZjDataReviewData" id="ZjDataReviewDataResult">
        <result property="id"    column="id"    />
        <result property="dataNo"    column="only_no"    />
        <result property="templateName"    column="templateName"    />
        <result property="firstCheckTime"    column="firstCheckTime"    />
        <result property="asrBy"    column="asrBy"    />
        <result property="templateId"    column="zj_template_id"    />
        <result property="dataId"    column="zj_template_id"    />
        <result property="workNo"    column="work_no"    />
        <result property="workName"    column="work_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="aiResult"    column="aiResult"    />
        <result property="dataId"    column="dataId"    />
    </resultMap>

    <select id="selectAiResultData" resultType="com.ideal.qc.domain.ZjDataAiresult">
        SELECT id,data_no dataNo,template_id templateId,(SELECT t.tpl_name FROM zj_score_template t WHERE t.id=template_id) templateName,
               item_id itemId,
               (SELECT t.item_name FROM zj_score_item t WHERE t.id=s.item_id) itemName,
               (SELECT t.parent_id FROM zj_score_item t WHERE t.id=s.item_id) parentId,
               ai_result FROM zj_data_airesult s WHERE data_no=#{dataNo} AND template_id=#{templateId}
    </select>

    <select id="selectAiTemplateData" resultType="com.ideal.qc.domain.ZjDataAiresult">
        SELECT
        t.id            templateId,
        t.tpl_name      templateName,
        i.id            id,
        i.item_name     itemName,
        i.parent_id     parentId,
        ai.ai_result    aiResult
        FROM zj_score_template t
        LEFT JOIN zj_score_item i
        ON t.id = i.scoretpl_id
        LEFT JOIN zj_data_airesult ai
        ON ai.template_id=t.id AND ai.item_id=i.id AND ai.data_no=#{dataNo}
        WHERE t.id = #{templateId}
    </select>

    <update id="completeZjDataReview" parameterType="Map">
        update zj_data_template set zj_status='2',replay_check_by=#{handleBy},replay_check_time=now() where id=#{id}
    </update>

    <select id="getReviewListByDataId" parameterType="java.lang.Long" resultMap="ZjDataReviewResult">
        <include refid="selectZjDataReviewVo"/>
        where data_id = #{value}
    </select>
</mapper>