package com.ideal.qc.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcRule;

/**
 * 质检规则Service接口
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface IQcRuleService
{
    /**
     * 查询质检规则
     *
     * @param ruleId 质检规则主键
     * @return 质检规则
     */
    public QcRule selectQcRuleByRuleId(Long ruleId);

    /**
     * 查询质检规则列表
     *
     * @param QcRule 质检规则
     * @return 质检规则集合
     */
    public List<QcRule> selectQcRuleList(JSONObject QcRule);

    /**
     * 新增质检规则
     *
     * @param QcRule 质检规则
     * @return 结果
     */
    public int insertQcRule(QcRule QcRule);

    /**
     * 修改质检规则
     *
     * @param QcRule 质检规则
     * @return 结果
     */
    public Long updateQcRule(QcRule QcRule);


	/**
	 * 查询质检规则
	 *
	 * @param ruleId 质检规则主键
	 * @return 质检规则
	 */
	QcRule selectQcRule(QcRule QcRule);

	void reloadRules();
	
	public List selectQcRuleRecording(JSONObject param);
	
	public List queryKeyWordLibList();
}
