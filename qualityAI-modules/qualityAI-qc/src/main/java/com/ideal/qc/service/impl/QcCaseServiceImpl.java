package com.ideal.qc.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcCase;
import com.ideal.qc.dto.QcCaseDTO;
import com.ideal.qc.mapper.QcCaseMapper;
import com.ideal.qc.service.QcCaseService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 案例Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/20 17:46
 */
@Service
public class QcCaseServiceImpl implements QcCaseService {

    @Resource
    private QcCaseMapper qcCaseMapper;

    /**
     * 查询案例列表
     */
    @Override
    public List<QcCase> getQcCaseList(JSONObject payload) {
        String caseStatus = payload.getString("caseStatus");
        if (ObjectUtil.isEmpty(caseStatus)) {
            payload.put("caseStatus", "1");
        }
        List<QcCase> qcCaseList = Lists.newArrayList();
        if (ObjectUtil.equal(caseStatus, "1") || ObjectUtil.equal(caseStatus, "2")) {
            qcCaseList = qcCaseMapper.getQcCaseList(payload);
        }
        if (ObjectUtil.equal(caseStatus, "3")) {
            Set<String> roleKeyList = SecurityUtils.getLoginUser().getRoles();
            if (roleKeyList.contains("admin") || roleKeyList.contains("manager")) {
                qcCaseList = qcCaseMapper.getQcCaseList(payload);
            } else {
                String username = SecurityUtils.getUsername();
                payload.put("username", username);
                qcCaseList = qcCaseMapper.getQcCaseListForRejected(payload);
            }
        }
        return qcCaseList;
    }

    /**
     * 新增案例
     */
    @Override
    public void addQcCase(JSONObject payload) {
        payload.put("createTime", DateTime.now());
        payload.put("createBy", SecurityUtils.getUsername());
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        payload.put("tenantId", SecurityUtils.getTenantId());
        qcCaseMapper.addQcCase(payload);
    }

    /**
     * 修改案例
     */
    @Override
    public void updateQcCase(JSONObject payload) {
        qcCaseMapper.updateQcCase(payload);
    }

    /**
     * 查询案列的每个案列状态数量
     */
    @Override
    public QcCaseDTO getQcCaseEachCaseStatusQuantity(JSONObject payload) {
        QcCaseDTO qcCaseDTO = qcCaseMapper.getQcCaseEachCaseStatusQuantity(payload);
        Set<String> roleKeyList = SecurityUtils.getLoginUser().getRoles();
        if (!roleKeyList.contains("admin") && !roleKeyList.contains("manager")) {
            String username = SecurityUtils.getUsername();
            payload.put("username", username);
            Integer rejectedQuantity = qcCaseMapper.getQcCaseRejectedQuantity(payload);
            qcCaseDTO.setRejectedQuantity(rejectedQuantity);
        }
        return qcCaseDTO;
    }

    /**
     * 查询标记数量根据人工计划ID
     */
    @Override
    public int listCount(JSONObject payload) {
        return qcCaseMapper.listCount(payload);
    }

    /**
     * 根据案例分类ID查询所有案例
     */
    @Override
    public List<QcCase> getQcCaseByCaseClassificationId(Long classificationId) {
        return qcCaseMapper.getQcCaseByCaseClassificationId(classificationId);
    }
}
