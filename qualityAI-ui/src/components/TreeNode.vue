<template>
  <div class="tree-node">
    <div class="node-content" :class="{
      'is-leaf': isLeaf,
      'is-selected': node.id === selectedId
    }">
      <span
        v-if="hasChildren"
        class="toggle-icon"
        @click.stop="toggleExpand"
      >
        {{ isExpanded ? '▼' : '▶' }}
      </span>
      <span v-else class="toggle-icon">•</span>

      <span class="node-label" @click.stop="handleNodeClick">
        {{ node.name }}
        <span v-if="!isLeaf" class="non-leaf-hint"> (非末级)</span>
      </span>
    </div>
    <div v-show="isExpanded && hasChildren" class="children">
      <tree-node
        v-for="child in node.children"
        :key="child.id"
        :node="child"
        :selected-id="selectedId"
        :default-expand-all="defaultExpandAll"
        @node-click="$emit('node-click', $event)"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'TreeNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    selectedId: {
      type: [String, Number],
      default: null
    },
    defaultExpandAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isExpanded: this.defaultExpandAll
    };
  },
  computed: {
    hasChildren() {
      return this.node.children && this.node.children.length > 0;
    },
    isLeaf() {
      return !this.hasChildren;
    }
  },
  methods: {
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },
    handleNodeClick() {
      this.$emit('node-click', {
        node: this.node,
        isLeaf: this.isLeaf
      });
    }
  }
};
</script>

<style scoped>
.tree-node {
  margin: 5px 0;
}

.node-content {
  padding: 6px 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
}

.node-content:hover {
  background-color: #f5f7fa;
}

.node-content.is-selected {
  background-color: #ecf5ff;
  color: #409eff;
}

.node-content.is-leaf .node-label {
  font-weight: bold;
}

.toggle-icon {
  display: inline-block;
  width: 20px;
  text-align: center;
  font-size: 12px;
  color: #909399;
  cursor: pointer;
}

.node-label {
  flex: 1;
}

.non-leaf-hint {
  font-size: 0.8em;
  color: #909399;
  font-style: italic;
  margin-left: 4px;
}

.children {
  margin-left: 20px;
  border-left: 1px dashed #e0e0e0;
  padding-left: 10px;
  transition: all 0.3s ease;
}
</style>
