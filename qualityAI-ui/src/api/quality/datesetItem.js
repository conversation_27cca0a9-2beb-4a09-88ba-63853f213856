import request from '@/utils/request'

// 查询语音数据明细列表
export function listDatesetItem(query) {
  return request({
    url: '/qc/datesetItem/list',
    method: 'get',
    params: query
  })
}

// 查询语音数据明细详细
export function getDatesetItem(id) {
  return request({
    url: '/qc/datesetItem/' + id,
    method: 'get'
  })
}

// 新增语音数据明细
export function addDatesetItem(data) {
  return request({
    url: '/qc/datesetItem',
    method: 'post',
    data: data
  })
}

// 修改语音数据明细
export function updateDatesetItem(data) {
  return request({
    url: '/qc/datesetItem',
    method: 'put',
    data: data
  })
}

// 删除语音数据明细
export function delDatesetItem(id) {
  return request({
    url: '/qc/datesetItem/' + id,
    method: 'delete'
  })
}
