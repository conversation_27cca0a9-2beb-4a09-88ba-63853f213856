package com.ideal.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.constant.CacheConstants;
import com.ideal.common.core.utils.StringUtils;
import com.ideal.common.redis.service.RedisService;
import com.ideal.system.api.domain.SysUser;
import com.ideal.system.domain.SysUserUnlock;
import com.ideal.system.service.ISysUserService;
import com.ideal.system.service.ISysUserUnlockService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 账户解锁Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/6/27 16:43
 */
@Service
public class SysUserUnlockServiceImpl implements ISysUserUnlockService {

    @Resource
    private RedisService redisService;
    @Resource
    private ISysUserService sysUserService;

    /**
     * 获取已锁定账户列表
     *
     * @param payload 需要的一些参数
     * @return 已锁定账户列表
     */
    @Override
    public List<SysUserUnlock> getUserUnlockList(JSONObject payload) {
        Collection<String> keys = redisService.keys(CacheConstants.PWD_ERR_CNT_KEY + "*");
        List<SysUserUnlock> userUnlockList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(keys)) {
            for (String key : keys) {
                SysUserUnlock sysUserUnlock = new SysUserUnlock();
                long expire = redisService.getExpire(key);
                sysUserUnlock.setLockTime(expire);
                String[] split = StringUtils.split(key, ":");
                SysUser sysUser = sysUserService.selectUserByUserName(split[1]);
                sysUserUnlock.setWorkNo(sysUser.getWorkNo());
                sysUserUnlock.setUserName(sysUser.getUserName());
                sysUserUnlock.setNickName(sysUser.getNickName());
                userUnlockList.add(sysUserUnlock);
            }
            String paramForWorkNo = payload.getString("workNo");
            String paramForUserName = payload.getString("userName");
            String paramForNickName = payload.getString("nickName");
            userUnlockList = userUnlockList.stream().filter(item -> item.getWorkNo().contains(paramForWorkNo)
                    && item.getUserName().contains(paramForUserName)
                    && item.getNickName().contains(paramForNickName)).collect(Collectors.toList());
        }
        return userUnlockList;
    }

    /**
     * 账户解锁
     *
     * @param userName 登录名称
     */
    @Override
    public void unlockUser(String userName) {
        redisService.deleteObject(CacheConstants.PWD_ERR_CNT_KEY + userName);
    }
}
