<template>
  <div class="tree-select" ref="selectContainer">
    <div class="select-input" @click="toggleDropdown">
      <span v-if="selectedNode">{{ selectedNode.name }}</span>
      <span v-else class="placeholder">{{ placeholder }}</span>
      <span class="arrow-icon">{{ isOpen ? '▲' : '▼' }}</span>
    </div>

    <div v-show="isOpen" class="dropdown-container">
      <div v-if="showAlert" class="alert-message">
        <i class="alert-icon">⚠️</i> 请选择最末级节点
      </div>
      <div class="tree-container">
        <tree-view
          ref="treeView"
          :tree-data="treeData"
          :default-expand-all="defaultExpandAll"
          @node-click="handleNodeClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import TreeView from './TreeView.vue';

export default {
  name: 'TreeSelect',
  components: {
    TreeView
  },
  props: {
    treeData: {
      type: Array,
      required: true,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    defaultExpandAll: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isOpen: false,
      selectedNode: null,
      showAlert: false
    };
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    toggleDropdown() {
      this.isOpen = !this.isOpen;
      this.showAlert = false;
    },
    handleNodeClick({ node, isLeaf }) {
      if (isLeaf) {
        this.selectedNode = node;
        this.isOpen = false;
        this.$emit('select', node);
      } else {
        this.showAlert = true;
        setTimeout(() => {
          this.showAlert = false;
        }, 2000);
      }
    },
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.isOpen = false;
      }
    }
  }
};
</script>

<style scoped>
.tree-select {
  position: relative;
  width: 300px;
  font-family: Arial, sans-serif;
  z-index: 1000;
}

.select-input {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
}

.select-input:hover {
  border-color: #c0c4cc;
}

.placeholder {
  color: #c0c4cc;
}

.arrow-icon {
  font-size: 12px;
  color: #c0c4cc;
}

.dropdown-container {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 260px;
  overflow-y: auto;
  margin-top: 5px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 2000;
}

.alert-message {
  padding: 8px 15px;
  background-color: #fff6f6;
  color: #f56c6c;
  border-bottom: 1px solid #fde2e2;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.alert-icon {
  margin-right: 5px;
}

.tree-container {
  padding: 10px;
}
</style>
