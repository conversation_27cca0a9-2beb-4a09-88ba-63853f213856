package com.ideal.qc.service.impl;

import java.util.List;
import com.ideal.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.ScoreTemplateMapper;
import com.ideal.qc.domain.ScoreTemplate;
import com.ideal.qc.service.IScoreTemplateService;

import javax.annotation.Resource;

/**
 * 评分模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Service
public class ScoreTemplateServiceImpl implements IScoreTemplateService
{
    @Resource
    private ScoreTemplateMapper scoreTemplateMapper;

    /**
     * 查询评分模板
     *
     * @param id 评分模板主键
     * @return 评分模板
     */
    @Override
    public ScoreTemplate selectScoreTemplateById(Long id)
    {
        return scoreTemplateMapper.selectScoreTemplateById(id);
    }

    /**
     * 查询评分模板列表
     *
     * @param scoreTemplate 评分模板
     * @return 评分模板
     */
    @Override
    public List<ScoreTemplate> selectScoreTemplateList(ScoreTemplate scoreTemplate)
    {
        return scoreTemplateMapper.selectScoreTemplateList(scoreTemplate);
    }

    /**
     * 新增评分模板
     *
     * @param scoreTemplate 评分模板
     * @return 结果
     */
    @Override
    public int insertScoreTemplate(ScoreTemplate scoreTemplate)
    {
        scoreTemplate.setCreateTime(DateUtils.getNowDate());
        return scoreTemplateMapper.insertScoreTemplate(scoreTemplate);
    }

    /**
     * 修改评分模板
     *
     * @param scoreTemplate 评分模板
     * @return 结果
     */
    @Override
    public int updateScoreTemplate(ScoreTemplate scoreTemplate)
    {
        scoreTemplate.setUpdateTime(DateUtils.getNowDate());
        return scoreTemplateMapper.updateScoreTemplate(scoreTemplate);
    }

    /**
     * 批量删除评分模板
     *
     * @param ids 需要删除的评分模板主键
     * @return 结果
     */
    @Override
    public int deleteScoreTemplateByIds(Long[] ids)
    {
        return scoreTemplateMapper.deleteScoreTemplateByIds(ids);
    }

    /**
     * 删除评分模板信息
     *
     * @param id 评分模板主键
     * @return 结果
     */
    @Override
    public int deleteScoreTemplateById(Long id)
    {
        return scoreTemplateMapper.deleteScoreTemplateById(id);
    }
}
