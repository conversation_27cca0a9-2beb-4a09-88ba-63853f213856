<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcSmartTaskMapper">

	<!-- resultMap qc_smart_task -->
	<resultMap type="QcSmartTask" id="QcSmartTaskResult">
		<id property="id" column="id" />
		<result property="taskName" column="task_name" />
		<result property="taskType" column="task_type" />
		<result property="sessionCount" column="session_count" />
		<result property="relSessionCount" column="rel_session_count" />
		<result property="hitRuleSessionCount" column="hit_rule_session_count" />
		<result property="sessionViolatePct" column="session_violate_pct" />
		<result property="createTime" column="create_time" />
		<result property="createBy" column="create_by" />
		<result property="updateTime" column="update_time" />
		<result property="updateBy" column="update_by" />
		<result property="remark" column="remark" />
		<result property="status" column="status" />
		<result property="tenantId" column="tenant_id" />
		<result property="planId" column="plan_id" />
		<result property="planName" column="plan_name" />
		<result property="progress" column="progress" />
		<result property="planExecTime" column="plan_exec_time" />
		<result property="isSematic" column="is_sematic" />
		<result property="reTryCount" column="re_try_count" />
		<result property="templateId" column="template_id" />
	</resultMap>
	<!-- select qc_smart_task sql -->
	<sql id="selectQcSmartTaskVo">
		select id,task_name,task_type,session_count,hit_rule_session_count,rel_session_count,session_violate_pct,create_time,create_by,update_time,update_by,remark,status,tenant_id,plan_id,plan_name,progress,is_sematic,re_try_count,template_id
		from qc_smart_task
	</sql>
	<!-- select qc_smart_task sql -->
	<sql id="selectQcSmartTaskWhere">
		<if test="taskName != null and taskName != ''">and task_name like concat('%',#{taskName},'%')</if>
		<if test="taskType != null and taskType != ''">and task_type=#{taskType}</if>
		<if test="sessionCount != null and sessionCount != ''">and session_count=#{sessionCount}</if>
		<if test="hitRuleSessionCount != null and hitRuleSessionCount != ''">and hit_rule_session_count=#{hitRuleSessionCount}</if>
		<if test="sessionViolatePct != null and sessionViolatePct != ''">and session_violate_pct=#{sessionViolatePct}</if>
		<if test="createTime != null and createTime != ''">and create_time=#{createTime}</if>
		<if test="createBy != null and createBy != ''">and create_by=#{createBy}</if>
		<if test="updateTime != null and updateTime != ''">and update_time=#{updateTime}</if>
		<if test="updateBy != null and updateBy != ''">and update_by=#{updateBy}</if>
		<if test="remark != null and remark != ''">and remark=#{remark}</if>
		<if test="status != null and status != ''">and status=#{status}</if>
		<if test="tenantId != null and tenantId != ''">and tenant_id=#{tenantId}</if>
		<if test="planId != null and planId != ''">and plan_id=#{planId}</if>
		<if test="planName != null and planName != ''">and plan_name like concat('%',#{planName},'%')</if>
		<if test="createTimeStr != null and createTimeStr.size() == 2">and create_time between #{createTimeStr[0]} and #{createTimeStr[1]}</if>
		<if test="isSematic != null and isSematic != ''">and is_sematic=#{isSematic}</if>
	</sql>
	<!-- select qc_smart_task sql -->
	<select id="selectQcSmartTaskList" parameterType="map" resultMap="QcSmartTaskResult">
		<include refid="selectQcSmartTaskVo" />
		<where>
			<include refid="selectQcSmartTaskWhere" />
		</where>
	</select>
	<!-- select qc_smart_task sql -->
	<select id="selectQcSmartTaskListCount" parameterType="map" resultType="int">
		select count(1) from qc_smart_task 
		<where>
			<include refid="selectQcSmartTaskWhere" />
		</where>
	</select>
	<!-- insert qc_smart_task sql -->
	<insert id="insertQcSmartTask" parameterType="QcSmartTask" useGeneratedKeys="true" keyProperty="id">
		insert into qc_smart_task
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="taskName != null and taskName != ''">task_name,</if>
			<if test="taskType != null and taskType != ''">task_type,</if>
			<if test="sessionCount != null and sessionCount != ''">session_count,</if>
			<if test="hitRuleSessionCount != null and hitRuleSessionCount != ''">hit_rule_session_count,</if>
			<if test="sessionViolatePct != null and sessionViolatePct != ''">session_violate_pct,</if>
			create_time,
			<if test="createBy != null and createBy != ''">create_by,</if>
			<if test="remark != null and remark != ''">remark,</if>
			<if test="status != null and status != ''">status,</if>
			<if test="tenantId != null and tenantId != ''">tenant_id,</if>
			<if test="planId != null and planId != ''">plan_id,</if>
			<if test="planName != null and planName != ''">plan_name,</if>
			<if test="planExecTime != null and planExecTime != ''">plan_exec_time,</if>
			<if test="isSematic != null and isSematic != ''">is_sematic,</if>
			<if test="templateId != null and templateId != ''">template_id,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="taskName != null and taskName != ''">#{taskName},</if>
			<if test="taskType != null and taskType != ''">#{taskType},</if>
			<if test="sessionCount != null and sessionCount != ''">#{sessionCount},</if>
			<if test="hitRuleSessionCount != null and hitRuleSessionCount != ''">#{hitRuleSessionCount},</if>
			<if test="sessionViolatePct != null and sessionViolatePct != ''">#{sessionViolatePct},</if>
			now(),
			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<if test="remark != null and remark != ''">#{remark},</if>
			<if test="status != null and status != ''">#{status},</if>
			<if test="tenantId != null and tenantId != ''">#{tenantId},</if>
			<if test="planId != null and planId != ''">#{planId},</if>
			<if test="planName != null and planName != ''">#{planName},</if>
			<if test="planExecTime != null and planExecTime != ''">#{planExecTime},</if>
			<if test="isSematic != null and isSematic != ''">#{isSematic},</if>
			<if test="templateId != null and templateId != ''">#{templateId},</if>
		</trim>
	</insert>
	<!-- update qc_smart_task sql -->
	<update id="updateQcSmartTask" parameterType="QcSmartTask" >
		update qc_smart_task
		<trim prefix="SET" suffixOverrides=",">
			<if test="taskName != null and taskName != ''">task_name=#{taskName},</if>
			<if test="taskType != null and taskType != ''">task_type=#{taskType},</if>
			<if test="sessionCount != null">session_count=#{sessionCount},</if>
			<if test="relSessionCount != null ">rel_session_count=#{relSessionCount},</if>
			<if test="hitRuleSessionCount != null ">hit_rule_session_count=#{hitRuleSessionCount},</if>
			<if test="sessionViolatePct != null and sessionViolatePct != ''">session_violate_pct=#{sessionViolatePct},</if>
			update_time=now(),
			<if test="updateBy != null and updateBy != ''">update_by=#{updateBy},</if>
			<if test="remark != null and remark != ''">remark=#{remark},</if>
			<if test="status != null and status != ''">status=#{status},</if>
			<if test="tenantId != null and tenantId != ''">tenant_id=#{tenantId},</if>
			<if test="planId != null and planId != ''">plan_id=#{planId},</if>
			<if test="planName != null and planName != ''">plan_name=#{planName},</if>
		</trim>
 		where id=#{id}
	</update>
	<!-- delete qc_smart_task sql -->
	<delete id="deleteQcSmartTaskByIds" parameterType="String" >
		delete from qc_smart_task where id in
		<foreach item="item" collection="array" open="(" separator="," close=")">
			#{item}
 		</foreach>
	</delete>
	<!-- resultMap qc_smart_task_detail -->
	<resultMap type="QcSmartTaskDetail" id="QcSmartTaskDetailResult">
		<id property="id" column="id" />
		<result property="smartTaskId" column="smart_task_id" />
		<result property="bizNo" column="biz_no" />
		<result property="workNo" column="work_no" />
		<result property="workName" column="work_name" />
		<result property="sessionLength" column="session_length" />
		<result property="hitRuleCount" column="hit_rule_count" />
		<result property="macInspectScore" column="mac_inspect_score" />
		<result property="macInspectTime" column="mac_inspect_time" />
		<result property="checkInspectTime" column="check_inspect_time" />
		<result property="status" column="status" />
		<result property="createTime" column="create_time" />
		<result property="createBy" column="create_by" />
		<result property="updateTime" column="update_time" />
		<result property="updateBy" column="update_by" />
		<result property="tenantId" column="tenant_id" />
		<result property="templateId" column="template_id" />
		<result property="hitItemCount" column="hit_item_count" />
		<result property="hitPlusRuleCount" column="hit_plus_rule_count" />
		<result property="hitMinusRuleCount" column="hit_minus_rule_count" />
		<result property="qcSmartSemanticsTaskId" column="qc_Smart_Semantics_Task_Id" />
		<result property="templateHitRule" column="template_hit_rule"/>
		<result property="macInspectResult" column="mac_inspect_result"/>
		<result property="checkInspectScore" column="check_inspect_score"/>
		
	</resultMap>
	<!-- select qc_smart_task_detail sql -->
	<sql id="selectQcSmartTaskDetailVo">
		select id,smart_task_id,biz_no,work_no,work_name,session_length,hit_rule_count,mac_inspect_score,mac_inspect_time,check_inspect_time,status,create_time,create_by,update_time,update_by,tenant_id,template_id,hit_plus_rule_count,hit_minus_rule_count,template_hit_rule,mac_inspect_result,check_inspect_score
		from qc_smart_task_detail qstd
	</sql>
	<sql id="selectQcSmartTaskDetailWhere">
		<if test="id != null and id != ''">and id=#{id}</if>
		<if test="createTimeBeg != null and createTimeBeg != ''">and create_time>#{createTimeBeg}</if>
		<if test="createTimeEnd != null and createTimeEnd != ''">and create_time&lt;#{createTimeEnd}</if>
		<if test="updateTimeBeg != null and updateTimeBeg != ''">and (update_time>#{updateTimeBeg} or create_time>#{updateTimeBeg})</if>
		<if test="updateTimeEnd != null and updateTimeEnd != ''">and (update_time&lt;#{updateTimeEnd} or create_time&lt;#{updateTimeEnd})</if>
		<if test="updateTime != null and updateTime != ''">and update_time=#{updateTime}</if>
		<if test="smartTaskId != null and smartTaskId != ''">and smart_task_id=#{smartTaskId}</if>
		<if test="status != null and status != ''">and status=#{status}</if>
		<if test="bizNo != null and bizNo != ''">and biz_no=#{bizNo}</if>
		<if test="workNo != null and workNo != ''">and work_no=#{workNo}</if>
		<if test="workName != null and workName != ''">and work_name=#{workName}</if>
		<if test="sessionLength != null and sessionLength != ''">and session_length=#{sessionLength}</if>
		<if test="hitRuleCount != null and hitRuleCount != ''">and hit_rule_count=#{hitRuleCount}</if>
		<if test="hitPlusRuleCount != null and hitPlusRuleCount != ''">and hit_plus_rule_count=#{hitPlusRuleCount}</if>
		<if test="hitMinusRuleCount != null and hitMinusRuleCount != ''">and hit_minus_rule_count=#{hitMinusRuleCount}</if>
		<if test="macInspectScore != null and macInspectScore != ''">and mac_inspect_score=#{macInspectScore}</if>
		<if test="macInspectTime != null and macInspectTime != ''">and mac_inspect_time=#{macInspectTime}</if>
		<if test="checkInspectTime != null and checkInspectTime != ''">and check_inspect_time=#{checkInspectTime}</if>
		<if test="createTime != null and createTime != ''">and create_time=#{createTime}</if>
		<if test="createTimeStr != null and createTimeStr.size() == 2">and create_time between #{createTimeStr[0]} and #{createTimeStr[1]}</if>
		<if test="createBy != null and createBy != ''">and create_by=#{createBy}</if>
		<if test="updateBy != null and updateBy != ''">and update_by=#{updateBy}</if>
		<if test="tenantId != null and tenantId != ''">and tenant_id=#{tenantId}</if>
		<if test="templateId != null and templateId != ''">and template_id=#{templateId}</if>
		<if test="macInspectResult != null and macInspectResult != ''">and mac_inspect_result=#{macInspectResult}</if>
		<if test="ruleIds != null and ruleIds.size() >0 ">and exists(select 1 from qc_smart_task_result qstr where qstr.task_detail_id=qstd.id and qstr.rule_id in
			<foreach collection="ruleIds" item="id" open="(" separator="," close=")">
			#{id}
			</foreach>
			)
		</if>	
	</sql>
	<!-- select qc_smart_task_detail sql -->
	<select id="selectQcSmartTaskDetailCount" parameterType="Map" resultType="int">
		select count(1) from qc_smart_task_detail
		<where>
			<include refid="selectQcSmartTaskDetailWhere" />
		</where>
	</select>
	<select id="selectQcSmartTaskDetailList" parameterType="Map" resultMap="QcSmartTaskDetailResult">
		<include refid="selectQcSmartTaskDetailVo" />
		<where>
			<include refid="selectQcSmartTaskDetailWhere" />
		</where>
		order by id asc
	</select>

	<!--人工复检筛选数据 (新建人工质检计划)-->
	<select id="selectSmartTaskDetailForRecheck" resultMap="QcSmartTaskDetailResult">
		<include refid="selectQcSmartTaskDetailVo" />
		where
		mac_inspect_time &gt; #{dataRangeDate}
		and status = '4'
		<if test="planId != null and planId != ''">
			and smart_task_id in (select id from qc_smart_task where plan_id = #{planId})
		</if>
		<if test="hitRules != null and hitRules.size() > 0">
			and id in
			(select distinct task_detail_id
			from qc_smart_task_result
			where rule_item_id in
			<foreach collection="hitRules" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
			)
		</if>
	</select>

	<!--人工复检筛选数据 （新建人工质检任务）-->
	<select id="selectSmartTaskDetailForRecheckForTask" resultMap="QcSmartTaskDetailResult">
		<include refid="selectQcSmartTaskDetailVo" />
		where
		mac_inspect_time &gt; #{dataRangeDate}
		and status = '4'
		and smart_task_id = #{smartTaskId}
		<if test="hitRules != null and hitRules.size() > 0">
			and id in
			(select distinct task_detail_id
			from qc_smart_task_result
			where rule_item_id in
			<foreach collection="hitRules" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
			)
		</if>
	</select>

	<!--获取所有已完成的智能质检任务-->
	<select id="selectQcSmartTaskFinished" resultMap="QcSmartTaskResult">
		<include refid="selectQcSmartTaskVo" />
		where status = 3
	</select>

	<!--	根据id查询智能质检任务-->
	<select id="selectQcSmartTaskById" resultMap="QcSmartTaskResult">
		<include refid="selectQcSmartTaskVo" />
		where id = #{id}
	</select>

	<!-- insert qc_smart_task_detail sql -->
	<insert id="insertQcSmartTaskDetail" useGeneratedKeys="true" keyProperty="id">
		insert ignore into qc_smart_task_detail
		(smart_task_id,biz_no,work_no,work_name,session_length,status,create_time,
			create_by,tenant_id,template_id,template_hit_rule)
		values
		<foreach item="item" collection="List" separator="," >
			(#{item.smartTaskId},#{item.bizNo},#{item.workNo},#{item.workName},#{item.sessionLength},#{item.status},
			now(),#{item.createBy},#{item.tenantId},#{item.templateId},#{item.templateHitRule})
		</foreach>
	</insert>
	<!-- delete qc_smart_task_detail sql -->
	<delete id="deleteQcSmartTaskDetailByIds" parameterType="String" >
		delete from qc_smart_task_detail where id in
		<foreach item="item" collection="deleteList" open="(" separator="," close=")">
			#{item.id}
 		</foreach>
	</delete>
	
	<!-- insert qc_smart_task_result sql -->
	<sql id="insertQcSmartTaskResultSql" >
		insert into qc_smart_task_result
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="taskDetailId != null and taskDetailId != ''">task_detail_id,</if>
			<if test="item.itemName != null and item.itemName != ''">item_name,</if>
			<if test="item.itemId != null and item.itemId != ''">item_id,</if>
			<if test="item.ruleId != null and item.ruleId != ''">rule_id,</if>
			<if test="item.ruleItemId != null and item.ruleItemId != ''">rule_item_id,</if>
			<if test="item.smartResult != null and item.smartResult != ''">smart_result,</if>
			<if test="item.result != null and item.result != ''">smart_result,</if>
			<!-- insert 时，智能结果为空 表示时复检插入 -->
			<if test="item.smartResult == null or item.smartResult == ''">first_recheck_time,</if>
			<if test="item.recheckResult != null and item.recheckResult != ''">recheck_Result,</if>
			<if test="item.firstRecheckTime != null and item.firstRecheckTime != ''">first_recheck_time,</if>
			<if test="item.finalResult != null and item.finalResult != ''">final_result,</if>
			create_time,
			<if test="createBy != null and createBy != ''">create_by,</if>
			<if test="item.createBy != null and item.createBy != ''">create_by,</if>
			<if test="item.detail != null and item.detail != ''">smart_result_detail,</if>
			<if test="item.semResult != null and item.semResult != ''">semantics_Result,</if>
			<if test="item.ruleName != null and item.ruleName != ''">rule_name,</if>
			<if test="item.itemResult != null and item.itemResult != ''">item_result,</if>
			<if test="item.remark != null and item.remark != ''">remark,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="taskDetailId != null and taskDetailId != ''">#{taskDetailId},</if>
			<if test="item.itemName != null and item.itemName != ''">#{item.itemName},</if>
			<if test="item.itemId != null and item.itemId != ''">#{item.itemId},</if>
			<if test="item.ruleId != null and item.ruleId != ''">#{item.ruleId},</if>
			<if test="item.ruleItemId != null and item.ruleItemId != ''">#{item.ruleItemId},</if>
			<if test="item.smartResult != null and item.smartResult != ''">#{item.smartResult},</if>
			<if test="item.result != null and item.result != ''">#{item.result},</if>
			<!-- insert 时，智能结果为空 表示时复检插入 -->
			<if test="item.smartResult == null or item.smartResult == ''">now(),</if>
			<if test="item.recheckResult != null and item.recheckResult != ''">#{item.recheckResult},</if>
			<if test="item.firstRecheckTime != null and item.firstRecheckTime != ''">now(),</if>
			<if test="item.finalResult != null and item.finalResult != ''">#{item.finalResult},</if>
			now(),
			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<if test="item.createBy != null and item.createBy != ''">#{item.createBy},</if>
			<if test="item.detail != null and item.detail != ''">#{item.detail},</if>
			<if test="item.semResult != null and item.semResult != ''">#{item.semResult},</if>
			<if test="item.ruleName != null and item.ruleName != ''">#{item.ruleName},</if>
			<if test="item.itemResult != null and item.itemResult != ''">#{item.itemResult},</if>
			<if test="item.remark != null and item.remark != ''">#{item.remark},</if>
		</trim>
	</sql>

	<insert id="addQcSmartTaskResult" >
		<foreach item="item" collection="detailList" separator=";">
			<include refid="insertQcSmartTaskResultSql" />
 		</foreach>
	
	</insert>

	<!-- resultMap qc_smart_task_result -->
	<resultMap type="QcSmartTaskResult" id="QcSmartTaskResultResult">
		<id property="id" column="id" />
		<result property="taskDetailId" column="task_detail_id" />
		<result property="itemName" column="item_name" />
		<result property="ruleId" column="rule_id" />
		<result property="ruleItemId" column="rule_item_id" />
		<result property="smartResult" column="smart_result" />
		<result property="recheckResult" column="recheck_result" />
		<result property="firstRecheckTime" column="first_recheck_time" />
		<result property="finalResult" column="final_result" />
		<result property="createTime" column="create_time" />
		<result property="createBy" column="create_by" />
		<result property="updateTime" column="update_time" />
		<result property="updateBy" column="update_by" />
		<result property="semanticsResult" column="semantics_result"/>
		<result property="remark" column="remark"/>
	</resultMap>
	<!-- select qc_smart_task_result sql -->
	<sql id="selectQcSmartTaskResultVo">
		select qstr.id,qstr.task_detail_id,qstr.item_name,qstr.rule_id,qstr.rule_item_id,qstr.smart_result,qstr.recheck_result,qstr.first_recheck_time,qstr.final_result,qstr.create_time,qstr.create_by,qstr.update_time,qstr.update_by,semantics_Result,smart_result_detail,remark
		from qc_smart_task_result qstr
	</sql>
	<!-- select qc_smart_task_result sql -->
	<select id="selectQcSmartTaskResult" parameterType="QcSmartTaskResult" resultMap="QcSmartTaskResultResult">
		<include refid="selectQcSmartTaskResultVo" />
		<where>
			<if test="createTime != null and createTime != ''">and qstr.create_time=#{createTime}</if>
			<if test="updateTime != null and updateTime != ''">and qstr.update_time=#{updateTime}</if>
			<if test="taskDetailId != null and taskDetailId != ''">and qstr.task_detail_id=#{taskDetailId}</if>
			<if test="ruleId != null and ruleId != ''">and qstr.rule_id=#{ruleId}</if>
			<if test="ruleItemId != null and ruleItemId != ''">and qstr.rule_item_id=#{ruleItemId}</if>
			<if test="itemName != null and itemName != ''">and qstr.item_name=#{itemName}</if>
			<if test="smartResult != null and smartResult != ''">and qstr.smart_result=#{smartResult}</if>
			<if test="recheckResult != null and recheckResult != ''">and qstr.recheck_result=#{recheckResult}</if>
			<if test="firstRecheckTime != null and firstRecheckTime != ''">and qstr.first_recheck_time=#{firstRecheckTime}</if>
			<if test="finalResult != null and finalResult != ''">and qstr.final_result=#{finalResult}</if>
			<if test="createBy != null and createBy != ''">and qstr.create_by=#{createBy}</if>
			<if test="updateBy != null and updateBy != ''">and qstr.update_by=#{updateBy}</if>
		</where>
	</select>
	<select id="selectSemanticsIdsByTempId" resultType="map">
		select 
		  qrc.semantics_ids as semanticsIds,group_concat(distinct qrc.id) as qrcIds
		from qc_scoring_template qst 
		  join qc_scoring_classification qsc on qst.id=qsc.scoring_template_id
		  join qc_scoring_item qsi on qsi.scoring_classification_id =qsc.id
		  join qc_scoring_hit_rule_group qshrg on qshrg.scoring_item_id=qsi.id
		  join qc_scoring_hit_rule_item qshri on qshri.scoring_hit_rule_group_id=qshrg.id
		  join qc_rule qr on qr.rule_id = qshri.rule_id
		  join qc_rule_cond qrc on qrc.rule_id=qr.rule_id
		where 
		  qst.id=#{id} and qrc.semantics_ids is not null
		group by qrc.semantics_ids
	</select>
	
	<!-- qc_smart_semantics_detail -->
	<!-- resultMap qc_smart_semantics_detail -->
	<!--  <resultMap type="QcSmartSemanticsDetail" id="QcSmartSemanticsDetailResult">
		<id property="id" column="id" />
		<result property="semanticsId" column="semantics_id" />
		<result property="ruleCondId" column="rule_cond_id" />
		<result property="matchRule" column="match_rule" />
		<result property="result" column="result" />
		<result property="status" column="status" />
		<result property="qcSmartSemanticsTaskId" column="qc_Smart_Semantics_Task_Id" />
	</resultMap>-->
	<!-- select qc_smart_semantics_detail sql -->
	<sql id="selectQcSmartSemanticsDetailVo">
		select qssd.id,qssd.semantics_id,qssd.rule_cond_id,qssd.match_rule,qssd.result,qssd.status
		from qc_smart_semantics_detail qssd
	</sql>
	<!-- select qc_smart_semantics_detail sql -->
	<!-- <select id="selectQcSmartSemanticsDetail" parameterType="QcSmartSemanticsDetail" resultMap="QcSmartSemanticsDetailResult">
		<include refid="selectQcSmartSemanticsDetailVo" />
		<where>
			<if test="semanticsId != null and semanticsId != ''">and qssd.semantics_id=#{semanticsId}</if>
			<if test="ruleCondId != null and ruleCondId != ''">and qssd.rule_cond_id=#{ruleCondId}</if>
			<if test="matchRule != null and matchRule != ''">and qssd.match_rule=#{matchRule}</if>
			<if test="result != null and result != ''">and qssd.result=#{result}</if>
			<if test="status != null and status != ''">and qssd.status=#{status}</if>
		</where>
	</select>
	 -->
	<insert id="insertQcSmartSemanticsTask" useGeneratedKeys="true" keyProperty="qcSmartSemanticsTaskId">
		insert into qc_smart_semantics_task
		(task_det_id,biz_no,create_time,tenant_id,status)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.id},#{item.bizNo},now(),#{item.tenantId},'wait')
		</foreach>
	</insert>
	<insert id="insertQcSmartSemanticsDetail" useGeneratedKeys="true" keyProperty="id">
		insert into qc_smart_semantics_detail
		(semantics_id,rule_cond_id,match_rule,status)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.semanticsId},#{item.ruleCondId},#{item.matchRule},'wait')
		</foreach>
	</insert>
	<select id="queryQcSmartSemanticsResult">
		select 
		  qssd.rule_cond_id as ruleCondId ,qssd.result ,qsst.status,qssd.match_rule as matchRule
		from 
		  qc_smart_semantics_task qsst join qc_smart_semantics_detail qssd on qsst.id = qssd.semantics_id
		where 
		  qsst.biz_no=#{bizNo} 
		  and qsst.task_det_id=#{taskDetId}
		  <if test="status != null and status != ''">
		  and qsst.`status`=#{status}
		  </if>
	
	</select>
	<!-- update qc_smart_task_detail sql -->
	<update id="updateQcSmartTaskDetail" parameterType="map" >
		update qc_smart_task_detail
		<trim prefix="SET" suffixOverrides=",">
			<if test="workNo != null and workNo != ''">work_no=#{workNo},</if>
			<if test="workName != null and workName != ''">work_name=#{workName},</if>
			<if test="sessionLength != null and sessionLength != ''">session_length=#{sessionLength},</if>
			<if test="hitRuleCount != null">hit_rule_count=#{hitRuleCount},</if>
			<if test="macInspectScore != null and macInspectScore != ''">mac_inspect_score=#{macInspectScore},</if>
			<if test="macInspectTime != null and macInspectTime != ''">mac_inspect_time=now(),</if>
			<if test="checkInspectTime != null and checkInspectTime != ''">check_inspect_time=#{checkInspectTime},</if>
			<if test="status != null and status != ''">status=#{status},</if>
			update_time=now(),
			<if test="updateBy != null and updateBy != ''">update_by=#{updateBy},</if>
			<if test="hitPlusRuleCount != null ">hit_plus_rule_count=#{hitPlusRuleCount},</if>
			<if test="hitMinusRuleCount != null">hit_minus_rule_count=#{hitMinusRuleCount},</if>
			<if test="templateHitRule != null and templateHitRule != ''">template_hit_rule=#{templateHitRule},</if>
			<if test="checkInspectScore != null and checkInspectScore != ''">check_inspect_score=#{checkInspectScore},</if>
			<if test="macInspectResult != null and macInspectResult != ''">mac_inspect_result=#{macInspectResult},</if>
			<if test="checkInspectResult != null and checkInspectResult != ''">check_inspect_result=#{checkInspectResult},</if>
		</trim>
 		where id=#{id}
 		<if test="detailList != null and detailList.size()>0">
	 		<foreach item="item" collection="detailList" >
	 			;
		 		<if test="item.id == null or item.id == ''">
					<include refid="insertQcSmartTaskResultSql" />
		 		</if>
		 		<if test="item.id != null and item.id != ''">
					<include refid="updateQcSmartTaskResultSql" />
		 		</if>
	 		</foreach>
 		</if>
	</update>
	<!-- update qc_smart_task_result sql -->
	<sql id="updateQcSmartTaskResultSql" >
		update qc_smart_task_result
		<trim prefix="SET" suffixOverrides=",">
			<if test="item.itemName != null and item.itemName != ''">item_name=#{item.itemName},</if>
			<if test="item.ruleId != null and item.ruleId != ''">rule_id=#{item.ruleId},</if>
			<if test="item.ruleItemId != null and item.ruleItemId != ''">rule_item_id=#{item.ruleItemId},</if>
			<if test="item.smartResult != null and item.smartResult != ''">smart_result=#{item.smartResult},</if>
			<if test="item.recheckResult != null and item.recheckResult != ''">recheck_result=#{item.recheckResult},</if>
			<if test="item.finalResult != null and item.finalResult != ''">final_result=#{item.finalResult},</if>
			update_time=now(),
			<if test="updateBy != null and updateBy != ''">update_by=#{updateBy},</if>
			<if test="item.updateBy != null and item.updateBy != ''">update_by=#{item.updateBy},</if>
			<if test="item.smartResultDetail != null and item.smartResultDetail != ''">smart_result_detail=#{item.smartResultDetail},</if>
			<if test="item.ruleName != null and item.ruleName != ''">rule_name=#{item.ruleName},</if>
			<if test="item.itemId != null and item.itemId != ''">item_id=#{item.itemId},</if>
			<if test="item.firstRecheckTime != null and item.firstRecheckTime != ''">first_recheck_time=now(),</if>
			<if test="item.itemResult != null and item.itemResult != ''">item_result=#{item.itemResult},</if>
			<if test="item.remark != null and item.remark != ''">remark=#{item.remark},</if>
		</trim>
 		where id=#{item.id}
	</sql>
	<update id="batchUpdateQcSmartTaskDetail" parameterType="list">
		<foreach item="item" collection="list" >
			<include refid="updateQcSmartTaskDetailSql" />;
 		</foreach>
	</update>
	<!-- update qc_smart_task_detail sql -->
	<sql id="updateQcSmartTaskDetailSql" >
		update qc_smart_task_detail
		<trim prefix="SET" suffixOverrides=",">
			<if test="item.smartTaskId != null and item.smartTaskId != ''">smart_task_id=#{item.smartTaskId},</if>
			<if test="item.bizNo != null and item.bizNo != ''">biz_no=#{item.bizNo},</if>
			<if test="item.workNo != null and item.workNo != ''">work_no=#{item.workNo},</if>
			<if test="item.workName != null and item.workName != ''">work_name=#{item.workName},</if>
			<if test="item.sessionLength != null and item.sessionLength != ''">session_length=#{item.sessionLength},</if>
			<if test="item.hitRuleCount != null and item.hitRuleCount != ''">hit_rule_count=#{item.hitRuleCount},</if>
			<if test="item.macInspectScore != null and item.macInspectScore != ''">mac_inspect_score=#{item.macInspectScore},</if>
			<if test="item.macInspectTime != null and item.macInspectTime != ''">mac_inspect_time=#{item.macInspectTime},</if>
			<if test="item.checkInspectTime != null and item.checkInspectTime != ''">check_inspect_time=#{item.checkInspectTime},</if>
			<if test="item.status != null and item.status != ''">status=#{item.status},</if>
			update_time=now(),
			<if test="item.updateBy != null and item.updateBy != ''">update_by=#{item.updateBy},</if>
			<if test="item.tenantId != null and item.tenantId != ''">tenant_id=#{item.tenantId},</if>
			<if test="item.templateId != null and item.templateId != ''">template_id=#{item.templateId},</if>
			<if test="item.hitPlusRuleCount != null and item.hitPlusRuleCount != ''">hit_plus_rule_count=#{item.hitPlusRuleCount},</if>
			<if test="item.hitMinusRuleCount != null and item.hitMinusRuleCount != ''">hit_minus_rule_count=#{item.hitMinusRuleCount},</if>
			<if test="item.templateHitRule != null and item.templateHitRule != ''">template_hit_rule=#{item.templateHitRule},</if>
			<if test="item.checkInspectScore != null and item.checkInspectScore != ''">check_inspect_score=#{item.checkInspectScore},</if>
			<if test="item.macInspectResult != null and item.macInspectResult != ''">mac_inspect_result=#{item.macInspectResult},</if>
			<if test="item.checkInspectResult != null and item.checkInspectResult != ''">check_inspect_result=#{item.checkInspectResult},</if>
			
		</trim>
 		where id=#{item.id}
	</sql>
	<update id="updateQcSmartTaskCount">
		UPDATE 
			qc_smart_task qst 
		set 
			qst.hit_rule_session_count=(select count(1) from qc_smart_task_detail qstd where qstd.smart_task_id=qst.id and qstd.status in (4,5) and qstd.hit_rule_count>0) 
		where	
			qst.id=#{id};
	</update>
	<update id="updateQcSmartTaskDetailStatus" parameterType="map">
		update qc_smart_task_detail set status=#{status}
		where id in (
		<!-- 当list为空时，防止sql报错 -->
		<if test="list == null or list.size() == 0">
			0
		</if>
		<if test="list != null and list.size() > 0">
			<foreach item="item" collection="list" separator=",">
				#{item.id}
 			</foreach>
		</if>
		)
	
	</update>
	<update id="stopQcSmartTask" parameterType="map">
		<!-- 停止大模型明细 记录 -->
		update 
			qc_smart_semantics_detail qssd
		set 
			status='stop'
		where 
			semantics_id in (select id from qc_smart_semantics_task where task_det_id in (select id from qc_smart_task_detail where smart_task_id=#{smartTaskId} and status=#{status}))
			and qssd.status not in ('wait','run');
		<!-- 停止大模型记录 -->
		update 
			qc_smart_semantics_task qsst
		set 
			update_time=now(),
			status='stop'
		where 
			task_det_id in (select id from qc_smart_task_detail where smart_task_id=#{smartTaskId} and status=#{status})
			and qsst.status not in ('wait','run');
		<!-- 停止智能质检明细 -->
		update 
			qc_smart_task_detail 
		set 
			status=7,
			update_time=now(),
			update_by=#{updateBy}
		where 
			smart_task_id=#{smartTaskId} and status=#{status};
		<!-- 更改task状态 -->
		update 
			qc_smart_task
		set 
			status=4,
			update_time=now(),
			update_by=#{updateBy}
		where 
			id=#{smartTaskId};
	</update>
</mapper>
