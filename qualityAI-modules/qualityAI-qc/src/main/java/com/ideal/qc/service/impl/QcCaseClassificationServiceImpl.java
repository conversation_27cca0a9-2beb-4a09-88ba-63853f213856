package com.ideal.qc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.exception.ServiceException;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcCase;
import com.ideal.qc.domain.QcCaseClassification;
import com.ideal.qc.mapper.QcCaseClassificationMapper;
import com.ideal.qc.service.QcCaseClassificationService;
import com.ideal.qc.service.QcCaseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 案例分类Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/20 17:46
 */
@Service
public class QcCaseClassificationServiceImpl implements QcCaseClassificationService {

    @Resource
    private QcCaseClassificationMapper qcCaseClassificationMapper;
    @Resource
    private QcCaseService qcCaseService;

    /**
     * 查询关键词库分类列表
     */
    @Override
    public List<QcCaseClassification> getQcCaseClassificationList(JSONObject payload) {
        return qcCaseClassificationMapper.getQcCaseClassificationList(payload);
    }

    /**
     * 新增关键词库分类
     */
    @Override
    public void addQcCaseClassification(JSONObject payload) {
        payload.put("createTime", DateTime.now());
        payload.put("createBy", SecurityUtils.getUsername());
        payload.put("updateTime", DateTime.now());
        payload.put("updateBy", SecurityUtils.getUsername());
        payload.put("tenantId", SecurityUtils.getTenantId());
        qcCaseClassificationMapper.addQcCaseClassification(payload);
    }

    /**
     * 修改关键词库分类
     */
    @Override
    public void updateQcCaseClassification(JSONObject payload) {
        qcCaseClassificationMapper.updateQcCaseClassification(payload);
    }

    /**
     * 删除关键词库分类
     */
    @Override
    public void removeQcCaseClassification(Long id) {
        List<QcCase> qcCaseList = qcCaseService.getQcCaseByCaseClassificationId(id);
        if (CollUtil.isNotEmpty(qcCaseList)) {
            throw new ServiceException("无法删除，已关联案例");
        }
        qcCaseClassificationMapper.removeQcCaseClassification(id);
    }
}
