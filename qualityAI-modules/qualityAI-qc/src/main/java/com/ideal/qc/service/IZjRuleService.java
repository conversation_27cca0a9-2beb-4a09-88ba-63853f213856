package com.ideal.qc.service;

import java.util.List;
import com.ideal.qc.domain.ZjRule;

/**
 * 质检规则Service接口
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface IZjRuleService
{
    /**
     * 查询质检规则
     *
     * @param ruleId 质检规则主键
     * @return 质检规则
     */
    public ZjRule selectZjRuleByRuleId(Long ruleId);

    /**
     * 查询质检规则列表
     *
     * @param zjRule 质检规则
     * @return 质检规则集合
     */
    public List<ZjRule> selectZjRuleList(ZjRule zjRule);

    /**
     * 新增质检规则
     *
     * @param zjRule 质检规则
     * @return 结果
     */
    public int insertZjRule(ZjRule zjRule);

    /**
     * 修改质检规则
     *
     * @param zjRule 质检规则
     * @return 结果
     */
    public int updateZjRule(ZjRule zjRule);

    /**
     * 批量删除质检规则
     *
     * @param ruleIds 需要删除的质检规则主键集合
     * @return 结果
     */
    public int deleteZjRuleByRuleIds(Long[] ruleIds);

    /**
     * 删除质检规则信息
     *
     * @param ruleId 质检规则主键
     * @return 结果
     */
    public int deleteZjRuleByRuleId(Long ruleId);
}
