package com.ideal.qc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ideal.qc.domain.QcDataAnnotation;
import com.ideal.qc.domain.ZjData;
import com.ideal.qc.mapper.ZjDataMapper;
import com.ideal.qc.service.QcDataAnnotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@RestController
@RequestMapping("/api/audio")
public class ExportSamplesController {

    @Autowired
    private QcDataAnnotationService qcDataAnnotationService;
   /* @Autowired
    private ZjDataMapper zjDataMapper;
    @PostMapping("/importDb")
    public void importDb() throws Exception {
        List<String> list = zjDataMapper.getDbTemp();//临时
        for(int i=0;i<list.size();i++){
            String v = list.get(i);
            JSONObject json = JSONObject.parseObject(v);
            String wavCid = json.getString("wavCid");
            Map<String,String> asrResult = new HashMap<>();
            String no = wavCid.substring(0,wavCid.length()-4);
            asrResult.put("only_no",no);
            asrResult.put("file_path",wavCid);
            asrResult.put("asr_result",v);
            zjDataMapper.saveAsrTemp(asrResult);//临时

            ZjData data = new ZjData();
            data.setOnlyNo(no);
            data.setAudioUrl(wavCid);
            zjDataMapper.insertZjData(data);//临时
        }
    }*/
    @PostMapping("/exportSamples")
    public ResponseEntity<Resource> exportTrainingSamples(QcDataAnnotation qcDataAnnotation) throws Exception {
        List<QcDataAnnotation> list = qcDataAnnotationService.selectQcDataAnnotationList(qcDataAnnotation);
        /*List<Test1.AudioSample> samples = repository.findAll();*/
        List<AudioSample> samples = new ArrayList<>();
        for(int i=0;i<list.size();i++){
            QcDataAnnotation za = list.get(i);
            File newFile = new File(za.getNewUrl());
            AudioSample sample = new AudioSample(za.getId().intValue(),downloadAudio(za.getNewUrl()),za.getNewUrlName(), za.getCorrectText());
            samples.add(sample);
        }

        File zipFile = exportTrainingSamples(samples);

        Path path = Paths.get(zipFile.getAbsolutePath());
        ByteArrayResource resource = new ByteArrayResource(Files.readAllBytes(path));

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + zipFile.getName())
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .contentLength(zipFile.length())
                .body(resource);
    }
    public static byte[] downloadAudio(String audioUrl) throws Exception {
        //本地测试
        File file = new File(audioUrl);

        try (InputStream inputStream = new FileInputStream(file);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray();
        } finally {
        }
        //正式
        /*URL url = new URL(audioUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray();
        } finally {
            connection.disconnect();
        }*/
    }
    public static void main(String[] args) {

    }

    /**
     * 导出训练样本到ZIP文件
     */
    public static File exportTrainingSamples(List<AudioSample> samples) throws IOException {
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("audio_training_");
        File wavDir = new File(tempDir.toFile(), "wav");
        wavDir.mkdir();

        // 创建文本文件
        File wavScpFile = new File(tempDir.toFile(), "wav.scp");
        File textFile = new File(tempDir.toFile(), "text.txt");
        File instructionFile = new File(tempDir.toFile(), "说明.txt");

        try (BufferedWriter wavScpWriter = new BufferedWriter(new FileWriter(wavScpFile));
             BufferedWriter textWriter = new BufferedWriter(new FileWriter(textFile));
             BufferedWriter instructionWriter = new BufferedWriter(new FileWriter(instructionFile))) {

            // 写入说明文件
            instructionWriter.write("text文件下是说话人标签和对应的文本\n");
            instructionWriter.write("wav.scp文件下是说话人标签和对应的音频路径\n");
            instructionWriter.write("其中，都是用tab分割符隔开\n");

            // 处理每个样本
            for (AudioSample sample : samples) {
                String fileName = sample.getFileName(); // 生成唯一ID

                // 1. 保存音频文件到wav目录
                File audioFile = new File(wavDir, fileName + ".wav");
                saveAudioFile(sample.getAudioData(), audioFile);

                // 2. 写入wav.scp文件 (格式: audio_id audio_path)
                wavScpWriter.write(fileName + " .wav/" + fileName + ".wav");
                wavScpWriter.newLine();

                // 3. 写入text.txt文件 (格式: audio_id transcript_text)
                textWriter.write(fileName + " " + sample.getRightText());
                textWriter.newLine();
            }
        }

        // 打包为ZIP文件
        File zipFile = new File("zip/audio_samples_" + System.currentTimeMillis() + ".zip");
        zipDirectory(tempDir.toFile(), zipFile);

        // 删除临时目录
        deleteDirectory(tempDir.toFile());

        return zipFile;
    }

    /**
     * 保存音频数据到文件
     */
    private static void saveAudioFile(byte[] audioData, File outputFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(audioData);
        }
    }

    /**
     * 将目录打包为ZIP文件
     */
    private static void zipDirectory(File sourceDir, File zipFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(zipFile);
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            Files.walk(sourceDir.toPath())
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        ZipEntry zipEntry = new ZipEntry(sourceDir.toPath().relativize(path).toString());
                        try {
                            zos.putNextEntry(zipEntry);
                            Files.copy(path, zos);
                            zos.closeEntry();
                        } catch (IOException e) {
                            System.err.println("无法添加文件到ZIP: " + path);
                        }
                    });
        }
    }

    /**
     * 递归删除目录
     */
    private static void deleteDirectory(File directory) throws IOException {
        Files.walk(directory.toPath())
                .sorted(Comparator.reverseOrder())
                .map(Path::toFile)
                .forEach(File::delete);
    }

    /**
     * 音频样本数据模型
     */
    static class AudioSample {
        private int id;
        private byte[] audioData;
        private String fileName;//文件名称
        private String rightText;//正确文本

        public AudioSample(int id, byte[] audioData, String fileName,String rightText) {
            this.id = id;
            this.audioData = audioData;
            this.fileName = fileName;
            this.rightText = rightText;
        }

        // Getter方法
        public int getId() { return id; }
        public byte[] getAudioData() { return audioData; }
        public String getFileName() { return fileName; }
        public String getRightText() { return rightText; }
    }
}