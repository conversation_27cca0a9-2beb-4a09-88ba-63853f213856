package com.ideal.qc.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.ZjDataEvaluationMapper;
import com.ideal.qc.domain.ZjDataEvaluation;
import com.ideal.qc.service.IZjDataEvaluationService;

/**
 * 质检评测Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-31
 */
@Service
public class ZjDataEvaluationServiceImpl implements IZjDataEvaluationService 
{
    @Autowired
    private ZjDataEvaluationMapper zjDataEvaluationMapper;

    /**
     * 查询质检评测
     * 
     * @param id 质检评测主键
     * @return 质检评测
     */
    @Override
    public ZjDataEvaluation selectZjDataEvaluationById(Long id)
    {
        return zjDataEvaluationMapper.selectZjDataEvaluationById(id);
    }

    /**
     * 查询质检评测列表
     * 
     * @param zjDataEvaluation 质检评测
     * @return 质检评测
     */
    @Override
    public List<ZjDataEvaluation> selectZjDataEvaluationList(ZjDataEvaluation zjDataEvaluation)
    {
        return zjDataEvaluationMapper.selectZjDataEvaluationList(zjDataEvaluation);
    }

    /**
     * 新增质检评测
     * 
     * @param zjDataEvaluation 质检评测
     * @return 结果
     */
    @Override
    public int insertZjDataEvaluation(ZjDataEvaluation zjDataEvaluation)
    {
        return zjDataEvaluationMapper.insertZjDataEvaluation(zjDataEvaluation);
    }

    /**
     * 修改质检评测
     * 
     * @param zjDataEvaluation 质检评测
     * @return 结果
     */
    @Override
    public int updateZjDataEvaluation(ZjDataEvaluation zjDataEvaluation)
    {
        return zjDataEvaluationMapper.updateZjDataEvaluation(zjDataEvaluation);
    }

    /**
     * 批量删除质检评测
     * 
     * @param ids 需要删除的质检评测主键
     * @return 结果
     */
    @Override
    public int deleteZjDataEvaluationByIds(Long[] ids)
    {
        return zjDataEvaluationMapper.deleteZjDataEvaluationByIds(ids);
    }

    /**
     * 删除质检评测信息
     * 
     * @param id 质检评测主键
     * @return 结果
     */
    @Override
    public int deleteZjDataEvaluationById(Long id)
    {
        return zjDataEvaluationMapper.deleteZjDataEvaluationById(id);
    }
}
