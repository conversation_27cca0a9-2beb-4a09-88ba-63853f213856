<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ideal</groupId>
        <artifactId>qualityAI-common</artifactId>
        <version>3.6.5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>qualityAI-common-swagger</artifactId>
	
    <description>
        qualityAI-common-swagger系统接口
    </description>

	<dependencies>

        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- SpringDoc webmvc -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>

	</dependencies>
</project>
