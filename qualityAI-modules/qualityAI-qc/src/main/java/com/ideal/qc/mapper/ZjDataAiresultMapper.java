package com.ideal.qc.mapper;

import java.util.List;
import com.ideal.qc.domain.ZjDataAiresult;

/**
 * AI质检结果Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
public interface ZjDataAiresultMapper 
{
    /**
     * 查询AI质检结果
     * 
     * @param id AI质检结果主键
     * @return AI质检结果
     */
    public ZjDataAiresult selectZjDataAiresultById(Long id);

    /**
     * 查询AI质检结果列表
     * 
     * @param zjDataAiresult AI质检结果
     * @return AI质检结果集合
     */
    public List<ZjDataAiresult> selectZjDataAiresultList(ZjDataAiresult zjDataAiresult);

    /**
     * 新增AI质检结果
     * 
     * @param zjDataAiresult AI质检结果
     * @return 结果
     */
    public int insertZjDataAiresult(ZjDataAiresult zjDataAiresult);

    /**
     * 修改AI质检结果
     * 
     * @param zjDataAiresult AI质检结果
     * @return 结果
     */
    public int updateZjDataAiresult(ZjDataAiresult zjDataAiresult);

    /**
     * 删除AI质检结果
     * 
     * @param id AI质检结果主键
     * @return 结果
     */
    public int deleteZjDataAiresultById(Long id);

    /**
     * 批量删除AI质检结果
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZjDataAiresultByIds(Long[] ids);
}
