package com.ideal.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.ideal.common.security.annotation.EnableCustomConfig;
import com.ideal.common.security.annotation.EnableRyFeignClients;

/**
 * 系统模块
 * 
 * <AUTHOR>
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class SystemApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(SystemApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ system 系统模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
