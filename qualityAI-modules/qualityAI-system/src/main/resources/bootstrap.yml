# Tomcat
server:
  port: 9201

# Spring
spring: 
  application:
    # 应用名称
    name: system
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        # 命名空间 可配置在系统环境变量中
        namespace: ${QC_NACOS_NSP:public}
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.username}
        # 命名空间 可配置在系统环境变量中
        namespace: ${QC_NACOS_NSP:public}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.yml
          - dynamicDatasource-${spring.profiles.active}.yml
          - redis-${spring.profiles.active}.yml

