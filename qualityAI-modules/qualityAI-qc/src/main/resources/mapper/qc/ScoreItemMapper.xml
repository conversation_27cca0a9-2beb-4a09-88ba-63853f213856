<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ScoreItemMapper">

    <resultMap type="ScoreItem" id="ScoreItemResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="scoretplId" column="scoretpl_id" />
        <result property="itemName"    column="item_name"    />
        <result property="scoreWay"    column="score_way"    />
        <result property="nature"    column="nature"    />
        <result property="scoreNum"    column="score_num"    />
        <result property="itemType"    column="item_type"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectScoreItemVo">
        select id, parent_id,scoretpl_id, item_name, score_way, nature, score_num, item_type, rule_id, create_time, update_time from zj_score_item
    </sql>

    <select id="selectScoreItemList" parameterType="ScoreItem" resultMap="ScoreItemResult">
        <include refid="selectScoreItemVo"/>
        <where>
            <if test="scoretplId != null "> and scoretpl_id = #{scoretplId}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="scoreWay != null  and scoreWay != ''"> and score_way = #{scoreWay}</if>
            <if test="nature != null  and nature != ''"> and nature = #{nature}</if>
            <if test="scoreNum != null "> and score_num = #{scoreNum}</if>
            <if test="itemType != null  and itemType != ''"> and item_type = #{itemType}</if>
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>
        </where>
    </select>

    <select id="selectScoreItemPnode" parameterType="ScoreItem" resultMap="ScoreItemResult">
        <include refid="selectScoreItemVo"/>
        where scoretpl_id = #{scoretplId} and item_type = "P"
    </select>


    <select id="selectScoreItemById" parameterType="Long" resultMap="ScoreItemResult">
        <include refid="selectScoreItemVo"/>
        where id = #{id}
    </select>
    <select id="selectScoreItemByTplId" parameterType="ScoreItem" resultMap="ScoreItemResult">
        <include refid="selectScoreItemVo"/>
        where scoretpl_id = #{scoretplId}
        <if test="itemName != null and itemName != ''"> and item_name like concat('%', #{itemName}, '%') </if>
    </select>

    <insert id="insertScoreItem" parameterType="ScoreItem" useGeneratedKeys="true" keyProperty="id">
        insert into zj_score_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="itemName != null">item_name,</if>
            <if test="scoretplId != null">scoretpl_id,</if>
            <if test="scoreWay != null">score_way,</if>
            <if test="nature != null">nature,</if>
            <if test="scoreNum != null">score_num,</if>
            <if test="itemType != null">item_type,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="scoretplId != null">#{scoretplId},</if>
            <if test="scoreWay != null">#{scoreWay},</if>
            <if test="nature != null">#{nature},</if>
            <if test="scoreNum != null">#{scoreNum},</if>
            <if test="itemType != null">#{itemType},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateScoreItem" parameterType="ScoreItem">
        update zj_score_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="scoreWay != null">score_way = #{scoreWay},</if>
            <if test="nature != null">nature = #{nature},</if>
            <if test="scoreNum != null">score_num = #{scoreNum},</if>
            <if test="itemType != null">item_type = #{itemType},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScoreItemById" parameterType="Long">
        delete from zj_score_item where id = #{id}
    </delete>

    <delete id="deleteScoreItemByIds" parameterType="String">
        delete from zj_score_item where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>