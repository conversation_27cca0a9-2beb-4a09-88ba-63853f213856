//package com.ideal.qc.service.impl;
//
//import com.ideal.qc.domain.QcDataset;
//import com.ideal.qc.mapper.QcDatasetItemMapper;
//import com.ideal.qc.mapper.QcDatasetMapper;
//import com.ideal.qc.mapper.QcSyncPlanMapper;
//import com.ideal.qc.utils.FastDfsUtil;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.Date;
//import java.util.List;
//
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class SyncPlanService {
//    private final QcSyncPlanMapper planMapper;
//    private final QcDatasetMapper datasetMapper;
//    private final QcDatasetItemMapper itemMapper;
//    private final QcAsrTaskMapper taskMapper;
//    private final QcSyncLogMapper logMapper;
//    private final FastDfsUtil fastDfs;
//
//    @Transactional(rollbackFor = Exception.class)
//    public void executePullPlan(Long planId) {
//        QcSyncPlan plan = planMapper.selectById(planId);
//        QcSyncLog log = new QcSyncLog();
//        log.setPlanId(planId);
//        log.setStatus("running");
//        log.setStartTime(new Date());
//        logMapper.insert(log);
//        try {
//            // 1) 创建数据集
//            QcDataset ds = new QcDataset();
//            ds.setName(plan.getName() + "_" + System.currentTimeMillis());
//            ds.setDataType(plan.getDataType());
//            ds.setPlanId(planId);
//            datasetMapper.insert(ds);
//
//            // 2) 从外部接口分页拉取 → 此处示例伪造一条
//            List<byte[]> audioList = ExternalApiMock.pullAudio();
//            for (byte[] audio : audioList) {
//                String fileId = fastDfs.upload(new ByteArrayMultipartFile(audio, "wav"));
//                QcDatasetItem item = new QcDatasetItem();
//                item.setDatasetId(ds.getId());
//                item.setFileId(fileId);
//                itemMapper.insert(item);
//
//                if (plan.getNeedAsr() == 1) {
//                    QcAsrTask task = new QcAsrTask();
//                    task.setItemId(item.getId());
//                    taskMapper.insert(task);
//                }
//            }
//            log.setStatus("success");
//        } catch (Exception e) {
//            log.setStatus("fail");
//            log.setMessage(e.getMessage());
//            throw new RuntimeException(e);
//        } finally {
//            log.setEndTime(new Date());
//            logMapper.updateById(log);
//        }
//    }
//}