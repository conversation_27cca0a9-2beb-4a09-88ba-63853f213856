# Tomcat
server:
  port: 9202

# Spring
spring: 
  application:
    # 应用名称
    name: gen
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ${QC_NACOS_NSP:public}
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.username}
        namespace: ${QC_NACOS_NSP:public}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.yml
          - druid-${spring.profiles.active}.yml
          - redis-${spring.profiles.active}.yml
