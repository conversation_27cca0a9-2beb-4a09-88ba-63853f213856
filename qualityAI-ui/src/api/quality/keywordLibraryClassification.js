import request from '@/utils/request'

// 查询关键词库分类列表
export function getQcKeywordLibraryClassificationList(data) {
  return request({
    url: '/qc/keyword/library/classification/list',
    method: 'post',
    data
  })
}

// 新增关键词库分类
export function addQcKeywordLibraryClassification(data) {
  return request({
    url: '/qc/keyword/library/classification/add',
    method: 'post',
    data: data
  })
}

// 修改关键词库分类
export function updateQcKeywordLibraryClassification(data) {
  return request({
    url: '/qc/keyword/library/classification/update',
    method: 'post',
    data: data
  })
}

// 删除关键词库分类
export function removeQcKeywordLibraryClassification(id) {
  return request({
    url: '/qc/keyword/library/classification/remove/' + id,
    method: 'post'
  })
}
