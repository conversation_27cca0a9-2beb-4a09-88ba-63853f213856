package com.ideal.qc.service;

import java.util.List;
import com.ideal.qc.domain.QcBizAnswer;

/**
 * 问卷答案(EAV)Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
public interface IQcBizAnswerService 
{
    /**
     * 查询问卷答案(EAV)
     * 
     * @param id 问卷答案(EAV)主键
     * @return 问卷答案(EAV)
     */
    public QcBizAnswer selectQcBizAnswerById(Long id);

    /**
     * 查询问卷答案(EAV)列表
     * 
     * @param qcBizAnswer 问卷答案(EAV)
     * @return 问卷答案(EAV)集合
     */
    public List<QcBizAnswer> selectQcBizAnswerList(QcBizAnswer qcBizAnswer);

    /**
     * 新增问卷答案(EAV)
     * 
     * @param qcBizAnswer 问卷答案(EAV)
     * @return 结果
     */
    public int insertQcBizAnswer(QcBizAnswer qcBizAnswer);

    /**
     * 修改问卷答案(EAV)
     * 
     * @param qcBizAnswer 问卷答案(EAV)
     * @return 结果
     */
    public int updateQcBizAnswer(QcBizAnswer qcBizAnswer);

    /**
     * 批量删除问卷答案(EAV)
     * 
     * @param ids 需要删除的问卷答案(EAV)主键集合
     * @return 结果
     */
    public int deleteQcBizAnswerByIds(Long[] ids);

    /**
     * 删除问卷答案(EAV)信息
     * 
     * @param id 问卷答案(EAV)主键
     * @return 结果
     */
    public int deleteQcBizAnswerById(Long id);
}
