package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcCase;
import com.ideal.qc.dto.QcCaseDTO;
import com.ideal.qc.service.QcCaseService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 案例Controller
 *
 * <AUTHOR>
 * @date 2025/5/26 17:24
 */
@RestController
@RequestMapping("/case")
public class QcCaseController extends BaseController {

    @Resource
    private QcCaseService qcCaseService;

    /**
     * 查询案例列表
     */
    @RequiresPermissions("quality:case:list")
    @PostMapping("/list")
    public TableDataInfo getQcCaseList(@RequestBody JSONObject payload) {
        startPage();
        List<QcCase> result = qcCaseService.getQcCaseList(payload);
        return getDataTable(result);
    }

    /**
     * 新增案例
     */
    @RequiresPermissions("quality:case:add")
    @PostMapping("/add")
    public AjaxResult addQcCase(@RequestBody JSONObject payload) {
        qcCaseService.addQcCase(payload);
        return AjaxResult.success();
    }

    /**
     * 审核案例
     */
    @RequiresPermissions("quality:case:edit")
    @PostMapping("/update")
    public AjaxResult updateQcCase(@RequestBody JSONObject payload) {
        qcCaseService.updateQcCase(payload);
        return AjaxResult.success();
    }

    /**
     * 查询案列的每个案列状态数量
     */
    @PostMapping("/each/caseStatus/quantity")
    public AjaxResult getQcCaseEachCaseStatusQuantity(@RequestBody JSONObject payload) {
        QcCaseDTO result = qcCaseService.getQcCaseEachCaseStatusQuantity(payload);
        return AjaxResult.success(result);
    }

    /**
     * 查询标记数量根据人工计划ID
     */
    @RequiresPermissions("quality:case:list")
    @PostMapping("/listCount")
    public AjaxResult listCount(@RequestBody JSONObject payload) {
        int count = qcCaseService.listCount(payload);
        return AjaxResult.success(count);
    }
}
