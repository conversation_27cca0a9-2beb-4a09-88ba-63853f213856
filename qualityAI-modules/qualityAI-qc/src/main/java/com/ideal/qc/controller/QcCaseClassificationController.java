package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcCaseClassification;
import com.ideal.qc.service.QcCaseClassificationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 案例分类Controller接口
 *
 * <AUTHOR>
 * @date 2025/5/20 17:38
 */
@RestController
@RequestMapping("/case/classification")
public class QcCaseClassificationController extends BaseController {

    @Resource
    private QcCaseClassificationService qcCaseClassificationService;

    /**
     * 查询关键词库分类列表
     */
    @RequiresPermissions("quality:caseClassification:query")
    @PostMapping("/list")
    public AjaxResult getQcCaseClassificationList(@RequestBody JSONObject payload) {
        List<QcCaseClassification> result = qcCaseClassificationService.getQcCaseClassificationList(payload);
        return AjaxResult.success(result);
    }

    /**
     * 新增关键词库分类
     */
    @RequiresPermissions("quality:caseClassification:add")
    @PostMapping("/add")
    public AjaxResult addQcCaseClassification(@RequestBody JSONObject payload) {
        qcCaseClassificationService.addQcCaseClassification(payload);
        return AjaxResult.success();
    }

    /**
     * 修改关键词库分类
     */
    @RequiresPermissions("quality:caseClassification:edit")
    @PostMapping("/update")
    public AjaxResult updateQcCaseClassification(@RequestBody JSONObject payload) {
        qcCaseClassificationService.updateQcCaseClassification(payload);
        return AjaxResult.success();
    }

    /**
     * 删除关键词库分类
     */
    @RequiresPermissions("quality:caseClassification:remove")
    @PostMapping("/remove/{id}")
    public AjaxResult removeQcCaseClassification(@PathVariable Long id) {
        qcCaseClassificationService.removeQcCaseClassification(id);
        return AjaxResult.success();
    }
}
