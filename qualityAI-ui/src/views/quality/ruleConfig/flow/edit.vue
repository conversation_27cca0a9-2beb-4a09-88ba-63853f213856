<template>
	<div class="app-container" style="padding-top:0px;">
    	<el-row >
			<div >
				<div class="el-dialog-title dy-title" style="width:120px;">{{title}}</div><div style="color:red;float:left;padding-left:5px;margin-top:3px;">(先保存流程规则基础信息，再添加流程节点)</div>
				<el-button v-if="op=='edit'"class="btn" type="primary" @click="submitForm('form')">保 存</el-button>
				<el-button class="btn" @click="cancel">取 消</el-button>
		    </div>
	  		<el-divider class="dy-divider"></el-divider>
			<el-form ref="form" size="small" :model="form" :rules="rules" label-width="120px" style="max-height:700px;overflow:auto;">
			<el-col >
					<el-form-item label="规则名称" prop="ruleName">
						<el-input v-model="form.ruleName" placeholder="请输入规则名称" maxlength="30" class="inputWidth" :disabled="op == 'view'" show-word-limit/>
					</el-form-item>
					<el-form-item label="说明" prop="remark">
				        <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" class="inputWidth" :disabled="op == 'view'" show-word-limit/>
					</el-form-item>
					<el-form-item label="流程节点个数" prop="nodeCount">
		  		        {{form.subList.length}}个
					</el-form-item>
			</el-col>
	  		<el-col v-if="form.ruleId && form.ruleId != ''">
				<el-button v-if="op=='edit'" type="primary" plain icon="el-icon-plus" size="small" @click="handleAdd">新增节点</el-button>
				<el-table v-loading="loading" :data="form.subList" >
	          		<el-table-column label="规则ID" align="left" key="ruleId" prop="ruleId" width="100"/>
<!--			  <el-table-column label="排序" align="center" key="order" prop="order" />-->
	          		<el-table-column label="节点名称" align="left" key="ruleName" prop="ruleName" :show-overflow-tooltip="true" width="300"/>
			  		<el-table-column label="状态" align="left" key="status" prop="status" :formatter="format" width="200"/>
	          		<el-table-column label="更新人" align="left" key="updateBy" prop="updateBy" :show-overflow-tooltip="true" width="200"/>
	          		<el-table-column label="更新时间" align="left" key="updateTime" prop="updateTime"  width="200" />
	          		<el-table-column label="操作" align="center" class-name="small-padding fixed-width">
	            		<template slot-scope="scope" v-if="scope.row.userId !== 1">
	              			<el-button size="mini" type="text" icon="el-icon-edit" @click="goRule(scope.row)" style="margin-right:10px;">{{op=='edit'?'修改':'查看'}}</el-button>
			  			</template>
	          		</el-table-column>
	        	</el-table>
	  		</el-col>
			</el-form>
		</el-row>
		<el-dialog title="" :visible.sync="open" width="1400px" 
			:close-on-press-escape="false"
			:show-close="false"
			:close-on-click-modal="false"
			custom-class="no-header-dialog"
			append-to-body
			height="860px"
		>
			<div class="app-container" style="max-height:860px">
			    <div :content="ruleTitle">
					<div class="el-dialog-title dy-title">{{ruleTitle}}</div>
					<el-button v-if="op=='edit'" class="btn" type="primary" @click="submitForm('nodeForm')">保 存</el-button>
					<el-button class="btn" @click="open=false">取 消</el-button>
			    </div>
			    <el-divider class="dy-divider"></el-divider>
			    <el-form ref="nodeForm" size="small" :model="nodeForm" :rules="rules" label-width="100px" style="max-height:750px;overflow:auto;">
		      		<el-form-item label="节点名称" prop="ruleName">
		        		<el-input v-model="nodeForm.ruleName" placeholder="请输入节点名称" maxlength="30" class="inputWidth" :disabled="op == 'view'" show-word-limit/>
	      			</el-form-item>
			  		<el-form-item label="状态" prop="ruleName">
						<el-select v-model="nodeForm.status" :disabled="op == 'view'">
							<el-option label="启用" :value="1" />
			  				<el-option label="禁用" :value="2" />
						</el-select>
	  	      		</el-form-item>
		      		<el-form-item label="节点描述" prop="remark">
		        		<el-input v-model="nodeForm.remark" type="textarea" placeholder="请输入内容" class="inputWidth" :disabled="op == 'view'" show-word-limit/>
	      			</el-form-item>
			  		<hitModal :hitList="nodeForm.hitList" :op="op" ref="hitModal"></hitModal>
		     	</el-form>
		  	</div>
		</el-dialog>
	</div>
</template>

<script>
import { listRule,getRule,updateRule} from "@/api/quality/rule";
import {checkFilterForm} from "@/api/quality/smart";
import hitModal from "@/views/quality/ruleConfig/common/hitList.vue";
export default {
	name: "flowRuleEdit",
	props:{
  		param:Object
	},
	components: {
		hitModal
	},
	data() {
	    return {
			// 遮罩层
	      	loading: false,
		  	op:'edit',
		  	form:{
				subList:[]
		  	},
	      	// 弹出层标题
	      	title: "",
	  		ruleTitle:"",
	      	open: false,
	      	// 查询参数
	      	queryParams: {
	        	pageNum: 1,
	        	pageSize: 10
	      	},
		  	queryData:{
				ruleGroup:"",
				ruleType:3,
				parentId:''
		  	},
		  	nodeForm:{
				status:1,
				hitList:[]
		  	},
		  	rules: {
	      		ruleName: [{required: true,trigger: '11'}],
				status: [{required: true,trigger: '11'}],
				nodeName: [{required: true, trigger: '11'}],
				hitList: [{required: true,trigger: '11'}],
				nodeCount: [{required: true,trigger: '11'}]
			},
			currSubmitForm:""
	    };
	},
	methods: {
		reload(query,op){
			if(op){
				this.op = op;
			}
			this.queryData = query;
			if(this.op == 'view'){
				this.title = '查看流程规则';
			}else{
				this.title = this.queryData.ruleId?'编辑流程规则':'新增流程规则';
			}
			if(!this.queryData.ruleId || this.queryData.ruleId ==''){
				this.nodeForm={ruleType:3,hitList:[]}
				this.form={ruleType:3,subList:[],hitList:[],ruleGroup:this.queryData.ruleGroup}
				return;
			}
			getRule({ruleId:this.queryData.ruleId,isDetail:true}).then(response => {
				this.form = response.data;
				this.loading = false;
		  	});
		},
		format(row,column,cellValue,index){
			var list = [];
			switch(column.property){
				case 'ruleType':
					list = this.ruleTypeList;
					break;
				case "status":
					list = [{dictValue:1,dictLabel:"启用"},{dictValue:2,dictLabel:"禁用"}];
					break;
				default:
					break;
			}
			for(var index in list){
				if(list[index]["dictValue"] == cellValue){
					return list[index]["dictLabel"];
				}
			}
		},
		closeDialog(){
			this.open = false;
		},
		goRule(row){
			if(this.op == 'view'){
				this.ruleTitle = '查看流程节点';
			}else{
				this.ruleTitle = row?"修改流程节点":"新增流程节点";
			}
			this.open = true;
			if(!row){
				this.nodeForm["parentId"] = this.form.ruleId;
				return;
			}
			getRule({ruleId:row.ruleId,isDetail:true}).then(response => {
				for(var i=0;i< response.data.hitList.length;i++){
					response.data.hitList[i].ruleCondDetail = JSON.parse(response.data.hitList[i].ruleCondDetail);
				}
				this.nodeForm = response.data;
		  	});
		},
    	getList() {
      		this.loading = true;
  			listRule(this.queryParams,this.queryData).then(response => {
          		this.ruleList = response.rows;
    		}).finally(v=>{
          		this.loading = false;
	  		});
		},
    	// 取消按钮
    	cancel() {
	  		if(this.op != 'edit'){
				this.goBack();
				return;
	  		}
	  		if(this.form.ruleId && this.form.ruleId != ''){
				if(!this.form.subList || this.form.subList.length < 1){
					this.$message({message: "至少添加一个流程节点",type: 'error'});
					return;
	  			}
	  		}
      		this.goBack();
    	},
    	/** 新增按钮操作 */
    	handleAdd() {
	  		this.nodeForm = {ruleType:3,parentId:this.queryData.ruleId,hitList:[],ruleGroup:3,status:1};
	  		this.goRule();
    	},
    	goBack() {
     		this.$emit("goList");
			this.op = "edit";
    	},
    	submitForm: function(target) {
			var tmp = this[target];
			this.currSubmitForm = target;
			if(!tmp.ruleName || tmp.ruleName == ''){
				this.$message({message: (target=='form'?"规则":"节点")+"名称不能为空",type: 'error'});
				return;
			}
			if(target == 'form'){
				if(tmp.ruleId && tmp.ruleId != ''){
					if(tmp.subList.length<1){
						this.$message({message: "至少配置一个流程节点",type: 'error'});
						return;
					}
				}
				
			}else{
				var tip = checkFilterForm(tmp.hitList,2);
				if(tip){
					this.$message({message: tip,type: 'error'});
					return 
				}
			}
			for(var i=0;i< tmp.hitList.length;i++){
				tmp.hitList[i]['ruleOrder'] = i+1;
			}
			tmp = JSON.stringify(tmp,	(key, value) => {
			    return value === null ? undefined : value;
			});
			updateRule(tmp).then(response => {
				this.$message({message: (!this[this.currSubmitForm].ruleId || this[this.currSubmitForm].ruleId=='')?"新增成功":"修改成功",type: 'success'});
				if(this.currSubmitForm == 'form'){
					if(this.form.ruleId && this.form.ruleId != ''){
						this.$emit("getList");
						this.op = "edit";
					}
				}else{
					this.open = false;
				}
				this.reload({ruleId:(!this.form.ruleId || this.form.ruleId=='')?response.data:this.form.ruleId})
			});
    	},
	  	updRow(tmp) {
			for(var i in this.form.subList){
				if(this.form.subList[i].ruleId == tmp.ruleId){
					this.form.subList[i].status = tmp.status==1?2:1;
					break;
				}
			}
	  	}
  	}
};
</script>

<style rel="stylesheet/scss" lang="scss">
.sortItem{
  border-left: 2px solid white;
  font-size: 13px;
  padding: 8px 14px;
  cursor: pointer;
}
.sortItem:hover{
  border-left: 2px solid #409EFF;
  background: #ecf5ff;
}
.childItem{
  padding-left:30px;
}
</style>