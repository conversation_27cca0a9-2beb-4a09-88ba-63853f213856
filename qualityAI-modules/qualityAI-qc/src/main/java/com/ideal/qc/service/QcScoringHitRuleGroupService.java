package com.ideal.qc.service;

import com.ideal.qc.domain.QcScoringHitRuleGroup;
import com.ideal.qc.dto.QcScoringHitRuleGroupDTO;

import java.util.List;

/**
 * 评分细则(项)规则组表Service接口
 *
 * <AUTHOR>
 * @date 2025/5/15 17:38
 */
public interface QcScoringHitRuleGroupService {

    /**
     * 新增评分细则命中规则组
     *
     * @param qcScoringHitRuleGroup 评分细则命中规则组实体
     */
    void addScoringHitRuleGroup(QcScoringHitRuleGroup qcScoringHitRuleGroup);

    /**
     * 批量删除评分细则命中规则组
     *
     * @param qcScoringHitRuleGroupIdList 评分细则命中规则组id列表
     */
    void removeScoringHitRuleGroupBatch(List<Long> qcScoringHitRuleGroupIdList);

    /**
     * 根据评分细则查询所有规则组id
     *
     * @param scoringItemId 评分细则ID
     * @return 所有规则组id
     */
    List<Long> getQcScoringHitRuleGroupIds(Long scoringItemId);

    /**
     * 修改评分细则命中规则组
     *
     * @param qcScoringHitRuleGroup 评分细则命中规则组实体
     */
    void updateScoringHitRuleGroup(QcScoringHitRuleGroup qcScoringHitRuleGroup);
}
