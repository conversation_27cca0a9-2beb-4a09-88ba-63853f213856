package com.ideal.qc.mapper;

import java.util.List;

import com.ideal.qc.domain.QcManualTaskResult;

/**
 * 人工质检规则结果Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface QcManualTaskResultMapper 
{
    /**
     * 查询人工质检规则结果
     * 
     * @param id 人工质检规则结果主键
     * @return 人工质检规则结果
     */
    public QcManualTaskResult selectQcManualTaskResultById(Long id);

    /**
     * 查询人工质检规则结果列表
     * 
     * @param qcManualTaskResult 人工质检规则结果
     * @return 人工质检规则结果集合
     */
    public List<QcManualTaskResult> selectQcManualTaskResultList(QcManualTaskResult qcManualTaskResult);

    /**
     * 获取人工质检结果表中的所有命中规则
     * @return
     */
    public List<QcManualTaskResult> getRulesFromManualResult();

    /**
     * 获取人工质检结果表中的所有评分项
     * @return
     */
    public List<QcManualTaskResult> getItemsFromManualResult();



    /**
     * 新增人工质检规则结果
     * 
     * @param qcManualTaskResult 人工质检规则结果
     * @return 结果
     */
    public int insertQcManualTaskResult(QcManualTaskResult qcManualTaskResult);

    /**
     * 修改人工质检规则结果
     * 
     * @param qcManualTaskResult 人工质检规则结果
     * @return 结果
     */
    public int updateQcManualTaskResult(QcManualTaskResult qcManualTaskResult);

    /**
     * 删除人工质检规则结果
     * 
     * @param id 人工质检规则结果主键
     * @return 结果
     */
    public int deleteQcManualTaskResultById(Long id);

    /**
     * 批量删除人工质检规则结果
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQcManualTaskResultByIds(Long[] ids);
}
