package com.ideal.qc.service;

import java.io.IOException;
import java.util.List;
import com.ideal.qc.domain.QcBizItem;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 业务数据明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IQcBizItemService 
{
    /**
     * 查询业务数据明细
     * 
     * @param id 业务数据明细主键
     * @return 业务数据明细
     */
    public QcBizItem selectQcBizItemById(Long id);

    /**
     * 查询业务数据明细列表
     * 
     * @param qcBizItem 业务数据明细
     * @return 业务数据明细集合
     */
    public List<QcBizItem> selectQcBizItemList(QcBizItem qcBizItem);

    /**
     * 新增业务数据明细
     * 
     * @param qcBizItem 业务数据明细
     * @return 结果
     */
    public int insertQcBizItem(QcBizItem qcBizItem);

    /**
     * 修改业务数据明细
     * 
     * @param qcBizItem 业务数据明细
     * @return 结果
     */
    public int updateQcBizItem(QcBizItem qcBizItem);

    /**
     * 批量删除业务数据明细
     * 
     * @param ids 需要删除的业务数据明细主键集合
     * @return 结果
     */
    public int deleteQcBizItemByIds(Long[] ids);

    /**
     * 删除业务数据明细信息
     * 
     * @param id 业务数据明细主键
     * @return 结果
     */
    public int deleteQcBizItemById(Long id);

    /**
     * 根据业务编号和类型查询业务数据明细
     *
     * @param bizNo 业务编号
     * @param type 业务类型
     * @return 业务数据明细
     */
    QcBizItem selectQcBizItemByNo(String bizNo,String type,Long manualDetailId);

    /**
     * 导入业务数据明细
     * @param rowDataList
     * @param operName
     * @return
     */
    String importBizItem(List<QcBizItem> rowDataList, String operName);


    /**
     * 导入Excel文件中的业务数据明细
     *
     * @param file Excel文件
     * @return 业务数据明细列表
     * @throws IOException 如果读取文件时发生错误
     */
    List<QcBizItem> importExcel(MultipartFile file) throws IOException;

    /**
     * 自定义导出业务数据明细（拆分answerJson为动态列）
     *
     * @param response HTTP响应
     * @param qcBizItem 查询条件
     */
    void exportBizItemWithDynamicColumns(HttpServletResponse response, QcBizItem qcBizItem);
}
