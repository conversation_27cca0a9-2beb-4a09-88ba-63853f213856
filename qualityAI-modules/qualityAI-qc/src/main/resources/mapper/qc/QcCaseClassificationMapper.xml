<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcCaseClassificationMapper">

    <resultMap id="qcCaseClassificationResultMap" type="QcCaseClassification">
        <result property="id"    column="id"    />
        <result property="caseClassificationName" column="case_classification_name" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <select id="getQcCaseClassificationList" resultMap="qcCaseClassificationResultMap">
        SELECT
            qcc.id,
            qcc.case_classification_name,
            qcc.create_by,
            qcc.create_time,
            qcc.update_by,
            qcc.update_time
        FROM
            qc_case_classification qcc
        <where>
            <if test="caseClassificationName != null and caseClassificationName != ''">
                AND qcc.case_classification_name LIKE concat('%', #{caseClassificationName}, '%')
            </if>
        </where>
        ORDER BY
            qcc.id ASC
    </select>

    <insert id="addQcCaseClassification">
        INSERT INTO
            qc_case_classification
        ( tenant_id, case_classification_name, create_time, create_by, update_time, update_by, remark )
        VALUES
        ( #{tenantId}, #{caseClassificationName}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy}, #{remark})
    </insert>

    <update id="updateQcCaseClassification">
        UPDATE qc_case_classification
        SET case_classification_name = #{caseClassificationName},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark}
        WHERE
            id = #{id}
    </update>

    <delete id="removeQcCaseClassification">
        delete from qc_case_classification where id = #{id}
    </delete>
</mapper>