<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcDatasetItemMapper">
    
    <resultMap type="QcDatasetItem" id="QcDatasetItemResult">
        <result property="id"    column="id"    />
        <result property="datasetId"    column="dataset_id"    />
        <result property="fileId"    column="file_id"    />
        <result property="durationSec"    column="duration_sec"    />
        <result property="callTime"    column="call_time"    />
        <result property="asrStatus"    column="asr_status"    />
        <result property="asrResult"    column="asr_result"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="bizNo"    column="biz_no"    />
        <result property="updateTime"    column="update_time"    />
        <result property="datasetName"    column="datasetName"    />
    </resultMap>
    <sql id="selectQcDatasetItemVo">
        select id, dataset_id, file_id, duration_sec, call_time, asr_status, asr_result, tenant_id, create_time, biz_no, update_time,
        (SELECT name FROM qc_dataset t WHERE t.id=s.dataset_id) datasetName
        from qc_dataset_item s
    </sql>
    <sql id="selectQdiCountVo">
        select count(1) as count from qc_dataset_item
    </sql>

    <select id="selectQcDatasetItemList" parameterType="QcDatasetItem" resultMap="QcDatasetItemResult">
        <include refid="selectQcDatasetItemVo"/>
        <where>  
            <if test="datasetId != null "> and dataset_id = #{datasetId}</if>
            <if test="datasetName != null "> and EXISTS(SELECT * FROM qc_dataset t WHERE t.id=s.dataset_id AND t.name LIKE '%${datasetName}%')</if>
            <if test="fileId != null  and fileId != ''"> and file_id = #{fileId}</if>
            <if test="durationSec != null "> and duration_sec = #{durationSec}</if>
            <if test="callTime != null "> and call_time = #{callTime}</if>
            <if test="asrStatus != null  and asrStatus != ''"> and asr_status = #{asrStatus}</if>
            <if test="asrResult != null  and asrResult != ''"> and asr_result = #{asrResult}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            <if test="bizNo != null  and bizNo != ''"> and biz_no = #{bizNo}</if>
        </where>
    </select>
    <select id="selectQcDatasetItemById" parameterType="Long" resultMap="QcDatasetItemResult">
        <include refid="selectQcDatasetItemVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcDatasetItem" parameterType="QcDatasetItem" useGeneratedKeys="true" keyProperty="id">
        insert into qc_dataset_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="datasetId != null">dataset_id,</if>
            <if test="fileId != null">file_id,</if>
            <if test="durationSec != null">duration_sec,</if>
            <if test="callTime != null">call_time,</if>
            <if test="asrStatus != null">asr_status,</if>
            <if test="asrResult != null">asr_result,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="bizNo != null and bizNo != ''">biz_no,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="datasetId != null">#{datasetId},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="durationSec != null">#{durationSec},</if>
            <if test="callTime != null">#{callTime},</if>
            <if test="asrStatus != null">#{asrStatus},</if>
            <if test="asrResult != null">#{asrResult},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="bizNo != null and bizNo != ''">#{bizNo},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQcDatasetItem" parameterType="QcDatasetItem">
        update qc_dataset_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="datasetId != null">dataset_id = #{datasetId},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="durationSec != null">duration_sec = #{durationSec},</if>
            <if test="callTime != null">call_time = #{callTime},</if>
            <if test="asrStatus != null">asr_status = #{asrStatus},</if>
            <if test="asrResult != null">asr_result = #{asrResult},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="bizNo != null and bizNo != ''">biz_no = #{bizNo},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcDatasetItemById" parameterType="Long">
        delete from qc_dataset_item where id = #{id}
    </delete>

    <delete id="deleteQcDatasetItemByIds" parameterType="String">
        delete from qc_dataset_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>