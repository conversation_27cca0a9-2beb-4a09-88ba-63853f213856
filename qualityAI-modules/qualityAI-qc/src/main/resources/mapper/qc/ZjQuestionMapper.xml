<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QuestionMapper">

	<resultMap type="com.ideal.qc.domain.Question" id="QuestionResult">
		<id     property="id"        column="id"       />
		<result property="qusNo"      column="qus_no"     />
		<result property="qusName"      column="qus_name"     />
		<result property="qusType"      column="qus_type"     />
		<result property="isValid"        column="is_valid"        />
		<result property="createBy"      column="create_by"     />
		<result property="createTime"    column="create_time"   />
		<result property="updateBy"      column="update_by"     />
		<result property="updateTime"    column="update_time"   />
		<result property="maxV"    column="max_v"   />
		<result property="minV"    column="min_v"   />

		<collection property="options" ofType="com.ideal.qc.domain.QuestionAnswer" column="id" select="getOptions"/>
	</resultMap>
	
	<sql id="selectQusVo">
        select id, qus_no, qus_name, qus_type, is_valid, create_by, create_time,update_by,update_time,max_v,min_v
		from zj_question
    </sql>
	<select id="getOptions" resultType="com.ideal.qc.domain.QuestionAnswer">
		select question_id questionId,answer,seq from zj_question_answer where question_id=#{id} order by seq
	</select>
	<select id="selectQusList" parameterType="com.ideal.qc.domain.Question" resultMap="QuestionResult">
	    <include refid="selectQusVo"/>
		<where>
			<if test="qusNo != null and qusNo != ''">
				AND qus_no like concat('%', #{qusNo}, '%')
			</if>
			<if test="isValid != null and isValid != ''">
				AND is_valid = #{isValid}
			</if>
			<if test="qusName != null and qusName != ''">
				AND qus_name like concat('%', #{qusName}, '%')
			</if>
			<if test="qusType != null and qusType != ''">
				AND qus_type =#{qusType}
			</if>
			<if test="maxV != null and maxV != ''">
				AND max_v &lt;= #{maxV}
			</if>
			<if test="minV != null and minV != ''">
				AND min_v &gt;= #{minV}
			</if>
		</where>
	</select>
	
	<select id="selectQusAll" resultMap="QuestionResult">
		<include refid="selectQusVo"/>
	</select>
	
	<select id="selectQusById" parameterType="Long" resultMap="QuestionResult">
		<include refid="selectQusVo"/>
		where id = #{questionId}
	</select>
	
	
	<select id="checkQusNameUnique" parameterType="String" resultMap="QuestionResult">
		<include refid="selectQusVo"/>
		 where qus_name=#{qusName} limit 1
	</select>
	
	<select id="checkQusCodeUnique" parameterType="String" resultMap="QuestionResult">
		<include refid="selectQusVo"/>
		 where qus_no=#{qusNo} limit 1
	</select>
	
	<update id="updateQus" parameterType="com.ideal.qc.domain.Question">
 		update zj_question
 		<set>
 			<if test="qusNo != null and qusNo != ''">qus_no = #{qusNo},</if>
 			<if test="qusName != null and qusName != ''">qus_name = #{qusName},</if>
 			<if test="qusType != null">qus_type = #{qusType},</if>
 			<if test="isValid != null and isValid != ''">is_valid = #{isValid},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="minV != null and minV != ''">min_v = #{minV},</if>
 			<if test="maxV != null and maxV != ''">max_v = #{maxV},</if>
 			update_time = sysdate()
 		</set>
 		where id = #{id}
	</update>
 	
 	<insert id="insertQus" parameterType="com.ideal.qc.domain.Question" useGeneratedKeys="true" keyProperty="id">
 		insert into zj_question(
 			<if test="id != null and id != 0">id,</if>
 			<if test="qusNo != null and qusNo != ''">qus_no,</if>
 			<if test="qusName != null and qusName != ''">qus_name,</if>
 			<if test="qusType != null">qus_type,</if>
 			<if test="isValid != null and isValid != ''">is_valid,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="maxV != null and maxV != ''">max_v,</if>
 			<if test="minV != null and minV != ''">min_v,</if>
 			create_time
 		)values(
 			<if test="id != null and id != 0">#{id},</if>
 			<if test="qusNo != null and qusNo != ''">#{qusNo},</if>
 			<if test="qusName != null and qusName != ''">#{qusName},</if>
 			<if test="qusType != null">#{qusType},</if>
 			<if test="isValid != null and isValid != ''">#{isValid},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="maxV != null and maxV != ''">#{maxV},</if>
 			<if test="minV != null and minV != ''">#{minV},</if>
 			sysdate()
 		)
	</insert>
	
	<delete id="deleteQusById" parameterType="Long">
		delete from zj_question where id = #{id}
	</delete>
	
	<delete id="deleteQusByIds" parameterType="Long">
 		delete from zj_question where id in
 		<foreach collection="array" item="id" open="(" separator="," close=")">
 			#{id}
        </foreach> 
 	</delete>

	<insert id="insertQusAnswer" parameterType="com.ideal.qc.domain.QuestionAnswer">
		insert into zj_question_answer(question_id,answer,seq) values(#{questionId},#{answer},#{seq})
	</insert>

	<delete id="delQusAnswer" parameterType="Long">
		delete from zj_question_answer where question_id = #{value}
	</delete>
</mapper>