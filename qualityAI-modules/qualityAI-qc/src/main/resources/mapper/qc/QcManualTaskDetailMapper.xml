<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcManualTaskDetailMapper">

    <resultMap type="QcManualTaskDetail" id="QcManualTaskDetailResult">
        <result property="id"    column="id"    />
        <result property="businessId"    column="business_id"    />
        <result property="manualTaskId"    column="manual_task_id"    />
        <result property="smartTaskDetailId"    column="smart_task_detail_id"    />
        <result property="manualType"    column="manual_type"    />
        <result property="bizNo"    column="biz_no"    />
        <result property="inspector"    column="inspector"    />
        <result property="sessionLength"    column="session_length"    />
        <result property="workNo"    column="work_no"    />
        <result property="workName"    column="work_name"    />
        <result property="manualInspectScore"    column="manual_inspect_score"    />
        <result property="manualInspectTime"    column="manual_inspect_time"    />
        <result property="macInspectScore"    column="mac_inspect_score"    />
        <result property="macInspectTime"    column="mac_inspect_time"    />
        <result property="status"    column="status"    />
        <result property="finishStatus"    column="finish_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="templateId"    column="template_id"    />
        <result property="hitPlusRuleCount"    column="hit_plus_rule_count"    />
        <result property="hitMinusRuleCount"    column="hit_minus_rule_count"    />
        <result property="hitItemCount"    column="hit_item_count"    />
        <result property="hitRuleCount"    column="hit_rule_count"    />
        <result property="hitRules"    column="hit_rules"    />
        <result property="hitItems"    column="hit_items"    />
        <result property="templateHitRule"    column="template_hit_rule"    />
    </resultMap>

    <resultMap type="QcManualTaskDetailVO" id="QcManualTaskDetailResultVO">
        <result property="id"    column="id"    />
        <result property="businessId"    column="business_id"    />
        <result property="manualTaskId"    column="manual_task_id"    />
        <result property="smartTaskDetailId"    column="smart_task_detail_id"    />
        <result property="manualType"    column="manual_type"    />
        <result property="bizNo"    column="biz_no"    />
        <result property="inspector"    column="inspector"    />
        <result property="sessionLength"    column="session_length"    />
        <result property="workNo"    column="work_no"    />
        <result property="workName"    column="work_name"    />
        <result property="manualInspectScore"    column="manual_inspect_score"    />
        <result property="manualInspectTime"    column="manual_inspect_time"    />
        <result property="macInspectScore"    column="mac_inspect_score"    />
        <result property="macInspectTime"    column="mac_inspect_time"    />
        <result property="status"    column="status"    />
        <result property="finishStatus"    column="finish_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="templateId"    column="template_id"    />
        <result property="hitPlusRuleCount"    column="hit_plus_rule_count"    />
        <result property="hitMinusRuleCount"    column="hit_minus_rule_count"    />
        <result property="hitItemCount"    column="hit_item_count"    />
        <result property="hitRuleCount"    column="hit_rule_count"    />
        <result property="hitRules"    column="hit_rules"/>
        <result property="hitItems"    column="hit_items"/>
        <result property="templateHitRule"    column="template_hit_rule"/>
        <result property="taskName"    column="task_name"/>
        <result property="planName"    column="plan_name"/>
        <result property="deadlineTime"    column="deadline_time"/>
    </resultMap>

    <sql id="selectQcManualTaskDetailVo">
        select id, business_id, manual_task_id, smart_task_detail_id, manual_type, biz_no, inspector, session_length, work_no, work_name,
        manual_inspect_score, manual_inspect_time, mac_inspect_score, mac_inspect_time, status, finish_status,
         create_time, create_by, update_time, update_by, tenant_id, template_id, hit_plus_rule_count,
         hit_minus_rule_count, hit_item_count, hit_rule_count, hit_rules, hit_items, template_hit_rule from qc_manual_task_detail
    </sql>


    <select id="selectCountByTaskId" resultType="java.util.Map">
        SELECT
            COUNT(1) AS session_count,
            SUM(CASE WHEN finish_status = 1 THEN 1 ELSE 0 END) AS finish_count
        FROM
            qc_manual_task_detail
        WHERE
            manual_task_id = #{taskId}
            AND finish_status != '2'
            <if test="visibleUsers != null and visibleUsers.size() > 0">
                AND inspector IN
                <foreach collection="visibleUsers" item="user" open="(" separator="," close=")">
                    #{user.workNo}
                </foreach>
            </if>
    </select>


    <select id="getNotFinishCount" resultType="java.lang.Integer">
        select count(1) from qc_manual_task_detail where  finish_status = '0' and manual_task_id = #{id}
    </select>


    <update id="recycleDetail">
        update qc_manual_task_detail set finish_status = '2' where manual_task_id = #{id} and finish_status = '0'
    </update>

    <select id="selectQcManualTaskDetailsByTaskId" resultMap="QcManualTaskDetailResultVO">
        SELECT
        d.id, d.business_id, d.manual_task_id, d.smart_task_detail_id, d.manual_type, d.biz_no, d.inspector, d.session_length, d.work_no, d.work_name,
        d.manual_inspect_score, d.manual_inspect_time, d.mac_inspect_score, d.mac_inspect_time, d.status, d.finish_status,
        d.create_time, d.create_by, d.update_time, d.update_by, d.tenant_id, d.template_id, d.hit_plus_rule_count,
        d.hit_minus_rule_count, d.hit_item_count, d.hit_rule_count, d.hit_rules, d.hit_items, d.template_hit_rule,
        t.deadline_time,
        t.task_name,
        t.plan_name
        FROM qc_manual_task_detail d
        left join qc_manual_task  t
        on d.manual_task_id = t.id
        where  d.manual_task_id  = #{manualTaskId}
        <if test="workNo != null  and workNo != ''"> and d.work_no = #{workNo}</if>
        <if test="workName != null  and workName != ''"> and d.work_name like concat('%', #{workName}, '%')</if>
        <if test="minSession != null and minSession != ''"> and d.session_length &gt;= #{minSession}</if>
        <if test="maxSession != null and maxSession != ''"> and d.session_length &lt;= #{maxSession}</if>
        <if test="finishStatus != null  and finishStatus != ''"> and d.finish_status = #{finishStatus}</if>
        <if test="hitRules != null and hitRules.size() > 0 or hitItem != null and hitItem != ''">
            AND d.id IN (
            SELECT DISTINCT task_detail_id
            FROM qc_manual_task_result
            WHERE 1=1
            <if test="hitRules != null and hitRules.size() > 0">
                AND rule_item_id IN
                <foreach collection="hitRules" item="hitRule" open="(" separator="," close=")">
                    #{hitRule}
                </foreach>
            </if>
            <if test="hitItem != null and hitItem != ''">
                AND item_id = #{hitItem}
            </if>
            )
        </if>
    </select>

    <select id="selectQcManualTaskDetailsByTaskIdForRole" resultMap="QcManualTaskDetailResultVO">
        SELECT
        d.id, d.business_id, d.manual_task_id, d.smart_task_detail_id, d.manual_type, d.biz_no, d.inspector, d.session_length, d.work_no, d.work_name,
        d.manual_inspect_score, d.manual_inspect_time, d.mac_inspect_score, d.mac_inspect_time, d.status, d.finish_status,
        d.create_time, d.create_by, d.update_time, d.update_by, d.tenant_id, d.template_id, d.hit_plus_rule_count,
        d.hit_minus_rule_count, d.hit_item_count, d.hit_rule_count, d.hit_rules, d.hit_items, d.template_hit_rule,
        t.deadline_time,
        t.task_name,
        t.plan_name
        FROM qc_manual_task_detail d
        left join qc_manual_task  t
        on d.manual_task_id = t.id
        where  d.manual_task_id  = #{manualTaskId}
        and d.finish_status != '2'
        <if test="workNo != null  and workNo != ''"> and d.work_no = #{workNo}</if>
        <if test="workName != null  and workName != ''"> and d.work_name like concat('%', #{workName}, '%')</if>
        <if test="minSession != null and minSession != ''"> and d.session_length &gt;= #{minSession}</if>
        <if test="maxSession != null and maxSession != ''"> and d.session_length &lt;= #{maxSession}</if>
        <if test="finishStatus != null  and finishStatus != ''"> and d.finish_status = #{finishStatus}</if>
        <if test="hitRules != null and hitRules.size() > 0 or hitItem != null and hitItem != ''">
        AND d.id IN (
        SELECT DISTINCT task_detail_id
        FROM qc_manual_task_result
        WHERE 1=1
        <if test="hitRules != null and hitRules.size() > 0">
            AND rule_item_id IN
            <foreach collection="hitRules" item="hitRule" open="(" separator="," close=")">
                #{hitRule}
            </foreach>
        </if>
        <if test="hitItem != null and hitItem != ''">
            AND item_id = #{hitItem}
        </if>
        )
        </if>
        <if test="visibleUsers != null and visibleUsers.size() > 0">
            AND d.inspector IN
            <foreach collection="visibleUsers" item="user" open="(" separator="," close=")">
                #{user.workNo}
            </foreach>
        </if>
    </select>


    <select id="selectQcManualTaskDetailList" parameterType="QcManualTaskDetail" resultMap="QcManualTaskDetailResult">
        <include refid="selectQcManualTaskDetailVo"/>
        <where>
            <if test="businessId != null  and businessId != ''"> and business_id = #{businessId}</if>
            <if test="manualTaskId != null "> and manual_task_id = #{manualTaskId}</if>
            <if test="smartTaskDetailId != null "> and smart_task_detail_id = #{smartTaskDetailId}</if>
            <if test="manualType != null  and manualType != ''"> and manual_type = #{manualType}</if>
            <if test="bizNo != null  and bizNo != ''"> and biz_no = #{bizNo}</if>
            <if test="inspector != null  and inspector != ''"> and inspector = #{inspector}</if>
            <if test="sessionLength != null "> and session_length = #{sessionLength}</if>
            <if test="workNo != null  and workNo != ''"> and work_no = #{workNo}</if>
            <if test="workName != null  and workName != ''"> and work_name like concat('%', #{workName}, '%')</if>
            <if test="manualInspectScore != null "> and manual_inspect_score = #{manualInspectScore}</if>
            <if test="manualInspectTime != null "> and manual_inspect_time = #{manualInspectTime}</if>
            <if test="macInspectScore != null "> and mac_inspect_score = #{macInspectScore}</if>
            <if test="macInspectTime != null "> and mac_inspect_time = #{macInspectTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="finishStatus != null  and finishStatus != ''"> and finish_status = #{finishStatus}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            <if test="templateId != null "> and template_id = #{templateId}</if>
            <if test="hitPlusRuleCount != null "> and hit_plus_rule_count = #{hitPlusRuleCount}</if>
            <if test="hitMinusRuleCount != null "> and hit_minus_rule_count = #{hitMinusRuleCount}</if>
            <if test="hitItemCount != null "> and hit_item_count = #{hitItemCount}</if>
            <if test="hitRuleCount != null "> and hit_rule_count = #{hitRuleCount}</if>
            <if test="hitRules != null  and hitRules != ''"> and hit_rules = #{hitRules}</if>
            <if test="hitItems != null  and hitItems != ''"> and hit_items = #{hitItems}</if>
            <if test="templateHitRule != null  and templateHitRule != ''"> and template_hit_rule = #{templateHitRule}</if>
        </where>
    </select>

    <select id="selectQcManualTaskDetailById" parameterType="Long" resultMap="QcManualTaskDetailResult">
        <include refid="selectQcManualTaskDetailVo"/>
        where id = #{id}
    </select>



    <insert id="insertQcManualTaskDetail" parameterType="QcManualTaskDetail" useGeneratedKeys="true" keyProperty="id">
        insert into qc_manual_task_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessId != null">business_id,</if>
            <if test="manualTaskId != null">manual_task_id,</if>
            <if test="smartTaskDetailId != null">smart_task_detail_id,</if>
            <if test="manualType != null">manual_type,</if>
            <if test="bizNo != null">biz_no,</if>
            <if test="inspector != null">inspector,</if>
            <if test="sessionLength != null">session_length,</if>
            <if test="workNo != null">work_no,</if>
            <if test="workName != null">work_name,</if>
            <if test="manualInspectScore != null">manual_inspect_score,</if>
            <if test="manualInspectTime != null">manual_inspect_time,</if>
            <if test="macInspectScore != null">mac_inspect_score,</if>
            <if test="macInspectTime != null">mac_inspect_time,</if>
            <if test="status != null">status,</if>
            <if test="finishStatus != null">finish_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="templateId != null">template_id,</if>
            <if test="hitPlusRuleCount != null">hit_plus_rule_count,</if>
            <if test="hitMinusRuleCount != null">hit_minus_rule_count,</if>
            <if test="hitItemCount != null">hit_item_count,</if>
            <if test="hitRuleCount != null">hit_rule_count,</if>
            <if test="hitRules != null">hit_rules,</if>
            <if test="hitItems != null">hit_items,</if>
            <if test="templateHitRule != null">template_hit_rule,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessId != null">#{businessId},</if>
            <if test="manualTaskId != null">#{manualTaskId},</if>
            <if test="smartTaskId != null">#{smartTaskId},</if>
            <if test="manualType != null">#{manualType},</if>
            <if test="bizNo != null">#{bizNo},</if>
            <if test="inspector != null">#{inspector},</if>
            <if test="sessionLength != null">#{sessionLength},</if>
            <if test="workNo != null">#{workNo},</if>
            <if test="workName != null">#{workName},</if>
            <if test="manualInspectScore != null">#{manualInspectScore},</if>
            <if test="manualInspectTime != null">#{manualInspectTime},</if>
            <if test="macInspectScore != null">#{macInspectScore},</if>
            <if test="macInspectTime != null">#{macInspectTime},</if>
            <if test="status != null">#{status},</if>
            <if test="finishStatus != null">#{finishStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="hitPlusRuleCount != null">#{hitPlusRuleCount},</if>
            <if test="hitMinusRuleCount != null">#{hitMinusRuleCount},</if>
            <if test="hitItemCount != null">#{hitItemCount},</if>
            <if test="hitRuleCount != null">#{hitRuleCount},</if>
            <if test="hitRules != null">#{hitRules},</if>
            <if test="hitItems != null">#{hitItems},</if>
            <if test="templateHitRule != null">#{templateHitRule},</if>
        </trim>
    </insert>


    <insert id="batchInsert">
        insert into
         qc_manual_task_detail(
         business_id,manual_task_id,smart_task_detail_id,manual_type,
         biz_no,inspector,session_length,work_no,work_name,mac_inspect_score,
         mac_inspect_time,status,finish_status,create_time,create_by,
         update_time,update_by,template_id,template_hit_rule)
        values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.businessId},#{item.manualTaskId},#{item.smartTaskDetailId},#{item.manualType},#{item.bizNo},
            #{item.inspector},#{item.sessionLength},#{item.workNo},#{item.workName},#{item.macInspectScore},#{item.macInspectTime},
            #{item.status},#{item.finishStatus},#{item.createTime},#{item.createBy},#{item.updateTime},#{item.updateBy},#{item.templateId},#{item.templateHitRule})
        </foreach>
    </insert>

    <update id="updateQcManualTaskDetail" parameterType="QcManualTaskDetail">
        update qc_manual_task_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="manualTaskId != null">manual_task_id = #{manualTaskId},</if>
            <if test="smartTaskDetailId != null">smart_task_detail_id = #{smartTaskDetailId},</if>
            <if test="manualType != null">manual_type = #{manualType},</if>
            <if test="bizNo != null">biz_no = #{bizNo},</if>
            <if test="inspector != null">inspector = #{inspector},</if>
            <if test="sessionLength != null">session_length = #{sessionLength},</if>
            <if test="workNo != null">work_no = #{workNo},</if>
            <if test="workName != null">work_name = #{workName},</if>
            <if test="manualInspectScore != null">manual_inspect_score = #{manualInspectScore},</if>
            <if test="manualInspectTime != null">manual_inspect_time = #{manualInspectTime},</if>
            <if test="macInspectScore != null">mac_inspect_score = #{macInspectScore},</if>
            <if test="macInspectTime != null">mac_inspect_time = #{macInspectTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="finishStatus != null">finish_status = #{finishStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="hitPlusRuleCount != null">hit_plus_rule_count = #{hitPlusRuleCount},</if>
            <if test="hitMinusRuleCount != null">hit_minus_rule_count = #{hitMinusRuleCount},</if>
            <if test="hitItemCount != null">hit_item_count = #{hitItemCount},</if>
            <if test="hitRuleCount != null">hit_rule_count = #{hitRuleCount},</if>
            <if test="hitRules != null">hit_rules = #{hitRules},</if>
            <if test="hitItems != null">hit_items = #{hitItems},</if>
            <if test="templateHitRule != null">template_hit_rule = #{templateHitRule},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcManualTaskDetailById" parameterType="Long">
        delete from qc_manual_task_detail where id = #{id}
    </delete>

    <delete id="deleteQcManualTaskDetailByIds" parameterType="String">
        delete from qc_manual_task_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>