package com.ideal.qc.controller;

import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcManualRuleClassify;
import com.ideal.qc.mapper.QcManualRuleClassifyMapper;
import com.ideal.qc.mapper.QcManualRuleConfMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/manual/conf/classify")
public class QcManualRuleClassifyController extends BaseController {


    @Autowired
    private QcManualRuleClassifyMapper qcManualRuleClassifyMapper;

    @Autowired
    private QcManualRuleConfMapper qcManualRuleConfMapper;

    @PostMapping("/add")
    public AjaxResult add(@RequestBody QcManualRuleClassify qcManualRuleClassify)
    {
        qcManualRuleClassify.setCreateBy(SecurityUtils.getUsername());
        qcManualRuleClassify.setCreateTime(new Date());
        //获取当前所属的分类
        return toAjax(qcManualRuleClassifyMapper.insertQcManualRuleClassify(qcManualRuleClassify));
    }


    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody QcManualRuleClassify qcManualRuleClassify){
        qcManualRuleClassify.setUpdateBy(SecurityUtils.getUsername());
        qcManualRuleClassify.setUpdateTime(new Date());
        return toAjax(qcManualRuleClassifyMapper.updateQcManualRuleClassify(qcManualRuleClassify));
    }


    @GetMapping("/list")
    public AjaxResult getList(){
        List<QcManualRuleClassify> list = new ArrayList<>();
        try {
            list = qcManualRuleClassifyMapper.selectQcManualRuleClassifyList(new QcManualRuleClassify());
        } catch (Exception e) {
            return error(e.getMessage());
        }
        return success(list);
    }

    @GetMapping("/del/{id}")
    public AjaxResult delClassify(@PathVariable Long id)
    {
        //查询当前分类下面是否有人工规则
        int count = qcManualRuleConfMapper.getCountByClassify(id);
        if (count > 0){
            return AjaxResult.error("当前分类下存在人工规则，禁止删除");
        }
        return toAjax(qcManualRuleClassifyMapper.deleteQcManualRuleClassifyByClassifyId(id));
    }

}
