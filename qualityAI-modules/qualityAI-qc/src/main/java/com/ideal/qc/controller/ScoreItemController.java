package com.ideal.qc.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ideal.common.core.utils.TreeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.ScoreItem;
import com.ideal.qc.service.IScoreItemService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 评分规则模板Controller
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@RestController
@RequestMapping("/scoreItem")
public class ScoreItemController extends BaseController
{
    @Autowired
    private IScoreItemService scoreItemService;

    /**
     * 查询评分规则模板列表
     */
//    @RequiresPermissions("quality:item:list")
    @GetMapping("/list")
    public AjaxResult list(ScoreItem scoreItem)
    {
        List<ScoreItem> list = scoreItemService.selectScoreItemList(scoreItem);
        return success(TreeUtils.buildTree(list));
    }
    /** 获取评分项分类节点 */
    @GetMapping("/listPnode")
    public AjaxResult listPnode(ScoreItem scoreItem){
        List<ScoreItem> list = scoreItemService.selectScoreItemPnode(scoreItem);
//        List<ScoreItem> tree = TreeUtils.buildTree(list);
        return success(TreeUtils.buildTree(list));
    }

//    @GetMapping("/listByTplId/")
//    public AjaxResult listByTplId( ScoreItem scoreItem)
//    {
//        scoreItem.setScoretplId(scoretplId);
//        List<ScoreItem> list = scoreItemService.selectScoreItemByTplId(scoreItem);
//        return success(list);
//    }

    /**
     * 导出评分规则模板列表
     */
//    @RequiresPermissions("quality:item:export")
    @Log(title = "评分规则模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScoreItem scoreItem)
    {
        List<ScoreItem> list = scoreItemService.selectScoreItemList(scoreItem);
        ExcelUtil<ScoreItem> util = new ExcelUtil<ScoreItem>(ScoreItem.class);
        util.exportExcel(response, list, "评分规则模板数据");
    }

    /**
     * 获取评分规则模板详细信息
     */
//    @RequiresPermissions("quality:item:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(scoreItemService.selectScoreItemById(id));
    }

    /**
     * 新增评分规则模板
     */
//    @RequiresPermissions("quality:item:add")
    @Log(title = "评分规则模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScoreItem scoreItem)
    {
        return toAjax(scoreItemService.insertScoreItem(scoreItem));
    }

    /**
     * 修改评分规则模板
     */
//    @RequiresPermissions("quality:item:edit")
    @Log(title = "评分规则模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScoreItem scoreItem)
    {
        return toAjax(scoreItemService.updateScoreItem(scoreItem));
    }

    /**
     * 删除评分规则模板
     */
//    @RequiresPermissions("quality:item:remove")
    @Log(title = "评分规则模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(scoreItemService.deleteScoreItemByIds(ids));
    }
}
