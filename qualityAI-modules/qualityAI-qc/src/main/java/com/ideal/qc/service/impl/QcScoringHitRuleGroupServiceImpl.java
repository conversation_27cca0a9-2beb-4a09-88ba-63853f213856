package com.ideal.qc.service.impl;

import com.ideal.qc.domain.QcScoringHitRuleGroup;
import com.ideal.qc.dto.QcScoringHitRuleGroupDTO;
import com.ideal.qc.mapper.QcScoringHitRuleGroupMapper;
import com.ideal.qc.service.QcScoringHitRuleGroupService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 评分细则(项)规则组表Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/15 17:38
 */
@Service
public class QcScoringHitRuleGroupServiceImpl implements QcScoringHitRuleGroupService {

    @Resource
    private QcScoringHitRuleGroupMapper qcScoringHitRuleGroupMapper;

    /**
     * 新增评分细则命中规则组
     */
    @Override
    public void addScoringHitRuleGroup(QcScoringHitRuleGroup qcScoringHitRuleGroup) {
        qcScoringHitRuleGroupMapper.addScoringHitRuleGroup(qcScoringHitRuleGroup);
    }

    /**
     * 批量删除评分细则命中规则组
     */
    @Override
    public void removeScoringHitRuleGroupBatch(List<Long> qcScoringHitRuleGroupIdList) {
        qcScoringHitRuleGroupMapper.removeScoringHitRuleGroupBatch(qcScoringHitRuleGroupIdList);
    }

    /**
     * 根据评分细则查询所有规则组列表与规则组下面挂着的规则列表
     */
    @Override
    public List<Long> getQcScoringHitRuleGroupIds(Long scoringItemId) {
        return qcScoringHitRuleGroupMapper.getQcScoringHitRuleGroupIds(scoringItemId);
    }

    /**
     * 修改评分细则命中规则组
     */
    @Override
    public void updateScoringHitRuleGroup(QcScoringHitRuleGroup qcScoringHitRuleGroup) {
        qcScoringHitRuleGroupMapper.updateScoringHitRuleGroup(qcScoringHitRuleGroup);
    }
}
