<template>
	<div class="" style="background-color:white;">
		<div class="template-header" >
		    <svg width="80%" height="36" style="padding-top:10px;">
		      <line x1="0" y1="10" x2="50" y2="10" stroke="green" stroke-width="5"/>
			  <text x="55" y="15" font-family="Arial" font-size="15" fill="green" >启用</text>
			  <line x1="100" y1="10" x2="150" y2="10" stroke="red" stroke-width="5"/>
			  <text x="155" y="15" font-family="Arial" font-size="15" fill="red" >命中</text>
			  <line x1="200" y1="10" x2="250" y2="10" stroke="gray" stroke-width="5"/>
			  <text x="255" y="15" font-family="Arial" font-size="15" fill="gray">禁用</text>
			  <rect x="300" y="2" rx="5" ry="5" width="100" height="15" fill="#5ceb9c" stroke="#5ceb9c"/>
			  <text x="415" y="15" font-family="Arial" font-size="15" fill="gray" >智能规则</text>
			  <rect x="500" y="2" rx="5" ry="5" width="100" height="15" fill="#409EFF" stroke="#409EFF"/>
  			  <text x="615" y="15" font-family="Arial" font-size="15" fill="gray" >人工规则</text>
			  <rect x="700" y="2" rx="5" ry="5" width="100" height="15" fill="#e2f594" stroke="#e2f594"/>
  			  <text x="815" y="15" font-family="Arial" font-size="15" fill="gray" >流程规则</text>
		    </svg>
			<el-button v-if="op=='edit'" class="btn" type="primary" @click="submitForm" :loading="submiting">保 存</el-button>
			<el-input v-if="test" type="textarea" v-model="input" />
			<el-input v-if="test" type="textarea" v-model="result" />
			<el-input v-if="test" type="textarea" v-model="retObj" />
			<el-button v-if="test" class="btn" type="primary" @click="getTemplate" :loading="submiting">获取数据</el-button>
			<el-button v-if="test" class="btn" type="primary" @click="formatSubmitResult" :loading="submiting">结果</el-button>
			<el-button v-if="test" class="btn" type="primary" @click="submitForm" :loading="submiting">保存测试</el-button>
<!--			<el-divider class="topLine"></el-divider>-->
	    </div>
		<div style="width: 100%; max-height: 700px;overflow:auto;">
			<div id="treePanel" ref="treePanel" style="width: 1600px;height: 800px;"></div>
		</div>
	<!-- 添加或修改分类对话框 -->
	    <el-dialog v-if="open" :visible.sync="open" width="1400px" append-to-body 	:close-on-press-escape="false"
					:show-close="false"
					:close-on-click-modal="false">
			<template #title>
			    <div>
			    	<span>{{_props.viewParam.qcType==2?'修改智能质检结果':'修改人工抽检结果'}}</span>
				  	<el-button class="btn" @click="cancel">取 消</el-button>
				  	<el-button class="btn" type="primary" @click="handManualCheckResult">暂 存</el-button>
				  	<el-button class="btn" type="primary" @click="viewRule" :loading="submiting">规则预览</el-button>
			  		<el-divider class="topLine"></el-divider>
			    </div>
			</template>
			<el-form ref="sortForm" :model="currNode" label-width="120px" :rules="rules">
	        	<el-form-item v-if="currNode.sRet.smartResult" label="智能结果" prop="dictLabel">
				  	<el-select v-model="currNode.sRet.smartResult" disabled>
						<el-option value="1" label="命中"/>
						<el-option value="2" label="未命中"/>
				  	</el-select>
		        </el-form-item>
				<el-form-item v-if="smartResult.length >0" label="命中语句" prop="dictLabel">
					<el-row v-for="item in smartResult">
						<el-col>{{item}}</el-col>
					</el-row>
		        </el-form-item>
				<el-form-item v-if="currNode.sRet.semanticsResult1" label="大模型结果" prop="dictLabel">
					<el-select v-model="currNode.sRet.semanticsResultId" disabled style="width:400px;">
			  			<el-option v-for="item in record.list" :key="item.id" :value="item.id" :label="item.agentName"/>
		  			</el-select>
		          	<el-input v-model="currNode.sRet.semanticsResult1" disabled style="width:100px;" />
		        </el-form-item>
				<el-form-item :label="_props.viewParam.qcType==2?'人工复检结果':'人工抽检结果'" prop="recheckResult">
				  	<el-select v-model="currNode.sRet.recheckResult" @change="handlerRecheck">
		  				<el-option v-for="item in sematicList" :key="item.dictCode" :value="item.dictLabel" :label="item.dictLabel"/>
	  			  	</el-select>
		        </el-form-item>
				<el-form-item label="最终结果" prop="dictLabel">
				  	<el-select v-model="currNode.sRet.finalResult" :disabled="true">
						<el-option value="1" label="命中"/>
						<el-option value="2" label="未命中"/>
				  	</el-select>
		        </el-form-item>
				<el-form-item label="备注" prop="dictLabel">
		          	<el-input v-model="currNode.sRet.remark" maxlength="30" />
		        </el-form-item>
			</el-form>
	    </el-dialog>
		<el-dialog title="" :visible.sync="visit.flow" width="1400px" 
			:close-on-press-escape="false"
			:show-close="false"
			:close-on-click-modal="false"
			custom-class="no-header-dialog"
			height="860px"
			append-to-body
			>
			<flow  @goList="goList" ref="flowView"></flow>
		</el-dialog>
		<el-dialog title="" :visible.sync="visit.smart" width="1400px" 
			:close-on-press-escape="false"
			:show-close="false"
			:close-on-click-modal="false"
			custom-class="no-header-dialog"
			height="860px"
			append-to-body
			>
			<smart @goList="goList" ref="smartView"></smart>
		</el-dialog>
		<el-dialog title="" :visible.sync="visit.man" width="1400px" 
			:close-on-press-escape="false"
			:show-close="false"
			:close-on-click-modal="false"
			custom-class="no-header-dialog"
			height="860px"
			append-to-body
			>
			<man @getList="goList" ref="manView"></man>
		</el-dialog>
	</div>
</template>

<script>
import { getTemplate} from "@/api/quality/scoringTemplate.js";
import { detailList,resultList,updateSmart,addManualResult} from "@/api/quality/smart.js";
import { getDetail,manualResultList} from "@/api/quality/manualTask.js";
import {recordList} from "@/api/quality/rule.js";
import * as echarts from '@/api/system/echarts.min.js';
import smart from "@/views/quality/ruleConfig/smart/edit.vue";
import flow from "@/views/quality/ruleConfig/flow/edit.vue";
import man from "@/views/quality/ruleConfig/man/edit.vue";
import { queryListByType} from "@/api/system/dict/data";
var myChart
var option

export default {
	name: "Review",
	props:{
  		viewParam:Object
    },
	components: {
  		flow,smart,man
	},
  	data() {
    	return {
			data:[],
			level:[
				{lkey:"qcScoringItemDTOList",nkey:"scoringTemplateName"},
				{lkey:"qcScoringHitRuleGroupDTOList",nkey:"scoringItemName"},
				{lkey:"qcScoringHitRuleItemList",nkey:""}
			],
			smartList:[],//接口查询出来的qc_smart_task_result
			smartResult:[],//接口查询出来的智能质检结果明细数据qc_smart_task_result.smart_result_detail
			sematicList:[],//人工复检选项枚举
			updObj:[],//待更新的明细列表qc_smart_task_result
			op:"view",
			submiting:false,
			open:false,
			currNode:{
				sRet:{}
			},show:false,input:"",title:"修改智能结果",result:"",test:false,
			visit:{
				flow:false,
				smart:false,
				man:false
			},
			minScore:0,
			record:{
				list:[]
			},
			hitItems:[],
			rules:{
				recheckResult:[{required:true,trigger:"11"}]
			}
		};
	},
  	created() {
		queryListByType	({dictTypeList:["qc_semantics_result_type"],status:"0"}).then(response => {
			if(response.data){
				this.sematicList = response.data["qc_semantics_result_type"];
				this.getLowerScore();
			}
		});
		this.getRecordList();
		if(!this._props || !this._props.viewParam){
			this.test = true;
			this.input='{"taskDetailId":1997,"op":"","qcType":2}';
			return ;
		}
		this.getTreeData();
		this.op = this._props.viewParam.op;
	},
  	methods: {
		getTemplate(){
			this["_props"]["viewParam"]=JSON.parse(this.input);
			this.getTreeData();
			this.op = this._props.viewParam.op;
		},
		formatSubmitResult(){
			this.hitItems = [];
			var score = this.data.basicScore;
			var hitItemCount = 0;
			var hitRuleCount = 0;
			var hitPRuleCount = 0;
			var hitMRuleCount = 0;
			var tmp = this.data.children;
			for(var index in tmp){
				var tmp1 = tmp[index];//评分项
				if(tmp1.isHit){
					//score += tmp1.hitRuleScore;
					let currScore = this.getItemScore(tmp1,index);
					this.hitItems.push({itemName:tmp[index].name,score:currScore});
					score += currScore;
					hitItemCount++;
				}
				for(var index2 in tmp1.children){
					var tmp2 = tmp1.children[index2];//规则组
					for(var index3 in tmp2.children){
						var tmp3 = tmp2.children[index3];//规则
						for(var tmp4 in this.updObj){
							if(this.updObj[tmp4].ruleItemId == tmp3.id){
								this.updObj[tmp4]['itemResult'] = tmp1.isHit?'1':"2";
								break;
							}
						}
						if(tmp1.isHit && tmp3.isHit){
							hitRuleCount++;
							if(tmp1.hitRuleScore < 0){
								hitMRuleCount++;
							}else{
								hitPRuleCount++;
							}
						}
					}
				}
			}
			if(score<this.data.lowestScore){
				score = this.data.lowestScore;
			}else if(score > this.data.highestScore){
				score = this.data.highestScore;
			}
			var ret = {
				id:this._props.viewParam.taskDetailId,
				taskDetailId:this._props.viewParam.taskDetailId,
				smartTaskId:this.smartTaskId,
				hitItemCount:hitItemCount,
				hitRuleCount:hitRuleCount,
				hitPlusRuleCount:hitPRuleCount,
				hitMinusRuleCount:hitMRuleCount,
				checkInspectScore:score,
				templateHitRule:JSON.stringify(this.data),
				detailList:[]
			}
			for(var tmp in this.updObj){
				ret.detailList.push(this.updObj[tmp]);
			}
			this.result = JSON.stringify(ret);
			this.retObj = JSON.stringify(this.hitItems);
			return ret;
		},
	    getTreeData(){
			if(this._props.viewParam.taskDetailId != undefined && this._props.viewParam.taskDetailId !=''){
				this.getResult();
			}else if(this._props.viewParam && this._props.viewParam.tempId && this._props.viewParam.tempId !=''){
				getTemplate(this._props.viewParam.tempId).then(response => {
					if(response.data ==undefined){
						this.$message.error('模板【'+this._props.viewParam.tempId+'】编号不存在');
						return;
					}
					this.data = response.data;
					this.formatData(this.data,0,0);
					this.init();
					
				});
			}else{
				this.$message.error('缺失模板编号');
				return;
			}
		},
		getResult(){
			this.hitItems = [];
			if(this._props.viewParam && this._props.viewParam.qcType == 2 || !this._props.viewParam.qcType){
				detailList({},{isPg:false,id:this._props.viewParam.taskDetailId}).then(response => {
					this.smartTaskId = response.rows[0].smartTaskId;
					this.data = JSON.parse(response.rows[0].templateHitRule);
					resultList({},{isPg:false,taskDetailId:this._props.viewParam.taskDetailId}).then(response => {
						this.smartList = response.rows;
						if(this.data == undefined){
							return;
						}
						this.formatData(this.data,0,0);
						this.updateNodeIndex(this.data,0,0);
						this.init();
						this.smartList = [];
			      	}).finally(v=>{});
		      	}).finally(v=>{});
			}else if(this._props.viewParam && this._props.viewParam.qcType == 1){
				getDetail(this._props.viewParam.taskDetailId).then(response => {
					this.smartTaskId = response.data.smartTaskId;
					this.data = JSON.parse(response.data.templateHitRule);
					manualResultList({taskDetailId:this._props.viewParam.taskDetailId}).then(response => {
						this.smartList = response.rows;
						if(this.data == undefined){
							return;
						}
						this.formatData(this.data,0,0);
						this.updateNodeIndex(this.data,0,0);
						this.init();
						this.smartList = [];
			      	}).finally(v=>{});
		      	}).finally(v=>{});
			}  
		},
		init () {
			myChart = echarts.init(this.$refs.treePanel)
	      	option = {
				legend: {
				    data: [
						{icon: 'circle'}
					],
					show:true
				},
	        	tooltip: { // 提示框浮层设置
					trigger: 'item',
					triggerOn: 'mousemove', // 提示框触发条件
		          	enterable: true, // 鼠标是否可进入提示框浮层中，默认false
		          	confine: true, // 是否将tooltip框限制在图表的区域内
		          	formatter: (params) => // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
		            	this.formatTip(params)
		          	,
		          	backgroundColor: '#FFF', // 提示框浮层的背景颜色
		          	borderColor: '#1890FF', // 提示框浮层的边框颜色
		          	borderWidth: 1, // 提示框浮层的边框宽
		          	borderRadius: 8, // 提示框浮层圆角
		          	padding: [6, 8], // 提示框浮层的内边距
		          	textStyle: { // 提示框浮层的文本样式
		            	color: '#333', // 文字颜色
		            	fontWeight: 400, // 字体粗细
		            	fontSize: 16, // 字体大小
		            	lineHeight: 20, // 行高
		            	width: 60, // 文本显示宽度
		            	// 文字超出宽度是否截断或者换行；只有配置width时有效
		            	overflow: 'breakAll', // truncate截断，并在末尾显示ellipsis配置的文本，默认为...;break换行;breakAll换行，并强制单词内换行
		            	ellipsis: '...'
		          	},
	          		extraCssText: 'box-shadow: 0 0 9px rgba(0, 0, 0, 0.3);text-align: right;' // 额外添加到浮层的css样式
	        	},
	        	series: [
		          	{
			            type: 'tree',
			            data: [this.data],
			            name: '树图',
			            top: '1%', // 组件离容器上侧的距离，像素值20，或相对容器的百分比20%
			            left: '7%', // 组件离容器左侧的距离
			            bottom: '1%', // 组件离容器下侧的距离
			            right: '20%', // 组件离容器右侧的距离
			            layout: 'orthogonal', // 树图的布局，正交orthogonal和径向radial两种
			            orient: 'LR', // 树图中正交布局的方向，'LR','RL','TB','BT'，只有布局是正交时才生效
			            edgeShape: 'curve', // 树图边的形状，有曲线curve和折线polyline两种，只有正交布局下生效
						width:'1000',
						height:'800',
			            roam: false, // 是否开启鼠标缩放或平移，默认false
			            initialTreeDepth: 5, // 树图初始的展开层级（深度），根节点是0，不设置时全部展开
			            // symbol: 'arrow', // 标记的图形，默认是emptyCircle;circle,rect,roundRect,triangle,diamond,pin,arrow,none
			            // symbolRotate: 270, // 配合arrow图形使用效果较好
			            symbolSize: 16, // 大于0时是圆圈，等于0时不展示，标记的大小
			            itemStyle: { // 树图中每个节点的样式
							color: '#1890FF', // 节点未展开时的填充色
				            borderColor: 'rgba(255, 144, 0, 1)', // 图形的描边颜色
							borderWidth: 1, // 描边线宽，为0时无描边
				            borderType: 'dotted', // 描边类型
				            borderCap: 'square', // 指定线段末端的绘制方式butt方形结束，round圆形结束，square
				            shadowColor: 'rgba(0,121,221,0.3)', // 阴影颜色
				            shadowBlur: 16, // 图形阴影的模糊大小
				            opacity: 1 // 图形透明度
			            },
			            label: { // 每个节点对应的文本标签样式
			              	show: true, // 是否显示标签
			              	distance: 8, // 文本距离图形元素的距离
			              	position: 'left', // 标签位置
			              	verticalAlign: 'middle', // 文字垂直对齐方式，默认自动，top，middle，bottom
			              	align: 'center', // 文字水平对齐方式，默认自动，left，right，center
			              	fontSize: 16, // 字体大小
			              	color: '#333', // 字体颜色
			              	backgroundColor: '#F0F5FA', // 文字块的背景颜色
			              	borderColor: '#1890FF', // 文字块边框颜色
			              	borderWidth: 1, // 文字块边框宽度
			              	borderType: 'solid', // 文字块边框描边类型 solid dashed dotted
			              	borderRadius: 4, // 文字块的圆角
			              	padding: [6, 12], // 文字块内边距
			              	shadowColor: 'rgba(0,121,221,0.3)', // 文字块的背景阴影颜色
			              	shadowBlur: 6, // 文字块的背景阴影长度
			              	width: 100,
			              	// 文字超出宽度是否截断或者换行；只有配置width时有效
			              	overflow: 'truncate', // truncate截断，并在末尾显示ellipsis配置的文本，默认为...;break换行;breakAll换行，并强制单词内换行
			              	ellipsis: '...'
		            	},
			            lineStyle: { // 树图边的样式
			              	color: 'rgba(0,0,0,.35)', // 树图边的颜色
			              	width: 2, // 树图边的宽度
			              	curveness: 0.5, // 树图边的曲度
			              	shadowColor: 'rgba(0, 0, 0, 0.5)', // 阴影颜色
			              	shadowBlur: 10 // 图形阴影的模糊大小
			            },
			            emphasis: { // 树图中图形和标签高亮的样式
			              	disabled: false, // 是否关闭高亮状态，默认false
			              	// 在高亮图形时，是否淡出其它数据的图形已达到聚焦的效果
		             	 	focus: 'self', // none不淡出其他图形（默认）；self只聚焦当前高亮的数据图形；series聚焦当前高亮的数据所在系列的所有图形；ancestor聚焦所有祖先节点；descendant聚焦所有子孙节点；relative聚焦所有子孙和祖先节点
			              	blurScope: 'coordinateSystem', // 开启focus时，配置淡出的范围，coordinateSystem淡出范围为坐标系（默认）；series淡出范围为系列；global淡出范围为全局
			              	itemStyle: { // 该节点的样式
				                color: '#1890FF', // 图形的颜色
				                // borderColor: 'rgba(255, 144, 0, 1)', // 图形的描边颜色
				                borderWidth: 1, // 描边线宽，为0时无描边
				                borderType: 'solid', // 描边类型 solid dashed dotted
				                borderCap: 'square', // 指定线段末端的绘制方式butt方形结束，round圆形结束，square
				                shadowColor: 'rgba(0,121,221,0.3)', // 阴影颜色
				                shadowBlur: 12, // 图形阴影的模糊大小
				                opacity: 1 // 图形透明度
			              	},
			              	lineStyle: { // 树图边的样式
				                color: '#4bcb78', // 树图边的颜色
				                width: 2, // 树图边的宽度
				                curveness: 0.5, // 树图边的曲度
				                shadowColor: 'rgba(0, 0, 0, 0.5)', // 阴影颜色
				                shadowBlur: 6 // 图形阴影的模糊大小
			              	},
			              	label: { // 高亮标签的文本样式
				                color: '#333',
				                fontWeight: 600
			              	}
		           		},
		            	blur: { // 淡出状态的相关配置，开启emphasis.focus后有效
							itemStyle: {color:"gray"}, // 节点的样式
			              	lineStyle: {}, // 树图边的样式
			              	label: {} // 淡出标签的文本样式
		            	},
		            	leaves: { // 叶子节点的特殊配置
	              			label: {// 叶子节点的文本标签样式
			                	distance: 8,
			                	// color: '#1890FF',
			                	position: 'right',
			                	verticalAlign: 'middle',
			                	align: 'left',
								formatter: function (params) {
				                    // 你可以在这里返回你想要显示的文字
				                    return params.data.name!=null?params.data.name:""; // 例如，显示节点名称
			                	}
		              		},
		              		itemStyle: {color:"gray"}, // 叶子节点的样式
		              		emphasis: {}, // 叶子节点高亮状态的配置
		              		blur: {}, // 叶子节点淡出状态的配置
		              		select: {} // 叶子节点选中状态的配置
	            		},
		            	animation: true, // 是否开启动画
			            expandAndCollapse: true, // 子树折叠和展开的交互，默认打开
			            animationDuration: 550, // 初始动画的时长
			            animationEasing: 'linear', // 初始动画的缓动效果
			            animationDelay: 0, // 初始动画的延迟
			            animationDurationUpdate: 750, // 数据更新动画的时长
			            animationEasingUpdate: 'cubicInOut', // 数据更新动画的缓动效果
			            animationDelayUpdate: 0 ,// 数据更新动画的延迟
						layout: { // 调整布局参数以优化显示。
			                orientation: 'vertical', // 或'horizontal'，根据需要选择。
			                nodePadding: 30, // 增加节点之间的间距。
			                layerPadding: 100 // 增加层级之间的间距。
		            	}
	          		}
        		]
	      }
	      myChart.setOption(option)
		  //if(this.op != "view"){
			  myChart.on("click", this.handleNodeClick)
		  //}
	    },
		handleNodeClick(params){
			if(params.data.children == undefined){
				var tmp = JSON.parse(JSON.stringify(params.data));
				if(this.op == 'view'){
					this.currNode = tmp;
					this.$nextTick(()=>{
						this.viewRule();
					})
					return;
				}else if(params.data.status != 1){
					this.$message.warning('该规则节点已禁用，不可修改');
					return
				}
				if(!params.data.sRet){
					tmp['sRet'] = {recheckResult:'',finalResult:'',remark:'',smartResult:""};
				}else{
					var smartResult = params.data.sRet.smartResultDetail?JSON.parse(params.data.sRet.smartResultDetail):{};
					for(var i in smartResult){
						this.smartResult.push(smartResult[i]);
					}
				}
				try{
					
					if(tmp.sRet.semanticsResult){
						var tmp1 = JSON.parse(tmp.sRet.semanticsResult);
						for(var i in tmp1){
							tmp.sRet['semanticsResult1'] = tmp1[i];
							tmp.sRet['semanticsResultId'] = parseInt(i);
						}
					}
				}catch(e){}
				this.open=true;
				this.currNode = tmp;
		  	}
		},
		handManualCheckResult(params){
			var node = this.currNode;
			if(!node.sRet.recheckResult  || node.sRet.recheckResult == ''){
				this.$message.warning(this._props.viewParam.qcType==2?'未选择人工复检结果':'未选择人工抽检结果');
				return;
			}
			var pids = node.parentId.split(",");
			var tmp = {
				ruleItemId:node.id,
				itemId:pids[2],
				ruleId:node.ruleId,
				taskDetailId:this._props.viewParam.taskDetailId,
				//recheckResult:params.data["isHit"],
				id:node["qstrId"]?node["qstrId"]:null,
				isHit:node.sRet.finalResult!='1'?false:true,
				recheckResult:node.sRet.recheckResult,
				finalResult:node.sRet.finalResult,
				ruleName:node.name,
				//updateBy:'CS1868',
				indexs:node.indexs,
				remark:node.sRet.remark
			};
			this.updObj[tmp.ruleItemId] = tmp;
			this.updateNodeInfo(tmp);
			//this.removeNodeInfo(node.indexs);
			this.updLastTreeNode(tmp);
			this.formatData(this.data,0,0);
			this.updateNodeIndex(this.data,0,0);
			this.init();
			this.open = false;
			return;
		},
		updateNodeInfo(node){
			var tmp = node["indexs"].split("-");
			if(this.data.children[tmp[1]].children[tmp[2]].children[tmp[3]].sRet){
				this.data.children[tmp[1]].children[tmp[2]].children[tmp[3]].sRet["remark"] = node.remark;
				this.data.children[tmp[1]].children[tmp[2]].children[tmp[3]].sRet["recheckResult"] = node.recheckResult;
				this.data.children[tmp[1]].children[tmp[2]].children[tmp[3]].sRet["finalResult"] = node.finalResult;
			}else{
				this.data.children[tmp[1]].children[tmp[2]].children[tmp[3]]["sRet"] = JSON.parse(JSON.stringify(node));
			}
		},
		updLastTreeNode(node){//更新叶子到顶层这条线的命中状态
			var tmp = node["indexs"].split("-");
			var parent = this.data.children[tmp[1]]//评分项
								  .children[tmp[2]];//规则组
			var isAll = parent.ruleGroupMode;
			var final = node.isHit;
			//更新叶子节点
			this.data.children[tmp[1]].children[tmp[2]].children[tmp[3]].isHit = final;
			//更新规则组
			if(isAll == '1'){//全部
				if(final == true){
					for(var j=0;j<parent.children.length;j++){
						if(j==tmp[3]){
							continue;
						}
						if(parent.children[j]["isHit"] ==undefined || parent.children[j]["isHit"] == false){
							final = false;
							break;
						}
					}
				}
			}else{//任一
				if(final == false){
					for(var j=0;j<parent.children.length;j++){
						if(j==tmp[3]){
							continue;
						}
						if(parent.children[j]["isHit"] && parent.children[j]["isHit"] == true){
							final = true;
							break;
						}
					}
				}
			}
			parent["isHit"] = final;
			//更新评分项
			if(final == false){
				var score = this.data.children[tmp[1]].children;
				for(var i=0;i<score.length;i++){
					if(i==tmp[2]){
						continue;
					}
					if(score[i].isHit){
						final = true;
						break;
					}
				}
				
			}
			this.data.children[tmp[1]].isHit = final;
		},
		formatData(data,index,index2){
			if(this.level.length==index){//底层,叶子节点，规则项 
				var status = 1;
				var tmpList = this.smartList;
				if(tmpList.length==0){
					if(data.isHit == true){
						status = 2;
					}
				}else{
					for(let tmp in tmpList){
						if(data.id == tmpList[tmp].ruleItemId && data.ruleId == tmpList[tmp].ruleId){
							data["sRet"] = tmpList[tmp];
							//data["itemStyle"] = {color : '#8AC007'};
							//data["lineStyle"] = {color:"red"};
							data["qstrId"] = tmpList[tmp].id;
							//if(tmpList[tmp]["isHit"] != undefined){
							//	data["isHit"] = tmpList[tmp]["isHit"];
							//}
							tmpList[tmp]["indexs"] = index2;
							data["indexs"] = index2;
							if(tmpList[tmp].finalResult && tmpList[tmp].finalResult == '1'){
								status = 2;
							}else if(tmpList[tmp].finalResult && tmpList[tmp].finalResult == '2'){
								status = 1;
							}else {
								status = 2;
							}
							break;
						}
						data["lineStyle"] = {color:"green"};
					}
				}
				data["indexs"] = index2
				if(data.status == 1 ){
					data["lineStyle"] = {color:"green"};
				}else{
					data["lineStyle"] = {color:"gray"};
				}
				if(status == 2){
					data["itemStyle"] = {color : '#8AC007'};
					data["lineStyle"] = {color:"red"};
					status = 2;
				}
				//data["name"] = data["ruleName"];
				var label = {};
				if(data.ruleClassification=='1'){
					label["borderColor"] = "#5ceb9c";
				}else if(data.ruleClassification=='2'){
					label["borderColor"] = "#409EFF";
				}else if(data.ruleClassification=='3'){
					label["borderColor"] = "#e2f594";
				}
				label["borderType"] = 'solid';
				label["borderWidth"] = '3';
				label["backgroundColor"] = status ==2?"red":"white";
				data["label"] = label;
				return status;
			}
			if(data.children == undefined){
				return ;
			}
			let tmp = data.children;
			if(!tmp || tmp.length ==0){
				data["children"] = [];
			}
			if(this.level[index].nkey == ''){
				data["name"] = "规则组"+"-" +(data["ruleGroupMode"]==1?"全部":"任一");
			}else{
				//data["name"] = data[this.level[index].nkey];
			}
			for(var obj in tmp){
				var loac = data.parentId?data.parentId:"";
				tmp[obj]["parentId"] = loac +","+data.id;
				var restlt = this.formatData(tmp[obj],index+1,index2+"-"+obj);
				//下层命中，添加样式
				if(restlt==2){
					//if(index ==1){
						tmp.unshift(tmp[obj]);
						tmp.splice(parseInt(obj)+1,1);
					//}
					data["lineStyle"] = {color:"red"};		
				}else if(restlt==1){
					data["lineStyle"] = {color:"green"};
				}
			}
			data["children"] = tmp;
			if(data.status == 1){
				data["lineStyle"] = {color:"green"};
			}
			if(data.isHit){
				//data["itemStyle"] = {color : '#8AC007'};
				data["lineStyle"] = {color:"red"};
				return 2;
			}
		},
		formatTip(params){
			if(params.data.scoringTemplateType){
				var str = "<div style='text-align: left;'><span>"+params.data.name+"</span>";
				if(params.data.basicScore){
					str += "</br><span>基础得分:"+params.data.basicScore+"</span>";
				}
				if(params.data.highestScore){
					str += "</br><span>最高得分:"+params.data.highestScore+"</span>";
				}
				str += "</br><span>最低得分:"+(params.data.lowestScore?params.data.lowestScore:0)+"</span>";
				if(params.data.macInspectScore){
					str += "</br><span>机检得分:"+params.data.macInspectScore+"</span>";
				}
				return str+"</div>";
			}else if(params.data.children){//非叶子节点
				var str = "<div style='text-align: left;'><span>"+params.data.name+"</span>";
				if(params.data.hitRuleScore){
					str += "</br><span>命中评分项得分:"+params.data.hitRuleScore+"</span>";
				}
				return str+"</div>";
			}else{
				var str = "<div style='text-align: left;'><span>"+params.data.name+"</span>";
				if(params.data.sRet){
					if(params.data.sRet.smartResult){
						str += "</br><span>机检结果:"+this.formatHitRet(params.data.sRet.smartResult)+"  机检时间:"+params.data.sRet.createTime+"</span>";
						if(params.data.sRet.smartResultDetail && params.data.sRet.smartResultDetail !='' && params.data.sRet.smartResultDetail != '{}'){
							str += "</br><span>智能规则命中语句</span>";
							var tmp = JSON.parse(params.data.sRet.smartResultDetail);
							for(var i in tmp){
								str += "</br><span>"+tmp[i]+"</span>";
							}
						}
						if(params.data.sRet.semanticsResult && params.data.sRet.semanticsResult !='' && params.data.sRet.semanticsResult != '{}'){
							str += "</br><span>大模型结果：";
							var tmp = JSON.parse(params.data.sRet.semanticsResult);
							for(var i in tmp){
								str += tmp[i];
							}
							str += "</span>";
						}
					}else{
					}
					//str += "</br><span>首次复检时间:"+params.data.sRet.firstRecheckTime+"</span>";
					if(params.data.sRet.recheckResult && params.data.sRet.recheckResult != ''){
						str += "</br><span>复检结果:"+params.data.sRet.recheckResult+"</span>";
					}
					if(params.data.sRet.finalResult && params.data.sRet.finalResult != ''){
						str += "</br><span>最终结果:"+this.formatHitRet(params.data.sRet.finalResult)+"</span>";
					}
				}
				return str+"</div>";
			}
		},
		formatHitRet(val){
			if(val == null || val == ''){
				return "无结果";
			}else if(val == 1 ){
				return "命中";
			}else if(val == 2 ){
				return "未命中";
			}
		},
		submitForm(){
			if(Object.keys(this.updObj).length === 0){
				this.$message.warning('模板信息未变更');
				return;
			}
			var ret = this.formatSubmitResult();
			if(this._props.viewParam && this._props.viewParam.qcType == 1){
				this.subimting = true;
				addManualResult(ret).then(response => {
					this.$message({message: "保存成功",type: 'success'});
					this.$emit('callBack',JSON.parse(JSON.stringify(this.hitItems)));
					this.getTreeData();
		      	}).finally(v=>{
					this.submiting = false;
					this.updObj = [];
				});
			}else if(this._props.viewParam && this._props.viewParam.qcType == 2 || !this._props.viewParam.qcType){
				this.subimting = true;
				updateSmart(ret).then(response => {
					this.$message({message: "保存成功",type: 'success'});
					this.$emit('callBack',JSON.parse(JSON.stringify(this.hitItems)));
					this.getTreeData();
		      	}).finally(v=>{
					this.submiting = false;
					this.updObj = [];
				});
			}
		},
		cancel(){
			this.open=false;
			this.smartResult = [];
			this.currNode = {sRet:{}};
		},
		viewRule(){
			var target = "";
			var tmp = {ruleId:this.currNode.ruleId}
			switch(this.currNode.ruleClassification){
				case "1":
					target="smart";
					tmp = {ruleId:this.currNode.ruleId}
					break;
				case "2":
					target="man";
					tmp = {id:this.currNode.ruleId}
					break;
				case "3":
					target="flow";
					tmp = {ruleId:this.currNode.ruleId}
					break;
				default:
					break;
			}
			this.visit[target] = true;
			this.$nextTick(()=>{
				this.$refs[target+"View"].reload(tmp,'view');
			})
		},
		goList(){
			this.visit.flow = false;
			this.visit.smart = false;
			this.visit.man = false;
		},
		/**
			获取单个Item的最终得分
		*/
		getItemScore(item,itemIdx1){
			var currScore = item.hitRuleScore;
			var gArr = item.children;
			for(var i in gArr){
				let gObj = gArr[i];
				if(gObj.isHit == true){
					for(var j in gObj.children){
						if(gObj.children[j].sRet){//存在结果信息
							if(gObj.children[j].sRet.finalResult){//复检数据
								if(gObj.children[j].sRet.finalResult == '1'){//命中
									currScore = this.getFinalScore(currScore,gObj.children[j].sRet.recheckResult);
								}
							}else{//机检数据
								if(gObj.children[j].sRet.semanticsResult && Object.keys(gObj.children[j].sRet.semanticsResult).length > 0){//大模型结果
									currScore = this.getFinalScore(currScore,gObj.children[j].sRet.semanticsResult);
								}
							}
						}
					}
				}
			}
			return currScore;
		},
		getFinalScore(currScore,orgResult){
			for(var i in this.sematicList){
				if(orgResult == this.sematicList[i].dictLabel){
					var tmps = parseInt(this.sematicList[i].dictValue);
					currScore = currScore>tmps?tmps:currScore;
					if(currScore == this.minScore){
						break;
					}
				}
			}
			return currScore;
		},
		getLowerScore(){
			for(var i in this.sematicList){
				if(this.minScore > parseInt(this.sematicList[i].dictValue)){
					this.minScore = parseInt(this.sematicList[i].dictValue);
				}
			}
		},
		removeNodeInfo(indexs){
			for(var i in this.smartList){
				if(indexs == this.smartList[i].indexs){
					this.smartList.splice(i, 1);
					break;
				}
			}
		},
		handlerRecheck(){
			this.currNode.sRet['finalResult'] = this.getFinalScore(10000,this.currNode.sRet.recheckResult)==0?'2':'1';
		},
		getRecordList(){
			if(this.record.list.length>0){
				return;
			}
			recordList({},{}).then(response=>{
				this.record.list = response.rows;
			});
		},
		updateNodeIndex(data,index){
			if(data.children){
				for(var key in data.children){
					this.updateNodeIndex(data.children[key],index+"-"+key);
				}
			}
			data['indexs'] = index;
		}
	}
};
</script>

<style rel="stylesheet/scss" lang="scss">
.no-header-dialog .el-dialog__header {
	display: none;
}
.no-header-dialog .el-dialog__body {
	padding:10px 10px;
}
.template-header{
	//position: absolute;
    //width: 95%;
    z-index: 10;
    padding: 0px 10px 0px 10px;
    background-color: white;
}
</style>
