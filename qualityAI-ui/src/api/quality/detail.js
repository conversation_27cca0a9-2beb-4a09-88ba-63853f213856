import request from '@/utils/request'

// 查询人工复检列表
export function listReview(query) {
  return request({
    url: '/qc/review/selectReviewData',
    method: 'get',
    params: query
  })
}

// 查询人工复检详细
export function getReview(id) {
  return request({
    url: '/qc/review/' + id,
    method: 'get'
  })
}

// 新增人工复检
export function addData(data) {
  return request({
    url: '/qc/qcBICheck',
    method: 'post',
    data: data
  })
}

// 修改人工复检
export function updateReview(data) {
  return request({
    url: '/qc/review',
    method: 'put',
    data: data
  })
}

// 删除人工复检
export function delReview(id) {
  return request({
    url: '/qc/review/' + id,
    method: 'delete'
  })
}

// 录音详情、评测详情
export function reviewInfo(bizNo,type,manualDetailId) {//type1为人工质检获取人工质检的模版Id和manualtaskdetailid,2为人工复检，取smarttaskdetailid
  return request({
    url: '/qc/bizItem/byNo/' + bizNo+ "/"+type+"/"+manualDetailId,
    method: 'get'
  })
}
export function tempFirstList(id) {
  return request({
    url: '/qc/qcBICheck/tempFirstList/' + id,
    method: 'get'
  })
}
export function tempAIList(bizNo) {
  return request({
    url: '/qc/qcBICheck/tempAIList/' + bizNo,
    method: 'get'
  })
}

// 质检结果
export function aiResList(query) {
  return request({
    url: '/qc/review/selectAiResultData',
    method: 'get',
    params: query
  })
}

// 模板列表
export function tempList(query) {
  return request({
    url: '/qc/review/selectAiTemplateData',
    method: 'get',
    params: query
  })
}


// 录音地址和文本
export function soundInfo(bizNo) {
  return request({
    url: '/qc/qcBICheck/getVideoAndText/'+ bizNo,
    method: 'get'
  })
}
//复检详情
export function reviewDetail(bizNo) {
  return request({
    url: '/qc/qcBICheck/getResultListByBizNo/'+bizNo,
    method: 'get'
  })
}
//获取itemId和name
export function getQcScoringHitRuleGroup(groupId,bizType,ruleId,ruleItemId,taskDetailId) {
  return request({
    url: '/qc/qcBICheck/getQcScoringHitRuleGroup/'+groupId+'/'+bizType+'/'+ruleId+'/'+ruleItemId+'/'+taskDetailId,
    method: 'get'
  })
}
export function getManualResult(bizNo) {
  return request({
    url: '/qc/qcBICheck/getManualResult/'+bizNo,
    method: 'get'
  })
}
export function getSmartCheckList(bizNo) {
  return request({
    url: '/qc/qcBICheck/getSmartCheckList/'+bizNo,
    method: 'get'
  })
}
export function getCaseArray() {
  return request({
    url: '/qc/case/classification/list',
    method: 'post',
    data: {}
  })
}
export function getCaseList(param) {
  return request({
    url: '/qc/case/listCount',
    method: 'post',
    data: param
  })
}
// 新增案例标记
export function saveCase(param) {
  return request({
    url: '/qc/case/add',
    method: 'post',
    data: param
  })
}
export function addManualResult(array) {
  return request({
    url: '/qc/qcBICheck/addManualResult',
    method: 'post',
    data: array
  })
}
// 新增人工复检规则结果
export function addAIResult(array) {
  return request({
    url: '/qc/qcBICheck/addAIResult',
    method: 'post',
    data: array
  })
}
export function getListenList(query) {
  return request({
    url: '/qc/listenLog/allList',
    method: 'get',
    params: query
  })
}
// 新增人工质检规则结果
export function addListen(data) {
  return request({
    url: '/qc/listenLog',
    method: 'post',
    data: data
  })
}
export function updateZjJh(id) {
  return request({
    url: '/qc/manual/task/status/'+id,
    method: 'get'
  })
}
export function manualResultDetail(manualDetailId) {
  return request({
    url: '/qc/qcBICheck/getResultByDetailId/'+manualDetailId,
    method: 'get'
  })
}
