<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="智能规则" name="first">
        <smart-rule></smart-rule>
      </el-tab-pane>
      <el-tab-pane label="人工规则" name="second">
        <man-rule></man-rule>
      </el-tab-pane>
      <el-tab-pane label="流程规则" name="third">
		<flow-rule></flow-rule>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import smartRule from './smart/index.vue'
import manRule from './manRule.vue'
import flowRule from './flow/index.vue'
export default {
  components: {
    smartRule,
    manRule,
	flowRule
  },
  name: "QualityRule",
  data() {
    return {
       activeName: 'first'
    };
  },
  created() {
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
   
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.el-dialog-title.dy-title{
	font-size:large;
	font-weight:900;
	width:300px;
	float:left;
}
.el-divider--horizontal.dy-divider{
	margin-top:40px;
}
.i-or-div{
	width: 100%;
    display: flex;
    justify-content: center;
}
.i-or{
	border-radius: 50%;
    width: 30px;
    height: 30px;
    background-color: #3350eb;
    color: white;
    text-align: center;
    font-style: normal;
	margin-bottom: 10px;
}
.ruleTop.row.spaceBet div,.ruleTop.row.spaceBet i{
	font-weight:800;
	color:#3350eb;
}
.ruleTop.row.spaceBet{
	background-color:#5b94dd;
}
</style>
