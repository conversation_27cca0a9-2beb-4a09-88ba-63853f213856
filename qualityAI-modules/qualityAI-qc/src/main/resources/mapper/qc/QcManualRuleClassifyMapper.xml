<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcManualRuleClassifyMapper">

    <resultMap type="QcManualRuleClassify" id="QcManualRuleClassifyResult">
        <result property="classifyId"    column="classify_id"    />
        <result property="classifyName"    column="classify_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectQcManualRuleClassifyVo">
        select classify_id, classify_name, create_by, create_time, update_by, update_time, tenant_id, remark from qc_manual_rule_classify
    </sql>

    <select id="selectQcManualRuleClassifyList" parameterType="QcManualRuleClassify" resultMap="QcManualRuleClassifyResult">
        <include refid="selectQcManualRuleClassifyVo"/>
        <where>
            <if test="classifyName != null "> and classify_name like concat('%', #{classifyName}, '%')</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
        </where>
    </select>

    <select id="selectQcManualRuleClassifyByClassifyId" parameterType="Long" resultMap="QcManualRuleClassifyResult">
        <include refid="selectQcManualRuleClassifyVo"/>
        where classify_id = #{classifyId}
    </select>

    <insert id="insertQcManualRuleClassify" parameterType="QcManualRuleClassify" useGeneratedKeys="true" keyProperty="classifyId">
        insert into qc_manual_rule_classify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classifyName != null">classify_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classifyName != null">#{classifyName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateQcManualRuleClassify" parameterType="QcManualRuleClassify">
        update qc_manual_rule_classify
        <trim prefix="SET" suffixOverrides=",">
            <if test="classifyName != null">classify_name = #{classifyName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where classify_id = #{classifyId}
    </update>

    <delete id="deleteQcManualRuleClassifyByClassifyId" parameterType="Long">
        delete from qc_manual_rule_classify where classify_id = #{classifyId}
    </delete>

    <delete id="deleteQcManualRuleClassifyByClassifyIds" parameterType="String">
        delete from qc_manual_rule_classify where classify_id in
        <foreach item="classifyId" collection="array" open="(" separator="," close=")">
            #{classifyId}
        </foreach>
    </delete>
</mapper>