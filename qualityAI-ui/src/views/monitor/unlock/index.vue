<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="用户工号" prop="workNo">
        <el-input v-model="queryParams.workNo" placeholder="请输入用户工号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="登录名称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入登录名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户姓名" prop="nickName">
        <el-input v-model="queryParams.nickName" placeholder="请输入用户姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>

    </el-form>
    <el-table v-loading="loading" :data="list" style="width: 100%;">
      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户工号" align="center" prop="workNo" :show-overflow-tooltip="true" />
      <el-table-column label="登录名称" align="center" prop="userName" :show-overflow-tooltip="true" />
      <el-table-column label="用户姓名" align="center" prop="nickName" :show-overflow-tooltip="true" />
      <el-table-column label="锁定时间" align="center" prop="lockTime">
        <template slot-scope="scope">
          <span>{{ scope.row.lockTime }}秒</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-unlock" @click="handleUnLock(scope.row)">解锁</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { getUserUnlockList, unlockUser } from "@/api/monitor/unlock";

export default {
  name: "Unlock",
  data() {
    return {
      loading: true,
      total: 0,
      list: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        workNo: '',
        userName: '',
        lockTime: ''
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      const queryParams = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize
      };
      const payload = {};
      getUserUnlockList(queryParams, payload).then(response => {
        this.list = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleUnLock(row) {
      this.$modal.confirm('是否确认解锁登录名称为"' + row.userName + '"的用户？').then(function () {
        return unlockUser(row.userName);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("解锁成功");
      }).catch(() => { });
    }
  }
};
</script>
