package com.ideal.qc.service.impl;

import com.ideal.common.core.constant.UserConstants;
import com.ideal.common.core.utils.StringUtils;
import com.ideal.qc.domain.Question;
import com.ideal.qc.domain.QuestionAnswer;
import com.ideal.qc.mapper.QuestionMapper;
import com.ideal.qc.service.IQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 题目信息 服务层处理
 * 
 * <AUTHOR>
@Service
public class QuestionServiceImpl implements IQuestionService
{
    @Autowired
    private QuestionMapper questionMapper;

    /**
     * 查询题目信息集合
     * 
     * @param question 题目信息
     * @return 题目信息集合
     */
    @Override
    public List<Question> selectQusList(Question question)
    {
        return questionMapper.selectQusList(question);
    }

    /**
     * 查询所有题目
     * 
     * @return 题目列表
     */
    @Override
    public List<Question> selectQusAll()
    {
        return questionMapper.selectQusAll();
    }

    /**
     * 通过题目ID查询题目信息
     * 
     * @param questionId 题目ID
     * @return 角色对象信息
     */
    @Override
    public Question selectQusById(Long questionId)
    {
        return questionMapper.selectQusById(questionId);
    }

    /**
     * 校验题目名称是否唯一
     * 
     * @param question 题目信息
     * @return 结果
     */
    @Override
    public boolean checkQusNameUnique(Question question)
    {
        Long questionId = StringUtils.isNull(question.getId()) ? -1L : question.getId();
        Question info = questionMapper.checkQusNameUnique(question.getQusName());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != questionId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验题目编码是否唯一
     * 
     * @param question 题目信息
     * @return 结果
     */
    @Override
    public boolean checkQusCodeUnique(Question question)
    {
        Long questionId = StringUtils.isNull(question.getId()) ? -1L : question.getId();
        Question info = questionMapper.checkQusCodeUnique(question.getQusNo());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != questionId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 删除题目信息
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    @Override
    public int deleteQusById(Long questionId)
    {
        return questionMapper.deleteQusById(questionId);
    }

    /**
     * 批量删除题目信息
     * 
     * @param questionIds 需要删除的题目ID
     * @return 结果
     */
    @Override
    public int deleteQusByIds(Long[] questionIds)
    {
        for (Long questionId : questionIds)
        {
            Question question = selectQusById(questionId);
        }
        return questionMapper.deleteQusByIds(questionIds);
    }

    /**
     * 新增保存题目信息
     * 
     * @param question 题目信息
     * @return 结果
     */
    @Override
    public int insertQus(Question question)
    {
        return questionMapper.insertQus(question);
    }

    /**
     * 修改保存题目信息
     * 
     * @param question 题目信息
     * @return 结果
     */
    @Override
    public int updateQus(Question question)
    {
        return questionMapper.updateQus(question);
    }

    @Override
    public void insertQusAnswer(QuestionAnswer qa) {
        questionMapper.insertQusAnswer(qa);
    }

    @Override
    public void delQusAnswer(Long id) {
        questionMapper.delQusAnswer(id);
    }
}
