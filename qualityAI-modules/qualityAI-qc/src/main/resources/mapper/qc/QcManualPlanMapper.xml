<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcManualPlanMapper">


    <resultMap type="QcManualPlan" id="QcManualPlanResult">
        <result property="id"    column="id"    />
        <result property="planName"    column="plan_name"    />
        <result property="planType"    column="plan_type"    />
        <result property="manualType"    column="manual_type"    />
        <result property="templateId"    column="template_id"    />
        <result property="timingPlanFlag"    column="timing_plan_flag"    />
        <result property="dataRange"    column="data_range"    />
        <result property="smartPlan"    column="smart_plan"    />
        <result property="extractMethod"    column="extract_method"    />
        <result property="extractType"    column="extract_type"    />
        <result property="extractValue"    column="extract_value"    />
        <result property="hitRules"    column="hit_rules"    />
        <result property="minDuration"    column="min_duration"    />
        <result property="maxDuration"    column="max_duration"    />
        <result property="weekDays"    column="week_days"    />
        <result property="completeTime"    column="complete_time"    />
        <result property="manualCompleteTime"    column="manual_complete_time"    />
        <result property="inspectorRate"    column="inspector_rate"    />
        <result property="dataCount"    column="data_count"    />
        <result property="seatCount"    column="seat_count"    />
        <result property="status"    column="status"    />
        <result property="followMatchDetail"    column="follow_match_detail"    />
        <result property="datasetId"    column="dataset_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectQcManualPlanVo">
        select id, plan_name, plan_type, manual_type, template_id, timing_plan_flag, data_range, smart_plan,
         extract_method, extract_type, extract_value, hit_rules, min_duration, max_duration, week_days,
         complete_time, manual_complete_time, inspector_rate, data_count, seat_count, status,
         follow_match_detail, dataset_id, create_by, create_time, update_by, update_time, remark from qc_manual_plan
    </sql>

    <select id="selectQcManualPlanList"  resultMap="QcManualPlanResult">
        <include refid="selectQcManualPlanVo"/>
        <where>
            <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
            <if test="planType != null  and planType != ''"> and plan_type = #{planType}</if>
            <if test="timingPlanFlag != null  and timingPlanFlag != ''"> and timing_plan_flag = #{timingPlanFlag}</if>
            <if test="templateId != null  and templateId != ''"> and template_id = #{templateId}</if>
            <if test="dataRange != null  and dataRange != ''"> and data_range = #{dataRange}</if>
            <if test="smartPlan != null "> and smart_plan = #{smartPlan}</if>
            <if test="extractMethod != null  and extractMethod != ''"> and extract_method = #{extractMethod}</if>
            <if test="extractType != null  and extractType != ''"> and extract_type = #{extractType}</if>
            <if test="extractValue != null "> and extract_value = #{extractValue}</if>
            <if test="hitRules != null  and hitRules != ''"> and hit_rules = #{hitRules}</if>
            <if test="minDuration != null "> and min_duration = #{minDuration}</if>
            <if test="maxDuration != null "> and max_duration = #{maxDuration}</if>
            <if test="weekDays != null  and weekDays != ''"> and week_days = #{weekDays}</if>
            <if test="completeTime != null  and completeTime != ''"> and complete_time = #{completeTime}</if>
            <if test="manualCompleteTime != null "> and manual_complete_time = #{manualCompleteTime}</if>
            <if test="inspectorRate != null  and inspectorRate != ''"> and inspector_rate = #{inspectorRate}</if>
            <if test="dataCount != null "> and data_count = #{dataCount}</if>
            <if test="seatCount != null "> and seat_count = #{seatCount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="followMatchDetail != null  and followMatchDetail != ''"> and follow_match_detail = #{followMatchDetail}</if>
            <if test="datasetId != null "> and dataset_id = #{datasetId}</if>
            <if test="beginTime != null and beginTime != ''"> and create_time &gt;= #{beginTime}</if>
            <if test="endTime != null and endTime != ''"> and create_time &lt;= #{endTime}</if>
        </where>
            order by  create_time desc
    </select>

    <!--查询所有定时计划-->
    <select id="selectQcManualTimingPlan"  resultMap="QcManualPlanResult">
        <include refid="selectQcManualPlanVo"/>
        where status = '1'
    </select>

    <select id="selectQcManualPlanById" parameterType="Long" resultMap="QcManualPlanResult">
        <include refid="selectQcManualPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcManualPlan" parameterType="QcManualPlan" useGeneratedKeys="true" keyProperty="id">
        insert into qc_manual_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planName != null">plan_name,</if>
            <if test="planType != null">plan_type,</if>
            <if test="manualType != null">manual_type,</if>
            <if test="templateId != null">template_id,</if>
            <if test="timingPlanFlag != null">timing_plan_flag,</if>
            <if test="dataRange != null">data_range,</if>
            <if test="smartPlan != null">smart_plan,</if>
            <if test="extractMethod != null">extract_method,</if>
            <if test="extractType != null">extract_type,</if>
            <if test="extractValue != null">extract_value,</if>
            <if test="hitRules != null">hit_rules,</if>
            <if test="minDuration != null">min_duration,</if>
            <if test="maxDuration != null">max_duration,</if>
            <if test="weekDays != null">week_days,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="manualCompleteTime != null">manual_complete_time,</if>
            <if test="inspectorRate != null">inspector_rate,</if>
            <if test="dataCount != null">data_count,</if>
            <if test="seatCount != null">seat_count,</if>
            <if test="status != null">status,</if>
            <if test="followMatchDetail != null">follow_match_detail,</if>
            <if test="datasetId != null">dataset_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planName != null">#{planName},</if>
            <if test="planType != null">#{planType},</if>
            <if test="manualType != null">#{manualType},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="timingPlanFlag != null">#{timingPlanFlag},</if>
            <if test="dataRange != null">#{dataRange},</if>
            <if test="smartPlan != null">#{smartPlan},</if>
            <if test="extractMethod != null">#{extractMethod},</if>
            <if test="extractType != null">#{extractType},</if>
            <if test="extractValue != null">#{extractValue},</if>
            <if test="hitRules != null">#{hitRules},</if>
            <if test="minDuration != null">#{minDuration},</if>
            <if test="maxDuration != null">#{maxDuration},</if>
            <if test="weekDays != null">#{weekDays},</if>
            <if test="completeTime != null">#{completeTime},</if>
            <if test="manualCompleteTime != null">#{manualCompleteTime},</if>
            <if test="inspectorRate != null">#{inspectorRate},</if>
            <if test="dataCount != null">#{dataCount},</if>
            <if test="seatCount != null">#{seatCount},</if>
            <if test="status != null">#{status},</if>
            <if test="followMatchDetail != null">#{followMatchDetail},</if>
            <if test="datasetId != null">#{datasetId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateQcManualPlan" parameterType="QcManualPlan">
        update qc_manual_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="planType != null">plan_type = #{planType},</if>
            <if test="manualType != null">manual_type = #{manualType},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="timingPlanFlag != null">timing_plan_flag = #{timingPlanFlag},</if>
            <if test="dataRange != null">data_range = #{dataRange},</if>
            <if test="smartPlan != null">smart_plan = #{smartPlan},</if>
            <if test="extractMethod != null">extract_method = #{extractMethod},</if>
            <if test="extractType != null">extract_type = #{extractType},</if>
            <if test="extractValue != null">extract_value = #{extractValue},</if>
            <if test="hitRules != null">hit_rules = #{hitRules},</if>
            <if test="minDuration != null">min_duration = #{minDuration},</if>
            <if test="maxDuration != null">max_duration = #{maxDuration},</if>
            <if test="weekDays != null">week_days = #{weekDays},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="manualCompleteTime != null">manual_complete_time = #{manualCompleteTime},</if>
            <if test="inspectorRate != null">inspector_rate = #{inspectorRate},</if>
            <if test="dataCount != null">data_count = #{dataCount},</if>
            <if test="seatCount != null">seat_count = #{seatCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="followMatchDetail != null">follow_match_detail = #{followMatchDetail},</if>
            <if test="datasetId != null">dataset_id = #{datasetId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateQcManualPlanDetail" parameterType="QcManualPlan">
        update qc_manual_plan
        SET
            plan_name = #{planName},
            template_id = #{templateId},
            data_range = #{dataRange},
            extract_type = #{extractType},
            extract_value = #{extractValue},
            smart_plan = #{smartPlan},
            hit_rules = #{hitRules},
            week_days = #{weekDays},
            inspector_rate = #{inspectorRate},
            update_time = #{updateTime},
            update_by = #{updateBy},
            status = #{status}
        where id = #{id}
    </update>

    <delete id="deleteQcManualPlanById" parameterType="Long">
        delete from qc_manual_plan where id = #{id}
    </delete>

    <delete id="deleteQcManualPlanByIds" parameterType="String">
        delete from qc_manual_plan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>