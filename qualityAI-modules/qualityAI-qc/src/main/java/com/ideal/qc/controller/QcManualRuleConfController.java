package com.ideal.qc.controller;

import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcManualRuleConf;
import com.ideal.qc.mapper.QcManualRuleConfMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/manual/conf")
public class QcManualRuleConfController extends BaseController {

    @Autowired
    private QcManualRuleConfMapper qcManualRuleConfMapper;

    /**
     * 查询人工规则配置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> params)
    {
        startPage();
        List<QcManualRuleConf> list = qcManualRuleConfMapper.selectQcManualRuleConfList(params);
        return getDataTable(list);
    }

    /**
     * 查询所有人工规则配置列表
     */
    @GetMapping("/getAll")
    public AjaxResult getAll()
    {
        List<QcManualRuleConf> list = qcManualRuleConfMapper.selectAll();
        return success(list);
    }


    /**
     * 下载导入模板
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<QcManualRuleConf> util = new ExcelUtil<QcManualRuleConf>(QcManualRuleConf.class);
        util.importTemplateExcel(response,"人工规则模板");
    }

    /**
     * 导入数据
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSuppor,@RequestParam("classifyId")Integer classifyId) throws Exception
    {
        try {
            ExcelUtil<QcManualRuleConf> util = new ExcelUtil<QcManualRuleConf>(QcManualRuleConf.class);
            List<QcManualRuleConf> list = util.importExcel(file.getInputStream());

            for (QcManualRuleConf qcManualRuleConf : list) {
                qcManualRuleConf.setClassifyId(classifyId);
                qcManualRuleConf.setStatus(1);
                qcManualRuleConf.setCreateBy(SecurityUtils.getUsername());
                qcManualRuleConf.setCreateTime(new Date());
                qcManualRuleConf.setUpdateBy(SecurityUtils.getUsername());
                qcManualRuleConf.setUpdateTime(new Date());
            }
            if (null == list || list.size() == 0){
                return AjaxResult.error("数据为空，请检查后重新导入");
            }
            qcManualRuleConfMapper.batchInsert(list);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }


    /**
     * 导出人工规则配置列表
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam Map<String, Object> params)
    {
//        List<QcManualRuleConf> list = qcManualRuleConfMapper.selectQcManualRuleConfList(params);
        List<QcManualRuleConf> list = qcManualRuleConfMapper.selectQcManualRuleConfList(new HashMap<>());
        for (QcManualRuleConf manualRuleConf : list) {
            manualRuleConf.setRuleGroup("demo");
        }
        ExcelUtil<QcManualRuleConf> util = new ExcelUtil<QcManualRuleConf>(QcManualRuleConf.class);
        util.exportExcel(response, list, "人工规则配置数据");
    }

    /**
     * 获取人工规则配置详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(qcManualRuleConfMapper.selectQcManualRuleConfById(id));
    }

    /**
     * 新增人工规则配置
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody QcManualRuleConf qcManualRuleConf)
    {
        qcManualRuleConf.setStatus(1);
        qcManualRuleConf.setCreateBy(SecurityUtils.getUsername());
        qcManualRuleConf.setCreateTime(new Date());
        qcManualRuleConf.setUpdateBy(SecurityUtils.getUsername());
        qcManualRuleConf.setUpdateTime(new Date());
        return toAjax(qcManualRuleConfMapper.insertQcManualRuleConf(qcManualRuleConf));
    }

    /**
     * 修改人工规则配置
     */
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody QcManualRuleConf qcManualRuleConf)
    {
        qcManualRuleConf.setUpdateBy(SecurityUtils.getUsername());
        qcManualRuleConf.setUpdateTime(new Date());
        return toAjax(qcManualRuleConfMapper.updateQcManualRuleConf(qcManualRuleConf));
    }


    /**
     * 删除人工规则配置
     */
    @GetMapping("/del/{id}")
    public AjaxResult removeById(@PathVariable Long id)
    {
        return toAjax(qcManualRuleConfMapper.deleteQcManualRuleConfById(id));
    }


    /**
     * 删除人工规则配置
     */
    @GetMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(qcManualRuleConfMapper.deleteQcManualRuleConfByIds(ids));
    }


}
