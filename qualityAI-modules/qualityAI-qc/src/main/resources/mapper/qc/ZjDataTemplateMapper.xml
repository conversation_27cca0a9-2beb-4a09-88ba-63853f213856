<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ZjDataTemplateMapper">

    <resultMap type="com.ideal.qc.domain.ZjDataTemplate" id="zjDataTemplateResult">
        <result property="id" column="id" />
        <result property="dataId" column="data_id"    />
        <result property="zjStatus" column="zj_status"    />
        <result property="zjTemplateId" column="zj_template_id"    />
        <result property="createBy" column="create_by"    />
        <result property="createTime" column="create_time"    />
        <result property="aiCheckTime" column="ai_check_time"    />
        <result property="replayCheckBy" column="replay_check_by"    />
        <result property="replayCheckTime" column="replay_check_time"    />
        <result property="zjTaskId" column="zj_task_id"    />
    </resultMap>

    <sql id="selectZjDataTemplateVo">
        select
        id,
        data_id,
        zj_status,
        zj_template_id,
        create_by,
        create_time,
        ai_check_time,
        replay_check_by,
        replay_check_time,
        zj_task_id
        from zj_data_template
    </sql>

    <select id="selectZjDataTemplateList" parameterType="com.ideal.qc.domain.ZjDataTemplate" resultMap="zjDataTemplateResult">
        <include refid="selectZjDataTemplateVo"/>
        <where>
            <if test="zjTemplateId != null  and zjTemplateId != ''"> and zj_template_id = #{zjTemplateId}</if>
            <if test="zjTaskId != null  and zjTaskId != ''"> and zj_task_id = #{zjTaskId}</if>
        </where>
    </select>

    <select id="selectZjDataTemplateByTaskId" parameterType="Long" resultMap="zjDataTemplateResult">
        <include refid="selectZjDataTemplateVo"/>
        where task_id = #{TaskId}
    </select>

    <insert id="insertZjDataTemplate" parameterType="com.ideal.qc.domain.ZjDataTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into zj_data_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null ">task_name,</if>
            <if test="templateId != null ">template_id,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateTime != null ">update_time,</if>
            <if test="priority != null ">priority,</if>
            <if test="executeStatus != null ">execute_status,</if>
            <if test="taskCompletion != null ">task_completion,</if>
            <if test="createBy != null ">create_by,</if>
            <if test="updateBy != null ">update_by,</if>
            <if test="taskStatus != null ">task_status,</if>
            <if test="tenantCode != null ">tenant_code,</if>
            <if test="extractionType != null ">extraction_type,</if>
            <if test="organization != null ">organization,</if>
            <if test="businessType != null ">business_type,</if>
            <if test="taskType != null ">task_type,</if>
            <if test="params.beginTime != null ">session_start_time,</if>
            <if test="params.endTime  != null ">session_end_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null ">#{taskName},</if>
            <if test="templateId != null ">#{templateId},</if>
            <if test="createTime != null ">date_format(#{createTime},'%Y-%m-%d %H:%s:%i'),</if>
            <if test="updateTime != null ">#{updateTime},</if>
            <if test="priority != null ">#{priority},</if>
            <if test="executeStatus != null ">#{executeStatus},</if>
            <if test="taskCompletion != null ">#{taskCompletion},</if>
            <if test="createBy != null ">#{createBy},</if>
            <if test="updateBy != null ">#{updateBy},</if>
            <if test="taskStatus != null ">#{taskStatus},</if>
            <if test="tenantCode != null ">#{tenantCode},</if>
            <if test="extractionType != null ">#{extractionType},</if>
            <if test="organization != null ">#{organization},</if>
            <if test="businessType != null ">#{businessType},</if>
            <if test="taskType != null ">#{taskType},</if>
            <if test="params.beginTime != null ">date_format(#{params.beginTime},'%Y-%m-%d'),</if>
            <if test="params.endTime  != null ">date_format(#{params.endTime},'%Y-%m-%d'),</if>
        </trim>
    </insert>

    <update id="updateZjDataTemplate" parameterType="com.ideal.qc.domain.ZjDataTemplate">
        update zj_data_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name=#{taskName},</if>
            <if test="templateId != null and templateId != ''">template_id=#{templateId},</if>
            <if test="createTime != null and createTime != ''">create_time=date_format(#{createTime},'%Y-%m-%d %H:%s:%i'),</if>
            <if test="updateTime != null and updateTime != ''">update_time=date_format(#{updateTime},'%Y-%m-%d %H:%s:%i'),</if>
            <if test="priority != null and priority != ''">priority=#{priority},</if>
            <if test="executeStatus != null and executeStatus != ''">execute_status=#{executeStatus},</if>
            <if test="taskCompletion != null and taskCompletion != ''">task_completion=#{taskCompletion},</if>
            <if test="createBy != null and createBy != ''">create_by=#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">update_by=#{updateBy},</if>
            <if test="taskStatus != null and taskStatus != ''">task_status=#{taskStatus},</if>
            <if test="tenantCode != null and tenantCode != ''">tenant_code=#{tenantCode},</if>
            <if test="extractionType != null and extractionType != ''">extraction_type=#{extractionType},</if>
            <if test="organization != null and organization != ''">organization=#{organization},</if>
            <if test="businessType != null and businessType != ''">business_type=#{businessType},</if>
            <if test="taskType != null and taskType != ''">task_type=#{taskType},</if>
            <if test="params.beginTime != null ">session_start_time=date_format(#{params.beginTime},'%Y-%m-%d'),</if>
            <if test="params.endTime  != null ">session_end_time=date_format(#{params.endTime},'%Y-%m-%d'),</if>
        </trim>
        where task_id = #{TaskId}
    </update>

    <delete id="deleteZjDataTemplateByTaskId" parameterType="Long">
        delete from zj_data_template where task_id = #{TaskId}
    </delete>

    <delete id="deleteZjDataTemplateByTaskIds" parameterType="String">
        delete from zj_data_template where task_id in
        <foreach item="TaskId" collection="array" open="(" separator="," close=")">
            #{TaskId}
        </foreach>
    </delete>

    <update id="updateZjDataTemplateStatus" parameterType="com.ideal.qc.domain.ZjDataTemplate">
        update zj_data_template
        set
            zj_status=#{zjStatus},
            ai_check_time=now()
        where data_id = #{dataId}
    </update>

</mapper>
