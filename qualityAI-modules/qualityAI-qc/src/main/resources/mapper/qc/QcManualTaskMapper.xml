<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcManualTaskMapper">

    <resultMap type="QcManualTask" id="QcManualTaskResult">
        <result property="id"    column="id"    />
        <result property="taskName"    column="task_name"    />
        <result property="taskType"    column="task_type"    />
        <result property="manualType"    column="manual_type"    />
        <result property="templateId"    column="template_id"    />
        <result property="dataRange"    column="data_range"    />
        <result property="extractType"    column="extract_type"    />
        <result property="extractValue"    column="extract_value"    />
        <result property="smartPlan"    column="smart_plan"    />
        <result property="hitRules"    column="hit_rules"    />
        <result property="sessionCount"    column="session_count"    />
        <result property="relSessionCount"    column="rel_session_count"    />
        <result property="hitRuleSessionCount"    column="hit_rule_session_count"    />
        <result property="sessionViolatePct"    column="session_violate_pct"    />
        <result property="deadlineTime"    column="deadline_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="enableStatus"    column="enable_status"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="planName"    column="plan_name"    />
        <result property="datasetId"    column="dataset_id"    />
        <result property="datasetId"    column="dataset_id"    />
        <result property="finishCount"    column="finish_count"    />
        <result property="inspectors"    column="inspectors"    />
        <result property="inspectorRate"    column="inspector_rate"    />
        <result property="realInspectors"    column="real_Inspectors"    />
    </resultMap>

    <sql id="selectQcManualTaskVo">
        select id, task_name, task_type, manual_type, template_id, data_range,extract_type,extract_value,
        smart_plan, hit_rules, session_count, finish_count,inspectors,inspector_rate,real_Inspectors,
        rel_session_count, hit_rule_session_count, session_violate_pct,
        deadline_time, create_time, create_by, update_time, update_by, remark,
        status, enable_status,tenant_id, plan_id, plan_name, dataset_id
        from qc_manual_task
    </sql>


    <select id="selectQcManualTaskList"  resultMap="QcManualTaskResult">
        <include refid="selectQcManualTaskVo"/>
        <where>
        <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
        <if test="sessionCountFlag"> and session_count > 0 </if>
        <if test="sessionCountFlag"> and status  != '2' </if>
        <if test="taskType != null  and taskType != ''"> and task_type = #{taskType}</if>
        <if test="manualType != null  and manualType != ''"> and manual_type = #{manualType}</if>
        <if test="templateId != null  and templateId != ''"> and template_id = #{templateId}</if>
        <if test="dataRange != null  and dataRange != ''"> and data_range = #{dataRange}</if>
        <if test="extractType != null  and extractType != ''"> and extract_type = #{extractType}</if>
        <if test="extractValue != null  and extractValue != ''"> and extract_value = #{extractValue}</if>
        <if test="smartPlan != null  and smartPlan != ''"> and smart_plan = #{smartPlan}</if>
        <if test="hitRules != null  and hitRules != ''"> and hit_rules = #{hitRules}</if>
        <if test="sessionCount != null "> and session_count = #{sessionCount}</if>
        <if test="relSessionCount != null "> and rel_session_count = #{relSessionCount}</if>
        <if test="hitRuleSessionCount != null "> and hit_rule_session_count = #{hitRuleSessionCount}</if>
        <if test="sessionViolatePct != null "> and session_violate_pct = #{sessionViolatePct}</if>
        <if test="deadlineTime != null "> and deadline_time = #{deadlineTime}</if>
        <if test="status != null "> and status = #{status}</if>
        <if test="enableStatus != null "> and enable_status = #{enableStatus}</if>
        <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
        <if test="planId != null "> and plan_id = #{planId}</if>
        <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
        <if test="datasetId != null "> and dataset_id = #{datasetId}</if>
        <if test="beginTime != null and beginTime != ''"> and create_time &gt;= #{beginTime}</if>
        <if test="endTime != null and endTime != ''"> and create_time &lt;= #{endTime}</if>
        </where>
        order by create_time desc
    </select>


    <select id="selectQcManualTaskListForRole"  resultMap="QcManualTaskResult">
        <include refid="selectQcManualTaskVo"/>
            where
                JSON_OVERLAPS
                (real_Inspectors,
                JSON_ARRAY(
                <foreach collection="visibleUsers" item="user" separator=",">
                    #{user.workNo}
                </foreach>
                )
                )
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="sessionCountFlag"> and session_count > 0 </if>
            <if test="sessionCountFlag"> and status  != '2' </if>
            <if test="taskType != null  and taskType != ''"> and task_type = #{taskType}</if>
            <if test="manualType != null  and manualType != ''"> and manual_type = #{manualType}</if>
            <if test="templateId != null  and templateId != ''"> and template_id = #{templateId}</if>
            <if test="dataRange != null  and dataRange != ''"> and data_range = #{dataRange}</if>
            <if test="smartPlan != null  and smartPlan != ''"> and smart_plan = #{smartPlan}</if>
            <if test="hitRules != null  and hitRules != ''"> and hit_rules = #{hitRules}</if>
            <if test="sessionCount != null "> and session_count = #{sessionCount}</if>
            <if test="relSessionCount != null "> and rel_session_count = #{relSessionCount}</if>
            <if test="hitRuleSessionCount != null "> and hit_rule_session_count = #{hitRuleSessionCount}</if>
            <if test="sessionViolatePct != null "> and session_violate_pct = #{sessionViolatePct}</if>
            <if test="deadlineTime != null "> and deadline_time = #{deadlineTime}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="enableStatus != null "> and enable_status = #{enableStatus}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
            <if test="datasetId != null "> and dataset_id = #{datasetId}</if>
            <if test="beginTime != null and beginTime != ''"> and create_time &gt;= #{beginTime}</if>
            <if test="endTime != null and endTime != ''"> and create_time &lt;= #{endTime}</if>
            order by create_time desc
    </select>

    <select id="selectQcManualTaskById" parameterType="Long" resultMap="QcManualTaskResult">
        <include refid="selectQcManualTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcManualTask" parameterType="QcManualTask" useGeneratedKeys="true" keyProperty="id">
        insert into qc_manual_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null">task_name,</if>
            <if test="taskType != null">task_type,</if>
            <if test="manualType != null">manual_type,</if>
            <if test="templateId != null">template_id,</if>
            <if test="dataRange != null ">data_range,</if>
            <if test="extractType != null ">extract_type,</if>
            <if test="extractValue != null ">extract_value,</if>
            <if test="smartPlan != null ">smart_plan,</if>
            <if test="hitRules != null  ">hit_rules,</if>
            <if test="sessionCount != null">session_count,</if>
            <if test="finishCount != null">finish_count,</if>
            <if test="inspectors != null">inspectors,</if>
            <if test="inspectorRate != null">inspector_rate,</if>
            <if test="realInspectors != null">real_Inspectors,</if>
            <if test="relSessionCount != null">rel_session_count,</if>
            <if test="hitRuleSessionCount != null">hit_rule_session_count,</if>
            <if test="sessionViolatePct != null">session_violate_pct,</if>
            <if test="deadlineTime != null">deadline_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="enableStatus != null">enable_status,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="planName != null">plan_name,</if>
            <if test="datasetId != null">dataset_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null">#{taskName},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="manualType != null">#{manualType},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="dataRange != null ">#{dataRange},</if>
            <if test="extractType != null ">#{extractType},</if>
            <if test="extractValue != null ">#{extractValue},</if>
            <if test="smartPlan != null ">#{smartPlan},</if>
            <if test="hitRules != null  ">#{hitRules},</if>
            <if test="sessionCount != null">#{sessionCount},</if>
            <if test="finishCount != null">#{finishCount},</if>
            <if test="inspectors != null">#{inspectors},</if>
            <if test="inspectorRate != null">#{inspectorRate},</if>
            <if test="realInspectors != null">#{realInspectors},</if>
            <if test="relSessionCount != null">#{relSessionCount},</if>
            <if test="hitRuleSessionCount != null">#{hitRuleSessionCount},</if>
            <if test="sessionViolatePct != null">#{sessionViolatePct},</if>
            <if test="deadlineTime != null">#{deadlineTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="enableStatus != null">#{enableStatus},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="planName != null">#{planName},</if>
            <if test="datasetId != null">#{datasetId},</if>
        </trim>
    </insert>

    <update id="updateQcManualTask" parameterType="QcManualTask">
        update qc_manual_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="manualType != null">manual_type = #{manualType},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="dataRange != null ">data_range = #{dataRange},</if>
            <if test="extractType != null ">extract_type = #{extractType},</if>
            <if test="extractValue != null ">extract_value = #{extractValue},</if>
            <if test="smartPlan != null ">smart_plan = #{smartPlan},</if>
            <if test="hitRules != null ">hit_rules = #{hitRules},</if>
            <if test="sessionCount != null">session_count = #{sessionCount},</if>
            <if test="finishCount != null">finish_count = #{finishCount},</if>
            <if test="inspectors != null">inspectors = #{inspectors},</if>
            <if test="inspectorRate != null">inspector_rate = #{inspectorRate},</if>
            <if test="realInspectors != null">real_Inspectors = #{realInspectors},</if>
            <if test="relSessionCount != null">rel_session_count = #{relSessionCount},</if>
            <if test="hitRuleSessionCount != null">hit_rule_session_count = #{hitRuleSessionCount},</if>
            <if test="sessionViolatePct != null">session_violate_pct = #{sessionViolatePct},</if>
            <if test="deadlineTime != null">deadline_time = #{deadlineTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="enableStatus != null">enable_status = #{enableStatus},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="datasetId != null">dataset_id = #{datasetId},</if>
        </trim>
        where id = #{id}
    </update>


    <update id="updateQcManualTaskDetail" parameterType="QcManualTask">
        update qc_manual_task
        SET
            task_name = #{taskName},
            template_id = #{templateId},
            data_range = #{dataRange},
            extract_type = #{extractType},
            extract_value = #{extractValue},
            smart_plan = #{smartPlan},
            hit_rules = #{hitRules},
            inspector_rate = #{inspectorRate},
            update_time = #{updateTime},
            update_by = #{updateBy},
            status = #{status},
            enable_status = #{enableStatus}
        where id = #{id}
    </update>


    <delete id="deleteQcManualTaskById" parameterType="Long">
        delete from qc_manual_task where id = #{id}
    </delete>

    <delete id="deleteQcManualTaskByIds" parameterType="String">
        delete from qc_manual_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>