<template>
	<div class="app-container">
  <div  v-show="task.show">
    <el-form ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="创建时间" prop="dataNo">
		<el-date-picker
			:default-time="['00:00:00', '23:59:59']"
	      	v-model="task.data.createTimeStr"
	      	type="datetimerange"
	      	range-separator="至"
		  	value-format="yyyy-MM-dd HH:mm:ss"
	      	start-placeholder="开始日期"
	      	end-placeholder="结束日期">
	    </el-date-picker>
      </el-form-item>
      <el-form-item label="任务类型" prop="templateName">
		<el-select v-model="task.data.taskType" placeholder="请选择任务类型" clearable :style="{width: '100%'}">
          <el-option v-for="(item, index) in taskTypeList" :key="index" :label="item.dictLabel"
                 :value="item.dictValue" :disabled="item.status==1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="质检计划" prop="workNo">
		<el-select v-model="task.data.planId" placeholder="请选择计划" :filterable="true" clearable :style="{width: '100%'}">
          <el-option v-for="(item, index) in planList" :key="index" :label="item.planName"
                 :value="item.id" :disabled="item.status!=1"></el-option>
        </el-select>
      </el-form-item>
	  <el-form-item label="任务名称" prop="workNo">
		<el-input v-model="task.data.taskName" placeholder="请输入任务名称" clearable :style="{width: '100%'}"/>
      </el-form-item>
	  <el-form-item label="状态" prop="workNo">
  		<el-select v-model="task.data.status" placeholder="请选择状态" clearable :style="{width: '100%'}">
            <el-option v-for="item in tsList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
		<el-form-item label="是否大模型" prop="workNo">
	  		<el-select v-model="task.data.isSematic" placeholder="请选择状态" clearable :style="{width: '100%'}">
	            <el-option :key="1" label="是" :value="1"></el-option>
				<el-option :key="2" label="否" :value="2"></el-option>
	          </el-select>
        </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery('task')">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList('task')">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain size="mini" @click="goList(true,'plan')" v-hasPermi="['quality:smart:plan:list']">计划</el-button>
		<el-button type="primary" plain size="mini" @click="goEdit('task')" v-hasPermi="['quality:smart:task:manual']">新建手动任务</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="task.loading" :data="task.list">
<!--      <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column label="编号" align="center" prop="id" width="55" fixed/>
      <el-table-column label="任务名称" align="center" prop="taskName" width="300" fixed show-overflow-tooltip />
	  <el-table-column label="状态" align="center" fixed prop="status" width="150">
	  	  <template slot-scope="scope">{{format(scope.row,{property:'task.status'},scope.row.status)}}</template>
	  </el-table-column>
	  <el-table-column label="计划名称" align="center" prop="planName" width="300" show-overflow-tooltip />
      <el-table-column label="任务类型" align="center" prop="taskType" :formatter="format" width="100"/>
      <el-table-column label="会话总量" align="center" prop="sessionCount" width="100"/>
      <el-table-column label="实际会话总量" align="center" prop="relSessionCount" width="100"/>
      <el-table-column label="命中规则会话数量" align="center" prop="hitRuleSessionCount" width="100"/>
      <el-table-column label="机检违规率" align="center" prop="sessionViolatePct" :formatter="format" width="100"/>
	  <el-table-column label="是否大模型任务" align="center" prop="isSematic" :formatter="format"width="100"/>
	  <el-table-column label="进度" align="center" prop="progress" width="100"/>
      <el-table-column label="创建人" align="center" prop="createBy" width="100"/>
	  <el-table-column label="创建时间" align="center" prop="createTime" width="200"/>
	  <el-table-column label="更新人" align="center" prop="updateBy" width="100"/>
  	  <el-table-column label="更新时间" align="center" prop="updateTime" width="200"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button v-if="scope.row.relSessionCount>0" size="mini" type="text" icon="el-icon-coordinate" @click="goDetail(scope.row)">查看详情</el-button>
		  <template v-if="scope.row.isSematic == 1 && scope.row.status != 3 && scope.row.status != 4 ">
		  <el-popconfirm title="你确定要终止吗？" @confirm="stopTask(scope.row)" :ref="scope.row.id">
			  <el-button slot="reference"  size="mini" type="text" icon="el-icon-coordinate">终止任务</el-button>
		  </el-popconfirm>
		  </template>	
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="task.query.total>0"
      :total="task.query.total"
      :page.sync="task.query.pageNum"
      :limit.sync="task.query.pageSize"
      @pagination="getList('task')"
    />
  </div>
  <div v-show="detail.show">
	<el-form size="small" :inline="true" label-width="120px">
	    <el-form-item label="任务名称" prop="dataNo">
			<el-input v-model="currTask.taskName" disabled style="width:300px;"/>
		</el-form-item>
	    <el-form-item label="会话数量" prop="workNo">
	      <el-input v-model="currTask.relSessionCount" disabled style="width:100px;"/>
	    </el-form-item>
	    <el-form-item label="命中规则会话数" prop="workName">
	      <el-input v-model="currTask.hitRuleSessionCount" disabled style="width:100px;"/>
	    </el-form-item>
		<el-form-item label="计划名称" prop="workName">
	      <el-input v-model="currTask.planName" disabled style="width:300px;"/>
	    </el-form-item>
		<el-form-item label="失败重试次数" prop="workName">
		      <el-input v-model="currTask.reTryCount" disabled style="width:300px;"/>
	    </el-form-item>
	  </el-form>
	<el-divider class="topLine"></el-divider>
      <el-form ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
        <el-form-item label="状态" prop="dataNo">
			<el-select v-model="detail.data.status" placeholder="请选择下拉选择" clearable :style="{width: '100%'}">
	            <el-option v-for="(item, index) in qsdsList" :key="index" :label="item.dictLabel"
	                       :value="item.dictValue" :disabled="item.disabled"></el-option>
	          </el-select>
        </el-form-item>
		<el-form-item label="会话时长" prop="workNo">
          <el-input v-model="detail.data.sessionLength" type="number" placeholder="请输入会话时长" clearable/>
        </el-form-item>
		<el-form-item label="创建时间" prop="dataNo">
			<el-date-picker
				:default-time="['00:00:00', '23:59:59']"
	     	 	v-model="detail.data.createTimeStr"
		      	type="datetimerange"
		      	range-separator="至"
			  	value-format="yyyy-MM-dd HH:mm:ss"
		      	start-placeholder="开始日期"
		      	end-placeholder="结束日期">
		    </el-date-picker>
	      </el-form-item>
		<el-form-item label="业务编号" prop="workNo">
          <el-input v-model="detail.data.bizNo" placeholder="请输入业务编号" clearable/>
        </el-form-item>
        <el-form-item label="坐席工号" prop="workNo">
          <el-input v-model="detail.data.workNo" placeholder="请输入坐席工号" clearable/>
        </el-form-item>
        <el-form-item label="坐席名称" prop="workName">
          <el-input v-model="detail.data.workName" placeholder="请输入坐席名称" clearable />
        </el-form-item>
		<el-form-item label="智能规则" prop="aiResult">
          <el-select v-model="detail.data.ruleIds" placeholder="请选择智能规则" multiple clearable :style="{width: '100%'}">
            <el-option v-for="(item, index) in ruleList" :key="index" :label="item.name"
                       :value="item.ruleId" :disabled="item.status!='1'"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="大模型结果" prop="aiResult">
          <el-select v-model="detail.data.macInspectResult" placeholder="请选择大模型结果" clearable :style="{width: '100%'}">
            <el-option v-for="(item, index) in aiResultArray" :key="index" :label="item.dictLabel"
                       :value="item.dictValue" :disabled="item.disabled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery('detail')">重置</el-button>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="getList('detail')">查询</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" size="mini" @click="goList(false,'task')">返回</el-button>
		  <el-button type="primary" size="mini" @click="handlerExport">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="goList(true,'detail')"></right-toolbar>
      </el-row>

      <el-table v-loading="detail.loading" :data="detail.list" style="width: 100%">
  <!--      <el-table-column type="selection" width="55" align="center" />-->
  		<el-table-column label="序号" align="center" fixed prop="id" width="100"/>
		<el-table-column label="状态" align="center" fixed prop="status" width="150">
			<template slot-scope="scope">{{format(scope.row,{property:'detail.status'},scope.row.status)}}</template>
		</el-table-column>
        <el-table-column label="业务编号" align="center" fixed prop="bizNo" width="100" show-overflow-tooltip/>
        <el-table-column label="坐席工号" align="center" prop="workNo" width="100" :formatter="format"/>
        <el-table-column label="坐席名称" align="center" prop="workName" width="100" :formatter="format"/>
        <el-table-column label="会话时长" align="center" prop="sessionLength" width="100"/>
        <el-table-column label="命中规则数" align="center" prop="hitRuleCount" width="100"/>
		<el-table-column label="命中加分项规则数量" align="center" prop="hitPlusRuleCount" width="100"/>
		<el-table-column label="命中减分项规则数量" align="center" prop="hitMinusRuleCount" width="100"/>
        <el-table-column label="机检得分" align="center" prop="macInspectScore" width="100"/>
		<el-table-column label="复检得分" align="center" prop="checkInspectScore" width="100"/>
		<el-table-column label="大模型结果" align="center" prop="macInspectResult" width="100" :formatter="format"/>
        <el-table-column label="机检完成时间" align="center" prop="macInspectTime" width="150"/>
        <el-table-column label="创建人" align="center" prop="createBy" width="100"/>
  	  	<el-table-column label="创建时间" align="center" prop="createTime" width="150"/>
		<el-table-column label="更新人" align="center" prop="updateBy" />
  	  	<el-table-column label="更新时间" align="center" prop="updateTime" width="150"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width"fixed="right">
		  <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-coordinate" @click="viewTempate(scope.row)" >智能结果</el-button>
            </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="detail.query.total>0"
        :total="detail.query.total"
        :page.sync="detail.query.pageNum"
        :limit.sync="detail.query.pageSize"
        @pagination="getList('detail')"
      />
    </div>
	<div v-show="plan.show">
	      <el-form ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
	        <el-form-item label="计划状态" prop="dataNo">
				<el-select v-model="plan.data.status" placeholder="请选择下拉选择" clearable :style="{width: '100%'}">
					<el-option :key="1" label="启用" :value="1"></el-option>
		            <el-option :key="2" label="禁用" :value="2"></el-option>
	          	</el-select>
	        </el-form-item>
	        <el-form-item label="计划类型" prop="workNo">
				<el-select v-model="plan.data.planType" placeholder="请选择计划类型" clearable :style="{width: '100%'}">
					<el-option :key="1" label="定时计划" :value="1"></el-option>
		            <el-option :key="2" label="实时计划" :value="2"></el-option>
	          	</el-select>
	        </el-form-item>
	        <el-form-item label="更新人" prop="workName">
	          <el-input v-model="plan.data.updateBy" placeholder="请输入更新人" clearable />
	        </el-form-item>
			<el-form-item label="计划名称" prop="workName">
		          <el-input v-model="plan.data.planName" placeholder="请输入计划名称" clearable />
	        </el-form-item>
			<el-form-item label="创建时间" prop="dataNo">
				<el-date-picker
					:default-time="['00:00:00', '23:59:59']"
			      	v-model="plan.data.createTimeStr"
			      	type="datetimerange"
			      	range-separator="至"
				  	value-format="yyyy-MM-dd HH:mm:ss"
			      	start-placeholder="开始日期"
			      	end-placeholder="结束日期">
			    </el-date-picker>
		      </el-form-item>
	        <el-form-item>
	          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery('plan')">重置</el-button>
	          <el-button type="primary" icon="el-icon-search" size="mini" @click="getList('plan')">查询</el-button>
	        </el-form-item>
	      </el-form>

	      <el-row :gutter="10" class="mb8">
	        <el-col :span="1.5">
	          <el-button type="primary" plain size="mini" @click="goList(false,'task')">返回</el-button>
	        </el-col>
			<el-col :span="1.5">
	          <el-button type="primary" plain size="mini" @click="goEdit('plan')" v-hasPermi="['quality:smart:plan:add']" >新建自动计划</el-button>
	        </el-col>
	        <right-toolbar :showSearch.sync="showSearch" @queryTable="goList(true,'plan')"></right-toolbar>
	      </el-row>

	      <el-table v-loading="plan.loading" :data="plan.list">
	  		<el-table-column label="序号" align="left" prop="id" width="50"/>
	        <el-table-column label="计划类型" align="left" prop="planType" :formatter="format" width="100"/>
	        <el-table-column label="计划名称" align="left" prop="planName" width="300"/>
	        <el-table-column label="计划状态" align="left" prop="status" width="100">
				<template slot-scope="scope"><div :style="`color:${scope.row.status==2?'red':'green'}`">{{format(scope.row,{property:'status'},scope.row.status)}}</div></template>
			</el-table-column>
	        <el-table-column label="评分模板" align="left" prop="scoreTemplate" :formatter="format" width="300"/>
			<el-table-column label="创建人" align="left" prop="createBy" width="100"/>
	  	  	<el-table-column label="创建时间" align="left" prop="createTime" width="200"/>
	        <el-table-column label="更新人" align="left" prop="updateBy" width="100"/>
	  	  	<el-table-column label="更新时间" align="left" prop="updateTime" width="200"/>
	        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
	          <template slot-scope="scope">
	            <el-button v-if="scope.row.status==2" size="mini" type="text" @click="goEdit('plan',scope.row)" v-hasPermi="['quality:smart:plan:edit']">修改</el-button>
				<el-button v-if="scope.row.status==1" size="mini" type="text" @click="goEdit('plan',scope.row,'view')" v-hasPermi="['quality:smart:plan:edit']">查看</el-button>
				<el-popconfirm :title="scope.row.status==2?'你确定要启用吗？':'你确定要禁用吗？'" @confirm="updPlan(scope.row)">
			    	<el-button  slot="reference" size="mini" type="text" v-hasPermi="['quality:smart:plan:edit']" :style="`color:${scope.row.status==2?'#1890ff':'red'}`">{{scope.row.status==2?'启用':'禁用'}}</el-button>
			  	</el-popconfirm>
	          </template>
	        </el-table-column>
	      </el-table>
	      <pagination
	        v-show="plan.query.total>0"
	        :total="plan.query.total"
	        :page.sync="plan.query.pageNum"
	        :limit.sync="plan.query.pageSize"
	        @pagination="getList('plan')"
	      />
	    </div>
		<el-dialog :title="conf.view.title" :visible.sync="conf.view.open" width="1600px"height="650px" append-to-body>
			<div v-if="conf.view.open" >
				<tempViewModal :viewParam="conf.view.param" ref="tempViewModal"></tempViewModal>
			</div>
	    </el-dialog>
		<el-dialog :show-header="false" :visible.sync="visit.plan" width="1200px" 
			:close-on-press-escape="false"
			:show-close="false"
			:close-on-click-modal="false"
			custom-class="no-header-dialog"
			height="860px"
		>
			<planModal  @goList="goList" ref="editPlan"></planModal>
		</el-dialog>
		<el-dialog :show-header="false" :visible.sync="visit.task" width="1200px" 
			:close-on-press-escape="false"
			:show-close="false"
			:close-on-click-modal="false"
			custom-class="no-header-dialog"
			height="860px"
		>
			<taskModal  @goList="goList" ref="editTask"></taskModal>
		</el-dialog>
	</div>
</template>

<script>
import { taskList, detailList,planList,updatePlan,stopTask} from "@/api/quality/smart.js";
import { getAllRulesById} from "@/api/quality/manualPlan.js";
import { getQcScoringTemplateList} from "@/api/quality/scoringTemplate.js";
import { queryListByType} from "@/api/system/dict/data";
import tempViewModal from "@/views/quality/smart/view/template2.vue";
import planModal from "@/views/quality/smart/plan/edit.vue";
import taskModal from "@/views/quality/smart/task/edit.vue";
export default {
  name: "Review",
  components: {
  	tempViewModal,planModal,taskModal
    },
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
	  //当前选中的任务
	  currTask:{},
      task: {
		list:[],
  		show:false,
		loading:false,
		query:{pageNum: 1,pageSize: 10,total:0},
		data:{}
	  },
	  detail: {
  		list:[],
  		show:false,
		loading:false,
		query:{pageNum: 1,pageSize: 10,total:0},
		data:{}
  	  },
	  plan: {
		list:[],
		show:false,
		loading:false,
		query:{pageNum: 1,pageSize: 10,total:0},
		data:{}
	  },
	  conf:{
	  		view:{
	  			title:"",
	  			open:false,
	  			param:{tempId:0}
	  		}
  	  },
      aiResultArray:[{
        "dictValue": "请选择",
        "dictLabel":""
      }, {
        "dictValue": "废卷",
        "dictLabel": "废卷"
      }, {
        "dictValue": "合格",
        "dictLabel": "合格"
      }, {
        "dictValue": "需修改",
        "dictLabel": "需修改"
      }],
      // 表单参数
      form: {},
	  tetList:[],
	  taskTypeList:[],
	  qsdsList:[],
	  planList:[],
	  tempList:[],
	  ruleList:[],
	  tsList:[],//质检任务状态枚举
	  visit:{
		plan:false,
		task:false
	  }
    };
  },
  created() {
	queryListByType	({dictTypeList:["quality_task_exec_type","quality_task_type","qc_smart_detail_status","qc_smart_task_status"],status:"0"}).then(response => {
			if(response.data){
				this.tetList = response.data["quality_task_exec_type"];
				this.taskTypeList = response.data["quality_task_exec_type"];
				this.qsdsList = response.data["qc_smart_detail_status"];
				this.tsList = response.data["qc_smart_task_status"];
			}
		  });
	  this.doPlanList(1);
	  getQcScoringTemplateList({pageNum:1,pageSize:100},{}).then(response=>{
		for(var i in response.rows){
			this.tempList.push({dictLabel:response.rows[i].scoringTemplateName,dictValue:response.rows[i].id});
			
		}
	  });
    this.goList(true,'task');
  },
  methods: {
	goList(isquery,target){
		this.task.show = false;
		this.detail.show = false;
		this.plan.show = false;
		this.visit.plan = false;
		this.visit.task = false;
		this.plan.data={};
		this.detail.data={};
		this[target].show = true;
		if(isquery){
			this.getList(target);
		}
	},
	doPlanList(status){
		planList({},{status:status,isPg:false}).then(response => {
			if(response.rows){
				this.planList = response.rows;
			}
	  	});
	},
	getList(target) {
		if(target == 'task'){
			this.task.loading = true;
			taskList(this.task.query,this.task.data).then(response => {
		        this.task.list = response.rows;
		        this.task.query.total = response.total;
	      	}).finally(v=>{
		        this.task.loading = false;
			});
		}else if(target == 'detail'){
			this.detail.loading = true;
			detailList(this.detail.query,this.detail.data).then(response => {
		        this.detail.list = response.rows;
		        this.detail.query.total = response.total;
			}).finally(v=>{
		        this.detail.loading = false;
			});
		}else if(target == 'plan'){
			this.plan.loading = true;
			this.plan.data['isPg'] = true;
			planList(this.plan.query,this.plan.data).then(response => {
		        this.plan.list = response.rows;
		        this.plan.query.total = response.total;
			}).finally(v=>{
		        this.plan.loading = false;
			});
	 	}
	      
	  },
    /** 重置按钮操作 */
    resetQuery(target) {
		var smartTaskId = "";
		if(target == 'detail'){
			smartTaskId = this[target].data.smartTaskId;
		}
      this[target].data = {};
	  if(target == 'detail'){
		this[target].data["smartTaskId"] = smartTaskId;
	  }
    },
	viewTempate(row){
			this.conf.view.title = row.bizNo+"-预览";
			this.conf.view.open = true;
			this.conf.view.param.tempId = row.templateId;
			this.conf.view.param.taskDetailId = row.id;
			this.conf.view.param.op = 'view';
		},
	goDetail(row){
		this.task.show = false;
		this.detail.show = true;
		this.detail.loading = true;
		this.currTask = row;
		if(row){this.detail.data.smartTaskId = row.id}
	    detailList(this.detail.query,this.detail.data).then(response => {
	        this.detail.list = response.rows;
	        this.detail.query.total = response.total;
	    }).finally(v=>{
	        this.detail.loading = false;
		});
		getAllRulesById(row.templateId).then(response=>{
			this.ruleList = response.data.rules;
			for(var i =0;i<this.ruleList.length;i++){
				if(this.ruleList[i]["ruleClassification"]=="2"){
					this.ruleList.splice(i,1);
					i--;
				}
			}
		});
	},
	format(row,column,cellValue,index){
		var list = [];
		switch(column.property){
			case 'taskType':
				list = this.tetList;
				break;
			case "planType":
				list = [{dictValue:1,dictLabel:"定时计划"},{dictValue:2,dictLabel:"实时计划"}];
				break;
			case "status":
				list = [{dictValue:1,dictLabel:"启用"},{dictValue:2,dictLabel:"禁用"}];
				break;
			case "scoreTemplate":
				list = this.tempList;
				break;
			case "detail.status":
				list = this.qsdsList;
				break;
			case "task.status":
				list = this.tsList;
				break;
			case "macInspectResult":
				list = this.aiResultArray;
				break;
			case "sessionViolatePct":
				return (cellValue * 100).toFixed(2) + '%';
			case "workNo":
			case "workName":
				if(!cellValue || cellValue == null || cellValue ==''){return ""}
				let rTmp = cellValue.substring(0,1);
				for(var i=1;i<cellValue.length;i++){
					rTmp += "*";
				}
				return rTmp;
			case "isSematic":
				list = [{dictValue:1,dictLabel:"是"},{dictValue:2,dictLabel:"否"}];
				break;
			default:
				break;
		}
		for(var index in list){
			if(list[index]["dictValue"] == cellValue){
				return list[index]["dictLabel"];
			}
		}
	},
	goEdit(target,row,op){
		this.visit[target] = true;
		this.$nextTick(()=>{
			this.$refs["edit"+target.substring(0,1).toUpperCase()+target.substring(1)].reload(row,this.tempList,op);
		})
	},
	closeDialog(target){
		this.visit[target] = false;
	},
	updPlan(row){
		var tmp = {id:row.id,status:row.status}
		tmp.status = tmp.status==1?2:1;
        updatePlan(tmp).then(res=>{
	     	if(res.code == 200){
		       	this.getList('plan');
	     	}
       })
	},
	stopTask(row){
		stopTask({status:3,smartTaskId:row.id}).then(response=>{
			this.$message({message: "任务停止成功",type: 'success'});
		}).finally(v=>{
			this.getList('task');
		});
	},
	handlerExport(){
		this.download('qc/smart/task/detail/export', this.detail.data, `user_${new Date().getTime()}.xlsx`)
	}
	
  }
};
</script>
<style>
.el-dialog__header {
  /*display: none;*/
}
.el-dialog__body{
	padding-top:0px;
}
</style>