package com.ideal.system.domain;

import java.util.List;

public class SysTenantPermi {

    private List<SysTenantMenu> menus;
    private List<Long> checkedMenuIds;

    public List<SysTenantMenu> getMenus() {
        return menus;
    }

    public void setMenus(List<SysTenantMenu> menus) {
        this.menus = menus;
    }

    public List<Long> getCheckedMenuIds() {
        return checkedMenuIds;
    }

    public void setCheckedMenuIds(List<Long> checkedMenuIds) {
        this.checkedMenuIds = checkedMenuIds;
    }
}
