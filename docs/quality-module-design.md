# Quality模块设计文档

## 1. 系统概述

Quality模块是AI质检系统的核心模块，主要负责业务数据管理、数据集管理、语音数据处理和质检流程管理。该模块采用前后端分离架构，前端使用Vue.js，后端使用Spring Boot微服务架构。

### 1.1 核心功能
- 业务数据明细管理（BizItem）
- 数据集管理（Dataset）
- 语音数据明细管理（DatasetItem）
- 质检流程管理
- 数据导入导出
- 语音识别（ASR）集成

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端Vue.js    │     │  Gateway网关    │     │   后端微服务    │
│                 │◄──►│                 │◄──►│                 │
│ - bizItem页面   │     │ - 路由转发      │     │ - QC模块        │
│ - dataset页面   │     │ - 权限验证      │     │ - Job模块       │
│ - 质检页面      │      │ - 负载均衡      │     │ - System模块    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                               ┌─────────────────┐
                                               │   数据存储层    │
                                               │                 │
                                               │ - MySQL数据库   │
                                               │ - FastDFS文件   │
                                               │ - Redis缓存     │
                                               └─────────────────┘
```

### 2.2 模块结构
```
qualityAI/
├── qualityAI-ui/                    # 前端Vue项目
│   └── src/views/quality/
│       ├── bizItem/                 # 业务数据明细页面
│       │   ├── index.vue           # 列表页面
│       │   ├── detail.vue          # 详情页面
│       │   ├── check.vue           # 质检页面
│       │   └── taskDetail.vue      # 任务详情页面
│       └── dataset/                # 数据集页面
│           └── index.vue           # 数据集管理页面
├── qualityAI-modules/
│   ├── qualityAI-qc/               # 质检核心模块
│   │   ├── controller/             # 控制器层
│   │   ├── service/                # 业务逻辑层
│   │   ├── mapper/                 # 数据访问层
│   │   └── domain/                 # 实体类
│   └── qualityAI-job/              # 任务调度模块
└── sql/                            # 数据库脚本
```

## 3. 数据模型设计

### 3.1 核心实体关系图
```
┌─────────────────┐    1:N    ┌─────────────────┐    1:N    ┌─────────────────┐
│   QcDataset     │◄─────────►│   QcBizItem     │◄─────────►│ QcDatasetItem   │
│                 │           │                 │           │                 │
│ - id            │           │ - id            │           │ - id            │
│ - name          │           │ - dataset_id    │           │ - dataset_id    │
│ - data_type     │           │ - unique_key    │           │ - biz_no        │
│ - total_count   │           │ - agent_id      │           │ - file_id       │
│ - process_status│           │ - answers_json  │           │ - asr_result    │
└─────────────────┘           └─────────────────┘           └─────────────────┘
```

### 3.2 数据表结构

#### 3.2.1 qc_dataset（数据集表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| name | varchar(255) | 数据集名称 |
| data_type | varchar(20) | 数据类型（voice/biz） |
| plan_id | bigint | 来源计划ID |
| total_count | bigint | 总数据量 |
| asr_progress | bigint | 语音转写进度 |
| biz_sample_cols | bigint | 业务问卷动态列数 |
| status | varchar(20) | 状态（new/done） |
| process_status | varchar(20) | 处理状态 |
| progress | decimal(5,2) | 完成百分比 |
| tenant_id | bigint | 租户ID |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

#### 3.2.2 qc_biz_item（业务数据明细表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| dataset_id | bigint | 所属数据集ID |
| platform | varchar(100) | 所属平台 |
| project_name | varchar(100) | 所属项目 |
| agent_id | varchar(50) | 坐席工号 |
| agent_name | varchar(50) | 坐席姓名 |
| unique_key | varchar(100) | 唯一主键号 |
| call_duration_s | bigint | 通话时长(秒) |
| recording_file | varchar(255) | 录音文件ID |
| answers_json | text | 动态问卷答案JSON |
| qc_lvl1_result | varchar(50) | 一级质检结果 |
| qc_lvl2_result | varchar(50) | 二级质检结果 |
| tenant_id | bigint | 租户ID |

#### 3.2.3 qc_dataset_item（语音数据明细表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| dataset_id | bigint | 数据集ID |
| biz_no | varchar(100) | 业务编号 |
| file_id | varchar(255) | FastDFS文件路径 |
| duration_sec | bigint | 时长(秒) |
| call_time | datetime | 通话时间 |
| asr_status | varchar(20) | 语音识别状态 |
| asr_result | text | 语音识别结果 |
| tenant_id | bigint | 租户ID |

## 4. 业务流程设计

### 4.1 数据导入流程
```
开始 → 上传Excel文件 → 解析Excel数据 → 数据验证 → 创建数据集 → 
批量插入BizItem → 处理录音文件 → 创建DatasetItem → 触发ASR任务 → 结束
```

### 4.2 质检流程
```
数据准备 → 智能质检 → 人工质检 → 人工复检 → 质检结果 → 数据导出
    ↓         ↓         ↓         ↓         ↓         ↓
  BizItem   AI分析    人工评分   复检确认   结果汇总   Excel导出
```

### 4.3 ASR处理流程
```
录音文件上传 → FastDFS存储 → 创建DatasetItem → 
ASR任务队列 → 语音识别 → 更新asr_result → 状态更新
```

## 5. 技术实现

### 5.1 前端技术栈
- **框架**: Vue.js 2.x
- **UI组件**: Element UI
- **状态管理**: Vuex
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **音频播放**: WaveSurfer.js

### 5.2 后端技术栈
- **框架**: Spring Boot 2.x
- **微服务**: Spring Cloud
- **数据访问**: MyBatis Plus
- **数据库**: MySQL 8.0
- **文件存储**: FastDFS
- **缓存**: Redis
- **任务调度**: Quartz

### 5.3 核心服务类

#### 5.3.1 QcBizItemService
```java
public interface IQcBizItemService {
    // 基础CRUD操作
    List<QcBizItem> selectQcBizItemList(QcBizItem qcBizItem);
    QcBizItem selectQcBizItemById(Long id);
    int insertQcBizItem(QcBizItem qcBizItem);
    int updateQcBizItem(QcBizItem qcBizItem);
    
    // 业务特定方法
    QcBizItem selectQcBizItemByNo(String bizNo, String type, Long manualDetailId);
    String importBizItem(List<QcBizItem> rowDataList, String operName);
    List<QcBizItem> importExcel(MultipartFile file);
    void exportBizItemWithDynamicColumns(HttpServletResponse response, QcBizItem qcBizItem);
}
```

#### 5.3.2 QcDatasetService
```java
public interface IQcDatasetService {
    List<QcDataset> selectQcDatasetList(QcDataset qcDataset);
    List<QcDataset> selectQcDatasetListByMap(JSONObject param);
    QcDataset selectQcDatasetById(Long id);
    int insertQcDataset(QcDataset qcDataset);
    int updateQcDataset(QcDataset qcDataset);
}
```

## 6. API接口设计

### 6.1 BizItem相关接口
```
GET    /qc/bizItem/list                    # 查询业务数据列表
GET    /qc/bizItem/{id}                    # 获取业务数据详情
POST   /qc/bizItem                        # 新增业务数据
PUT    /qc/bizItem                        # 修改业务数据
DELETE /qc/bizItem/{ids}                  # 删除业务数据
GET    /qc/bizItem/byNo/{bizNo}/{type}/{manualDetailId}  # 根据业务编号查询
POST   /qc/bizItem/importData             # 导入数据
POST   /qc/bizItem/export                 # 标准导出
POST   /qc/bizItem/exportWithDynamicColumns  # 动态列导出
```

### 6.2 Dataset相关接口
```
GET    /qc/dataset/list                   # 查询数据集列表
POST   /qc/dataset/list/byMap             # 条件查询数据集
GET    /qc/dataset/{id}                   # 获取数据集详情
POST   /qc/dataset                       # 新增数据集
PUT    /qc/dataset                       # 修改数据集
DELETE /qc/dataset/{ids}                 # 删除数据集
```

### 6.3 DatasetItem相关接口
```
GET    /qc/datasetItem/list              # 查询语音数据列表
GET    /qc/datasetItem/{id}              # 获取语音数据详情
POST   /qc/datasetItem                   # 新增语音数据
PUT    /qc/datasetItem                   # 修改语音数据
DELETE /qc/datasetItem/{ids}             # 删除语音数据
GET    /qc/datasetItem/getFileByFileId   # 获取音频文件
```

## 7. 前端页面设计

### 7.1 页面路由配置
```javascript
// 业务数据相关路由
{
  path: '/quality-detail',
  component: Layout,
  hidden: true,
  children: [{
    path: 'index',
    component: () => import('@/views/quality/bizItem/detail'),
    name: 'QualityDetail',
    meta: { title: '录音详情' }
  }]
},
{
  path: '/quality-new-check',
  component: Layout,
  hidden: true,
  children: [{
    path: 'index',
    component: () => import('@/views/quality/bizItem/check'),
    name: 'QualityCheck',
    meta: { title: '人工质检' }
  }]
}
```

### 7.2 核心组件

#### 7.2.1 BizItem列表页面（index.vue）
- 数据表格展示
- 搜索筛选功能
- 分页处理
- 导入导出功能
- 跳转到详情/质检页面

#### 7.2.2 质检页面（check.vue）
- 音频播放器集成
- 质检表单
- 评分规则展示
- 标注功能
- 结果提交

#### 7.2.3 详情页面（detail.vue）
- 业务信息展示
- 质检历史记录
- 音频播放
- 标注查看

## 8. 数据流转

### 8.1 导入数据流转
```
Excel文件 → BizDynamicListener解析 → QcBizItem对象列表 → 
数据验证 → 创建QcDataset → 批量插入QcBizItem → 
处理录音文件 → 创建QcDatasetItem → 更新数据集统计
```

### 8.2 质检数据流转
```
选择BizItem → 获取关联DatasetItem → 加载音频文件 → 
展示质检表单 → 填写质检结果 → 保存到质检结果表 → 
更新BizItem状态 → 生成质检报告
```

## 9. 性能优化

### 9.1 数据库优化
- 建立合适的索引（dataset_id, unique_key, agent_id等）
- 分页查询优化
- 批量操作优化

### 9.2 文件处理优化
- FastDFS分布式文件存储
- 音频文件流式传输
- 断点续传支持

### 9.3 前端优化
- 组件懒加载
- 音频预加载
- 虚拟滚动（大数据量表格）

## 10. 安全设计

### 10.1 权限控制
- 基于角色的访问控制（RBAC）
- 接口权限验证
- 数据权限隔离（租户隔离）

### 10.2 数据安全
- 敏感数据脱敏
- 文件访问权限控制
- 操作日志记录

## 11. 扩展性设计

### 11.1 多租户支持
- 所有核心表包含tenant_id字段
- 数据隔离和权限控制
- 配置隔离

### 11.2 插件化设计
- ASR服务插件化
- 质检规则引擎可扩展
- 导入导出格式可扩展

## 12. 监控和运维

### 12.1 系统监控
- 接口性能监控
- 数据库性能监控
- 文件存储监控

### 12.2 业务监控
- 导入成功率监控
- ASR处理进度监控
- 质检完成率监控

## 13. 部署架构

### 13.1 微服务部署
```
Load Balancer → Gateway → [QC Service, Job Service, System Service]
                    ↓
              [MySQL Cluster, Redis Cluster, FastDFS Cluster]
```

### 13.2 容器化部署
- Docker容器化
- Kubernetes编排
- 服务发现和配置管理

## 14. 详细技术实现

### 14.1 Excel导入实现

#### 14.1.1 动态Excel解析器
```java
public class BizDynamicListener extends AnalysisEventListener<Map<Integer, String>> {
    private List<QcBizItem> items = new ArrayList<>();
    private Map<Integer, String> headerMap = new HashMap<>();

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        this.headerMap = headMap;
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        QcBizItem item = new QcBizItem();
        // 固定列映射
        item.setPlatform(data.get(0));
        item.setProjectName(data.get(1));
        // ... 其他固定字段

        // 动态列处理（Q1-Q11等问卷答案）
        Map<String, Object> dynamicAnswers = new HashMap<>();
        for (int i = FIXED_COLUMN_COUNT; i < headerMap.size(); i++) {
            String columnName = headerMap.get(i);
            String value = data.get(i);
            if (StringUtils.isNotBlank(value)) {
                dynamicAnswers.put(columnName, value);
            }
        }
        item.setAnswersJson(JSON.toJSONString(dynamicAnswers));
        items.add(item);
    }
}
```

#### 14.1.2 数据验证和导入逻辑
```java
@Override
@Transactional
public String importBizItem(List<QcBizItem> rowDataList, String operName) {
    // 1. 创建数据集
    QcDataset dataset = new QcDataset();
    dataset.setName("导入数据集_" + DateUtils.dateTimeNow());
    dataset.setDataType("biz");
    dataset.setTotalCount((long) rowDataList.size());
    dataset.setProcessStatus("importing");
    qcDatasetMapper.insertQcDataset(dataset);

    // 2. 批量验证和插入
    int successNum = 0;
    int failureNum = 0;
    StringBuilder failureMsg = new StringBuilder();

    for (QcBizItem item : rowDataList) {
        try {
            // 唯一性验证
            if (qcBizItemMapper.selectQcBizItemByNo(item.getUniqueKey()) != null) {
                failureNum++;
                continue;
            }

            item.setDatasetId(dataset.getId());
            item.setCreateBy(operName);
            qcBizItemMapper.insertQcBizItem(item);
            successNum++;
        } catch (Exception e) {
            failureNum++;
            failureMsg.append("第").append(successNum + failureNum).append("行导入失败：").append(e.getMessage());
        }
    }

    // 3. 更新数据集状态
    dataset.setTotalCount((long) successNum);
    dataset.setProcessStatus("ready");
    qcDatasetMapper.updateQcDataset(dataset);

    return String.format("导入完成！成功%d条，失败%d条。%s", successNum, failureNum, failureMsg.toString());
}
```

### 14.2 动态Excel导出实现

```java
@Override
public void exportBizItemWithDynamicColumns(HttpServletResponse response, QcBizItem qcBizItem) {
    List<QcBizItem> list = qcBizItemMapper.selectQcBizItemList(qcBizItem);

    // 1. 收集所有动态列
    Set<String> dynamicColumns = new LinkedHashSet<>();
    for (QcBizItem item : list) {
        if (StringUtils.isNotBlank(item.getAnswersJson())) {
            Map<String, Object> answers = JSON.parseObject(item.getAnswersJson(), Map.class);
            dynamicColumns.addAll(answers.keySet());
        }
    }

    // 2. 构建表头
    List<String> headers = new ArrayList<>();
    headers.addAll(Arrays.asList("所属平台", "所属项目", "指标名称", "坐席工号", "坐席姓名", "唯一主键号"));
    headers.addAll(dynamicColumns);

    // 3. 构建数据行
    List<List<Object>> dataRows = new ArrayList<>();
    for (QcBizItem item : list) {
        List<Object> row = new ArrayList<>();
        // 固定列
        row.add(item.getPlatform());
        row.add(item.getProjectName());
        row.add(item.getMetricName());
        row.add(item.getAgentId());
        row.add(item.getAgentName());
        row.add(item.getUniqueKey());

        // 动态列
        Map<String, Object> answers = StringUtils.isNotBlank(item.getAnswersJson())
            ? JSON.parseObject(item.getAnswersJson(), Map.class)
            : new HashMap<>();
        for (String column : dynamicColumns) {
            row.add(answers.getOrDefault(column, ""));
        }
        dataRows.add(row);
    }

    // 4. 写入Excel
    EasyExcel.write(response.getOutputStream())
        .head(headers.stream().map(h -> Arrays.asList(h)).collect(Collectors.toList()))
        .sheet("业务数据")
        .doWrite(dataRows);
}
```

### 14.3 音频文件处理

#### 14.3.1 音频上传和存储
```java
@Service
public class AudioFileService {

    @Value("${fastdfs.tracker-servers}")
    private String trackerServers;

    public String uploadAudioFile(MultipartFile file) throws Exception {
        // 1. 初始化FastDFS客户端
        ClientGlobal.initByTrackers(trackerServers);
        TrackerClient trackerClient = new TrackerClient();
        TrackerServer trackerServer = trackerClient.getConnection();
        StorageClient storageClient = new StorageClient(trackerServer, null);

        // 2. 上传文件
        String[] uploadResult = storageClient.upload_file(
            file.getBytes(),
            FilenameUtils.getExtension(file.getOriginalFilename()),
            null
        );

        return uploadResult[0] + "/" + uploadResult[1]; // group/filename
    }

    public void downloadAudioFile(String fileId, HttpServletResponse response) throws Exception {
        String[] parts = fileId.split("/");
        String groupName = parts[0];
        String fileName = parts[1];

        // 获取文件信息
        FileInfo fileInfo = storageClient.get_file_info(groupName, fileName);
        byte[] fileBytes = storageClient.download_file(groupName, fileName);

        // 设置响应头
        response.setContentType("audio/wav");
        response.setContentLength((int) fileInfo.getFileSize());
        response.setHeader("Accept-Ranges", "bytes");

        // 支持断点续传
        String range = request.getHeader("Range");
        if (StringUtils.isNotBlank(range)) {
            handleRangeRequest(fileBytes, range, response);
        } else {
            response.getOutputStream().write(fileBytes);
        }
    }
}
```

#### 14.3.2 ASR集成处理
```java
@Component
public class AsrProcessor {

    @Async
    public void processAudioFile(Long datasetItemId) {
        QcDatasetItem item = datasetItemMapper.selectQcDatasetItemById(datasetItemId);

        try {
            // 1. 更新状态为处理中
            item.setAsrStatus("run");
            datasetItemMapper.updateQcDatasetItem(item);

            // 2. 调用ASR服务
            String audioFileUrl = buildAudioFileUrl(item.getFileId());
            AsrResult result = asrService.recognize(audioFileUrl);

            // 3. 更新识别结果
            item.setAsrResult(result.getText());
            item.setAsrStatus("done");
            datasetItemMapper.updateQcDatasetItem(item);

            // 4. 更新数据集进度
            updateDatasetProgress(item.getDatasetId());

        } catch (Exception e) {
            log.error("ASR处理失败", e);
            item.setAsrStatus("fail");
            datasetItemMapper.updateQcDatasetItem(item);
        }
    }
}
```

### 14.4 质检流程实现

#### 14.4.1 质检页面数据加载
```java
@GetMapping("/byNo/{bizNo}/{type}/{manualDetailId}")
public AjaxResult getQualityCheckData(@PathVariable String bizNo,
                                     @PathVariable String type,
                                     @PathVariable Long manualDetailId) {
    // 1. 获取业务数据
    QcBizItem bizItem = qcBizItemService.selectQcBizItemByNo(bizNo, type, manualDetailId);

    // 2. 获取关联的语音数据
    List<QcDatasetItem> audioItems = datasetItemService.selectByBizNo(bizNo);

    // 3. 获取质检模板
    QcScoringTemplate template = templateService.selectById(bizItem.getTempId());

    // 4. 获取历史质检记录
    List<QcManualTaskResult> historyResults = manualResultService.selectByBizNo(bizNo);

    // 5. 组装返回数据
    Map<String, Object> result = new HashMap<>();
    result.put("bizItem", bizItem);
    result.put("audioItems", audioItems);
    result.put("template", template);
    result.put("historyResults", historyResults);

    return AjaxResult.success(result);
}
```

#### 14.4.2 质检结果保存
```java
@PostMapping("/saveQualityResult")
@Transactional
public AjaxResult saveQualityResult(@RequestBody QcManualTaskResult result) {
    try {
        // 1. 保存质检结果
        result.setCreateTime(new Date());
        result.setCreateBy(SecurityUtils.getUsername());
        manualResultService.insertQcManualTaskResult(result);

        // 2. 更新业务数据状态
        QcBizItem bizItem = new QcBizItem();
        bizItem.setUniqueKey(result.getBizNo());
        bizItem.setQcLvl1Result(result.getQualityLevel());
        bizItem.setQcLvl1Comment(result.getComment());
        bizItem.setQcLvl1EmpId(SecurityUtils.getUsername());
        qcBizItemService.updateQcBizItem(bizItem);

        // 3. 更新任务状态
        updateTaskStatus(result.getTaskDetailId(), "completed");

        return AjaxResult.success("质检结果保存成功");
    } catch (Exception e) {
        log.error("保存质检结果失败", e);
        return AjaxResult.error("保存失败：" + e.getMessage());
    }
}
```

### 14.5 前端核心组件实现

#### 14.5.1 音频播放器组件
```vue
<template>
  <div class="audio-player">
    <div ref="waveform" class="waveform"></div>
    <div class="controls">
      <el-button @click="playPause" :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'">
        {{ isPlaying ? '暂停' : '播放' }}
      </el-button>
      <el-slider v-model="currentTime" :max="duration" @change="seekTo"></el-slider>
    </div>
  </div>
</template>

<script>
import WaveSurfer from 'wavesurfer.js'

export default {
  name: 'AudioPlayer',
  props: {
    audioUrl: String,
    annotations: Array
  },
  data() {
    return {
      wavesurfer: null,
      isPlaying: false,
      currentTime: 0,
      duration: 0
    }
  },
  mounted() {
    this.initWaveSurfer()
  },
  methods: {
    initWaveSurfer() {
      this.wavesurfer = WaveSurfer.create({
        container: this.$refs.waveform,
        waveColor: '#4FC3F7',
        progressColor: '#1976D2',
        responsive: true,
        height: 100
      })

      this.wavesurfer.load(this.audioUrl)

      this.wavesurfer.on('ready', () => {
        this.duration = this.wavesurfer.getDuration()
        this.loadAnnotations()
      })

      this.wavesurfer.on('audioprocess', () => {
        this.currentTime = this.wavesurfer.getCurrentTime()
      })
    },

    playPause() {
      this.wavesurfer.playPause()
      this.isPlaying = !this.isPlaying
    },

    seekTo(time) {
      this.wavesurfer.seekTo(time / this.duration)
    },

    loadAnnotations() {
      // 加载标注信息到波形图
      this.annotations.forEach(annotation => {
        this.wavesurfer.addRegion({
          start: annotation.startTime,
          end: annotation.endTime,
          color: annotation.color,
          data: annotation
        })
      })
    }
  }
}
</script>
```

#### 14.5.2 质检表单组件
```vue
<template>
  <div class="quality-check-form">
    <el-form :model="checkForm" :rules="rules" ref="checkForm">
      <el-form-item label="质检等级" prop="qualityLevel">
        <el-select v-model="checkForm.qualityLevel">
          <el-option label="优秀" value="excellent"></el-option>
          <el-option label="良好" value="good"></el-option>
          <el-option label="一般" value="normal"></el-option>
          <el-option label="较差" value="poor"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="评分项目">
        <div v-for="item in scoreItems" :key="item.id" class="score-item">
          <span>{{ item.name }}</span>
          <el-rate v-model="checkForm.scores[item.id]" :max="item.maxScore"></el-rate>
        </div>
      </el-form-item>

      <el-form-item label="质检评语" prop="comment">
        <el-input type="textarea" v-model="checkForm.comment" :rows="4"></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitCheck">提交质检</el-button>
        <el-button @click="saveAsDraft">保存草稿</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'QualityCheckForm',
  props: {
    bizItem: Object,
    template: Object
  },
  data() {
    return {
      checkForm: {
        qualityLevel: '',
        scores: {},
        comment: ''
      },
      rules: {
        qualityLevel: [{ required: true, message: '请选择质检等级', trigger: 'change' }],
        comment: [{ required: true, message: '请填写质检评语', trigger: 'blur' }]
      }
    }
  },
  computed: {
    scoreItems() {
      return this.template ? this.template.scoreItems : []
    }
  },
  methods: {
    submitCheck() {
      this.$refs.checkForm.validate(valid => {
        if (valid) {
          const result = {
            bizNo: this.bizItem.uniqueKey,
            qualityLevel: this.checkForm.qualityLevel,
            scores: this.checkForm.scores,
            comment: this.checkForm.comment,
            status: 'completed'
          }

          this.$emit('submit', result)
        }
      })
    },

    saveAsDraft() {
      const result = {
        bizNo: this.bizItem.uniqueKey,
        qualityLevel: this.checkForm.qualityLevel,
        scores: this.checkForm.scores,
        comment: this.checkForm.comment,
        status: 'draft'
      }

      this.$emit('save-draft', result)
    }
  }
}
</script>
```

## 15. 数据库优化策略

### 15.1 索引设计
```sql
-- qc_biz_item表索引
CREATE INDEX idx_dataset_id ON qc_biz_item(dataset_id);
CREATE INDEX idx_unique_key ON qc_biz_item(unique_key);
CREATE INDEX idx_agent_id ON qc_biz_item(agent_id);
CREATE INDEX idx_create_time ON qc_biz_item(create_time);
CREATE INDEX idx_tenant_id ON qc_biz_item(tenant_id);

-- qc_dataset_item表索引
CREATE INDEX idx_dataset_id ON qc_dataset_item(dataset_id);
CREATE INDEX idx_biz_no ON qc_dataset_item(biz_no);
CREATE INDEX idx_asr_status ON qc_dataset_item(asr_status);

-- qc_dataset表索引
CREATE INDEX idx_process_status ON qc_dataset(process_status);
CREATE INDEX idx_data_type ON qc_dataset(data_type);
```

### 15.2 分表策略
```sql
-- 按月分表示例
CREATE TABLE qc_biz_item_202501 LIKE qc_biz_item;
CREATE TABLE qc_biz_item_202502 LIKE qc_biz_item;
-- ... 其他月份表

-- 分表路由逻辑
@Component
public class TableShardingStrategy {
    public String getTableName(String baseTableName, Date createTime) {
        String suffix = DateFormatUtils.format(createTime, "yyyyMM");
        return baseTableName + "_" + suffix;
    }
}
```

## 16. 缓存策略

### 16.1 Redis缓存设计
```java
@Service
public class QcCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String DATASET_CACHE_KEY = "qc:dataset:";
    private static final String BIZITEM_CACHE_KEY = "qc:bizitem:";

    public QcDataset getDatasetFromCache(Long datasetId) {
        String key = DATASET_CACHE_KEY + datasetId;
        return (QcDataset) redisTemplate.opsForValue().get(key);
    }

    public void cacheDataset(QcDataset dataset) {
        String key = DATASET_CACHE_KEY + dataset.getId();
        redisTemplate.opsForValue().set(key, dataset, Duration.ofHours(1));
    }

    public void invalidateDatasetCache(Long datasetId) {
        String key = DATASET_CACHE_KEY + datasetId;
        redisTemplate.delete(key);
    }
}
```

## 17. 监控和告警

### 17.1 业务监控指标
```java
@Component
public class QcMetricsCollector {

    private final MeterRegistry meterRegistry;

    // 导入成功率
    private final Counter importSuccessCounter;
    private final Counter importFailureCounter;

    // ASR处理时长
    private final Timer asrProcessingTimer;

    // 质检完成率
    private final Gauge qualityCheckCompletionRate;

    public QcMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.importSuccessCounter = Counter.builder("qc.import.success").register(meterRegistry);
        this.importFailureCounter = Counter.builder("qc.import.failure").register(meterRegistry);
        this.asrProcessingTimer = Timer.builder("qc.asr.processing.time").register(meterRegistry);
    }

    public void recordImportSuccess() {
        importSuccessCounter.increment();
    }

    public void recordImportFailure() {
        importFailureCounter.increment();
    }

    public Timer.Sample startAsrTimer() {
        return Timer.start(meterRegistry);
    }
}
```

---

