package com.ideal.qc.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.ZjDataMapper;
import com.ideal.qc.domain.ZjData;
import com.ideal.qc.service.IZjDataService;

/**
 * 质检源数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-27
 */
@Service
public class ZjDataServiceImpl implements IZjDataService 
{
    @Autowired
    private ZjDataMapper zjDataMapper;

    /**
     * 查询质检源数据
     * 
     * @param id 质检源数据主键
     * @return 质检源数据
     */
    @Override
    public ZjData selectZjDataById(Long id)
    {
        return zjDataMapper.selectZjDataById(id);
    }

    /**
     * 查询质检源数据列表
     * 
     * @param zjData 质检源数据
     * @return 质检源数据
     */
    @Override
    public List<ZjData> selectZjDataList(ZjData zjData)
    {
        return zjDataMapper.selectZjDataList(zjData);
    }

    /**
     * 新增质检源数据
     * 
     * @param zjData 质检源数据
     * @return 结果
     */
    @Override
    public int insertZjData(ZjData zjData)
    {
        return zjDataMapper.insertZjData(zjData);
    }

    /**
     * 修改质检源数据
     * 
     * @param zjData 质检源数据
     * @return 结果
     */
    @Override
    public int updateZjData(ZjData zjData)
    {
        return zjDataMapper.updateZjData(zjData);
    }

    /**
     * 批量删除质检源数据
     * 
     * @param ids 需要删除的质检源数据主键
     * @return 结果
     */
    @Override
    public int deleteZjDataByIds(Long[] ids)
    {
        return zjDataMapper.deleteZjDataByIds(ids);
    }

    /**
     * 删除质检源数据信息
     * 
     * @param id 质检源数据主键
     * @return 结果
     */
    @Override
    public int deleteZjDataById(Long id)
    {
        return zjDataMapper.deleteZjDataById(id);
    }

    @Override
    public String getVideoText(String onlyNo) {
        return zjDataMapper.getVideoText(onlyNo);
    }

    @Override
    public List<String> getVideoTextMatchKey(String onlyNo) {
        return zjDataMapper.getVideoTextMatchKey(onlyNo);
    }
}
