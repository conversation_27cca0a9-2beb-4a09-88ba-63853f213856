package com.ideal.qc.service.impl;

import cn.hutool.core.io.FileUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.ideal.common.core.utils.DateUtils;
import com.ideal.qc.domain.QcDatasetItem;
import com.ideal.qc.mapper.QcDatasetItemMapper;
import com.ideal.qc.service.IQcDatasetItemService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;

/**
 * 语音数据明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class QcDatasetItemServiceImpl implements IQcDatasetItemService
{
    @Resource
    private QcDatasetItemMapper qcDatasetItemMapper;


    @Value("${audio.folder}")
    private String audioFolder; // 录音文件存储目录
    /**
     * 查询语音数据明细
     *
     * @param id 语音数据明细主键
     * @return 语音数据明细
     */
    @Override
    public QcDatasetItem selectQcDatasetItemById(Long id)
    {
        return qcDatasetItemMapper.selectQcDatasetItemById(id);
    }

    /**
     * 查询语音数据明细列表
     *
     * @param qcDatasetItem 语音数据明细
     * @return 语音数据明细
     */
    @Override
    public List<QcDatasetItem> selectQcDatasetItemList(QcDatasetItem qcDatasetItem)
    {
        return qcDatasetItemMapper.selectQcDatasetItemList(qcDatasetItem);
    }

    /**
     * 新增语音数据明细
     *
     * @param qcDatasetItem 语音数据明细
     * @return 结果
     */
    @Override
    public int insertQcDatasetItem(QcDatasetItem qcDatasetItem)
    {
        qcDatasetItem.setCreateTime(DateUtils.getNowDate());
        return qcDatasetItemMapper.insertQcDatasetItem(qcDatasetItem);
    }

    /**
     * 修改语音数据明细
     *
     * @param qcDatasetItem 语音数据明细
     * @return 结果
     */
    @Override
    public int updateQcDatasetItem(QcDatasetItem qcDatasetItem)
    {
        qcDatasetItem.setUpdateTime(DateUtils.getNowDate());
        return qcDatasetItemMapper.updateQcDatasetItem(qcDatasetItem);
    }

    /**
     * 批量删除语音数据明细
     *
     * @param ids 需要删除的语音数据明细主键
     * @return 结果
     */
    @Override
    public int deleteQcDatasetItemByIds(Long[] ids)
    {
        return qcDatasetItemMapper.deleteQcDatasetItemByIds(ids);
    }

    /**
     * 删除语音数据明细信息
     *
     * @param id 语音数据明细主键
     * @return 结果
     */
    @Override
    public int deleteQcDatasetItemById(Long id)
    {
        return qcDatasetItemMapper.deleteQcDatasetItemById(id);
    }


//
//    @Transactional
//    public void importExcel(MultipartFile excel, Long datasetId) throws Exception {
//        List<BizImportRow> rows = ExcelUtil.read(excel.getInputStream()); // 自行实现
//        for (BizImportRow r : rows) {
//            // 1. 处理录音
//            String fileId = null;
//            if (StringUtils.isNotBlank(r.getRecordingFilePath())) {
//                fileId = fastDfs.upload(new FileSystemResource(r.getRecordingFilePath()));
//            }
//            // 2. 写 item
//            QcBizItem item = r.toEntity();
//            item.setRecordingFile(fileId);
//            item.setDatasetId(datasetId);
//            itemMapper.insert(item);
//
//            // 3. 可选写答案表
//            r.getAnswers().forEach((k,v) -> answerMapper.insert(
//                    new QcBizAnswer(item.getId(), k, v)));
//        }
//    }
    /**
     * 通过 fileId 获取录音文件，用于外部调听
     * @param fileId 文件ID
     * @return 录音文件，如果不存在则返回null
     */
    @Override
    public File getAudioFileByFileId(String fileId) {
        if (StringUtils.isBlank(fileId)) return null;
        // 假设 fileId 格式为：日期批次号/UUID/文件名
        File targetFile = FileUtil.file(audioFolder, fileId);
        if (!targetFile.exists()) return null;
        return targetFile;
    }
}
