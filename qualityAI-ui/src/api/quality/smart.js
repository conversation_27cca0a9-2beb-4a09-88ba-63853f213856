import request from '@/utils/request'

// 查询质检规则列表
export function taskList(query,data) {
  return request({
    url: '/qc/smart/task/list',
    method: 'post',
    params: query,
	data:data
  })
}
export function detailList(query,data) {
  return request({
    url: '/qc/smart/task/detail/list',
    method: 'post',
    params: query,
	data:data
  })
}
export function resultList(query,data) {
  return request({
    url: '/qc/smart/task/result/list',
    method: 'post',
    params: query,
	data:data
  })
}
export function planList(query,data) {
  return request({
    url: '/qc/smart/task/plan/list',
    method: 'post',
    params: query,
	data:data
  })
}

// 修改质检规则
export function updateSmart(data) {
  return request({
    url: '/qc/smart/task/detail/update',
    method: 'post',
    data: data
  })
}
// 修改质检规则
export function updatePlan(data) {
  return request({
    url: '/qc/smart/task/plan/update',
    method: 'post',
    data: data
  })
}
export function execPlan(data) {
  return request({
    url: '/qc/smart/task/plan/exec',
    method: 'post',
    data: data
  })
}
export function createTask(data) {
  return request({
    url: '/qc/smart/task/create',
    method: 'post',
    data: data
  })
}
export function stopTask(data) {
  return request({
    url: '/qc/smart/task/stop',
    method: 'post',
    data: data
  })
}
export function addManualResult(data) {
  return request({
    url: '/qc/qcBICheck/addManual',
    method: 'post',
    data: data
  })
}
export function checkFilterForm(list,type){
	var tip = "";
	if(type == 1){
		for(var i in list){
			var tmp = list[i].ruleCondDetail.options
			for(var j in tmp){
				var item2 = tmp[j]
				if(!item2.key ||item2.key == ''){
				}else if(!item2.op ||item2.op == ''){
				}else if(item2.op == 'less' || item2.op == 'more' || item2.op == 'equal'){
					if(item2.val1 && item2.val1!='' && item2.val1 != null){
						continue;
					}
				}else if(item2.op == 'in' || item2.op == 'nIn'){
					if(item2.val1 && item2.val1!='' && item2.val1 != null){
						if(item2.val2 && item2.val2!='' && item2.val2 != null){
							continue;
						}
					}
				}else if(item2.op == 'contain' || item2.op == 'nContain'){
					if(item2.inputKeys && item2.inputKeys != null && item2.inputKeys.length > 0 ){
						continue;
					}
				}
				return "业务标签第"+(parseInt(j)+1)+"行配置不正确";
			}
		}
		return undefined;
	}else if(type == 2){
		if(list.length<1){
			return "至少配置一个命中条件组合";
		}
		for(var i in list){
			var tmp = list[i].ruleCondDetail.options
			for(var j in tmp){
				var item2 = tmp[j]
				if(item2.rule==1){
					if(list[i].semanticsIds && list[i].semanticsIds != null && list[i].semanticsIds != ''){
						continue;
					}
				}else if(item2.rule==2){
					if(item2.dynamicTags && item2.dynamicTags.length >0){
						continue;
					}
				}else if(item2.rule==3){
					if(item2.keyWord && item2.keyWord !='' && item2.keyWord !=null && item2.kwsCount && item2.kwsCount !='' && item2.kwsCount!=null){
						continue;
					}
				}else if(item2.rule==4){
					if(item2.dynamicTags && item2.dynamicTags.length >0){
						continue;
					}
				}else if(item2.rule==5){
					if(item2.reg && item2.reg != null && item2.reg !='' ){
						continue;
					}
				}else if(item2.rule==6){
					if(item2.speed && item2.speed != null && item2.speed != '' ){
						continue;
					}
				}
				return "规则命中条件-条件组合"+(parseInt(i)+1)+" 第"+(parseInt(j)+1)+"行配置不正确";
			}
		}
		return undefined;
	}
}