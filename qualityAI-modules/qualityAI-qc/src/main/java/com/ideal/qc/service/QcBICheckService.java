package com.ideal.qc.service;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcDataResult;
import com.ideal.qc.domain.QcManualTaskResult;

import java.util.List;
import java.util.Map;

public interface QcBICheckService {
    List<Map<String,Object>> getVideoText(String bizNo);

    List<String> getVideoTextMatchKey(String bizNo);

    void insertQcDataResult(QcDataResult qcDataResult);

    List<QcDataResult> getResultListByBizNo(String bizNo);

    String tempFirstList(String bizNo);

    String tempAIList(String bizNo);

    void updateManualStatus(QcDataResult qcDataResult);

    void updateSmartStatus(QcDataResult qcDataResult);

    List<QcManualTaskResult> getManualResult(String bizNo);

    void addQcManualTaskResult(QcManualTaskResult manual);

    Map<String, String> getQcScoringHitRuleGroup(Long groupId);

    Map<String,Object> getAiResultByCondition(Map<String, String> param);

    void updateAiSmartResult(QcManualTaskResult manual);

    void addQcSmartTaskResult(QcManualTaskResult param);

    List<QcManualTaskResult> getSmartCheckList(String bizNo);

    void updateSmartCountStatus(QcDataResult qcDataResult);

    void updateManualResult(JSONObject json);

    List<QcManualTaskResult> manualResultList(String taskDetailId);

    List<QcDataResult> getResultByDetailId(String manualDetailId);
}
