package com.ideal.qc.service;

import com.ideal.qc.domain.Question;
import com.ideal.qc.domain.QuestionAnswer;

import java.util.List;

/**
 * 题目信息 服务层
 * 
 * <AUTHOR>
public interface IQuestionService
{
    /**
     * 查询题目信息集合
     * 
     * @param post 题目信息
     * @return 题目列表
     */
    public List<Question> selectQusList(Question post);

    /**
     * 查询所有题目
     * 
     * @return 题目列表
     */
    public List<Question> selectQusAll();

    /**
     * 通过题目ID查询题目信息
     * 
     * @param postId 题目ID
     * @return 角色对象信息
     */
    public Question selectQusById(Long postId);


    /**
     * 校验题目名称
     * 
     * @param post 题目信息
     * @return 结果
     */
    public boolean checkQusNameUnique(Question post);

    /**
     * 校验题目编码
     * 
     * @param post 题目信息
     * @return 结果
     */
    public boolean checkQusCodeUnique(Question post);

    /**
     * 删除题目信息
     * 
     * @param postId 题目ID
     * @return 结果
     */
    public int deleteQusById(Long postId);

    /**
     * 批量删除题目信息
     * 
     * @param postIds 需要删除的题目ID
     * @return 结果
     */
    public int deleteQusByIds(Long[] postIds);

    /**
     * 新增保存题目信息
     * 
     * @param post 题目信息
     * @return 结果
     */
    public int insertQus(Question post);

    /**
     * 修改保存题目信息
     * 
     * @param post 题目信息
     * @return 结果
     */
    public int updateQus(Question post);

    void insertQusAnswer(QuestionAnswer qa);

    void delQusAnswer(Long id);
}
