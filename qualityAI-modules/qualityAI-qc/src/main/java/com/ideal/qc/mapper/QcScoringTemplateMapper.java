package com.ideal.qc.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcScoringTemplate;
import com.ideal.qc.dto.QcScoringTemplateDTO;
import com.ideal.qc.vo.QcScoringTemplateVo;

import java.util.List;

/**
 * 评分模板表Mapper接口
 *
 * <AUTHOR>
 * @date 2025/5/14 14:43
 */
public interface QcScoringTemplateMapper {

    /**
     * 查询评分模板列表
     *
     * @param payload 需要的一些参数
     * @return 评分模板列表
     */
    List<QcScoringTemplate> getQcScoringTemplateList(JSONObject payload);

    /**
     * 新增评分模板
     *
     * @param payload 需要的一些参数
     */
    void addQcScoringTemplate(JSONObject payload);

    /**
     * 修改评分模板
     *
     * @param payload 需要的一些参数
     */
    void updateQcScoringTemplate(JSONObject payload);

    /**
     * 删除评分模板
     *
     * @param ids ids
     */
    void removeQcScoringTemplate(Long[] ids);

    /**
     * 查询评分模板详情根据评分模板ID
     *
     * @param id 评分模板ID
     * @return 评分模板详情
     */
    QcScoringTemplateVo getQcScoringTemplate(Long id);

    /**
     * 查询所有状态正常的评分模板
     *
     * @return 所有状态正常的评分模板
     */
    List<QcScoringTemplate> getAllQcScoringTemplate();
}
