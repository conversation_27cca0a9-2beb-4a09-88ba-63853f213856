<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.system.mapper.SysTenantMapper">

    <resultMap type="SysTenant" id="SysTenantResult">
        <result property="tenantId"    column="tenant_id"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="openTime" column="open_time"/>
        <result property="expireTime" column="expire_time"/>
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysTenantVo">
        select tenant_id, tenant_name, open_time, expire_time, create_time, update_time from sys_tenant where tenant_id > 0
    </sql>

    <select id="selectSysTenantList" parameterType="SysTenant" resultMap="SysTenantResult">
        <include refid="selectSysTenantVo"/>
        <if test="tenantName != null  and tenantName != ''"> and tenant_name like concat('%', #{tenantName}, '%')</if>
    </select>

    <select id="selectSysTenantByTenantId" parameterType="Long" resultMap="SysTenantResult">
        select tenant_id, tenant_name, open_time, expire_time, create_time, update_time from sys_tenant where tenant_id = #{tenantId}
    </select>

    <insert id="insertSysTenant" parameterType="SysTenant" useGeneratedKeys="true" keyProperty="tenantId">
        insert into sys_tenant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantName != null and tenantName != ''">tenant_name,</if>
            <if test="openTime != null">open_time,</if>
            <if test="expireTime != null">expire_time,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantName != null and tenantName != ''">#{tenantName},</if>
            <if test="openTime != null">#{openTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateSysTenant" parameterType="SysTenant">
        update sys_tenant
        <trim prefix="SET" suffixOverrides=",">
            <if test="tenantName != null and tenantName != ''">tenant_name = #{tenantName},</if>
            <if test="openTime != null">open_time = #{openTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            update_Time = sysdate()
        </trim>
        where tenant_id = #{tenantId}
    </update>

    <delete id="deleteSysTenantByTenantId" parameterType="Long">
        delete from sys_tenant where tenant_id = #{tenantId}
    </delete>

    <delete id="deleteSysTenantByTenantIds" parameterType="String">
        delete from sys_tenant where tenant_id in
        <foreach item="tenantId" collection="array" open="(" separator="," close=")">
            #{tenantId}
        </foreach>
    </delete>

    <select id="selectMenus" resultType="com.ideal.system.domain.SysTenantMenu">
        SELECT
            m.menu_id, m.parent_id, m.menu_name, m.visible,
            m.status, m.menu_type, m.icon, m.order_num
        FROM sys_menu m
        WHERE m.status = '0'
        ORDER BY m.parent_id, m.order_num
    </select>

    <select id="selectMenuIdsByTenant" resultType="Long">
        SELECT menu_id FROM sys_tenant_menu WHERE tenant_id = #{tenantId}
    </select>
</mapper>