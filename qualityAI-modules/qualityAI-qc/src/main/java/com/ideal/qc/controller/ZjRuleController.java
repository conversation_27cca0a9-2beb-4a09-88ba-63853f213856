package com.ideal.qc.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.ZjRule;
import com.ideal.qc.service.IZjRuleService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 质检规则Controller
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@RestController
//@RequestMapping("/rule")
public class ZjRuleController extends BaseController
{
    @Autowired
    private IZjRuleService zjRuleService;

    /**
     * 查询质检规则列表
     */
    @RequiresPermissions("quality:rule:list")
    @GetMapping("/list")
    public TableDataInfo list(ZjRule zjRule)
    {
        startPage();
        List<ZjRule> list = zjRuleService.selectZjRuleList(zjRule);
        return getDataTable(list);
    }

    /**
     * 导出质检规则列表
     */
    @RequiresPermissions("quality:rule:export")
    @Log(title = "质检规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZjRule zjRule)
    {
        List<ZjRule> list = zjRuleService.selectZjRuleList(zjRule);
        ExcelUtil<ZjRule> util = new ExcelUtil<ZjRule>(ZjRule.class);
        util.exportExcel(response, list, "质检规则数据");
    }

    /**
     * 获取质检规则详细信息
     */
    @RequiresPermissions("quality:rule:query")
    @GetMapping(value = "/{ruleId}")
    public AjaxResult getInfo(@PathVariable("ruleId") Long ruleId)
    {
        return success(zjRuleService.selectZjRuleByRuleId(ruleId));
    }

    /**
     * 新增质检规则
     */
    @RequiresPermissions("quality:rule:add")
    @Log(title = "质检规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZjRule zjRule)
    {
        return toAjax(zjRuleService.insertZjRule(zjRule));
    }

    /**
     * 修改质检规则
     */
    @RequiresPermissions("quality:rule:edit")
    @Log(title = "质检规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZjRule zjRule)
    {
        return toAjax(zjRuleService.updateZjRule(zjRule));
    }

    /**
     * 删除质检规则
     */
    @RequiresPermissions("quality:rule:remove")
    @Log(title = "质检规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ruleIds}")
    public AjaxResult remove(@PathVariable Long[] ruleIds)
    {
        return toAjax(zjRuleService.deleteZjRuleByRuleIds(ruleIds));
    }
}
