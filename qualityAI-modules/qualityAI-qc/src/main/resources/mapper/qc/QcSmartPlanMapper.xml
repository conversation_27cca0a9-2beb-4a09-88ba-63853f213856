<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcSmartPlanMapper">

	
	<resultMap type="QcSmartPlan" id="QcSmartPlanResult">
		<id property="id" column="id" />
		<result property="planType" column="plan_type" />
		<result property="planExecType" column="plan_exec_type" />
		<result property="planName" column="plan_name" />
		<result property="planExecTime" column="plan_exec_time" />
		<result property="dataBegTime" column="data_beg_time" />
		<result property="dataEndTime" column="data_end_time" />
		<result property="scoreTemplate" column="score_template" />
		<result property="filterEmptySession" column="filter_empty_session" />
		<result property="followMatchDetail" column="follow_match_detail" />
		<result property="createTime" column="create_time" />
		<result property="createBy" column="create_by" />
		<result property="updateTime" column="update_time" />
		<result property="updateBy" column="update_by" />
		<result property="remark" column="remark" />
		<result property="status" column="status" />
		<result property="datasetId" column="dataset_id" />
		<result property="taskName" column="task_name" />
		<result property="datasetPlanId" column="dataset_plan_id" />
		<collection property="timeList" select="selectQcSmartPlanTimeList" column="id" />
	</resultMap>
	<sql id="selectQcSmartPlanVo">
		select qsp.id,plan_type,plan_exec_type,plan_name,score_template,filter_empty_session,follow_match_detail,create_time,create_by,update_time,update_by,remark,status,dataset_id,task_name,dataset_plan_id
		<if test="planExecTime != null  and  planExecTime != ''">
		,qspt.plan_exec_time,qspt.data_beg_time,qspt.data_end_time
		</if>
		from qc_smart_plan qsp 
		<if test="planExecTime != null  and  planExecTime != ''">
			left join qc_smart_plan_time qspt on qsp.id=qspt.plan_id and qspt.plan_exec_time=#{planExecTime}
		
		</if>
	</sql>

	<!--根据id查询智能质检计划	-->
	<select id="selectById" resultMap="QcSmartPlanResult">
		select id,plan_type,plan_exec_type,plan_name,score_template,filter_empty_session,follow_match_detail,
		create_time,create_by,update_time,update_by,remark,status,dataset_id,task_name,dataset_plan_id
		from qc_smart_plan qsp
		where id = #{id}
	</select>

	<sql id="insertQcSmartPlanSql" >
		insert into qc_smart_plan
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="planType != null and planType != ''">plan_type,</if>
			<if test="planExecType != null and planExecType != ''">plan_exec_type,</if>
			<if test="planName != null and planName != ''">plan_name,</if>
			<if test="scoreTemplate != null and scoreTemplate != ''">score_template,</if>
			<if test="filterEmptySession != null and filterEmptySession != ''">filter_empty_session,</if>
			<if test="followMatchDetail != null and followMatchDetail != ''">follow_match_detail,</if>
			create_time,
			<if test="createBy != null and createBy != ''">create_by,</if>
			<if test="remark != null and remark != ''">remark,</if>
			status,
			<if test="datasetId != null and datasetId != ''">dataset_id,</if>
			<if test="taskName != null and taskName != ''">task_name,</if>
			<if test="datasetPlanId != null and datasetPlanId != ''">dataset_plan_id,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="planType != null and planType != ''">#{planType},</if>
			<if test="planExecType != null and planExecType != ''">#{planExecType},</if>
			<if test="planName != null and planName != ''">#{planName},</if>
			<if test="scoreTemplate != null and scoreTemplate != ''">#{scoreTemplate},</if>
			<if test="filterEmptySession != null and filterEmptySession != ''">#{filterEmptySession},</if>
			<if test="followMatchDetail != null and followMatchDetail != ''">#{followMatchDetail},</if>
			now(),
			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<if test="remark != null and remark != ''">#{remark},</if>
			1,
			<if test="datasetId != null and datasetId != ''">#{datasetId},</if>
			<if test="taskName != null and taskName != ''">#{taskName},</if>
			<if test="datasetPlanId != null and datasetPlanId != ''">#{datasetPlanId},</if>
		</trim>
	</sql>
	<sql id="insertQcSmartPlanTimeSql" >
		insert into qc_smart_plan_time
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="item.planExecTime != null and item.planExecTime != ''">plan_exec_time,</if>
			<if test="item.dataBegTime != null and item.dataBegTime != ''">data_beg_time,</if>
			<if test="item.dataEndTime != null and item.dataEndTime != ''">data_end_time,</if>
			plan_id
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="item.planExecTime != null and item.planExecTime != ''">#{item.planExecTime},</if>
			<if test="item.dataBegTime != null and item.dataBegTime != ''">#{item.dataBegTime},</if>
			<if test="item.dataEndTime != null and item.dataEndTime != ''">#{item.dataEndTime},</if>
			#{id}
		</trim>
	</sql>
	<sql id="updateQcSmartPlanSql" >
		update qc_smart_plan
		<trim prefix="SET" suffixOverrides=",">
			<if test="planType != null and planType != ''">plan_type=#{planType},</if>
			<if test="planExecType != null and planExecType != ''">plan_exec_type=#{planExecType},</if>
			<if test="planName != null and planName != ''">plan_name=#{planName},</if>
			<if test="planExecTime != null and planExecTime != ''">plan_exec_time=#{planExecTime},</if>
			<if test="dataBegTime != null and dataBegTime != ''">data_beg_time=#{dataBegTime},</if>
			<if test="dataEndTime != null and dataEndTime != ''">data_end_time=#{dataEndTime},</if>
			<if test="scoreTemplate != null and scoreTemplate != ''">score_template=#{scoreTemplate},</if>
			<if test="filterEmptySession != null and filterEmptySession != ''">filter_empty_session=#{filterEmptySession},</if>
			<if test="followMatchDetail != null and followMatchDetail != ''">follow_match_detail=#{followMatchDetail},</if>
			update_time=now(),
			<if test="updateBy != null and updateBy != ''">update_by=#{updateBy},</if>
			<if test="remark != null and remark != ''">remark=#{remark},</if>
			<if test="status != null and status != ''">status=#{status},</if>
			<if test="datasetId != null and datasetId != ''">dataset_id=#{datasetId},</if>
			<if test="taskName != null and taskName != ''">task_name=#{taskName},</if>
			<if test="datasetPlanId != null and datasetPlanId != ''">dataset_plan_id=#{datasetPlanId},</if>
		</trim>
 		where id=#{id}
	</sql>
	<sql id="updateQcSmartPlanTimeSql" >
		update qc_smart_plan_time
		<trim prefix="SET" suffixOverrides=",">
			<if test="item.planExecTime != null and item.planExecTime != ''">plan_exec_time=#{item.planExecTime},</if>
			<if test="item.dataBegTime != null and item.dataBegTime != ''">data_beg_time=#{item.dataBegTime},</if>
			<if test="item.dataEndTime != null and item.dataEndTime != ''">data_end_time=#{item.dataEndTime},</if>
		</trim>
 		where id=#{item.id}
	</sql>
	<sql id="deleteQcSmartPlanTimeSql" >
		delete from qc_smart_plan_time where id in
		<foreach item="item" collection="deleteList" open="(" separator="," close=")">
			#{item.id}
 		</foreach>
	</sql>
	<select id="selectQcSmartPlanList" parameterType="QcSmartPlan" resultMap="QcSmartPlanResult">
		<include refid="selectQcSmartPlanVo" />
		<where>
			<if test="id != null and id != ''">and qsp.id=#{id}</if>
			<if test="planType != null and planType != ''">and plan_type=#{planType}</if>
			<if test="planExecType != null and planExecType != ''">and plan_exec_type=#{planExecType}</if>
			<if test="planName != null and planName != ''">and plan_name like concat('%',#{planName},'%')</if>
			<if test="planExecTime != null and planExecTime != ''">and qspt.plan_exec_time=#{planExecTime}</if>
			<if test="scoreTemplate != null and scoreTemplate != ''">and score_template=#{scoreTemplate}</if>
			<if test="filterEmptySession != null and filterEmptySession != ''">and filter_empty_session=#{filterEmptySession}</if>
			<if test="followMatchDetail != null and followMatchDetail != ''">and follow_match_detail=#{followMatchDetail}</if>
			<if test="createTime != null and createTime != ''">and create_time=#{createTime}</if>
			<if test="createTimeStr != null and createTimeStr.size() == 2">and create_time between #{createTimeStr[0]} and #{createTimeStr[1]}</if>
			<if test="createBy != null and createBy != ''">and create_by=#{createBy}</if>
			<if test="updateTime != null and updateTime != ''">and update_time=#{updateTime}</if>
			<if test="updateBy != null and updateBy != ''">and update_by=#{updateBy}</if>
			<if test="remark != null and remark != ''">and remark=#{remark}</if>
			<if test="status != null and status != ''">and status=#{status}</if>
			<if test="datasetId != null and datasetId != ''">and dataset_id=#{datasetId}</if>
			<if test="datasetPlanId != null and datasetPlanId != ''">and dataset_plan_id=#{datasetPlanId}</if>
		</where>
	</select>
	<select id="selectQcSmartPlanTimeList" resultType="map">
		select id,plan_exec_time as planExecTime,data_beg_time as dataBegTime,data_end_time as dataEndTime from qc_smart_plan_time where plan_id=#{id}
	</select>
	<insert id="insertQcSmartPlan">
		<include refid="insertQcSmartPlanSql"/>
		<foreach item="item" collection="timeList" separator=";" >
			<include refid="insertQcSmartPlanTimeSql"/>
		</foreach>
	</insert>
	<update id="updateQcSmartPlan" parameterType="map" keyProperty="id" useGeneratedKeys="true">
		<if test="id == null or id == ''">
			<include refid="insertQcSmartPlanSql"/>
		</if>
		<if test="id != null and id != ''">
			<include refid="updateQcSmartPlanSql"/>
			<if test="timeList != null and timeList.size() > 0">
				<foreach item="item" collection="timeList"  >
					<if test="item.id == null or item.id == ''">
						;<include refid="insertQcSmartPlanTimeSql"/>
					</if>
					<if test="item.id != null and item.id != ''">
						;<include refid="updateQcSmartPlanTimeSql"/>
					</if>
				</foreach>
			</if>
			<if test="deleteList != null and deleteList.size() > 0">
				<foreach item="item" collection="deleteList" >
					;<include refid="deleteQcSmartPlanTimeSql"/>
				</foreach>
			</if>
		</if>
	</update>

</mapper>