package com.ideal.qc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.constant.SecurityConstants;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.redis.service.RedisService;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.*;
import com.ideal.qc.mapper.*;
import com.ideal.system.api.RemoteUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/manual/task")
public class QcManualTaskController extends BaseController {

    @Autowired
    private QcManualTaskMapper qcManualTaskMapper;

    @Autowired
    private QcManualTaskDetailMapper qcManualTaskDetailMapper;

    @Autowired
    private QcManualTaskResultMapper qcManualTaskResultMapper;


    @Autowired
    private RemoteUserService remoteUserService;


    @Autowired
    private QcSmartTaskMapper qcSmartTaskMapper;

    @Autowired
    private QcBizItemMapper qcBizItemMapper;

    @Autowired
    private QcScoringTemplateMapper qcScoringTemplateMapper;
    @Autowired
    private RedisService redisService;

    /**
     * 模糊查询人工质检任务列表
     */
    @GetMapping("/list")
    public AjaxResult list(@RequestParam Map<String, Object> params)
    {
        String beginTime = "";
        String endTime = "";
        if (null != params.get("dateRange[0]") && null != params.get("dateRange[1]")){
             beginTime = params.get("dateRange[0]").toString();
             endTime = params.get("dateRange[1]").toString();
            if (beginTime.equals(endTime)){
                //如果开始时间和结束时间是同一天，手动改为00:00:00 到 23:59:59
                beginTime += " 00:00:00";
                endTime += " 23:59:59";
            }
        }
        Map<String,Object> map = new HashMap<>();
        map.put("taskName",params.get("taskName"));
        map.put("taskType",params.get("taskType"));
        map.put("beginTime",beginTime);
        map.put("endTime",endTime);
        //sessionCountFlag用于人工质检台查询任务列表，过滤掉会话数为0的任务,且过滤掉已禁用/已回收的任务
        map.put("sessionCountFlag",true);
        List<QcManualTask> list = new ArrayList<>();
        //获取当前登录用户的用户名
        String username = SecurityUtils.getUsername();
        Set<String> roles = SecurityUtils.getLoginUser().getRoles();
        if (roles.contains("manager") || roles.contains("admin")){
            //如果当前用户是管理员，则查询所有的任务
            list = qcManualTaskMapper.selectQcManualTaskList(map);
        }else {
            //如果是其他角色，则获取该用户能查看的用户范围
            map.put("visibleUsers",remoteUserService.getGroupMemberListByGroupLeaderUserName(SecurityConstants.INNER).getData());
            list = qcManualTaskMapper.selectQcManualTaskListForRole(map);
        }
        return success(list);
    }


    /**
     * 分页查询人工质检任务列表
     */
    @GetMapping("/list/page")
    public TableDataInfo pageList(@RequestParam Map<String, Object> params)
    {
        String beginTime = "";
        String endTime = "";
        if (null != params.get("dateRange[0]") && null != params.get("dateRange[1]")){
            beginTime = params.get("dateRange[0]").toString();
            endTime = params.get("dateRange[1]").toString();
            if (beginTime.equals(endTime)){
                //如果开始时间和结束时间是同一天，手动改为00:00:00 到 23:59:59
                beginTime += " 00:00:00";
                endTime += " 23:59:59";
            }
        }
        Map<String,Object> map = new HashMap<>();
        map.put("taskName",params.get("taskName"));
        map.put("taskType",params.get("taskType"));
        map.put("beginTime",beginTime);
        map.put("endTime",endTime);
        List<QcManualTask> list = new ArrayList<>();

        //获取当前登录用户的用户名
        String username = SecurityUtils.getUsername();
        Set<String> roles = SecurityUtils.getLoginUser().getRoles();
        if (roles.contains("manager") || roles.contains("admin")){
            //如果当前用户是管理员，则查询所有的任务
            startPage();
            list = qcManualTaskMapper.selectQcManualTaskList(map);
        }else {
            //如果是其他角色，则获取该用户能查看的用户范围
            map.put("visibleUsers",remoteUserService.getGroupMemberListByGroupLeaderUserName(SecurityConstants.INNER).getData());
            startPage();
            list = qcManualTaskMapper.selectQcManualTaskListForRole(map);
        }
        return getDataTable(list);
    }



    /**
     * 禁用/启用任务
     */
    @PostMapping("/enable/status")
    @Transactional
    public AjaxResult updateEnableStatus(@RequestBody QcManualTask qcManualTask)
    {
        try {
            qcManualTask.setUpdateBy(SecurityUtils.getUsername());
            qcManualTask.setUpdateTime(new Date());
            qcManualTaskMapper.updateQcManualTask(qcManualTask);
            if ("0".equals(qcManualTask.getEnableStatus())) {
                //如果是禁用任务，需要回收当前任务下未完成的的任务明细
                recycleTask(qcManualTask);
            }

        } catch (Exception e) {
            logger.error("修改人工质检任务出错:",e);
            throw new RuntimeException(e.getMessage());
        }
        return success();
    }



    public void recycleTask(QcManualTask qcManualTask){
        //查询当前任务下未完成的任务明细数量
        int notFinishCount = qcManualTaskDetailMapper.getNotFinishCount(qcManualTask.getId());
        //将未完成的任务明细状态为已回收
        qcManualTaskDetailMapper.recycleDetail(qcManualTask.getId());
        //回收完成后,修改当前任务的会话数量，状态
        qcManualTask.setSessionCount(qcManualTask.getSessionCount() - notFinishCount);
        qcManualTask.setStatus("2");
        qcManualTaskMapper.updateQcManualTask(qcManualTask);

    }

    /**
     * 修改任务具体内容
     */
    @PostMapping("/update")
    @Transactional
    public AjaxResult updateTask(@RequestBody JSONObject payload)
    {
        try {
            QcManualTask qcManualTask = new QcManualTask();
            qcManualTask.setId(payload.getLong("id"));
            qcManualTask.setTaskName(payload.getString("taskName"));
            qcManualTask.setManualType(payload.getString("manualType"));
            qcManualTask.setDataRange(payload.getString("dataRange"));
            qcManualTask.setExtractType(payload.getString("extractType"));
            qcManualTask.setExtractValue(payload.getInteger("extractValue"));
            qcManualTask.setSmartPlan(payload.getLong("smartTask"));
            qcManualTask.setTemplateId(payload.getLong("templateId"));
            qcManualTask.setHitRules(payload.getJSONArray("hitRules").toJSONString());
//        qcManualTask.setInspectors(payload.getJSONArray("inspectors").toJSONString());
            qcManualTask.setInspectorRate(payload.getJSONArray("inspectorRate").toJSONString());
            qcManualTask.setSessionCount(payload.getInteger("sessionCount"));
            qcManualTask.setFinishCount(payload.getInteger("finishCount"));

            qcManualTask.setStatus("0");
            qcManualTask.setEnableStatus("1");
            qcManualTask.setUpdateBy(SecurityUtils.getUsername());
            qcManualTask.setUpdateTime(new Date());
            //先调用方法单独修改任务明细，如果是人工复检，页面允许修改机检计划、命中规则等为空，如果放在后面分配任务时统一修改，这些字段为空时不会生效
            qcManualTaskMapper.updateQcManualTaskDetail(qcManualTask);
            //修改后重新执行任务并分配
            //如果是人工抽检，则去原始数据表筛选数据
            if("1".equals(qcManualTask.getManualType())){
                samplingAndAllocation(qcManualTask);
            }
            //如果是人工复检,则去智能质检结果表筛选数据
            if ("2".equals(qcManualTask.getManualType())){
                recheckAndAllocation(qcManualTask);
            }
        } catch (Exception e) {
            logger.error("修改人工质检任务出错:",e);
            throw new RuntimeException(e.getMessage());
        }
        return success();
    }


    /**
     * 新增任务
     */
    @PostMapping("/add")
    @Transactional
    public AjaxResult addTask(@RequestBody JSONObject payload)
    {
        try {
            QcManualTask qcManualTask = new QcManualTask();
            qcManualTask.setTaskName(payload.getString("taskName"));
            //页面直接添加的任务为手动任务
            qcManualTask.setTaskType("0");
            qcManualTask.setManualType(payload.getString("manualType"));
            qcManualTask.setDataRange(payload.getString("dataRange"));
            qcManualTask.setExtractType(payload.getString("extractType"));
            qcManualTask.setExtractValue(payload.getInteger("extractValue"));
            qcManualTask.setSmartPlan(payload.getLong("smartTask"));
            qcManualTask.setTemplateId(payload.getLong("templateId"));
            qcManualTask.setHitRules(payload.getJSONArray("hitRules").toJSONString());
            qcManualTask.setInspectorRate(payload.getJSONArray("inspectorRate").toJSONString());
            qcManualTask.setStatus("0");
            qcManualTask.setEnableStatus("1");
            //手动添加任务时，会话数量和已完成数量默认为0，方便后续修改任务重新分配时不用单独修改会话数量字段，直接在原有会话数量上累加
            qcManualTask.setSessionCount(0);
            qcManualTask.setFinishCount(0);
            qcManualTask.setCreateBy(SecurityUtils.getUsername());
            qcManualTask.setCreateTime(new Date());
            //将任务落表，并获取任务id
            qcManualTaskMapper.insertQcManualTask(qcManualTask);
            //如果是人工抽检，则去原始数据表筛选数据
            if("1".equals(qcManualTask.getManualType())){
                samplingAndAllocation(qcManualTask);
            }
            //如果是人工复检,则去智能质检结果表筛选数据
            if ("2".equals(qcManualTask.getManualType())){
                recheckAndAllocation(qcManualTask);
            }
        } catch (Exception e) {
            logger.error("新建手动人工质检任务出错:",e);
            throw new RuntimeException(e.getMessage());
        }
        return success();
    }


    /**
     * 获取所有已完成的智能质检任务
     */

    @GetMapping("/smart/task/list")
    public AjaxResult getSmartTaskList(){
        List<QcSmartTask> qcSmartTasks = qcSmartTaskMapper.selectQcSmartTaskFinished();
        return success(qcSmartTasks);
    }


    /**
     * 根据id查询智能质检任务
     */
    @GetMapping("/smart/task/get/{id}")
    public AjaxResult getSmartTaskById(@PathVariable("id") Long id){
        QcSmartTask qcSmartTask = qcSmartTaskMapper.selectQcSmartTaskById(id);
        return success(qcSmartTask);
    }


    /**
     * 筛选人工抽检的数据并分配到质检员
     */
    public void samplingAndAllocation(QcManualTask qcManualTask){
        //获取数据时间范围
        String dataRange = qcManualTask.getDataRange();
        String dataRangeDate = "";
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if ("1".equals(dataRange)){
            //一小时内
            dataRangeDate = now.minusHours(1).format(formatter);
        }else if ("2".equals(dataRange)){
            //两小时内
            dataRangeDate = now.minusHours(2).format(formatter);
        }else if("3".equals(dataRange)){
            //四小时内
            dataRangeDate = now.minusHours(4).format(formatter);
        }else if ("4".equals(dataRange)){
            //八小时内
            dataRangeDate = now.minusHours(8).format(formatter);
        }else if ("5".equals(dataRange)){
            //24小时内
            dataRangeDate = now.minusHours(24).format(formatter);
        }else if ("6".equals(dataRange)){
            //七天内
            dataRangeDate = now.minusDays(7).format(formatter);
        }
        //组装查询条件
        Map<String,Object> map = new HashMap<>();
        map.put("dataRangeDate",dataRangeDate);
        List<QcBizItem> qcBizItems = qcBizItemMapper.selectDataForSampling(map);
        //新建手动任务或修改任务时，如果筛选出的会话数为0，则不做分配
//        if (null == qcBizItems || qcBizItems.size() == 0){
//            throw new RuntimeException("当前任务未筛选到会话,请重新选择");
//        }
        //根据页面配置的抽取数量切割list
        String extractType = qcManualTask.getExtractType();
        Integer extractValue = qcManualTask.getExtractValue();
        List<QcBizItem> resultList = new ArrayList<>();
        int originalSize = qcBizItems.size();
        if ("count".equals(extractType)) {
            // 按数量切割
            int count = Math.min(extractValue, originalSize); // 确保不超过原列表大小
            resultList = qcBizItems.subList(0, count);
        } else if ("percent".equalsIgnoreCase(extractType)) {
            // 按百分比切割
            int count = (int) Math.floor(originalSize * extractValue / 100.0); // 向上取整
            resultList = qcBizItems.subList(0, count);
        }

        //设置任务的会话数量(在原有会话数量上累加，因为修改和新增任务共用一个方法)
        qcManualTask.setSessionCount(qcManualTask.getSessionCount() + resultList.size());
        //将筛选完成的数据，根据比例分配给质检员并落到人工质检任务详情表
        //定义Map,key表示质检员工号，value表示比例
        Map<String,Double> ratioMap = new HashMap<>();
        //获取页面参数
        JSONArray jsonArray = JSON.parseArray(qcManualTask.getInspectorRate());
        for (int i = 0; i < jsonArray.size(); i++) {
            ratioMap.put(jsonArray.getJSONObject(i).get("name").toString(),Double.valueOf(jsonArray.getJSONObject(i).get("ratio").toString()));
        }
        List<String> keyList = ratioMap.keySet().stream().collect(Collectors.toList());
        //设置分配的质检员
        qcManualTask.setInspectors(JSONObject.toJSONString(keyList));
        allocateTaskForSampling(resultList,ratioMap,qcManualTask);

    }


    /**
     * 根据质检员比例分配任务(人工抽检)
     */
    public void allocateTaskForSampling(List<QcBizItem> tasks,Map<String,Double> userRatios,QcManualTask qcManualTask){
        //定义根据质检员分配后的结果,key表示质检员，value表示对应的数据集
        Map<String,List<QcBizItem>> resultMap = new HashMap<>();
        userRatios.keySet().forEach(userName -> resultMap.put(userName, new ArrayList<>()));
        // 随机打乱任务顺序，确保分配公平
        Collections.shuffle(tasks);
        int totalTasks = tasks.size();
        int remainingTasks = totalTasks;
        int index = 0;
        // 遍历用户，按比例分配任务
        List<String> userNames = new ArrayList<>(userRatios.keySet());
        for (int i = 0; i < userNames.size() - 1; i++) {
            String userName = userNames.get(i);
            double ratio = userRatios.get(userName);
            // 计算应分配的任务数量（向下取整）
            int taskCount = (int) Math.floor(totalTasks * ratio / 100);
            // 分配任务
            List<QcBizItem> assignedTasks = tasks.subList(index, index + taskCount);
            resultMap.get(userName).addAll(assignedTasks);

            index += taskCount;
            remainingTasks -= taskCount;
        }
        // 最后一个用户分配剩余任务，避免舍入误差
        String lastUserName = userNames.get(userNames.size() - 1);
        resultMap.get(lastUserName).addAll(tasks.subList(index, index + remainingTasks));
        //移除没有分到任务的质检员，没有分到的质检员在任务列表中和人工质检台看不到该任务
        resultMap.entrySet().removeIf(entry ->
                entry.getValue() == null || entry.getValue().isEmpty()
        );
        //实际分配到数据的质检员字段
        List<String> realList = resultMap.keySet().stream().collect(Collectors.toList());
        qcManualTask.setRealInspectors(JSONObject.toJSONString(realList));
        qcManualTask.setUpdateBy(SecurityUtils.getUsername());
        qcManualTask.setUpdateTime(new Date());
        //更新task表
        qcManualTaskMapper.updateQcManualTask(qcManualTask);
        batchInsertDataForSampling(resultMap,qcManualTask);
    }

    /**
     * 将分配后的数据集落表(人工抽检)
     */
    public void batchInsertDataForSampling(Map<String,List<QcBizItem>> resultMap,QcManualTask qcManualTask){
        List<QcManualTaskDetail> qcManualTaskDetailList = new ArrayList<>();
//        String currTempStr = JSON.toJSONString(qcScoringTemplateMapper.getQcScoringTemplate(qcManualTask.getTemplateId()));

        for (Map.Entry<String, List<QcBizItem>> entry : resultMap.entrySet()) {
            String userName = entry.getKey();
            List<QcBizItem> qcBizItemss = entry.getValue();
            //遍历结果集,将相关字段值赋给人工质检结果表
            for (QcBizItem qcBizItem : qcBizItemss) {
                QcManualTaskDetail qcManualTaskDetail = new QcManualTaskDetail();
                qcManualTaskDetail.setInspector(userName);//质检员工号
                qcManualTaskDetail.setBusinessId("");
                qcManualTaskDetail.setManualTaskId(qcManualTask.getId());
                qcManualTaskDetail.setManualType("1");
                qcManualTaskDetail.setBizNo(qcBizItem.getUniqueKey());
                qcManualTaskDetail.setSessionLength(qcBizItem.getCallDurationS());
                qcManualTaskDetail.setWorkNo(qcBizItem.getAgentId());
                qcManualTaskDetail.setWorkName(qcBizItem.getAgentName());
                qcManualTaskDetail.setManualInspectScore(null);
                qcManualTaskDetail.setManualInspectTime(null);
                qcManualTaskDetail.setFinishStatus("0");
                qcManualTaskDetail.setTemplateId(qcManualTask.getTemplateId());
//                qcManualTaskDetail.setTemplateHitRule(currTempStr);
                qcManualTaskDetail.setCreateBy(SecurityUtils.getUsername());
                qcManualTaskDetail.setCreateTime(new Date());
                qcManualTaskDetail.setUpdateBy(SecurityUtils.getUsername());
                qcManualTaskDetail.setUpdateTime(new Date());
                qcManualTaskDetailList.add(qcManualTaskDetail);
            }
        }
        //将人工质检结果落表
        if (qcManualTaskDetailList.size() > 0){
            qcManualTaskDetailMapper.batchInsert(qcManualTaskDetailList);
        }
    }



    /**
     * 筛选人工复检的数据并分配到质检员
     */
    public void recheckAndAllocation(QcManualTask qcManualTask){
        //命中规则
        List<Integer> hitRules = JSON.parseArray(qcManualTask.getHitRules(), Integer.class);
        //获取数据时间范围
        String dataRange = qcManualTask.getDataRange();
        String dataRangeDate = "";
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if ("1".equals(dataRange)){
            //一小时内
            dataRangeDate = now.minusHours(1).format(formatter);
        }else if ("2".equals(dataRange)){
            //两小时内
            dataRangeDate = now.minusHours(2).format(formatter);
        }else if("3".equals(dataRange)){
            //四小时内
            dataRangeDate = now.minusHours(4).format(formatter);
        }else if ("4".equals(dataRange)){
            //八小时内
            dataRangeDate = now.minusHours(8).format(formatter);
        }else if ("5".equals(dataRange)){
            //24小时内
            dataRangeDate = now.minusHours(24).format(formatter);
        }else if ("6".equals(dataRange)){
            //七天内
            dataRangeDate = now.minusDays(7).format(formatter);
        }
        //组装查询条件
        Map<String,Object> map = new HashMap<>();
        map.put("smartTaskId",qcManualTask.getSmartPlan());
        map.put("hitRules",hitRules);
        map.put("dataRangeDate",dataRangeDate);
        //新建手动任务时，如果选择人工复检，此时筛选的是智能质检任务而不是智能质检计划
        List<QcSmartTaskDetail> qcSmartTaskDetails = qcSmartTaskMapper.selectSmartTaskDetailForRecheckForTask(map);

        //新建手动任务或修改任务时，如果筛选出的会话数为0，则不做分配
        if (null == qcSmartTaskDetails || qcSmartTaskDetails.size() == 0){
            throw new RuntimeException("当前任务未筛选到会话,请重新选择");
        }
        //设置任务的会话数量(在原有会话数量上累加，因为修改和新增任务共用一个方法)
        qcManualTask.setSessionCount(qcSmartTaskDetails.size() + qcManualTask.getSessionCount());
        //将筛选完成的数据，根据比例分配给质检员并落到人工质检任务详情表
        //定义Map,key表示质检员工号，value表示比例
        Map<String,Double> ratioMap = new HashMap<>();
        //获取页面参数
        JSONArray jsonArray = JSON.parseArray(qcManualTask.getInspectorRate());
        for (int i = 0; i < jsonArray.size(); i++) {
            ratioMap.put(jsonArray.getJSONObject(i).get("name").toString(),Double.valueOf(jsonArray.getJSONObject(i).get("ratio").toString()));
        }
        List<String> keyList = ratioMap.keySet().stream().collect(Collectors.toList());
        qcManualTask.setInspectors(JSONObject.toJSONString(keyList));
        allocateTaskForRecheck(qcSmartTaskDetails,ratioMap,qcManualTask);

    }

    /**
     * 根据质检员比例分配任务(人工复检)
     */
    public void allocateTaskForRecheck(List<QcSmartTaskDetail> tasks,Map<String,Double> userRatios,QcManualTask qcManualTask){
        //定义根据质检员分配后的结果,key表示质检员，value表示对应的数据集
        Map<String,List<QcSmartTaskDetail>> resultMap = new HashMap<>();
        userRatios.keySet().forEach(userName -> resultMap.put(userName, new ArrayList<>()));
        // 随机打乱任务顺序，确保分配公平
        Collections.shuffle(tasks);
        int totalTasks = tasks.size();
        int remainingTasks = totalTasks;
        int index = 0;
        // 遍历用户，按比例分配任务
        List<String> userNames = new ArrayList<>(userRatios.keySet());
        for (int i = 0; i < userNames.size() - 1; i++) {
            String userName = userNames.get(i);
            double ratio = userRatios.get(userName);
            // 计算应分配的任务数量（向下取整）
            int taskCount = (int) Math.floor(totalTasks * ratio / 100);
            // 分配任务
            List<QcSmartTaskDetail> assignedTasks = tasks.subList(index, index + taskCount);
            resultMap.get(userName).addAll(assignedTasks);

            index += taskCount;
            remainingTasks -= taskCount;
        }
        // 最后一个用户分配剩余任务，避免舍入误差
        String lastUserName = userNames.get(userNames.size() - 1);
        resultMap.get(lastUserName).addAll(tasks.subList(index, index + remainingTasks));
        //移除没有分到任务的质检员，没有分到的质检员在任务列表中和人工质检台看不到该任务
        resultMap.entrySet().removeIf(entry ->
                entry.getValue() == null || entry.getValue().isEmpty()
        );
        //实际分配到数据的质检员字段
        List<String> realList = resultMap.keySet().stream().collect(Collectors.toList());
        qcManualTask.setRealInspectors(JSONObject.toJSONString(realList));
        qcManualTask.setUpdateBy(SecurityUtils.getUsername());
        qcManualTask.setUpdateTime(new Date());
        //更新task表
        qcManualTaskMapper.updateQcManualTask(qcManualTask);
        batchInsertDataForRecheck(resultMap,qcManualTask);
    }

    /**
     * 将分配后的数据集落表(人工复检)
     */
    public void batchInsertDataForRecheck(Map<String,List<QcSmartTaskDetail>> resultMap,QcManualTask qcManualTask){
        List<QcManualTaskDetail> qcManualTaskDetailList = new ArrayList<>();
//        String currTempStr = JSON.toJSONString(qcScoringTemplateMapper.getQcScoringTemplate(qcManualTask.getTemplateId()));
        for (Map.Entry<String, List<QcSmartTaskDetail>> entry : resultMap.entrySet()) {
            String userName = entry.getKey();
            List<QcSmartTaskDetail> smartTaskDetails = entry.getValue();
            //遍历智能质检结果集,将相关字段值赋给人工质检结果表
            for (QcSmartTaskDetail smartTaskDetail : smartTaskDetails) {
                QcManualTaskDetail qcManualTaskDetail = new QcManualTaskDetail();
                qcManualTaskDetail.setInspector(userName);//质检员工号
                qcManualTaskDetail.setBusinessId("");
                qcManualTaskDetail.setManualTaskId(qcManualTask.getId());
                qcManualTaskDetail.setSmartTaskDetailId(smartTaskDetail.getId());
                qcManualTaskDetail.setManualType("2");
                qcManualTaskDetail.setBizNo(smartTaskDetail.getBizNo());
                qcManualTaskDetail.setSessionLength(smartTaskDetail.getSessionLength());
                qcManualTaskDetail.setWorkNo(smartTaskDetail.getWorkNo());
                qcManualTaskDetail.setWorkName(smartTaskDetail.getWorkName());
                qcManualTaskDetail.setMacInspectScore(smartTaskDetail.getMacInspectScore());
                qcManualTaskDetail.setMacInspectTime(smartTaskDetail.getMacInspectTime());
                qcManualTaskDetail.setFinishStatus("0");
                //如果是人工复检，则模板用智能质检任务对应的模板
                qcManualTaskDetail.setTemplateId(smartTaskDetail.getTemplateId());
//                qcManualTaskDetail.setTemplateHitRule(currTempStr);
                qcManualTaskDetail.setCreateBy(SecurityUtils.getUsername());
                qcManualTaskDetail.setCreateTime(new Date());
                qcManualTaskDetail.setUpdateBy(SecurityUtils.getUsername());
                qcManualTaskDetail.setUpdateTime(new Date());
                qcManualTaskDetailList.add(qcManualTaskDetail);
            }
        }
        //将人工质检结果落表
        if (qcManualTaskDetailList.size() > 0) {
            qcManualTaskDetailMapper.batchInsert(qcManualTaskDetailList);
        }

    }


    /**
     * 修改该任务的已完成数量及完成状态
     */
    @GetMapping("/status/{id}")
    public AjaxResult updateStatus(@PathVariable("id") Long id)
    {
        //根据传过来的detailId获取该条明细的任务id
        QcManualTaskDetail detail = qcManualTaskDetailMapper.selectQcManualTaskDetailById(id);
        //获取对应的任务
        QcManualTask manualTask = qcManualTaskMapper.selectQcManualTaskById(detail.getManualTaskId());
        Integer finishCount = manualTask.getFinishCount();
        manualTask.setFinishCount(++finishCount);
        if (finishCount == manualTask.getSessionCount()){
            manualTask.setStatus("1");
        }
        qcManualTaskMapper.updateQcManualTask(manualTask);
        return success();
    }


    /**
     * 根据任务id和相关条件，查询该任务下所有明细
     */
    @PostMapping("/detail/list")
    public TableDataInfo detailList(@RequestParam Map<String, Object> params, @RequestBody JSONObject payload)
    {
        Set<String> roles = SecurityUtils.getLoginUser().getRoles();
        if (roles.contains("manager") || roles.contains("admin")){
            //如果是管理员，则查询所有人的任务明细，把visibleUsers置为空即可查询所有人的任务明细
            payload.put("visibleUsers",null);
        }else {
            //如果是质检组长或普通质检员，则获取该用户能查看的用户范围
            payload.put("visibleUsers",remoteUserService.getGroupMemberListByGroupLeaderUserName(SecurityConstants.INNER).getData());
        }
        startPage();
        List<QcManualTaskDetailVO> detailVOS = qcManualTaskDetailMapper.selectQcManualTaskDetailsByTaskIdForRole(payload);
        return getDataTable(detailVOS);
    }


    /**
     * 查询人工质检任务详情
     */
    @GetMapping("/detail/{id}")
    public AjaxResult detail(@PathVariable("id") Long id)
    {
        QcManualTaskDetail detailVOS = qcManualTaskDetailMapper.selectQcManualTaskDetailById(id);
        if(detailVOS != null && StringUtils.isBlank(detailVOS.getTemplateHitRule())){
        	detailVOS.setTemplateHitRule(redisService.getCacheMapValue("qcScoreTemp", detailVOS.getTemplateId()+""));
        }
        return success(detailVOS);
    }


    /**
     * 根据任务id获取当前用户下某条任务下的已完成数量及会话数(不同角色的用户看到的数量不同)
     * @return
     */
    @GetMapping("/finish/count/{id}")
    public AjaxResult getCountAndDate(@PathVariable("id") Long taskId){
        Map<String,Object> map = new HashMap<>();
        map.put("taskId",taskId);
        Set<String> roles = SecurityUtils.getLoginUser().getRoles();
        if (roles.contains("manager") || roles.contains("admin")){
            //如果是管理员，则查询所有人的任务明细，把visibleUsers置为空即可查询所有人的任务明细
            map.put("visibleUsers",null);
        }else {
            //如果是质检组长或普通质检员，则获取该用户能查看的用户范围
            map.put("visibleUsers",remoteUserService.getGroupMemberListByGroupLeaderUserName(SecurityConstants.INNER).getData());
        }
        Map<String, Object> count = qcManualTaskDetailMapper.selectCountByTaskId(map);
        Map<String,Object> result = new HashMap<>();
        result.put("finishCount",count.get("finish_count"));
        result.put("sessionCount",count.get("session_count"));
        return success(result);
    }


    /**
     * 获取人工质检结果表中的所有命中规则、命中的评分项
     */
    @GetMapping("/result/rule/list")
    public AjaxResult resultRuleList(@RequestParam Map<String, Object> params)
    {
        List<QcManualTaskResult> rules = qcManualTaskResultMapper.getRulesFromManualResult();
        List<QcManualTaskResult> items = qcManualTaskResultMapper.getItemsFromManualResult();
        Map<String,Object> map = new HashMap<>();
        map.put("rules",rules);
        map.put("items",items);
        return success(map);
    }



}
