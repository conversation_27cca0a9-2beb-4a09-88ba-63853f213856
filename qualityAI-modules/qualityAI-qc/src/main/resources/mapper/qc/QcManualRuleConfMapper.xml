<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcManualRuleConfMapper">

    <resultMap type="QcManualRuleConf" id="QcManualRuleConfResult">
        <result property="id"    column="id"    />
        <result property="classifyId"    column="classify_id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleGroup"    column="rule_group"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="ruleDes"    column="rule_des"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectQcManualRuleConfVo">
        select id, classify_id,rule_name, rule_group, rule_type, rule_des, status,create_by, create_time, update_by, update_time,tenant_id,remark from qc_manual_rule_conf
    </sql>

    <select id="selectQcManualRuleConfList"  resultMap="QcManualRuleConfResult">
        <include refid="selectQcManualRuleConfVo"/>
        <where>
            <if test="classifyId != null  and classifyId != ''"> and classify_id = #{classifyId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="content != null  and content != ''"> and rule_name like concat('%', #{content}, '%')</if>
            <if test="ruleId != null and ruleId != ''"> and id = #{ruleId}</if>
        </where>
    </select>

    <select id="selectAll"  resultMap="QcManualRuleConfResult">
        <include refid="selectQcManualRuleConfVo"/>
    </select>

    <select id="selectQcManualRuleConfById" parameterType="Long" resultMap="QcManualRuleConfResult">
        <include refid="selectQcManualRuleConfVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcManualRuleConf" parameterType="QcManualRuleConf" useGeneratedKeys="true" keyProperty="id">
        insert into qc_manual_rule_conf
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classifyId != null">classify_id,</if>
            <if test="ruleName != null">rule_name,</if>
            <if test="ruleGroup != null">rule_group,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="ruleDes != null">rule_des,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classifyId != null">#{classifyId},</if>
            <if test="ruleName != null">#{ruleName},</if>
            <if test="ruleGroup != null">#{ruleGroup},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="ruleDes != null">#{ruleDes},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <insert id="batchInsert">
        insert into qc_manual_rule_conf values
        <foreach item="item" index="index" collection="list" separator=",">
            (default,#{item.classifyId},#{item.ruleName},#{item.ruleGroup},#{item.ruleType},#{item.ruleDes},#{item.status},
            #{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},#{item.tenantId},#{item.remark})
        </foreach>
    </insert>

    <update id="updateQcManualRuleConf" parameterType="QcManualRuleConf">
        update qc_manual_rule_conf
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="ruleGroup != null">rule_group = #{ruleGroup},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="ruleDes != null">rule_des = #{ruleDes},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcManualRuleConfById" parameterType="Long">
        delete from qc_manual_rule_conf where id = #{id}
    </delete>

    <delete id="deleteQcManualRuleConfByIds" parameterType="String">
        delete from qc_manual_rule_conf where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCountByClassify"  parameterType="Long">
        select count(1) from qc_manual_rule_conf where classify_id = #{id}
    </select>

</mapper>