import request from '@/utils/request'

// 查询质检规则列表
export function listRule(query,data) {
  return request({
    url: '/qc/rule/list',
    method: 'post',
    params: query,
	data:data
  })
}
// 查询质检规则列表
export function recordList(query,data) {
  return request({
    url: '/qc/rule/record/list',
    method: 'post',
    params: query,
	data:data
  })
}
// 查询质检规则列表
export function kwlList(query,data) {
  return request({
    url: '/qc/rule/kwl/list',
    method: 'post',
    params: query,
	data:data
  })
}

// 查询质检规则详细
/*export function getRule(ruleId) {
  return request({
    url: '/qc/rule/' + ruleId,
    method: 'get'
  })
}*/
// 查询质检规则详细
export function getRule(data) {
  return request({
    url: '/qc/rule/query',
    method: 'post',
	data: data
  })
}

// 新增质检规则
export function addRule(data) {
  return request({
    url: '/qc/rule',
    method: 'post',
    data: data
  })
}

// 修改质检规则
export function updateRule(data) {
  return request({
    url: '/qc/rule',
    method: 'post',
    data: data
  })
}

// 删除质检规则
export function delRule(ruleId) {
  return request({
    url: '/qc/rule/',
    method: 'delete',
	data:ruleId
  })
}
