package com.ideal.qc.mapper;

import java.util.List;
import java.util.Map;

import com.ideal.qc.domain.ZjQuestionnaire;

/**
 * 问卷Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-24
 */
public interface ZjQuestionnaireMapper 
{
    /**
     * 查询问卷
     * 
     * @param id 问卷主键
     * @return 问卷
     */
    public ZjQuestionnaire selectZjQuestionnaireById(Long id);

    /**
     * 查询问卷列表
     * 
     * @param zjQuestionnaire 问卷
     * @return 问卷集合
     */
    public List<ZjQuestionnaire> selectZjQuestionnaireList(ZjQuestionnaire zjQuestionnaire);

    /**
     * 新增问卷
     * 
     * @param zjQuestionnaire 问卷
     * @return 结果
     */
    public int insertZjQuestionnaire(ZjQuestionnaire zjQuestionnaire);

    /**
     * 修改问卷
     * 
     * @param zjQuestionnaire 问卷
     * @return 结果
     */
    public int updateZjQuestionnaire(ZjQuestionnaire zjQuestionnaire);

    /**
     * 删除问卷
     * 
     * @param id 问卷主键
     * @return 结果
     */
    public int deleteZjQuestionnaireById(Long id);

    /**
     * 批量删除问卷
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZjQuestionnaireByIds(Long[] ids);

    void delQsQuetion(String qsNo);

    void saveQsQuetion(Map<String, Object> map);
}
