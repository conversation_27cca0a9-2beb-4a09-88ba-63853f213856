# Tomcat
server:
  port: 9204

# Spring
spring: 
  application:
    # 应用名称
    name: qc
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ${QC_NACOS_NSP:public}
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.username}
        namespace: ${QC_NACOS_NSP:public}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.yml
          - dynamicDatasource-${spring.profiles.active}.yml
          - redis-${spring.profiles.active}.yml
# 如下配置放到nacos配置
deepseek:
  api:
    url: http://127.0.0.1:11434/api/generate
    model: deepseek-r1:1.5b