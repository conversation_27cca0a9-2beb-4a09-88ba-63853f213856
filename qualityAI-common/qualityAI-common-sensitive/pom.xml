<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ideal</groupId>
        <artifactId>qualityAI-common</artifactId>
        <version>3.6.5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>qualityAI-common-sensitive</artifactId>

    <description>
        qualityAI-common-sensitive数据脱敏
    </description>

    <dependencies>

        <!-- Common Core -->
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>qualityAI-common-core</artifactId>
        </dependency>

    </dependencies>
</project>