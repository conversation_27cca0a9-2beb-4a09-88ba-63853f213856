package com.ideal.qc.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.ScoreItem;
import com.ideal.qc.domain.ZjDataAiresult;
import com.ideal.qc.domain.ZjDataReviewData;
import com.ideal.qc.service.IScoreItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.ZjDataReview;
import com.ideal.qc.service.IZjDataReviewService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 人工复检Controller
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@RestController
@RequestMapping("/review")
public class ZjDataReviewController extends BaseController
{
    @Autowired
    private IZjDataReviewService zjDataReviewService;
    @Autowired
    private IScoreItemService scoreItemService;

    /**
     * 查询人工复检列表
     */
    @RequiresPermissions("quality:review:list")
    @GetMapping("/selectReviewData")
    public TableDataInfo selectReviewData(ZjDataReviewData zjDataReviewData)
    {
        startPage();
        List<ZjDataReviewData> list = zjDataReviewService.selectReviewData(zjDataReviewData);
        return getDataTable(list);
    }
    /**
     * 查询AI智能结果
     */
    @GetMapping("/selectAiResultData")
    public AjaxResult selectAiResultData(ZjDataReviewData zjDataReviewData)
    {
        List<ZjDataAiresult>  list = zjDataReviewService.selectAiResultData(zjDataReviewData);
        for(int i=0;i<list.size();i++){
            ZjDataAiresult t = list.get(i);
            if(t.getParentId()!=null&&t.getParentId()!=0){
                ScoreItem si = scoreItemService.selectScoreItemById(t.getParentId());
                list.get(i).setParentName(si.getItemName());
            }else{
                list.get(i).setParentName(list.get(i).getItemName());
                list.get(i).setItemName("");
            }
        }
        return success(list);
    }
    /**
     * 查询AI智能结果
     */
    @GetMapping("/selectAiTemplateData")
    public AjaxResult selectAiTemplateData(ZjDataReviewData zjDataReviewData)
    {
        List<ZjDataAiresult>  list = zjDataReviewService.selectAiTemplateData(zjDataReviewData);
        return success(list);
    }
    /**
     * 查询人工复检列表
     */
    @RequiresPermissions("quality:review:list")
    @GetMapping("/list")
    public TableDataInfo list(ZjDataReview zjDataReview)
    {
        startPage();
        List<ZjDataReview> list = zjDataReviewService.selectZjDataReviewList(zjDataReview);
        return getDataTable(list);
    }

    /**
     * 导出人工复检列表
     */
    @RequiresPermissions("quality:review:export")
    @Log(title = "人工复检", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZjDataReview zjDataReview)
    {
        List<ZjDataReview> list = zjDataReviewService.selectZjDataReviewList(zjDataReview);
        ExcelUtil<ZjDataReview> util = new ExcelUtil<ZjDataReview>(ZjDataReview.class);
        util.exportExcel(response, list, "人工复检数据");
    }

    /**
     * 获取人工复检详细信息
     */
    @RequiresPermissions("quality:review:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(zjDataReviewService.selectZjDataReviewById(id));
    }
    /**
     * 获取人工复检详细信息
     */
    @RequiresPermissions("quality:review:query")
    @GetMapping(value = "/getReviewListByDataId/{dataId}")
    public AjaxResult getReviewListByDataId(@PathVariable("dataId") Long dataId)
    {
        return success(zjDataReviewService.getReviewListByDataId(dataId));
    }
    /**
     * 新增人工复检
     */
    @RequiresPermissions("quality:review:add")
    @Log(title = "人工复检", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JSONArray list)
    {
        Long id = null;//数据末班ID
        Map<String,Object> map = new HashMap<>();
        for(int i=0;i<list.size();i++){
            JSONObject js = list.getJSONObject(i);
            ZjDataReview zjDataReview = JSONObject.parseObject(js.toJSONString(),ZjDataReview.class);
            id = zjDataReview.getId();
            zjDataReview.setCreateBy(SecurityUtils.getUsername());
            zjDataReviewService.insertZjDataReview(zjDataReview);
        }
        map.put("id",id);
        map.put("handleBy", SecurityUtils.getUsername());
        zjDataReviewService.completeZjDataReview(map);

        return success();
    }

    /**
     * 修改人工复检
     */
    @RequiresPermissions("quality:review:edit")
    @Log(title = "人工复检", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZjDataReview zjDataReview)
    {
        return toAjax(zjDataReviewService.updateZjDataReview(zjDataReview));
    }

    /**
     * 删除人工复检
     */
    @RequiresPermissions("quality:review:remove")
    @Log(title = "人工复检", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zjDataReviewService.deleteZjDataReviewByIds(ids));
    }
}
