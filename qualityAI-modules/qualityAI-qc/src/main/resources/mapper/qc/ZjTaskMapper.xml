<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ZjTaskMapper">

    <resultMap type="com.ideal.qc.domain.ZjTask" id="ZjTaskResult">
        <result property="taskId" column="task_id" />
        <result property="taskName" column="task_name"    />
        <result property="templateId" column="template_id"    />
        <result property="templateName" column="templateName" />
        <result property="createTime" column="create_time"    />
        <result property="updateTime" column="update_time"    />
        <result property="priority" column="priority"    />
        <result property="executeStatus" column="execute_status"    />
        <result property="taskCompletion" column="task_completion"    />
        <result property="createBy" column="create_by"    />
        <result property="updateBy" column="update_by"    />
        <result property="taskStatus" column="task_status"    />
        <result property="tenantCode" column="tenant_code"    />
        <result property="extractionType" column="extraction_type"    />
        <result property="organization" column="organization"    />
        <result property="businessType" column="business_type"    />
        <result property="taskType" column="task_type"    />
        <result property="sessionStartTime" column="session_start_time"    />
        <result property="sessionEndTime" column="session_end_time"    />
    </resultMap>
    <resultMap type="com.ideal.qc.domain.ZjTaskScoreData" id="ZjTaskScoreDataResult">
        <result property="ruleId" column="ruleId" />

        <result property="detectionMode" column="detectionMode" />
        <result property="detectionContent" column="detectionContent" />
        <result property="detectionRole" column="detectionRole" />

        <result property="nature" column="nature" />

        <result property="content" column="content" />

        <result property="zjScoreItemId" column="zjScoreItemId" />

        <result property="itemName" column="itemName" />

        <result property="callId" column="callId" />
        <result property="templateId" column="templateId" />
        <result property="dataId" column="dataId" />

    </resultMap>

    <sql id="selectZjTaskVo">
        select
             task_id,
             task_name,
             template_id,
             (SELECT s.tpl_name FROM zj_score_template s WHERE s.id=template_id) templateName,
             create_time,
             update_time,
             priority,
             execute_status,
             task_completion,
             create_by,
             update_by,
             task_status,
             tenant_code,
             extraction_type,
             organization,
             business_type,
             task_type,
             session_start_time,
             session_end_time
        from zj_task
    </sql>

    <select id="selectZjTaskList" parameterType="com.ideal.qc.domain.ZjTask" resultMap="ZjTaskResult">
        <include refid="selectZjTaskVo"/>
        <where>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskStatus != null  and taskStatus != ''"> and task_status = #{taskStatus}</if>
        </where>
    </select>

    <select id="selectZjTaskByTaskId" parameterType="Long" resultMap="ZjTaskResult">
        <include refid="selectZjTaskVo"/>
        where task_id = #{taskId}
    </select>

    <insert id="insertZjTask" parameterType="com.ideal.qc.domain.ZjTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into zj_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null ">task_name,</if>
            <if test="templateId != null ">template_id,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateTime != null ">update_time,</if>
            <if test="priority != null ">priority,</if>
            <if test="executeStatus != null ">execute_status,</if>
            <if test="taskCompletion != null ">task_completion,</if>
            <if test="createBy != null ">create_by,</if>
            <if test="updateBy != null ">update_by,</if>
            <if test="taskStatus != null ">task_status,</if>
            <if test="tenantCode != null ">tenant_code,</if>
            <if test="extractionType != null ">extraction_type,</if>
            <if test="organization != null ">organization,</if>
            <if test="businessType != null ">business_type,</if>
            <if test="taskType != null ">task_type,</if>
            <if test="params.beginTime != null ">session_start_time,</if>
            <if test="params.endTime  != null ">session_end_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null ">#{taskName},</if>
            <if test="templateId != null ">#{templateId},</if>
            <if test="createTime != null ">date_format(#{createTime},'%Y-%m-%d %H:%s:%i'),</if>
            <if test="updateTime != null ">#{updateTime},</if>
            <if test="priority != null ">#{priority},</if>
            <if test="executeStatus != null ">#{executeStatus},</if>
            <if test="taskCompletion != null ">#{taskCompletion},</if>
            <if test="createBy != null ">#{createBy},</if>
            <if test="updateBy != null ">#{updateBy},</if>
            <if test="taskStatus != null ">#{taskStatus},</if>
            <if test="tenantCode != null ">#{tenantCode},</if>
            <if test="extractionType != null ">#{extractionType},</if>
            <if test="organization != null ">#{organization},</if>
            <if test="businessType != null ">#{businessType},</if>
            <if test="taskType != null ">#{taskType},</if>
            <if test="params.beginTime != null ">date_format(#{params.beginTime},'%Y-%m-%d'),</if>
            <if test="params.endTime  != null ">date_format(#{params.endTime},'%Y-%m-%d'),</if>
        </trim>
    </insert>

    <update id="updateZjTask" parameterType="com.ideal.qc.domain.ZjTask">
        update zj_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name=#{taskName},</if>
            <if test="templateId != null and templateId != ''">template_id=#{templateId},</if>
            <if test="createTime != null and createTime != ''">create_time=now(),</if>
            <if test="updateTime != null and updateTime != ''">update_time=now(),</if>

            <if test="priority != null and priority != ''">priority=#{priority},</if>
            <if test="executeStatus != null and executeStatus != ''">execute_status=#{executeStatus},</if>
            <if test="taskCompletion != null and taskCompletion != ''">task_completion=#{taskCompletion},</if>
            <if test="createBy != null and createBy != ''">create_by=#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">update_by=#{updateBy},</if>
            <if test="taskStatus != null and taskStatus != ''">task_status=#{taskStatus},</if>
            <if test="tenantCode != null and tenantCode != ''">tenant_code=#{tenantCode},</if>
            <if test="extractionType != null and extractionType != ''">extraction_type=#{extractionType},</if>
            <if test="organization != null and organization != ''">organization=#{organization},</if>
            <if test="businessType != null and businessType != ''">business_type=#{businessType},</if>
            <if test="taskType != null and taskType != ''">task_type=#{taskType},</if>
            <if test="params.beginTime != null ">session_start_time=date_format(#{params.beginTime},'%Y-%m-%d'),</if>
            <if test="params.endTime  != null ">session_end_time=date_format(#{params.endTime},'%Y-%m-%d'),</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteZjTaskByTaskId" parameterType="Long">
        delete from zj_task where task_id = #{taskId}
    </delete>

    <delete id="deleteZjTaskByTaskIds" parameterType="String">
        delete from zj_task where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

    <insert id="insertZjDataTemplate" parameterType="com.ideal.qc.domain.ZjTask">
        insert into zj_data_template
        (data_id,zj_template_id,create_by,create_time,zj_task_id,zj_status)
        (
            SELECT z.id as data_id,
            '${templateId}' as zj_template_id,
            '${createBy}' as create_by,
            DATE_FORMAT(STR_TO_DATE('${createTime}', '%a %b %d %H:%i:%s CST %Y'),'%Y-%m-%d %H:%i:%s') as create_time,
            '${taskId}' as zj_task_id,
            '0' zj_status
            FROM zj_data z
            <where>
            NOT EXISTS (
                SELECT 1
                FROM zj_data_template a
                WHERE a.data_id = z.id
                )
                <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                    AND date_format(z.start_time,'%Y-%m-%d') &gt;= date_format(#{params.beginTime},'%Y-%m-%d')
                </if>
                    <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                        AND date_format(z.end_time,'%Y-%m-%d') &lt;= date_format(#{params.endTime},'%Y-%m-%d')
                    </if>
                    <if test="workNo != null  and workNo != ''">
                        and z.work_no = #{workNo}
                    </if>
                    <if test="businessType != null  and businessType != ''">
                        and z.business_type = #{businessType}
                    </if>
                    <if test="productType != null  and productType != ''">
                        and z.product_type = #{productType}
                    </if>
                    <if test="serviceType != null  and serviceType != ''">
                        and z.service_type = #{serviceType}
                    </if>
                </where>
            )
    </insert>

    <select id="getZjTaskScoreData" parameterType="String" resultMap="ZjTaskScoreDataResult">
            select
                b.rule_id ruleId,
                e.detection_mode detectionMode,
                e.detection_content detectionContent,
                e.detection_role detectionRole,
                b.nature,
                d.asr_result content,
                b.id zjScoreItemId,
                b.item_name itemName,
                c.only_no callId,
                a.zj_template_id templateId,
                a.data_id dataId
            from  zj_data_template a
            LEFT JOIN (select * from zj_score_item where item_type='C') b on a.zj_template_id=b.scoretpl_id
            LEFT JOIN zj_data c on a.data_id=c.id

            LEFT JOIN zj_ai_book_asr_result d on c.only_no=d.only_no COLLATE utf8mb4_unicode_ci
            LEFT JOIN zj_rule e on b.rule_id=e.rule_id
            where a.zj_status='0' and a.data_id=#{zjDataId}
    </select>
</mapper>
