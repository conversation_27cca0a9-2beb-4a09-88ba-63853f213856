<template>
	<el-form-item label="筛选条件" prop="filterList" style="width:100%;">
	<div >
    <div class="ruleBox" v-for="(item,index) in filterList" :key="index">
	<template v-if="item.status!='2'">
		<div class="i-or-div">
      		<i v-if="index!=0" class="i-or" circle >或</i>
		</div>
      <div class="ruleTop row spaceBet">
        <div>条件组合{{index+1}}</div>
        <i v-if="op != 'view'" class="el-icon-close" @click="cutRule('filterList',index)"></i>
      </div>
      <div class="ruleCon">
        <div class="row">
          符合下列
          <el-select v-model="item.ruleCondDetail.isAll" placeholder="请选择" class="selectS" style="margin-left:10px;" :disabled="op == 'view'">
            <el-option label="全部" value="1" />
            <el-option label="任一" value="0" />
          </el-select>
          规则，则【条件组合{{index+1}}】命中
        </div>
        <div v-for="(item2,index2) in item.ruleCondDetail.options" :key="index2" class="row" style="margin-top:20px;">
          在业务标签中
          <el-select v-model="item2.key" placeholder="请选择" class="selectL" @change="ruleChange($event,index,index2)" value-key="key" :disabled="op == 'view'">
            <el-option v-for="(items,indexk) in busList" :key="indexk" :label="items.label" :value="items.key" :disabled="items.status != '1'"></el-option>
          </el-select>
          <el-select v-model="item2.op" placeholder="请选择" class="selectM" style="margin-left:10px;" :disabled="op == 'view'">
            <el-option v-if="item2.type == '3' || item2.type == '2'" v-for="op in opi" :key="op.key" :label="op.label" :value="op.key" 
			></el-option>
			<el-option v-if="item2.type == '1'"v-for="op in ops" :key="op.key" :label="op.label" :value="op.key" 
			></el-option>
          </el-select>
		  <template v-if="item2.op == 'less' || item2.op == 'more' || item2.op == 'equal' ">
			<el-input v-if="item2.type == '2'" class="input-new-tag" type="number"  v-model="item2.val1" ref="saveTagInput" :disabled="op == 'view'"></el-input>
			<el-date-picker v-if="item2.type == '3'" class="selectT" v-model="item2.val1" type="datetime" placeholder="选择日期时间" :disabled="op == 'view'"> </el-date-picker>
		  </template>
		  <template v-if="item2.op == 'in' || item2.op == 'nIn'">
  			<el-input v-if="item2.type == '2'" class="input-new-tag" type="number"  v-model="item2.val1" ref="saveTagInput" :disabled="op == 'view'"></el-input>
			<el-date-picker v-if="item2.type == '3'" class="selectT" v-model="item2.val1" type="datetime" placeholder="选择日期时间" :disabled="op == 'view'"> </el-date-picker>
			<div style="margin-right: 10px;">和</div>
			<el-input v-if="item2.type == '2'" class="input-new-tag" type="number"  v-model="item2.val2" ref="saveTagInput" :disabled="op == 'view'"></el-input>
  			<el-date-picker v-if="item2.type == '3'" class="selectT" v-model="item2.val2" type="datetime" placeholder="选择日期时间" :disabled="op == 'view'"> </el-date-picker>
			<div style="margin-right: 10px;">之间</div>
  		  </template>
		  <template v-if="item2.op == 'contain' || item2.op == 'nContain'">
			<template v-if="item2.sMode != '1'">
			<el-input class="input-new-tag" placeholder="输入按回车确定"  v-model="item2.inputKey" ref="saveTagInput" :disabled="op == 'view'"
                @keyup.enter.native="handleInputConfirm(index,index2,item2.inputKey)" @blur="handleInputConfirm(index,index2,item2.inputKey)">
              </el-input>
              <el-tag :key="tag+index" v-for="tag in item2.inputKeys" :closable="op != 'view'" :disable-transitions="false" @close="handleClose(index,index2,tag)" style="margin-right:10px;">
                {{tag}}
              </el-tag>
			  </template>
			  <el-select v-if="item2.sMode == '1'" v-model="item2.inputKeys" multiple collapse-tags :multiple-limit="3" :disabled="op == 'view'">
	              <el-option v-for="op in item2.tmpList" :key="op.value" :label="op.label" :value="op.value" 
	  			></el-option>
            </el-select>
		  </template>
          <el-button v-if="op != 'view'" icon="el-icon-minus" :disabled="index2==0" @click="cutFilOpt(index,index2)"></el-button>
          <el-button v-if="op != 'view'" icon="el-icon-plus" @click="addFilOpt(index)" style="margin-left:5px;"></el-button>
        </div>
	  </div>
	  </template>
    </div>
    <el-button v-if="op != 'view' && (type !='flData' || filterList.length<1)" type="primary" plain icon="el-icon-plus" @click="addRule">添加组合规则</el-button>
	</div>
  </el-form-item>

</template>

<script>
import { getQcBusinessLabelList } from "@/api/quality/businessLabel.js";
export default {
  name: "smartEdit",
  props:{
	filterList:Array,
	type:String,
	op:String
  },
  data() {
    return {
      info:{id:""},
      form: {
        ruleType:'',
        filterList:[],
        filterList:[],
		deleteList:[]
      },
	  busList:[],
	  ops:[
		{key:"contain",label:"包含"},
		{key:"nContain",label:"不包含"}
	  ],
	  opi:[
	  	 	{key:"less",label:"小于"},
	  		{key:"more",label:"大于"},
	  		{key:"equal",label:"等于"},
	  		{key:"in",label:"在"},
	  		{key:"nIn",label:"不在"}
	  	  ],
	tmpList:[]
    };
  },
  created() {
	this.getQcBusinessLabelList();
	if(this.type=='flData'){
		//this.addRule();
	}
  },
  methods: {
	getQcBusinessLabelList(){
		getQcBusinessLabelList({},{businessLabelStatus:1}).then(response=>{
			if(response.code=200){
				for(var tmp in response.rows){
					this.busList.push({
						key:response.rows[tmp].businessLabelKey,
						type:response.rows[tmp].businessLabelType,
						label:response.rows[tmp].businessLabelName,
						lValue:response.rows[tmp].businessLabelValue,
						sMode:response.rows[tmp].businessLabelStringMode,
						status:response.rows[tmp].businessLabelStatus
					});
				}
				
			}
		}).catch(error => {
	      console.error('请求失败', error);
	    });
	},
	// 筛选条件
	    addRule(){
	      this.filterList.push({ruleCondType:1,ruleId:this.info.id,ruleCondDetail:{isAll:'1',options:[{op:'',column:'',type:1,val1:"",val2:"",inputKey:"",inputKeys:[]}]}})
	    },
	    cutRule(key,index){
		  if(this[key][index].id && this[key][index].id != ''){
			this[key][index].status = 2
			this[key].push(this[key][index])
		  }
		  this[key].splice(index,1);
	    },
	    addFilOpt(index){
	      this.filterList[index].ruleCondDetail.options.push({op:'',column:'',type:1,val1:"",val2:"",inputKey:"",inputKeys:[]})
	    },
	    cutFilOpt(index,index2){
	      this.filterList[index].ruleCondDetail.options.splice(index2,1);
	    },
	    chgHitRule(e,index,index2){
	      const obj = this.filterList[index].ruleCondDetail.options[index2]
	      if(e ==2){
	        this.$set(obj,'dynamicTags',[])
	      }else{
	        if(obj.dynamicTags){
	          delete obj.dynamicTags
	        }
	      }

	    },
	    cutHit(item,index){
	      this.filterList[index]["isDelete"] = true;
	    },
	    addHitOpt(index){
	      this.filterList[index].ruleCondDetail.options.push({scene:'1',role:'1',isContain:'1',rule:1})
	    },
	    cutHitOpt(index,index2){
	      this.filterList[index].ruleCondDetail.options.splice(index2,1);
	    },
	    goBack() {
	     this.$emit("goList");
	    },

	    handleInputConfirm(index,index2,inputValue) {
		  if(inputValue == undefined || inputValue == '' || inputValue == null){
			return;
		  }
	      const obj = this.filterList[index].ruleCondDetail.options[index2]
		  if(obj.inputKeys.length==5){
			this.$message({message: "最多添加5个",type: 'warning'});
			return;
		  }
	      if (inputValue) {
	        obj.inputKeys.push(inputValue);
	        this.$set(obj,'inputKeys',obj.inputKeys)
	      }
	      this.$set(obj,'inputKey','')
	    },
	    handleClose(index,index2,tag) {
	      const inputKeys = this.filterList[index].ruleCondDetail.options[index2].inputKeys;
	      inputKeys.splice(inputKeys.indexOf(tag), 1);

	    },
		ruleChange(key,index,index1){
			var tmp = {}
			for(var i in this.busList){
				if(this.busList[i].key == key){
					tmp = JSON.parse(JSON.stringify(this.busList[i]));
					break;
				}
			}
			this.filterList[index].ruleCondDetail.options[index1]["op"] = tmp.type == '1'?"contain":"less";
			this.filterList[index].ruleCondDetail.options[index1]["inputKeys"] = [];
			this.filterList[index].ruleCondDetail.options[index1]["val1"] = "";
			this.filterList[index].ruleCondDetail.options[index1]["val2"] = "";
			this.filterList[index].ruleCondDetail.options[index1]["inputKey"] = "";
			this.filterList[index].ruleCondDetail.options[index1]["type"] = tmp.type;
			this.filterList[index].ruleCondDetail.options[index1]["sMode"] = tmp.sMode;
			if(tmp.type == '1' && tmp.sMode == '1'){
				this.filterList[index].ruleCondDetail.options[index1].tmpList = JSON.parse(tmp.lValue);
			}
		}
	
	
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.inputWidth{
  width: 450px!important;
}
.selectS{
  width: 80px!important;
  margin-right: 10px;
}
.selectM{
  max-width: 120px!important;
  margin-right: 10px;
}
.selectL{
  width: 180px!important;
  margin-right: 10px;
}
.selectT{
	width: 190px!important;
	margin-left:10px;
	margin-right:10px;
}
.ruleBox{
  margin-bottom: 20px;
  .ruleTop{
    background: #ebebeb;
    padding: 2px 15px;
    cursor: pointer;
  }
  .ruleCon{
     padding: 18px 15px;
     font-size: 12px;
     background-color: #fafafa;
  }
  button{
        padding: 9px;
  }
}
.dialog-footer{
  width: 100%;
  margin-top: 40px;
  justify-content: center;

}
.orBt{
  margin-bottom: 15px;
}
.hitBox{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top:20px;
  row-gap: 10px; 
}
.keyInput{
  width: 150px;
  margin-right: 10px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 125px;
  margin-right: 10px;
  vertical-align: bottom;
  
}
.btn{
	float:right;
	margin-right:5px;
}
.no-label_el-form-item .el-form-item__content{
	margin-left: 0px!important;
}
</style>
