<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcKeywordLibraryClassificationMapper">

    <resultMap id="qcKeywordLibraryClassificationResultMap" type="QcKeywordLibraryClassification">
        <result property="id"    column="id"    />
        <result property="keywordLibraryClassificationName" column="keyword_library_classification_name" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <select id="getQcKeywordLibraryClassificationList" resultMap="qcKeywordLibraryClassificationResultMap">
        SELECT
            qklc.id,
            qklc.keyword_library_classification_name,
            qklc.create_by,
            qklc.create_time,
            qklc.update_by,
            qklc.update_time,
            qklc.status,
            qklc.is_deleted
        FROM
            qc_keyword_library_classification qklc
        WHERE
            qklc.is_deleted = '0'
            <if test="keywordLibraryClassificationName != null and keywordLibraryClassificationName != ''">
                AND qklc.keyword_library_classification_name LIKE concat('%', #{keywordLibraryClassificationName}, '%')
            </if>
        ORDER BY
            qklc.id ASC
    </select>

    <insert id="addQcKeywordLibraryClassification">
        INSERT INTO
            qc_keyword_library_classification
        ( tenant_id,  keyword_library_classification_name, create_time, create_by, update_time, update_by, remark )
        VALUES
        ( #{tenantId}, #{keywordLibraryClassificationName}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy}, #{remark})
    </insert>

    <update id="updateQcKeywordLibraryClassification">
        UPDATE qc_keyword_library_classification
        SET keyword_library_classification_name = #{keywordLibraryClassificationName},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark},
            status = #{status}
        WHERE
            id = #{id}
    </update>

    <update id="removeQcKeywordLibraryClassification">
        UPDATE qc_keyword_library_classification SET
            is_deleted = '1'
        WHERE
            id = #{id}
    </update>
</mapper>