package com.ideal.qc.service.impl;

import com.ideal.qc.domain.QcScoringHitRuleItem;
import com.ideal.qc.mapper.QcScoringHitRuleItemMapper;
import com.ideal.qc.service.QcScoringHitRuleItemService;
import com.ideal.qc.vo.QcScoringHitRuleItemVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 评分细则(项)规则组关联规则表Service接口的实现类
 *
 * <AUTHOR>
 * @date 2025/5/15 17:38
 */
@Service
public class QcScoringHitRuleItemServiceImpl implements QcScoringHitRuleItemService {

    @Resource
    private QcScoringHitRuleItemMapper qcScoringHitRuleItemMapper;

    /**
     * 新增评分细则命中规则项
     */
    @Override
    public void addScoringHitRuleItem(QcScoringHitRuleItem qcScoringHitRuleItem) {
        qcScoringHitRuleItemMapper.addScoringHitRuleItem(qcScoringHitRuleItem);
    }

    /**
     * 批量删除评分细则命中规则项
     */
    @Override
    public void removeScoringHitRuleItemBatch(List<Long> qcScoringHitRuleItemIdList) {
        qcScoringHitRuleItemMapper.removeScoringHitRuleItemBatch(qcScoringHitRuleItemIdList);
    }

    /**
     * 修改评分细则命中规则项
     */
    @Override
    public void updateScoringHitRuleItem(QcScoringHitRuleItem qcScoringHitRuleItem) {
        qcScoringHitRuleItemMapper.updateScoringHitRuleItem(qcScoringHitRuleItem);
    }

    /**
     * 根据规则组ID查询所有规则项id
     */
    @Override
    public List<Long> getQcScoringHitRuleItemIds(Long scoringHitRuleGroupId) {
        return qcScoringHitRuleItemMapper.getQcScoringHitRuleItemIds(scoringHitRuleGroupId);
    }

    /**
     * 批量删除评分细则命中规则项根据规则组ID
     */
    @Override
    public void removeScoringHitRuleItemBatchByGroupIds(List<Long> qcScoringHitRuleGroupIds) {
        qcScoringHitRuleItemMapper.removeScoringHitRuleItemBatchByGroupIds(qcScoringHitRuleGroupIds);
    }

    /**
     * 根据规则ID获取规则名称
     */
    @Override
    public String getRuleNameByRuleId(Long ruleId) {
        return qcScoringHitRuleItemMapper.getRuleNameByRuleId(ruleId);
    }

    /**
     * 根据规则ID获取规则名称
     */
    @Override
    public String getRuleNameById(Long ruleId) {
        return qcScoringHitRuleItemMapper.getRuleNameById(ruleId);
    }

    /**
     * 查询规则列表根据模板ID
     */
    @Override
    public List<QcScoringHitRuleItemVo> getQcScoringHitRuleItemListByTemplateId(Long scoringTemplateId) {
        List<QcScoringHitRuleItemVo> qcScoringHitRuleItemVoList = qcScoringHitRuleItemMapper.getQcScoringHitRuleItemListByTemplateId(scoringTemplateId);
        return qcScoringHitRuleItemVoList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                            QcScoringHitRuleItemVo::getName,
                            Function.identity(),
                            (existing, replacement) -> existing
                        ), map -> new ArrayList<>(map.values())
                ));
    }
}
