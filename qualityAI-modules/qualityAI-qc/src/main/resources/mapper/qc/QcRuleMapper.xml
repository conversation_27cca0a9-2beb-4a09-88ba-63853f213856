<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcRuleMapper">

    <resultMap type="QcRule" id="QcRuleResult">
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="ruleGroup"    column="rule_group"    />
		<result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createBy"    column="create_by"    />
        <result property="status"    column="status"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ruleClassificate" column="rule_classificate"/>
    </resultMap>
    <resultMap type="QcRuleCond" id="QcRuleCondResult">
    	<id property="id"    column="id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="ruleCondDetail" column="rule_cond_detail"/>
        <result property="ruleCondType" column="rule_cond_type"/>
        <result property="ruleOrder" column="rule_order"/>
        <result property="semanticsIds" column="semantics_ids"/>
    </resultMap>

    <sql id="selectQcRuleVo">
        select 
        	qr.rule_id, 
        	qr.rule_name, 
        	qr.rule_type, 
        	qr.rule_group, 
        	qr.remark ,  
        	qr.create_time,
        	qr.update_by, 
        	qr.update_time,
        	qr.create_by,
        	qr.status,
        	qr.rule_classificate,
        	qr.parent_id
       	from 
       		qc_rule qr
    </sql>
	<sql id="selectQcRuleCondVo">
        select id,rule_id, rule_cond_detail,create_time, update_time,rule_cond_type,semantics_ids from qc_rule_cond
    </sql>
    <!-- insert qc_rule_cond sql -->
    <sql id="insertQcRuleCondSql" >
        ;insert into qc_rule_cond
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="item.createTime != null">create_time,</if>
            <if test="ruleId != null and ruleId != ''">rule_id,</if>
            <if test="item.ruleCondDetail != null">rule_cond_detail,</if>
            <if test="item.ruleCondType != null">rule_cond_type,</if>
            <if test="item.ruleOrder != null">rule_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="item.semanticsIds != null">semantics_ids,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="item.createTime != null">#{item.createTime},</if>
            <if test="ruleId != null and ruleId != ''">#{ruleId},</if>
            <if test="item.ruleCondDetail != null">#{item.ruleCondDetail},</if>
            <if test="item.ruleCondType != null">#{item.ruleCondType},</if>
            <if test="item.ruleOrder != null">#{item.ruleOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="item.semanticsIds != null">#{item.semanticsIds},</if>
        </trim>
    </sql>
    <!-- update qc_rule_cond sql -->
    <sql id="updateQcRuleCondSql">
		;update qc_rule_cond
        <trim prefix="SET" suffixOverrides=",">
            update_time = now(),
            <if test="item.ruleCondDetail != null">rule_cond_detail = #{item.ruleCondDetail},</if>
            <if test="item.ruleCondType != null">rule_cond_type = #{item.ruleCondType},</if>
            <if test="item.ruleOrder != null">rule_order = #{item.ruleOrder},</if>
            <if test="item.ruleOrder != null">rule_order = #{item.ruleOrder},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            semantics_ids=#{item.semanticsIds}
        </trim>
        where id = #{item.id}
	</sql>
	<sql id="deleteQcRuleCondSql">
		delete from qc_rule_cond where id =
            #{item.id}
	</sql>
	<!-- foreach updete qc_rule_cond sql -->
    <sql id="editSubRuleSql">
		<if test="filterList != null and filterList.size() > 0">
        	<foreach item="item" collection="filterList"  >
            	<if test="item.id == null or item.id == ''">
            		<include refid="insertQcRuleCondSql"></include>
            	</if>
            	<if test="item.id != null and item.id != ''">
            		<if test="item.status != null and item.status == 2">
            			;<include refid="deleteQcRuleCondSql"></include>
            		</if>
            		<if test="item.status != null and item.status != 2">
            			<include refid="updateQcRuleCondSql"></include>
            		</if>
            	</if>
        	</foreach>
        </if>
        <if test="hitList != null and hitList.size() > 0">
        	<foreach item="item" collection="hitList"  >
        		<if test="item.id == null or item.id == ''">
            		<include refid="insertQcRuleCondSql"></include>
            	</if>
            	<if test="item.status != null and item.status == 2">
           			;<include refid="deleteQcRuleCondSql"></include>
           		</if>
           		<if test="item.status != null and item.status != 2">
           			<include refid="updateQcRuleCondSql"></include>
           		</if>
        	</foreach>
        </if>
<!--         <if test="deleteList != null and deleteList.size() > 0"> -->
<!--         	;<include refid="deleteQcRuleCondSql"></include> -->
<!--         </if> -->
	</sql>
    
    <select id="selectQcRuleList" parameterType="map" resultMap="QcRuleResult">
        <include refid="selectQcRuleVo"/>
        <where>
            <if test="ruleName != null and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="ruleType != null and ruleType != ''"> and rule_type = #{ruleType}</if>
            <if test="ruleGroup != null  and ruleGroup != ''"> and rule_group = #{ruleGroup}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="ruleId != null and ruleId != ''"> and rule_id = #{ruleId}</if>
            <if test="parentId == null or parentId == ''"> and qr.parent_id is null</if>
            <if test="ids != null and ids.length >0">
            	and qr.rule_id in (
            	<foreach item="id" collection="ids" separator=",">
		            #{id}
		        </foreach>
            	)
            </if>
        </where>
    </select>
    <select id="selectSubQcRuleList" parameterType="QcRule" resultMap="QcRuleResult">
        <include refid="selectQcRuleVo"/> 
        <where>
            <if test="parentId != null and parentId != ''"> and qr.parent_id =#{parentId}</if>
            <if test="parentId == null or parentId == ''"> and false</if>
<!--             <if test="status != null and status != ''"> and qr.status=#{status}</if> -->
        </where>
    </select>

    <select id="selectQcRuleByRuleId" parameterType="Long" resultMap="QcRuleResult">
        <include refid="selectQcRuleVo"/>
        where rule_id = #{ruleId}
    </select>
    <select id="selectQcRule" parameterType="QcRule" resultMap="QcRuleResult">
        <include refid="selectQcRuleVo"/>
        where rule_id = #{ruleId}
    </select>
	<select id="selectQcRuleCond" parameterType="QcRuleCond" resultMap="QcRuleCondResult">
        <include refid="selectQcRuleCondVo"/>
        where rule_id = #{ruleId}
        order by rule_order asc
    </select>
    <select id="selectQcRuleCondList" parameterType="QcRuleCond" resultMap="QcRuleCondResult">
        <include refid="selectQcRuleCondVo"/>
        <where>
        	<if test="ruleId != null and ruleId != ''"> and rule_id = ${ruleId}</if>
        	<if test="ruleCondType != null  and ruleCondType != ''"> and rule_cond_type = ${ruleCondType}</if>
        </where>
        order by rule_order asc
    </select>
    <insert id="insertQcRule" parameterType="QcRule" useGeneratedKeys="true" keyProperty="ruleId">
        insert into qc_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="ruleGroup != null">rule_group,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ruleClassificate != null">rule_classificate,</if>
            status
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="ruleGroup != null">#{ruleGroup},</if>
            <if test="createTime != null">now(),</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ruleClassificate">#{ruleClassificate},</if>
            1
        </trim>
    </insert>
    <insert id="insertQcRuleCond" parameterType="QcRule" >
    	update qc_rule set rule_id=#{ruleId} where rule_id = #{ruleId}
        <include refid="editSubRuleSql"></include>
    </insert>

    <update id="updateQcRule" parameterType="QcRule">
        update qc_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="ruleGroup != null">rule_group = #{ruleGroup},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = now(),</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="ruleClassificate">rule_classificate = #{ruleClassificate},</if>
        </trim>
        where rule_id = #{ruleId}
        <include refid="editSubRuleSql"></include>
    </update>

    <delete id="deleteQcRuleByRuleId" parameterType="Long">
        delete from qc_rule where rule_id = #{ruleId};
        delete from qc_rule_cond where rule_id = #{ruleId};
    </delete>

    <delete id="deleteQcRuleByRuleIds" parameterType="String">
        delete from qc_rule where rule_id in
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
        ;delete from qc_rule_cond where rule_id in
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>
    <select id="selectQcRuleRecording" resultType="map">
    	select id,agent_name as agentName from qc_rule_recording
    </select>
    <select id="queryKeyWordLibList" resultType="map">
        SELECT
            qkl.id as id,
            qkl.keyword_library_name as name,
            qkl.status as status
        FROM
            qc_keyword_library qkl
        WHERE
            qkl.is_deleted = '0'
        ORDER BY
            qkl.id ASC
    </select>
    <select id="selectDictDataByType" parameterType="String" resultType="map">
		select dict_label as label, dict_value as value 
		from sys_dict_data
		where status = '0' and dict_type = #{dictType} order by dict_sort asc
	</select>
</mapper>