package com.ideal.gen;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.ideal.common.security.annotation.EnableCustomConfig;
import com.ideal.common.security.annotation.EnableRyFeignClients;

/**
 * 代码生成
 * 
 * <AUTHOR>
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class GenApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(GenApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ gen 代码生成模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
