<template>
  <div class="app-container">
    <el-row :gutter="40">
      <el-col :span="4" style="border-right:1px solid #F2F6FC;padding:0;">
        <div class="sortItem row spaceBet" @click="handleScoringClassification()">
          <div>全部分类</div>
          <i class="el-icon-plus" @click="handleAdd"></i>
        </div>
        <div class="sortChild">
          <div v-for="(item, index) in scoringClassificationList" :key="index" class="sortItem row spaceBet childItem"
            :class="{ 'active-item': activeScoringClassification === item }" @click="handleScoringClassification(item)">
            <div>{{ item.scoringClassificationName }}</div>
            <el-dropdown size="mini" @command="(command) => handleCommand(command, item)">
              <i class="el-icon-more"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="handleReset" icon="el-icon-refresh-right">重命名</el-dropdown-item>
                <el-dropdown-item command="handleDelete" icon="el-icon-delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-col>

      <el-col :span="20">
        <el-form :model="queryParams" ref="queryForm" size="mini" :inline="true" label-width="68px" class="row spaceBet"
          style="align-items:flex-start;">
          <el-form-item label="" prop="scoringItemName">
            <el-input v-model="queryParams.scoringItemName" placeholder="评分名称" clearable style="width: 240px">
              <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
            </el-input>
          </el-form-item>
          <el-row :gutter="10">
            <el-col :span="1.5">
              <el-button v-hasPermi="['quality:scoringItem:add']" type="primary" plain icon="el-icon-plus" size="mini"
                @click="addScoringItem">新增评分细则</el-button>
            </el-col>
          </el-row>
        </el-form>
        <el-table v-loading="loading" :data="scoringItemList">
          <el-table-column label="评分名称" align="center" prop="scoringItemName" />
          <el-table-column label="归属模板" align="center" prop="scoringTemplateName" />
          <el-table-column label="归属分类" align="center" prop="scoringClassificationName" />
          <el-table-column label="命中分数" align="center" prop="hitRuleScore" />
          <el-table-column label="评分描述" align="center" prop="scoringItemDescription" :show-overflow-tooltip="true" />
          <el-table-column label="是否启用" align="center">
            <template slot-scope="scope">
              <el-switch active-value="1" inactive-value="0" v-model="scope.row.status"
                @change="handleScoringItemStatus(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="editScoringItem(scope.row)"
                style="margin-right:10px;">修改</el-button>
              <el-popconfirm title="你确定要删除吗？" @confirm="delScoringItem(scope.row.id)">
                <el-button slot="reference" size="mini" type="text" icon="el-icon-delete">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getScoringItemList" />
      </el-col>
    </el-row>

    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="scoringClassificationAddOrUpdateForm" size="mini" :inline="true" label-position="top"
        :model="scoringClassificationAddOrUpdateForm" :rules="scoringClassificationAddOrUpdateFormRules"
        label-width="160px" class="scoringClassificationAddOrUpdateForm">
        <el-form-item label="模板分类名称" prop="scoringClassificationName">
          <el-input v-model="scoringClassificationAddOrUpdateForm.scoringClassificationName" placeholder="请输入评分模板分类名称"
            maxlength="30" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitScoringClassificationAddOrUpdateForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="scoringItemTitle" :visible.sync="scoringItemDialog" width="50%" append-to-body
      :close-on-click-modal="false">
      <el-form size="mini" :inline="true" label-position="top" ref="scoringItemForm" :model="scoringItemForm"
        :rules="scoringItemFormRules" label-width="100px" class="scoringItemForm">
        <el-form-item label="评分分类" prop="scoringClassificationId">
          <el-select v-model="scoringItemForm.scoringClassificationId" placeholder="请选择评分分类" clearable>
            <el-option v-for="item in scoringClassificationList" :key="item.id" :label="item.scoringClassificationName"
              :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="评分名称" prop="scoringItemName">
          <el-input v-model="scoringItemForm.scoringItemName" placeholder="请输入评分名称" />
        </el-form-item>
        <el-form-item label="评分描述" prop="scoringItemDescription">
          <el-input type="textarea" v-model="scoringItemForm.scoringItemDescription" placeholder="请输入评分描述" />
        </el-form-item>
        <el-form-item label="命中分数(加分/减分)" prop="hitRuleScore">
          <el-input type="number" placeholder="请输入命中分数" v-model="scoringItemForm.hitRuleScore" />
        </el-form-item>
        <el-form-item label="命中规则" :rules="{
          required: true, message: '命中规则不能为空', trigger: 'blur'
        }">
          <div v-for="(item, ruleGroupIndex) in scoringItemForm.qcScoringHitRuleGroupDTOList" :key="ruleGroupIndex">
            <el-tag v-if="ruleGroupIndex > 0" effect="dark" size="mini" style="margin-bottom: 20px;">
              或
            </el-tag>
            <el-card class="box-card" shadow="never" style="margin-bottom: 20px;" body-style="background-color: #FBFBFB">
              <div slot="header" class="clearfix">
                <div style="display: flex; flex-flow: row nowrap; justify-content: space-between; align-items: center;">
                  <div>条件组合{{ ruleGroupIndex + 1 }}</div>
                  <div>
                    <el-button size="mini" @click="removeScoringHitRuleGroup(item)"
                      :disabled="ruleGroupIndex === 0">移除组合</el-button>
                    <el-button @click="addScoringHitRuleGroup()" size="mini">添加组合</el-button>
                    <el-button size="mini" v-if="item.status === '1'"
                      @click="disableScoringHitRuleGroup(ruleGroupIndex, item)">禁用组合</el-button>
                    <el-button size="mini" v-if="item.status === '0'"
                      @click="enableScoringHitRuleGroup(ruleGroupIndex, item)">启用组合</el-button>
                  </div>
                </div>
              </div>
              <div>
                <div style="display: flex; flex-flow: row nowrap; justify-content: start;">
                  <div>
                    符合下列
                  </div>
                  <div style="margin-left: 10px;">
                    <el-form-item class="ruleGroupModeClass">
                      <el-select v-model="item.ruleGroupMode" placeholder="请选择" clearable
                        :disabled="item.status === '0'">
                        <el-option v-for="item in rule_group_mode" :key="item.value" :label="item.label"
                          :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </div>
                  <div style="margin-left: 10px;">规则，则【条件组合{{ ruleGroupIndex + 1 }}】命中
                  </div>
                </div>
                <el-form-item class="scoringSelect" v-for="(children, ruleItemIndex) in item.qcScoringHitRuleItemList"
                  label="包含" :key="ruleItemIndex">
                  <div class="ruleItemClass">
                    <el-select v-model="children.ruleClassification" placeholder="请选择"
                      @change="handleRuleClassification(children, ruleGroupIndex, ruleItemIndex)" clearable
                      style="margin-right: 10px;" :disabled="children.status === '0'">
                      <el-option v-for="dict in dict.type.quality_rule_classification" :key="dict.value"
                        :label="dict.label" :value="dict.value" />
                    </el-select>
                    <el-select v-model="children.ruleId" placeholder="请选择" clearable
                      style="margin-left: 10px; margin-right: 10px;" :disabled="children.status === '0'">
                      <el-option v-for="item in children.ruleList" :key="item.ruleId" :label="item.ruleName"
                        :disabled="item.status !== 1" :value="item.ruleId" />
                    </el-select>
                    <el-button size="mini" @click="removeScoringHitRuleItem(ruleGroupIndex, children)"
                      :disabled="ruleItemIndex === 0"><i class="el-icon-minus" style="font-weight: bolder;"></i></el-button>
                    <el-button size="mini" @click="addScoringHitRuleItem(ruleGroupIndex, ruleItemIndex)"><i class="el-icon-plus" style="font-weight: bolder;"></i></el-button>
                    <el-button v-if="children.status === '0'" size="mini"
                      @click="enableScoringHitRuleItem(ruleGroupIndex, ruleItemIndex)"><svg-icon style="font-size: 12px; font-weight: bolder;" icon-class="enabled" /></el-button>
                    <el-button v-else size="mini"
                      @click="disableScoringHitRuleItem(ruleGroupIndex, ruleItemIndex)"><svg-icon style="font-size: 12px; font-weight: bolder;" icon-class="disabled" /></el-button>
                  </div>
                </el-form-item>
              </div>
            </el-card>
          </div>
        </el-form-item>

      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="scoringItemDialog = false" size="small">取 消</el-button>
        <el-button type="primary" @click="subScoringItemForm" size="small">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { getQcScoringClassificationList, addQcScoringClassification, updateQcScoringClassification, removeQcScoringClassification } from "@/api/quality/scoringClassification"
import { getQcScoringItemList, getQcScoringItem, addQcScoringItem, updateQcScoringItem, removeQcScoringItem, updateQcScoringItemStatus } from "@/api/quality/scoringItem"
import { listRule } from "@/api/quality/rule";
import { getAll } from "@/api/quality/manualRule";

export default {
  name: "ScoringClassification",
  dicts: ['quality_rule_classification'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      open: false,
      // 表单参数
      form: {},
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        scoringItemName: '',
        scoringClassificationId: null
      },

      scoringClassificationList: [],
      scoringItemForm: {
        qcScoringHitRuleGroupDTOList: [
          {
            qcScoringHitRuleItemList: [{}]
          }
        ]
      },
      scoringItemDialog: false,
      scoringItemTitle: '',
      scoringItemFormRules: {
        scoringItemName: [
          { required: true, message: "评分名称不能为空", trigger: "blur" }
        ],
        hitRuleScore: [
          { required: true, message: "命中分数不能为空", trigger: "blur" }
        ],
        scoringClassificationId: [
          { required: true, message: "评分分类不能为空", trigger: "change" }
        ]
      },
      scoringClassificationAddOrUpdateFormRules: {
        scoringClassificationName: [
          { required: true, message: "评分模板分类名称不能为空", trigger: "blur" }
        ],
      },
      scoringClassificationAddOrUpdateForm: {
        scoringClassificationName: ''
      },
      scoringItemList: null,
      rule_group_mode: [
        { label: "全部", value: "1" },
        { label: "任一", value: "2" }
      ],
      activeScoringClassification: {},
      rules: {
        processRules: [],
        smartRules: [],
        artificialRules: []
      }
    };
  },
  created() {
    this.getQcScoringClassificationList();
    this.getScoringItemList();
  },
  methods: {
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleReset":
          this.handleUpdate(row);
          break;
        case "handleDelete":
          this.handleDelete(row);
          break;
        default:
          break;
      }
    },
    getQcScoringClassificationList() {
      this.scoringClassificationList = [];
      const scoringTemplateId = this.$route.params && this.$route.params.scoringTemplateId;
      getQcScoringClassificationList(scoringTemplateId).then(response => {
        this.scoringClassificationList = response.data;
      });
    },
    submitScoringClassificationAddOrUpdateForm: function () {
      this.$refs["scoringClassificationAddOrUpdateForm"].validate(valid => {
        if (valid) {
          const scoringTemplateId = this.$route.params && this.$route.params.scoringTemplateId;
          this.scoringClassificationAddOrUpdateForm.scoringTemplateId = scoringTemplateId;
          if (this.scoringClassificationAddOrUpdateForm.id != undefined) {
            updateQcScoringClassification(this.scoringClassificationAddOrUpdateForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getQcScoringClassificationList();
              this.getScoringItemList();
            });
          } else {
            addQcScoringClassification(this.scoringClassificationAddOrUpdateForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getQcScoringClassificationList();
              this.getScoringItemList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      const scoringClassificationId = row.id;
      this.$modal.confirm('确定删除该分类吗').then(function () {
        return removeQcScoringClassification(scoringClassificationId);
      }).then(() => {
        this.getQcScoringClassificationList();
        this.getScoringItemList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加评分模板分类";
    },
    handleUpdate(row) {
      this.reset();
      let scoringClassificationAddOrUpdateForm = JSON.parse(JSON.stringify(row))
      this.scoringClassificationAddOrUpdateForm = scoringClassificationAddOrUpdateForm
      this.open = true;
      this.title = "修改评分模板分类";
    },
    delScoringItem(id) {
      removeQcScoringItem(id).then(response => {
        this.getScoringItemList();
      });
    },
    getScoringItemList() {
      this.loading = true;
      const queryParams = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize
      };
      const payload = {
        scoringTemplateId: this.$route.params && this.$route.params.scoringTemplateId,
        scoringItemName: this.queryParams.scoringItemName,
        scoringClassificationId: this.activeScoringClassification.id
      };
      getQcScoringItemList(queryParams, payload).then(response => {
        this.scoringItemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.scoringClassificationAddOrUpdateForm = {};
      this.resetForm("scoringClassificationAddOrUpdateForm");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getScoringItemList();
    },
    addScoringItem() {
      this.resetScoringItem();
      this.scoringItemDialog = true;
      this.scoringItemTitle = "添加评分细则";
    },
    editScoringItem(row) {
      this.resetScoringItem();
      getQcScoringItem(row.id).then(response => {
        this.scoringItemForm = response.data;
        this.scoringItemDialog = true;
        this.scoringItemTitle = "修改评分细则";
        const qcScoringHitRuleGroupDTOList = this.scoringItemForm.qcScoringHitRuleGroupDTOList;
        for (let i = 0; i < qcScoringHitRuleGroupDTOList.length; i++) {
          const qcScoringHitRuleItemList = qcScoringHitRuleGroupDTOList[i].qcScoringHitRuleItemList;
          for (let j = 0; j < qcScoringHitRuleItemList.length; j++) {
            this.scoringItemForm.qcScoringHitRuleGroupDTOList[i].qcScoringHitRuleItemList[j].ruleId = qcScoringHitRuleItemList[j].ruleId + ""
            this.handleRuleClassification(qcScoringHitRuleItemList[j], i, j);
          }
        }
      });
    },
    resetScoringItem() {
      this.scoringItemForm = {
        qcScoringHitRuleGroupDTOList: [
          {
            qcScoringHitRuleItemList: [{}]
          }
        ]
      };
      this.resetForm("scoringItemForm");
    },
    subScoringItemForm() {
      this.$refs["scoringItemForm"].validate(valid => {
        if (valid) {
          const scoringTemplateId = this.$route.params && this.$route.params.scoringTemplateId;
          this.scoringItemForm.templateId = scoringTemplateId;
          console.log(this.scoringItemForm)
          if (this.scoringItemForm.id != undefined) {
            updateQcScoringItem(this.scoringItemForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.scoringItemDialog = false;
              this.getScoringItemList();
            });
          } else {
            addQcScoringItem(this.scoringItemForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.scoringItemDialog = false;
              this.getScoringItemList();
            });
          }
        }
      });
    },
    removeScoringHitRuleItem(ruleGroupIndex, children) {
      var index = this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].qcScoringHitRuleItemList.indexOf(children)
      if (index !== -1) {
        this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].qcScoringHitRuleItemList.splice(index, 1)
      }
    },
    addScoringHitRuleItem(ruleGroupIndex, ruleItemIndex) {
      this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].qcScoringHitRuleItemList.push({ status: '1' });
    },
    enableScoringHitRuleItem(ruleGroupIndex, ruleItemIndex) {
      this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].qcScoringHitRuleItemList[ruleItemIndex].status = '1';
    },
    disableScoringHitRuleItem(ruleGroupIndex, ruleItemIndex) {
      this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].qcScoringHitRuleItemList[ruleItemIndex].status = '0';
    },
    addScoringHitRuleGroup() {
      this.scoringItemForm.qcScoringHitRuleGroupDTOList.push({
        qcScoringHitRuleItemList: [{}]
      });
    },
    removeScoringHitRuleGroup(item) {
      var index = this.scoringItemForm.qcScoringHitRuleGroupDTOList.indexOf(item)
      if (index !== -1) {
        this.scoringItemForm.qcScoringHitRuleGroupDTOList.splice(index, 1)
      }
    },
    enableScoringHitRuleGroup(ruleGroupIndex) {
      for (var i = 0; i < this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].qcScoringHitRuleItemList.length; i++) {
        this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].qcScoringHitRuleItemList[i].status = '1'
        this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].status = '1';
      }
    },
    disableScoringHitRuleGroup(ruleGroupIndex) {
      for (var i = 0; i < this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].qcScoringHitRuleItemList.length; i++) {
        this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].qcScoringHitRuleItemList[i].status = '0'
        this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex].status = '0';
      }
    },
    handleScoringClassification(row) {
      if (row) {
        this.activeScoringClassification = row;
      } else {
        this.activeScoringClassification = {};
      }
      this.getScoringItemList();
    },
    handleRuleClassification(row, ruleGroupIndex, index) {
      const ruleClassification = row.ruleClassification;
      const payload = {
        random: this.randomString(10)
      };
      if (ruleClassification === '1') {
        payload.ruleType = "1"
        listRule({ pageNum: 1, pageSize: 10000 }, payload).then(response => {
          const rows = response.rows.map(item => ({
            ruleName: item.ruleName,
            ruleId: item.ruleId + "",
            status: item.status
          }));
          this.$set(
            this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex]
              .qcScoringHitRuleItemList[index],
            'ruleList',
            rows
          );
        });
      } else if (ruleClassification === '2') {
        getAll().then(response => {
          const rows = response.data.map(item => ({
            ruleName: item.ruleName,
            ruleId: item.id + "",
            status: item.status
          }));
          this.$set(
            this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex]
              .qcScoringHitRuleItemList[index],
            'ruleList',
            rows
          );
        });
      } else if (ruleClassification === '3') {
        payload.ruleType = '3'
        listRule({ pageNum: 1, pageSize: 10000 }, payload).then(response => {
          const rows = response.rows.map(item => ({
            ruleName: item.ruleName,
            ruleId: item.ruleId + "",
            status: item.status
          }));
          this.$set(
            this.scoringItemForm.qcScoringHitRuleGroupDTOList[ruleGroupIndex]
              .qcScoringHitRuleItemList[index],
            'ruleList',
            rows
          );
        });
      }
    },
    randomString(length) {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    },
    handleScoringItemStatus(row) {
      this.scoringItemForm = row;
      updateQcScoringItemStatus(this.scoringItemForm).then(response => {
        if (row.status === '0') {
          this.$modal.msgSuccess("禁用成功");
        }
        if (row.status === '1') {
          this.$modal.msgSuccess("启用成功");
        }
        this.getScoringItemList();
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.sortItem {
  border-left: 2px solid white;
  font-size: 13px;
  padding: 8px 14px;
  cursor: pointer;
}

.sortItem:hover {
  border-left: 2px solid #409EFF;
  background: #ecf5ff;
}

div.sortItem.row.spaceBet.childItem.active-item {
  border-left: 2px solid #409EFF;
  background: #ecf5ff;
}

.childItem {
  padding-left: 30px;
}
</style>

<style scoped>
.scoringClassificationAddOrUpdateForm .el-form-item {
  width: 100%;
}

::v-deep .scoringClassificationAddOrUpdateForm .el-form-item .el-form-item__label {
  padding: 0px;
}

.scoringClassificationAddOrUpdateForm .el-select {
  width: 100%;
}

.scoringItemForm .el-form-item {
  width: 100%;
}

.scoringItemForm .el-select {
  width: 100%;
}

::v-deep .scoringItemForm .el-form-item .el-form-item__label {
  padding: 0px;
}

.scoringItemForm .scoringSelect .el-select {
  width: 100%;
}

.ruleItemClass {
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: stretch;
}

.box-card /deep/ .el-card__header {
  background-color: #EAF0FD;
  font-weight: bolder;
}
</style>
