<template>
  <div class="annotation-container" ref="container">
    <!-- 可标注内容区域 -->
    <div
      class="annotated-content"
      @mouseup="handleTextSelection"
      @contextmenu.prevent="showContextMenu"
    >
      <slot></slot>

      <!-- 标注框 -->
      <div
        v-for="annotation in annotations"
        :key="annotation.id"
        class="annotation-box"
        @click.stop="showAnnotationDetail(annotation)"
      ></div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-show="showMenu"
      class="custom-context-menu"
      :style="menuPosition"
    >
      <div class="menu-item" @click="openAnnotationModal">
        <i class="el-icon-edit"></i> 添加标注
      </div>
    </div>

    <!-- 标注编辑弹窗 -->
    <el-dialog
      :title="currentAnnotation ? '编辑标注' : '添加标注'"
      :visible.sync="showAnnotationModal"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form :model="annotationForm" label-width="100px">
        <el-form-item label="原始文本">
          <el-input
            v-model="annotationForm.originalText"
            type="textarea"
            :rows="3"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item label="修正文本" prop="correctText">
          <el-input
            v-model="annotationForm.correctText"
            type="textarea"
            :rows="3"
            placeholder="请输入修正后的文本"
          ></el-input>
        </el-form-item>
        <el-form-item label="标注类型">
          <el-select v-model="annotationForm.type" placeholder="请选择标注类型">
            <el-option
              v-for="item in annotationTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注信息">
          <el-input
            v-model="annotationForm.note"
            type="textarea"
            :rows="2"
            :maxlength="200"
            placeholder="可输入备注信息"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAnnotationModal = false">取 消</el-button>
        <el-button
          type="primary"
          @click="saveAnnotation"
          :loading="saving"
        >保 存</el-button>
      </span>
    </el-dialog>

    <!-- 标注详情弹窗 -->
    <el-dialog
      title="标注详情"
      :visible.sync="showDetailModal"
      width="40%"
      :close-on-click-modal="false"
    >
      <div class="annotation-detail">
        <div class="detail-row">
          <span class="detail-label">标注类型：</span>
          <el-tag :type="getTagType(currentAnnotation.type)">
            {{ getTypeLabel(currentAnnotation.type) }}
          </el-tag>
        </div>
        <div class="detail-row">
          <span class="detail-label">原始文本：</span>
          <div class="detail-content">{{ currentAnnotation.originalText }}</div>
        </div>
        <div class="detail-row">
          <span class="detail-label">修正文本：</span>
          <div class="detail-content">{{ currentAnnotation.correctText }}</div>
        </div>
        <div class="detail-row" v-if="currentAnnotation.note">
          <span class="detail-label">备注信息：</span>
          <div class="detail-content">{{ currentAnnotation.note }}</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDetailModal = false">关 闭</el-button>
        <el-button type="primary" @click="editCurrentAnnotationD">编 辑</el-button>
        <el-button type="danger" @click="confirmDelete">删 除</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getAnnotation, delAnnotation, addAnnotation, updateAnnotation } from "@/api/quality/annotation";
export default {
  name: 'EditableTextAnnotation',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    parentData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showMenu: false,
      menuPosition: {
        left: '0px',
        top: '0px'
      },
      selectedText: '',
      selectedRange: null,
      selectedRect: null,
      annotations: [],
      showAnnotationModal: false,
      showDetailModal: false,
      saving: false,
      currentAnnotation: {
        id: '',
        originalText: '',
        correctText: '',
        type: '',
        note: '',
        position: {}
      },
      annotationForm: {
        originalText: '',
        correctText: '',
        type: 'correction',
        note: ''
      },
      annotationTypes: [
        { value: 'correction', label: '文本修正' },
        { value: 'highlight', label: '重点标注' },
        { value: 'question', label: '疑问点' },
        { value: 'comment', label: '评注' }
      ]
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.annotations = [...val]
      }
    }
  },
  mounted() {
    document.addEventListener('click', this.handleDocumentClick)
    window.addEventListener('resize', this.hideContextMenu)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleDocumentClick)
    window.removeEventListener('resize', this.hideContextMenu)
  },
  methods: {
    // 处理文本选择
    handleTextSelection() {
      const selection = window.getSelection()
      if (!selection.isCollapsed) {
        this.selectedText = selection.toString().trim()
        if (this.selectedText) {
          this.selectedRange = selection.getRangeAt(0)
          this.selectedRect = this.selectedRange.getBoundingClientRect()
        }
      }
    },

    // 显示右键菜单
    showContextMenu(event) {
      const range = document.createRange();
      range.selectNodeContents(event.target);

      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
      this.selectedText = selection.toString().trim()
      this.showMenu = true
      this.menuPosition = {
        left: `${event.clientX}px`,
        top: `${event.clientY}px`
      }
      /*if (this.selectedText) {
        this.menuPosition = {
          left: `${e.clientX}px`,
          top: `${e.clientY}px`
        }
        this.showMenu = true
      }*/
    },

    // 隐藏右键菜单
    hideContextMenu() {
      this.showMenu = false
    },

    // 处理文档点击事件
    handleDocumentClick(e) {
      if (!this.$el.contains(e.target)) {
        this.hideContextMenu()
        this.clearSelection()
      }
    },

    // 清除选择
    clearSelection() {
      //window.getSelection().removeAllRanges()
      this.selectedText = ''
      this.selectedRange = null
      this.selectedRect = null
    },

    // 打开标注弹窗
    openAnnotationModal() {
      if (!this.selectedText) return

      this.annotationForm = {
        originalText: this.selectedText,
        correctText: this.selectedText,
        type: 'correction',
        note: ''
      }

      this.showAnnotationModal = true
      this.hideContextMenu()
    },

    // 保存标注
    saveAnnotation() {
      if (!this.annotationForm.correctText.trim()) {
        this.$message.warning('请输入修正文本')
        return
      }
      this.saving = true
      if(this.parentData){
        if(this.parentData.annotations){
          this.annotations=this.parentData.annotations;
        }
        this.annotationForm.dataId= this.parentData.dataId;
        let url = this.parentData.oldUrl;
        if(url!=null&&url!=undefined&&url!=""){
          this.annotationForm.oldUrl= this.parentData.oldUrl;
          this.annotationForm.newUrl= this.parentData.newUrl;
          this.annotationForm.newUrlName= this.parentData.newUrlName;
        }
        this.annotationForm.startTime= this.parentData.startTime;
        this.annotationForm.endTime=this.parentData.endTime;
      }
      const annotationData = {
        ...this.annotationForm
      }
      if (this.currentAnnotation.id) {
        const index = this.parentData.annotations.findIndex(a => a.id === this.currentAnnotation.id)
        annotationData.dataId= this.parentData.annotations[index].dataId;
        let url = this.parentData.annotations[index].oldUrl;
        if(url!=null&&url!=undefined&&url!=""){
          annotationData.oldUrl= this.parentData.annotations[index].oldUrl;
          annotationData.newUrl= this.parentData.annotations[index].newUrl;
          annotationData.newUrlName= this.parentData.annotations[index].newUrlName;
        }
        annotationData.startTime= this.parentData.annotations[index].startTime;
        annotationData.endTime= this.parentData.annotations[index].endTime;
        annotationData.id= this.currentAnnotation.id;
        updateAnnotation(annotationData).then(response => {
          // 更新现有标注
          annotationData.id = this.currentAnnotation.id
          let date = new Date();
          const yyyy = date.getFullYear();
          const mm = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的
          const dd = String(date.getDate()).padStart(2, '0');
          const hh = String(date.getHours()).padStart(2, '0');
          const mi = String(date.getMinutes()).padStart(2, '0');
          const ss = String(date.getSeconds()).padStart(2, '0');
          annotationData.createTime = `${yyyy}-${mm}-${dd} ${hh}:${mi}:${ss}`;
         // annotationData.position = this.currentAnnotation.position;
          const index = this.annotations.findIndex(a => a.id === this.currentAnnotation.id)
          if (index !== -1) {
            this.annotations.splice(index, 1, annotationData)
          }
          this.currentAnnotation = {};
          // 模拟异步保存
          setTimeout(() => {
            this.saving = false
            this.showAnnotationModal = false
            this.clearSelection()
            this.$emit('child-to-parent', this.annotations)
            this.$message.success('标注保存成功')
          }, 500)
        });

      } else {
        const containerRect = this.$refs.container.getBoundingClientRect()

        /*const position = {
          x: this.selectedRect.left - containerRect.left,
          y: this.selectedRect.top - containerRect.top,
          width: this.selectedRect.width,
          height: this.selectedRect.height
        }*/
        const annotationData = {
          ...this.annotationForm
        }
        addAnnotation(annotationData).then(response => {
          // 添加新标注
          annotationData.id =  response.data.id;

          let date = new Date();
          const yyyy = date.getFullYear();
          const mm = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的
          const dd = String(date.getDate()).padStart(2, '0');
          const hh = String(date.getHours()).padStart(2, '0');
          const mi = String(date.getMinutes()).padStart(2, '0');
          const ss = String(date.getSeconds()).padStart(2, '0');
          annotationData.createTime = `${yyyy}-${mm}-${dd} ${hh}:${mi}:${ss}`;
          this.annotations.push(annotationData)
          // 模拟异步保存
          setTimeout(() => {
            this.saving = false
            this.showAnnotationModal = false
            this.clearSelection()
            this.$emit('child-to-parent', this.annotations)
            this.$message.success('标注保存成功')
          }, 500)
        });
      }


    },

    // 显示标注详情
    showAnnotationDetail(annotation) {
      this.currentAnnotation = { ...annotation }
      this.showDetailModal = true
    },

    // 编辑当前标注
    editCurrentAnnotation(annotation) {
      this.currentAnnotation = { ...annotation }
      this.annotationForm = {
        originalText: this.currentAnnotation.originalText,
        correctText: this.currentAnnotation.correctText,
        type: this.currentAnnotation.type,
        note: this.currentAnnotation.note || ''
      }

      this.showDetailModal = false
      this.showAnnotationModal = true
    },
    editCurrentAnnotationD() {
      this.editCurrentAnnotation(this.currentAnnotation)
    },

    // 确认删除
    confirmDelete() {
      this.$confirm('确定要删除此标注吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAnnotation()
      }).catch(() => {})
    },

    // 删除标注
    deleteAnnotation() {
      /*this.$confirm('确定要删除此标注吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {*/
        delAnnotation(this.currentAnnotation.id).then(response => {
          this.annotations=this.parentData.annotations;
          this.annotations = this.annotations.filter(a => a.id !== this.currentAnnotation.id)
          this.$emit('child-to-parent', this.annotations)
          this.$message.success('标注已删除')
          this.showDetailModal = false
        });
      /*}).catch(() => {})
      this.annotations = this.annotations.filter(
        a => a.id !== this.currentAnnotation.id
      )*/
    },

    // 获取标签类型
    getTagType(type) {
      switch(type) {
        case 'correction': return 'danger'
        case 'highlight': return 'warning'
        case 'question': return 'primary'
        case 'comment': return 'success'
        default: return 'info'
      }
    },
    // 获取类型标签
    getTypeLabel(type) {
      const item = this.annotationTypes.find(t => t.value === type)
      return item ? item.label : '未知类型'
    }
  }
}
</script>

<style scoped>
.annotation-container {
  position: relative;
  width: 100%;
  min-height: 200px;
}

.annotated-content {
  position: relative;
  padding: 15px;
  line-height: 1.8;
  white-space: pre-wrap;
  user-select: text;
}

.annotation-box {
  position: absolute;
  border-radius: 3px;
  cursor: pointer;
  pointer-events: auto;
  transition: all 0.2s;
}

.annotation-box:hover {
  transform: scale(1.01);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.custom-context-menu {
  position: fixed;
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 9999;
  min-width: 120px;
}

.menu-item {
  padding: 8px 15px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.menu-item i {
  margin-right: 5px;
}

.menu-item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.annotation-detail {
  padding: 10px;
}

.detail-row {
  margin-bottom: 15px;
  display: flex;
}

.detail-label {
  display: inline-block;
  width: 80px;
  color: #909399;
  font-weight: bold;
  flex-shrink: 0;
}

.detail-content {
  flex: 1;
  word-break: break-all;
}
</style>
