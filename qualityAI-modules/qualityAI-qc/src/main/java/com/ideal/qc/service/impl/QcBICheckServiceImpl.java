package com.ideal.qc.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcDataResult;
import com.ideal.qc.domain.QcManualTaskResult;
import com.ideal.qc.mapper.QcBICheckMapper;
import com.ideal.qc.mapper.QcBizItemMapper;
import com.ideal.qc.mapper.QcManualTaskResultMapper;
import com.ideal.qc.service.QcBICheckService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class QcBICheckServiceImpl implements QcBICheckService {
    @Resource
    private QcBICheckMapper qcBICheckMapper;
    @Resource
    private QcManualTaskResultMapper qcManualTaskResultMapper;
    @Override
    public List<Map<String,Object>> getVideoText(String bizNo) {
        return qcBICheckMapper.getVideoText(bizNo);
    }

    @Override
    public List<String> getVideoTextMatchKey(String bizNo) {
        return qcBICheckMapper.getVideoTextMatchKey(bizNo);
    }

    @Override
    public void insertQcDataResult(QcDataResult qcDataResult) {
        qcBICheckMapper.insertQcDataResult(qcDataResult);
    }

    @Override
    public List<QcDataResult> getResultListByBizNo(String bizNo) {
        return qcBICheckMapper.getResultListByBizNo(bizNo);
    }

    @Override
    public String tempFirstList(String id) {
        return qcBICheckMapper.tempFirstList(id);
    }

    @Override
    public String tempAIList(String bizNo) {
        return qcBICheckMapper.tempAIList(bizNo);
    }

    @Override
    public void updateManualStatus(QcDataResult qcDataResult) {
        qcBICheckMapper.updateManualStatus(qcDataResult);
    }

    @Override
    public void updateSmartStatus(QcDataResult qcDataResult) {
        qcBICheckMapper.updateSmartStatus(qcDataResult);
    }

    @Override
    public List<QcManualTaskResult> getManualResult(String bizNo) {
        QcManualTaskResult model = new QcManualTaskResult();
        model.setBizNo(bizNo);
        return qcManualTaskResultMapper.selectQcManualTaskResultList(model);
    }

    @Override
    public void addQcManualTaskResult(QcManualTaskResult manual) {
        qcManualTaskResultMapper.insertQcManualTaskResult(manual);
    }

    @Override
    public Map<String, String> getQcScoringHitRuleGroup(Long groupId) {
        return qcBICheckMapper.getQcScoringHitRuleGroup(groupId);
    }

    @Override
    public Map<String, Object> getAiResultByCondition(Map<String, String> param) {
        return qcBICheckMapper.getAiResultByCondition(param);
    }

    @Override
    public void updateAiSmartResult(QcManualTaskResult manual) {
        qcBICheckMapper.updateAiSmartResult(manual);
    }

    @Override
    public void addQcSmartTaskResult(QcManualTaskResult param) {
        qcBICheckMapper.addQcSmartTaskResult(param);
    }

    @Override
    public List<QcManualTaskResult> getSmartCheckList(String bizNo) {
        return qcBICheckMapper.getSmartCheckList(bizNo);
    }

    @Override
    public void updateSmartCountStatus(QcDataResult qcDataResult) {
        qcBICheckMapper.updateSmartCountStatus(qcDataResult);
    }

    @Override
    public void updateManualResult(JSONObject json) {
        qcBICheckMapper.updateManualResult(json);
    }

    @Override
    public List<QcManualTaskResult> manualResultList(String taskDetailId) {
        return qcBICheckMapper.manualResultList(taskDetailId);
    }

    @Override
    public List<QcDataResult> getResultByDetailId(String manualDetailId) {
        return qcBICheckMapper.getResultByDetailId(manualDetailId);
    }
}
