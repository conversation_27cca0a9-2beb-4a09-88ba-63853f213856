<template>
	<div class="app-container">
	    <el-row :gutter="40">
	    	<el-col :span="24">
	        	<el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px" class="row spaceBet" style="align-items:flex-start;">
	          		<el-form-item label="" prop="userName">
	            		<el-input v-model="queryData.ruleName" placeholder="搜索规则名称" clearable style="width: 240px"></el-input>
	          		</el-form-item>
			  		<el-form-item label="" prop="ruleId">
	  		     		<el-input v-model="queryData.ruleId" placeholder="搜索规则Id" clearable style="width: 240px"></el-input>
	      	  		</el-form-item>
			  		<el-form-item label="状态" prop="status">
  	          			<el-select v-model="queryData.status" clearable style="width: 240px"  >
	  	              		<el-option label="启用" value="1" />
	  				  		<el-option label="禁用" value="2" />
	  	          		</el-select>
      	  			</el-form-item>
	           		<el-form-item>
	            		<el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
	            		<el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
	          		</el-form-item> 
	          		<el-row :gutter="10">
	            		<el-col :span="1.5">
	              			<el-button type="primary" plain icon="el-icon-plus" size="small" @click="goRule">新建规则</el-button>
            			</el-col>
	          		</el-row>
	        	</el-form>
	        	<el-table v-loading="loading" :data="ruleList">
	          		<el-table-column label="规则ID" align="left" key="ruleId" prop="ruleId" />
	          		<el-table-column label="规则名称" align="left" key="ruleName" prop="ruleName" show-overflow-tooltip width="300"/>
	          		<el-table-column label="规则类型" align="left" key="ruleClassificate" prop="ruleClassificate" :formatter="format" />
			  		<el-table-column label="状态" align="left" key="status" prop="status" :formatter="format">
						<template slot-scope="scope"><div :style="`color:${scope.row.status==2?'red':'green'}`">{{format(scope.row,{property:'status'},scope.row.status)}}</div></template>
					</el-table-column>
	  		  		<el-table-column label="创建人" align="left" key="createBy" prop="createBy"/>
	          		<el-table-column label="创建时间" align="left" key="createTime" prop="createTime"  width="200" />	
	          		<el-table-column label="更新人" align="left" key="updateBy" prop="updateBy"/>
	          		<el-table-column label="更新时间" align="left" key="updateTime" prop="updateTime"  width="200" />
	          		<el-table-column label="操作" align="left" width="160" class-name="small-padding fixed-width">
	            		<template slot-scope="scope">
	              			<el-button v-if="scope.row.status==2" size="mini" type="text" @click="goRule(scope.row)" style="margin-right:10px;">修改</el-button>
							<el-button v-if="scope.row.status==1" size="mini" type="text" @click="goRule(scope.row,'view')" style="margin-right:10px;">查看</el-button>
				  			<el-popconfirm :title="scope.row.status==2?'你确定要启用吗？':'你确定要禁用吗？'" @confirm="updRow(scope.row)">
				    			<el-button  slot="reference" size="mini" type="text" :style="`color:${scope.row.status==2?'#1890ff':'red'}`">{{scope.row.status==2?'启用':'禁用'}}</el-button>
			  				</el-popconfirm>
	            		</template>
	          		</el-table-column>
        		</el-table>
	        	<pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
			</el-col>
		</el-row>
	</div>
</template>

<script>
import { listRule,updateRule} from "@/api/quality/rule";
import { queryListByType,listData } from "@/api/system/dict/data";

export default {
	name: "User",
  	data() {
    	return {
	      	// 遮罩层
	      	loading: true,
	      	// 总条数
	      	total: 0,
	      	// 弹出层标题
	      	title: "",
	      	open: false,
	      	// 表单参数
	      	sortForm: {dictLabel:""},
	      	// 查询参数
	      	queryParams: {
	        	pageNum: 1,
	        	pageSize: 10
	      	},
		  	queryData:{
				ruleGroup:"",
				ruleType:"1"
		  	},
	      	// 表单校验
	      	rules: {
				dictLabel: [
	          		{ required: true, message: "分类名称不能为空", trigger: "11" }
	        	]
	      	},
	      	sortList:[],
		  	ruleList:[]
    	};
  	},
  	watch: {
   
  	},
	created() {
    	this.getList();
		queryListByType	({dictTypeList:["quality_smart_rule_group","quality_rule_classificate"],status:"0"}).then(response => {
			if(response.data){
				this.sortList = response.data["quality_smart_rule_group"];
				this.ruleTypeList = response.data["quality_rule_classificate"];
			}
		});
	},
	methods: {
		format(row,column,cellValue,index){
			var list = [];
			switch(column.property){
				case 'ruleClassificate':
					list = this.ruleTypeList;
					break;
				case 'status':
					list = [{dictValue:1,dictLabel:"启用"},{dictValue:2,dictLabel:"禁用"}];
					break;
				default:
					break;
			}
			for(var index in list){
				if(list[index]["dictValue"] == cellValue){
					return list[index]["dictLabel"];
				}
			}
		},
	    goRule(row,op){
	      	this.$emit("goEdit",row,op);
	    },
		updRow(tmp) {
			tmp.status = tmp.status==1?2:1;
	        updateRule(tmp).then(res=>{
	         	if(res.code == 200){
	       			this.getList();
	       	   		this.msgSuccess("更新成功");
	         	}
	       	}).finally(v=>{});
	    },
	    /** 查询用户列表 */
	    getList() {
	      	this.loading = true;
		  	this.queryData['isPg'] = true;
	      	listRule(this.queryParams,this.queryData).then(response => {
	          	this.ruleList = response.rows;
	          	this.total = response.total;
	      	}).finally(v=>{
	          	this.loading = false;
		  	});
	    },
		/** 查询用户列表 */
	    getSortList() {
			this.loading = true;
	      	listData({dictType:"quality_smart_rule_group",pageNum:1,pageSize:100}).then(response => {
	          	this.sortList = response.rows;
	        }).finally(v=>{
	          	this.loading = false;
			});
	    },
	    // 取消按钮
	    cancel() {
	      	this.open = false;
	    },
	    // 表单重置
	    reset() {
	      	this.form = {};
	      	this.resetForm("form");
	    },
	    /** 搜索按钮操作 */
	    handleQuery() {
	      	this.queryParams.pageNum = 1;
	      	this.getList();
	    },
	    /** 重置按钮操作 */
	    resetQuery() {
	      	this.queryData = {};
	    },
	    /** 修改按钮操作 */
	    handleUpdate(row) {
	      	this.reset();
	      	let form = JSON.parse(JSON.stringify(row))
	      	this.form = form
	      	this.open = true;
	      	this.title = "修改分类名称";
	    },
  	}
};
</script>

<style rel="stylesheet/scss" lang="scss">
.sortItem{
  border-left: 2px solid white;
  font-size: 13px;
  padding: 8px 14px;
  cursor: pointer;
}
.sortItem:hover{
  border-left: 2px solid #409EFF;
  background: #ecf5ff;
}
.childItem{
  padding-left:30px;
}
</style>