package com.ideal.qc.service;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcKeywordLibraryClassification;

import java.util.List;

/**
 * 关键词库分类Service接口
 *
 * <AUTHOR>
 * @date 2025/5/20 17:45
 */
public interface QcKeywordLibraryClassificationService {

    /**
     * 查询关键词库分类列表
     *
     * @param payload 需要的一些参数
     * @return 关键词库分类列表
     */
    List<QcKeywordLibraryClassification> getQcKeywordLibraryClassificationList(JSONObject payload);

    /**
     * 新增关键词库分类
     *
     * @param payload 需要的一些参数
     */
    void addQcKeywordLibraryClassification(JSONObject payload);

    /**
     * 修改关键词库分类
     *
     * @param payload 需要的一些参数
     */
    void updateQcKeywordLibraryClassification(JSONObject payload);

    /**
     * 删除关键词库分类
     *
     * @param id 关键词库分类ID
     */
    void removeQcKeywordLibraryClassification(Long id);
}
