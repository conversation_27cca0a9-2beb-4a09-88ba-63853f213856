<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="规则名称" prop="ruleName">
        <el-input
          v-model="queryParams.ruleName"
          placeholder="请输入规则名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分组" prop="ruleGroup">
        <el-select v-model="queryParams.ruleGroup" placeholder="请选择组别" clearable>
          <el-option
            v-for="dict in dict.type.quality_rule_group"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
            @keyup.enter.native="handleQuery"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="检测方式" prop="detectionMode">
        <el-select v-model="queryParams.detectionMode" placeholder="请选择检测方式" clearable>
          <el-option
            v-for="dict in dict.type.quality_detection_mode"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
            @keyup.enter.native="handleQuery"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="质检对象" prop="detectionRole">
        <el-select v-model="queryParams.detectionRole" placeholder="请选择质检对象" clearable>
          <el-option
            v-for="dict in dict.type.quality_role"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
            @keyup.enter.native="handleQuery"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['quality:rule:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['quality:rule:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['quality:rule:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ruleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="规则Id" align="center" prop="ruleId" />-->
      <el-table-column label="规则名称" align="center" prop="ruleName" width="150"/>
      <el-table-column label="规则类型" align="center" prop="ruleType" width="150">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.quality_rule_type" :value="scope.row.ruleType"/>
        </template>
      </el-table-column>
      <el-table-column label="分组" align="center" prop="ruleGroup" width="150">
        <template slot-scope="scope">
        <dict-tag :options="dict.type.quality_rule_group" :value="scope.row.ruleGroup"/>
        </template>
      </el-table-column>
      <el-table-column label="检测方式" align="center" prop="detectionMode" width="150">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.quality_detection_mode" :value="scope.row.detectionMode"/>
        </template>
      </el-table-column>
      <el-table-column label="质检对象" align="center" prop="detectionRole" width="150">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.quality_role" :value="scope.row.detectionRole"/>
        </template>
      </el-table-column>
      <el-table-column label="匹配规则" align="center" prop="detectionContent" :show-overflow-tooltip="true" >
        <template slot-scope="scope">
          <span>{{scope.row.detectionContent}}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['quality:rule:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['quality:rule:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改质检规则对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col>
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="业务组别" prop="ruleGroup">
              <el-select v-model="form.ruleGroup" placeholder="请选择组别" clearable>
                <el-option
                  v-for="dict in dict.type.quality_rule_group"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测方式" prop="detectionMode">
              <el-select v-model="form.detectionMode" placeholder="请选择检测方式" clearable>
                <el-option
                  v-for="dict in dict.type.quality_detection_mode"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="质检对象" prop="detectionRole">
              <el-select v-model="form.detectionRole" placeholder="请选择质检对象" clearable>
                <el-option
                  v-for="dict in dict.type.quality_role"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规则类型" prop="ruleType">
              <el-select v-model="form.ruleType" placeholder="请选择规则类型" clearable>
                <el-option
                  v-for="dict in dict.type.quality_rule_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>



        <el-form-item prop="detectionContent">
          <span slot="label" style="display: inline-block">
            匹配规则
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content">
            　　&，逻辑与运算，其前后的对象都要满足，如：信号&比较差； <br/>
            　　|，逻辑或运算，其前后的对象要至少满足一个，如：你好|您好；<br/>
            　　!，逻辑非运算，不能满足其后的对象，如：!(挂失|注销)； <br/>
            　　#，逻辑近运算，按顺序满足各个关键词，最后的距离配置数字指定所有关键词之间的最大字数，如：(信号#差#换#位置#6)；"
              </div>
              <i class=" el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-input
            type="textarea"
            :rows="6"
            placeholder="请输入匹配规则"
            v-model="form.detectionContent">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRule, getRule, delRule, addRule, updateRule } from "@/api/quality/rule";
import { queryListByType } from "@/api/system/dict/data";

export default {
  name: "QualityRule",
  dicts: ['quality_role','quality_detection_mode','quality_rule_group','quality_rule_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 质检规则表格数据
      ruleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleName: null,
        ruleType: null,
        ruleGroup: null,
        detectionMode: null,
        detectionContent: null,
        detectionRole: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        ruleName: [
          { required: true, message: "规则名称不能为空", trigger: "blur" }
        ],
        ruleGroup: [
          { required: true, message: "组别不能为空", trigger: "blur" }
        ],
        detectionMode: [
          { required: true, message: "质检方式不能为空", trigger: "blur" }
        ],
        detectionRole: [
          { required: true, message: "质检对象不能为空", trigger: "blur" }
        ],
        detectionContent: [
          { required: true, message: "匹配规则不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
	console.log("----rule/index.vue----")
    this.getList();
	queryListByType	({dictTypes:["sys_user_sex","sys_show_hide"]}).then(response => {
	        console.log(response);
	      });
  },
  methods: {
    /** 查询质检规则列表 */
    getList() {
      this.loading = true;
      listRule(this.queryParams).then(response => {
        this.ruleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        ruleId: null,
        ruleName: null,
        ruleType: null,
        ruleGroup: null,
        detectionMode: null,
        detectionContent: null,
        detectionRole: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.ruleId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加质检规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const ruleId = row.ruleId || this.ids
      getRule(ruleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改质检规则";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.ruleId != null) {
            updateRule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ruleIds = row.ruleId || this.ids;
      this.$modal.confirm('是否确认删除质检规则编号为"' + ruleIds + '"的数据项？').then(function() {
        return delRule(ruleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('quality/rule/export', {
        ...this.queryParams
      }, `rule_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.el-tooltip__popper {
  max-width: 30%;
  line-height: 24px;
}
</style>
