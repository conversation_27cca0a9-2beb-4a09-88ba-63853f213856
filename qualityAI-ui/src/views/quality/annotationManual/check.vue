<template>
  <div class="app-container">
    <div class="app-main">
      <el-page-header @back="goBack" :content="'人工标注'">
      </el-page-header>
      <el-divider class="topLine"></el-divider>

      <!-- <WaveSurferPlayer  ref="audioPlayer" :audioSrc="audioUrl"
      @time-update="handleTimeUpdate"
      @duration="handleDurationUpdate"/> -->

      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane v-for="(item,index) in videoList" :key="index" :label="'录音'+(index+1)" :name="index+''">
          <WaveSurferPlayer
          ref="audioPlayer"
          :audioSrc="item"
          @time-update="(time) => handleTimeUpdate(time, index)"
          @duration="handleDurationUpdate"/>
        </el-tab-pane>
      </el-tabs>


      <div class="conMain row">
        <div class="leftMain">
          <!--{{currentTime}}-->
          <el-card>
            <div slot="header" class="card-tit">
              <span>智能转译文本</span>
            </div>
            <editable-text-annotation
              @child-to-parent="handleChildData"
              :parentData="parentData"
              ref="annotator"
            >
            <div class="card-body chatMain" v-if="msgList.length">
              <div :class="!item.spk?'chatBox chatMy':'chatBox'" v-for="(item,index) in msgList" :key="index" ref="itemRefs">

                <img v-if="item.spk" src="@/assets/images/agent.png" mode="" />
                <div v-if="!item.spk" class="chatTime row">
                  <!-- {{convertHours(item.ed-item.bg)}} {{item.bg}}-{{item.ed}}-->
                  <div>{{convertHours(item.ed-item.bg)}}</div><img class="icon" src="@/assets/images/triangle.png" />
                </div>

                <div :style="{textAlign:(!item.spk?'left':'left')}" class="msg" :class="{'blueText': isActive(item) }" @click="jumpToTime(item.bg,item.ed,$event)">
                    <span v-html="highlightTitle(item.text)" @contextmenu.prevent="selectAllText($event,item.bg,item.ed)" class="selectable-text"></span>
                </div>

                <div v-if="item.spk" class="chatTime row">
                  <div>{{convertHours(item.ed-item.bg)}}</div><img class="icon" src="@/assets/images/triangle.png" />
                </div>

                <img v-if="!item.spk" src="@/assets/images/user.png" mode="" />
              </div>
            </div>
            </editable-text-annotation>
          </el-card>
        </div>


        <div class="rightMain">
          <el-card>
            <div slot="header" class="card-tit">
              <span>评测信息</span>
            </div>
            <div class="card-body">
              <div class="ruleBox">
                <div v-for="(item,index) in scoreList" :key="index">
                  <div class="row">
                    <el-tooltip  effect="dark"  placement="top">
                      <div slot="content" class="toolBox">{{item.quesCode}}</div>
                      <div class="rule-tit">{{index+1}}.{{item.quesCode}}</div>
                    </el-tooltip>
                    <div>{{item.ansValue}}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>


        </div>

      </div>
      <div class="an-container">
        <el-card shadow="hover" class="annotation-list-card">
          <div slot="header">
            <span>标注列表 (共{{annotations.length}}条)</span>
            <div class="card-actions" style="float: right;padding-right: 20px;">
              <el-button
                type="primary"
                size="small"
                @click="exportAnnotations"
              >导出标注</el-button>
            </div>
          </div>
          <el-table
            :data="annotations"
            border
            style="width: 100%"
            @row-click="handleRowClick"
          >
            <el-table-column prop="type" label="类型" width="120">
              <template slot-scope="scope">
                <el-tag :type="getTagType(scope.row.type)">
                  {{ getTypeLabel(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="originalText" label="原始文本" width="200">
              <template slot-scope="scope">
                <div class="text-truncate" :title="scope.row.originalText">
                  {{ scope.row.originalText }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="correctText" label="修正文本">
              <template slot-scope="scope">
                <div class="text-truncate" :title="scope.row.correctText">
                  {{ scope.row.correctText }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click.stop="editAnnotation(scope.row)"
                >编辑</el-button>
                <el-button
                  size="mini"
                  type="danger"
                  @click.stop="deleteAnnotation(scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>


  </div>

</template>

<script>
import { reviewInfo,aiResList,tempList,addReview,soundInfo,reviewDetail } from "@/api/quality/annotationCheck";
import {listAnnotation, delAnnotation, listAllAnnotation} from "@/api/quality/annotation";
import WaveSurferPlayer from '@/components/WaveSurferPlayer'
import { Utils } from '@/api/util/common';
import EditableTextAnnotation  from '@/components/AdvancedTextAnnotation'
export default {
  name: "History",
  components:{
    WaveSurferPlayer,
    EditableTextAnnotation
  },
  data() {
    return {
      currentTab: 0,
      annotations: [],
      parentData: {
        dataId: "",
        oldUrl: "",
        newUrl: "",
        newUrlName: "",
        startTime: "",
        endTime: ""},
      msgList:[],
      scoreList:[],
      tableData:[],
      ruleVal:'',
      ruleList:[],
      treeList:[],
      ruleIndex:null,
      manCheckData:[],
      qualTempData:[],// 评分规则模板表格数据
      reResultList:['0','1','2','3','4','5','6','7','8','9','10','废卷','待修正'],
      drawer:false,
      // audioUrl: require('../../../assets/images/test2.wav'),
      // audioUrl:'',
      info:{},

      currentTime: 0,
      duration: 0,
      keyWords:[],
      // 是否展开，默认全部折叠
      isExpandAll: false,
      isEdit:1,//1带编辑的，0仅查看
      videoList: [],
      txtList:[],
      activeName: '0',
      form: {},
      currDatasetItemId:'',
      maxTimes: [], // 存储每个音频的最大播放时间
      currentTimes: [], // 存储每个音频的当前播放时间
      bizNo: null
    };
  },
  created() {
    let bizNo = this.$route.query.bizNo
    this.bizNo = bizNo;
    this.getInfo(bizNo);
  },
  watch: {
  },
  methods: {
    handleChildData(data){
      this.annotations=data;
    },
    selectAllText(event,startTime,endTime) {
      this.parentData.startTime=startTime;
      this.parentData.endTime=endTime;
    },
    // 导出标注
    exportAnnotations() {
      this.download('qc/api/audio/exportSamples', {
        dataId:this.currDatasetItemId
      }, `audio_samples_${new Date().getTime()}.zip`)
    },

    // 清空所有标注
    clearAllAnnotations() {
      if (this.annotations.length === 0) return

      this.$confirm('确定要清空所有标注吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.annotations = []
        this.$message.success('已清空所有标注')
      }).catch(() => {})
    },

    // 表格行点击
    handleRowClick(row) {
      this.$refs.annotator.showAnnotationDetail(row)
    },

    // 编辑标注
    getAnnotation(dataId) {
      listAllAnnotation({dataId:dataId}).then(response => {
        this.annotations = response.data;
      });
    },
    // 编辑标注
    editAnnotation(row) {
      this.parentData.annotations=this.annotations;
      this.$refs.annotator.editCurrentAnnotation(row)
    },

    // 删除标注
    deleteAnnotation(row) {
      this.$confirm('确定要删除此标注吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delAnnotation(row.id).then(response => {
          this.annotations = this.annotations.filter(a => a.id !== row.id)
          this.parentData.annotations=this.annotations;
          this.$message.success('标注已删除')
        });
      }).catch(() => {})
    },

    // 获取标签类型
    getTagType(type) {
      switch(type) {
        case 'correction': return 'danger'
        case 'highlight': return 'warning'
        case 'question': return 'primary'
        case 'comment': return 'success'
        default: return 'info'
      }
    },

    // 获取类型标签
    getTypeLabel(type) {
      const types = [
        { value: 'correction', label: '文本修正' },
        { value: 'highlight', label: '重点标注' },
        { value: 'question', label: '疑问点' },
        { value: 'comment', label: '评注' }
      ]
      const item = types.find(t => t.value === type)
      return item ? item.label : '未知类型'
    },
    //获取当前播放时长
    handleTimeUpdate(timeInMs, index) {
      this.currentTime = Math.round(timeInMs*1000);
      this.highlightAndScroll();

      const timeInSeconds = Math.round(timeInMs * 1000);
      this.$set(this.currentTimes, index, timeInSeconds);
      // 更新最大播放时间
      if (!this.maxTimes[index] || timeInSeconds > this.maxTimes[index]) {
        this.$set(this.maxTimes, index, timeInSeconds);
      }
    },
    calculateTotalMaxTime() {
      const total = this.maxTimes.reduce((sum, time) => sum + (time || 0), 0);
      console.log('每段录音的最大播放时间:', this.maxTimes);
      console.log('总播放时长:', total);
      if(total > 0){
        // 这里可以发送到服务器或存储到Vuex/store
        let listenLong =this.formatMilliseconds(total)
        console.log(listenLong,'总时长----------'); // 输出 "00:01:47"
        let json = {listenLong:listenLong,bizNo:this.bizNo}
        addListen(json).then(res => {
          console.log('addListen保存播放时长:', res);
        });
      }
      return total;
    },
    handleClick(tab, event) {
      let index = Number(tab.index)
      this.msgList = []
      this.msgList = this.txtList[index].msgList.list
      this.currDatasetItemId = this.txtList[index].msgList.datasetItemId;
      this.parentData.dataId=this.currDatasetItemId;
      this.getAnnotation(this.currDatasetItemId);
      this.$refs.audioPlayer[index].seekTo(0);
    },

    handleDurationUpdate(durationInMs) {
      this.duration = durationInMs;
    },
    isActive(item) {
      return this.currentTime >= item.bg && this.currentTime < item.ed;
    },
    jumpToTime(startTime,endTime,event) {
      this.currentTime = startTime
      this.$refs.audioPlayer[Number(this.activeName)].seekTo(startTime);
      this.parentData.startTime=startTime;
      this.parentData.endTime=endTime;
      this.parentData.dataId=this.info.id;
      this.parentData.annotations=this.annotations;
      if (event.target.classList.contains('span_c')) {
        const vValue = event.target.getAttribute("_v");
        this.showAnnotation(vValue);
      }
    },
    //滚动到高亮位置
    highlightAndScroll() {
      const highlightedIndex = this.msgList.findIndex(item => this.isActive(item));
      if (highlightedIndex !== -1) {
        const itemElement = this.$refs.itemRefs[highlightedIndex];
        if (itemElement) {
          itemElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }
    },
    //匹配关键词
    highlightTitle(msg) {
      let highlightedText = msg;
      let i = 0;
      this.annotations.forEach(key => {
        if (msg.includes(key.originalText)) {
          highlightedText = highlightedText.replace(
            new RegExp(key.originalText, 'g'),
            `<span style="color: #F56C6C;border: 2px dashed blue;" class="span_c" _v="${i}">${key.originalText}</span>`
          );
        }
        i++;
      });
      return highlightedText;
    },
    showAnnotation(idx){
      let tmp = this.annotations[parseInt(idx)]
      this.$refs.annotator.showAnnotationDetail(tmp)
    },

    //录音信息
    getInfo(bizNo) {
      reviewInfo(this.bizNo,'3',0).then(res => {
        this.info = res.data
        let answersJson = this.info.answersJson
        if (answersJson != null && answersJson != "") {
          this.scoreList = JSON.parse(answersJson);
        }
        this.parentData.dataId=this.info.id;
        this.getVoice();
      });
    },
    getTempList(){
      if(!this.info.templateId) return
      tempList({dataNo:this.info.bizNo,templateId:this.info.templateId}).then(res => {
        this.ruleList = res.data
        this.treeList = Utils.listToTree(res.data)
      });
    },
    getVoice(){
      soundInfo(this.bizNo).then(res => {
       this.videoList = res.data.videoList
       this.txtList = res.data.txtList
        this.msgList = res.data.txtList[0].msgList.list
        this.currDatasetItemId = res.data.txtList[0].msgList.datasetItemId;
        this.parentData.dataId=this.currDatasetItemId;
        this.getAnnotation(this.currDatasetItemId);
        // this.keyWords = res.data.keys
        // console.log(this.msgList,'this.msgList----')
      });
    },
    getManCheckList(){
      reviewDetail(this.info.id).then(res => {
        this.manCheckData = res.data
      });
    },
    goBack() {
      this.$store.dispatch("tagsView/delView", this.$route); //关闭当前页
      // this.$router.replace({ path: "/data/quality"}); // 要打开的页面
      this.$router.back()
    },
    changeRule(){

    },
    submitRule(){
      if(!this.manCheckData.length){
        this.$message.warning('请至少添加一条规则！')
      }else{
      //  const hasNullId = this.manCheckData.some(item => item.reviewResult === null);
      //  if(hasNullId){
      //   this.$message.warning('请填写完整复检结果！')
      //  }else{
        const newArr = this.manCheckData.map(item=>{
          let obj = {
            id:this.id,
            dataId:this.dataId,
            templateId:item.templateId,
            ruleId:item.ruleId,
            aiResult:item.aiResult,
            reviewResult:item.reviewResult

          }
          return obj
        })
        addReview(newArr).then(res => {
          if(res.code == 200){
            this.$message.success('添加成功')
            this.goBack()
          }
        })
       }

      // }
    },
    addRule(){
      if(this.ruleVal==''){
         this.$message.warning('请选择规则！')
      }else{
        const info = this.ruleList.find(item=>item.id == this.ruleVal)
        // console.log(info,'info--')
        this.manCheckData.unshift({...info,reviewResult:null})
      }
    },
    delRule(index){
      this.manCheckData.splice(index,1);
    }


  }
}
</script>
<style lang="scss" scoped>

.app-container{
  background: #eff1f4;
  padding: 24px;
  min-height: calc(100vh - 50px);
}
.app-main{
  background: rgb(255, 255, 255);
  min-height: calc(-98px + 100vh);
  border-radius: 4px;
  padding: 20px;
}
.blueText{
  color: #409EFF!important;
}
.redText{
  color: #F56C6C!important;
}
.topLine{
  margin: 15px 0;
}
.conMain{
  width: 100%;
  align-items: flex-start;
  margin-top: 10px;
}
.leftMain{
  padding-right: 10px;
  // border: 1px solid red;
  width: 50%;
}
.chatMain{
  //  border: 1px solid red;
  height: 400px;
  // height: calc(100vh - 280px);
  overflow: auto;
}
.rightMain{
  width: 50%;
}

.card-tit{
  font-size: 14px;
}
.card-body{
  font-size: 13px;
}
.ruleBox{
  display: grid;
  grid-template-columns: repeat(2,1fr);
  grid-column-gap: 20px;
  grid-row-gap: 18px;
  justify-content: space-between;
}
.rule-tit{
  width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
}


.resBt{
  margin: 0 20px;
}
.selBox{
  margin-bottom: 10px;
  .label{
    font-size: 13px;
    margin-right: 10px;
  }
  .addBt{
    margin-left: 10px;
  }
}
.footBt{
  justify-content: center;
  margin: 10px 0;
}


.chatBox{
	display: flex;
	flex-direction: row;
	margin-bottom: 10px;
  font-size: 13px;
  // border: 1px solid blue;
	img{
		width: 40px;
		height: 40px;
	}
	.msg{
    // display: inline-block;
		border-radius: 5px;
		background-color: #f5f5f5;
		padding: 8px 12px;
		max-width: 360px;
		word-wrap: break-word;
		word-break: break-all;
    margin: 0 10px;
    position: relative;
    text-align: left;
    color: #606266;
    line-height: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
	}


}
.chatTime{
  color: #999;
  font-size:12px;
  cursor: pointer;
  .icon{
    width: 20px;
    height: 20px;
  }
}
.chatMy{
	justify-content: flex-end;
}

.msg-item.chat-mine {
	text-align: right;
	padding-left: 0;
	padding-right: 30px;

	.chat-user {
	  left: auto;
	  right: 3px;

	  cite {
		left: auto;
		right: 30px;
		text-align: right;

		i {
		  padding-left: 0;
		  // padding-right: 15px;
		}
	  }
	}

	.chat-text {
	  margin-left: 0;
	  text-align: left;
	  background-color: #cad5e5;
	  color: #000;
	}

	.chat-text::after {
	  top: 15px;
	  left: auto;
	  right: -10px;
	  border-top: 5upx solid transparent;
	  border-left: 10upx solid #cad5e5;
	  border-bottom: 5upx solid transparent;
	  border-right: unset;
	}

	.chat-system {
	  margin-left: 15px;
	}
}
.drawerBox{
  padding: 0 20px;
}
.toolBox{
  max-width: 600px;
}
.annotation-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

</style>
