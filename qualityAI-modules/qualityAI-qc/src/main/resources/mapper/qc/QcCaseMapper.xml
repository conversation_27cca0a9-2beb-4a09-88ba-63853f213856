<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcCaseMapper">

    <resultMap id="qcCaseResultMap" type="QcCase">
        <result property="id"    column="id"    />
        <result property="caseClassificationId" column="case_classification_id" />
        <result property="caseClassificationName" column="case_classification_name" />
        <result property="manualTaskDetailId" column="manual_task_detail_id" />
        <result property="caseStatus" column="case_status" />
        <result property="macInspectTime" column="mac_inspect_time" />
        <result property="manualInspectTime" column="manual_inspect_time" />
        <result property="workNo" column="work_no" />
        <result property="workName" column="work_name" />
        <result property="bizNo" column="biz_no" />
        <result property="manualType" column="manual_type" />
        <result property="smartTaskDetailId" column="smart_task_detail_id" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <select id="getQcCaseList" resultMap="qcCaseResultMap">
        SELECT
            qc.id,
            qc.case_classification_id,
            qc.manual_task_detail_id,
            qc.case_status,
            qc.create_by,
            qc.create_time,
            qc.update_by,
            qc.update_time,
            qc.remark,
            qmtd.mac_inspect_time,
            qmtd.manual_inspect_time,
            qmtd.work_no,
            qmtd.work_name,
            qmtd.biz_no,
            qmtd.manual_type,
            qmtd.smart_task_detail_id,
            qcc.case_classification_name
        FROM
            qc_case qc
        LEFT JOIN
            qc_case_classification qcc ON qcc.id = qc.case_classification_id
        LEFT JOIN
            qc_manual_task_detail qmtd ON qmtd.id = qc.manual_task_detail_id
        WHERE
            qc.case_status = #{caseStatus}
            <if test="caseClassificationId != null">
                AND qc.case_classification_id = #{caseClassificationId}
            </if>
            <if test="workNo != null and workNo != ''">
                AND qmtd.work_no LIKE concat('%', #{workNo}, '%')
            </if>
        ORDER BY
            qc.id ASC
    </select>

    <select id="getQcCaseListForRejected" resultMap="qcCaseResultMap">
        SELECT DISTINCT
            qc.id,
            qc.case_classification_id,
            qc.manual_task_detail_id,
            qc.case_status,
            qc.create_by,
            qc.create_time,
            qc.update_by,
            qc.update_time,
            qc.remark,
            qmtd.mac_inspect_time,
            qmtd.manual_inspect_time,
            qmtd.work_no,
            qmtd.work_name,
            qmtd.biz_no,
            qmtd.manual_type,
            qmtd.smart_task_detail_id,
            qcc.case_classification_name
        FROM
            qc_case qc
        LEFT JOIN
            qc_case_classification qcc ON qcc.id = qc.case_classification_id
        LEFT JOIN
            qc_manual_task_detail qmtd ON qmtd.id = qc.manual_task_detail_id
        WHERE
            qc.case_status = #{caseStatus}
            AND qc.create_by = #{username}
        <if test="caseClassificationId != null">
            AND qc.case_classification_id = #{caseClassificationId}
        </if>
        <if test="workNo != null and workNo != ''">
            AND qmtd.work_no LIKE concat('%', #{workNo}, '%')
        </if>
        ORDER BY
            qc.id ASC
    </select>

    <insert id="addQcCase">
        INSERT INTO
            qc_case
        ( tenant_id, case_classification_id, manual_task_detail_id, create_time, create_by, update_time, update_by, remark )
        VALUES
        ( #{tenantId}, #{caseClassificationId}, #{manualTaskDetailId}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy}, #{remark})
    </insert>

    <update id="updateQcCase">
        UPDATE qc_case
        SET case_status = #{caseStatus},
            update_by = #{updateBy},
            update_time = #{updateTime}
        WHERE
            id = #{id}
    </update>

    <resultMap id="qcCaseDTOResultMap" type="com.ideal.qc.dto.QcCaseDTO">
        <result property="pendingReviewQuantity" column="pending_review_quantity" />
        <result property="passedQuantity" column="passed_quantity" />
    </resultMap>

    <select id="getQcCaseEachCaseStatusQuantity" resultMap="qcCaseDTOResultMap">
        SELECT
            COUNT( CASE WHEN qc.case_status = '1' THEN 1 END ) AS pending_review_quantity,
            COUNT( CASE WHEN qc.case_status = '2' THEN 1 END ) AS passed_quantity,
            COUNT( CASE WHEN qc.case_status = '3' THEN 1 END ) AS rejected_quantity
        FROM
            qc_case qc
        LEFT JOIN
            qc_manual_task_detail qmtd ON qmtd.id = qc.manual_task_detail_id
        WHERE
            1 = 1
        <if test="caseClassificationId != null">
            AND qc.case_classification_id = #{caseClassificationId}
        </if>
        <if test="workNo != null and workNo != ''">
            AND qmtd.work_no LIKE concat('%', #{workNo}, '%')
        </if>
    </select>

    <select id="getQcCaseRejectedQuantity" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            qc_case qc
        LEFT JOIN
            qc_manual_task_detail qmtd ON qmtd.id = qc.manual_task_detail_id
        WHERE
            1 = 1
        AND qc.create_by = #{username}
        and qc.case_status = '3'
        <if test="caseClassificationId != null">
            AND qc.case_classification_id = #{caseClassificationId}
        </if>
        <if test="workNo != null and workNo != ''">
            AND qmtd.work_no LIKE concat('%', #{workNo}, '%')
        </if>
    </select>

    <select id="listCount" resultType="int">
        select count(1) from qc_case qc where qc.manual_task_detail_id=#{manualTaskDetailId}
    </select>

    <select id="getQcCaseByCaseClassificationId" resultMap="qcCaseResultMap">
        SELECT
            qc.id,
            qc.case_classification_id,
            qc.manual_task_detail_id,
            qc.case_status,
            qc.create_by,
            qc.create_time,
            qc.update_by,
            qc.update_time,
            qc.remark
        FROM
            qc_case qc
        ORDER BY
            qc.id ASC
    </select>
</mapper>