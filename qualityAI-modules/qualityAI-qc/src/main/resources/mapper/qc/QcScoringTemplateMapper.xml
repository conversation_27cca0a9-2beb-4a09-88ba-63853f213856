<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcScoringTemplateMapper">

    <resultMap id="qcScoringTemplateResultMap" type="QcScoringTemplate">
        <result property="id"    column="id"    />
        <result property="scoringTemplateName"    column="scoring_template_name"    />
        <result property="scoringTemplateType" column="scoring_template_type" />
        <result property="basicScore"    column="basic_score"    />
        <result property="highestScore"    column="highest_score"    />
        <result property="lowestScore"    column="lowest_score"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <select id="getQcScoringTemplateList" resultMap="qcScoringTemplateResultMap">
        SELECT
            qct.id,
            qct.scoring_template_name,
            qct.scoring_template_type,
            qct.basic_score,
            qct.highest_score,
            qct.lowest_score,
            qct.create_by,
            qct.create_time,
            qct.update_by,
            qct.update_time,
            qct.remark,
            qct.status
        FROM
            qc_scoring_template qct
        WHERE
            qct.is_deleted = '0'
        <if test="scoringTemplateName != null and scoringTemplateName != ''">
            AND qct.scoring_template_name LIKE concat('%', #{scoringTemplateName}, '%')
        </if>
        <if test="scoringTemplateType != null and scoringTemplateType != ''">
            AND qct.scoring_template_type = #{scoringTemplateType}
        </if>
        <if test="status != null and status != ''">
            AND qct.status = #{status}
        </if>
    </select>

    <insert id="addQcScoringTemplate">
        INSERT INTO
            qc_scoring_template
            ( tenant_id, scoring_template_name, scoring_template_type, basic_score, highest_score,
             lowest_score, create_time, create_by, update_time, update_by, remark )
        VALUES
            ( #{tenantId}, #{scoringTemplateName}, #{scoringTemplateType}, #{basicScore}, #{highestScore},
             #{lowestScore}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy}, #{remark} )
    </insert>

    <update id="updateQcScoringTemplate">
        UPDATE qc_scoring_template
        SET scoring_template_name = #{scoringTemplateName},
            scoring_template_type = #{scoringTemplateType},
            status = #{status},
            basic_score = #{basicScore},
            highest_score = #{highestScore},
            lowest_score = #{lowestScore},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark}
        WHERE
	        id = #{id}
    </update>

    <update id="removeQcScoringTemplate">
        UPDATE qc_scoring_template
        SET is_deleted = '1'
        WHERE
        <foreach item="item" collection="array" open="(" close=")" separator=",">
            id = #{item}
        </foreach>
    </update>

    <resultMap id="qcScoringTemplateVoResultMap" type="com.ideal.qc.vo.QcScoringTemplateVo">
        <result property="id"    column="id"    />
        <result property="name"    column="scoring_template_name"    />
        <result property="scoringTemplateType" column="scoring_template_type" />
        <result property="basicScore"    column="basic_score"    />
        <result property="highestScore"    column="highest_score"    />
        <result property="lowestScore"    column="lowest_score"    />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
        <collection property="children" select="getQcScoringItem" column="id" />
    </resultMap>

    <select id="getQcScoringTemplate" resultMap="qcScoringTemplateVoResultMap">
        SELECT
            qct.id,
            qct.scoring_template_name,
            qct.scoring_template_type,
            qct.basic_score,
            qct.highest_score,
            qct.lowest_score,
            qct.status,
            qct.is_deleted
        FROM
            qc_scoring_template qct
        WHERE
            qct.id = #{id}
            AND qct.is_deleted = '0'
    </select>

    <resultMap id="qcScoringItemVoResultMap" type="com.ideal.qc.vo.QcScoringItemVo">
        <result property="id"    column="id"    />
        <result property="scoringClassificationId" column="scoring_classification_id" />
        <result property="name" column="scoring_item_name" />
        <result property="scoringItemDescription" column="scoring_item_description" />
        <result property="hitRuleScore" column="hit_rule_score" />
        <result property="templateId" column="template_id" />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
        <collection property="children" select="getQcScoringHitRuleGroup" column="id" />
    </resultMap>

    <select id="getQcScoringItem" resultMap="qcScoringItemVoResultMap">
        SELECT
            qsi.id,
            qsi.scoring_classification_id,
            qsi.scoring_item_name,
            qsi.scoring_item_description,
            qsi.hit_rule_score,
            qsi.template_id,
            qsi.status,
            qsi.is_deleted
        FROM
            qc_scoring_item qsi
        LEFT JOIN
            qc_scoring_classification qsc ON qsc.id = qsi.scoring_classification_id
        WHERE
            qsc.scoring_template_id = #{scoringTemplateId}
            AND qsi.is_deleted = '0'
        ORDER BY
            qsi.id ASC
    </select>

    <resultMap id="qcScoringHitRuleGroupVoResultMap" type="com.ideal.qc.vo.QcScoringHitRuleGroupVo">
        <result property="id" column="id" />
        <result property="scoringItemId" column="scoring_item_id" />
        <result property="name" column="rule_group_name" />
        <result property="ruleGroupMode" column="rule_group_mode" />
        <result property="busSort" column="bus_sort" />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
        <collection property="children" select="getQcScoringHitRuleItem" column="id" />
    </resultMap>

    <select id="getQcScoringHitRuleGroup" resultMap="qcScoringHitRuleGroupVoResultMap">
        SELECT
            qshrg.id,
            qshrg.scoring_item_id,
            '规则组' AS rule_group_name,
            qshrg.rule_group_mode,
            qshrg.bus_sort,
            qshrg.status,
            qshrg.is_deleted
        FROM
            qc_scoring_hit_rule_group qshrg
        WHERE
            qshrg.scoring_item_id = #{scoringItemId}
            AND qshrg.is_deleted = '0'
        ORDER BY
            qshrg.bus_sort ASC
    </select>

    <resultMap id="qcScoringHitRuleItemVoResultMap" type="com.ideal.qc.vo.QcScoringHitRuleItemVo">
        <result property="id" column="id" />
        <result property="scoringHitRuleGroupId" column="scoring_hit_rule_group_id" />
        <result property="ruleId" column="rule_id" />
        <result property="ruleClassification" column="rule_classification" />
        <result property="busSort" column="bus_sort" />
        <result property="name" column="rule_name" />
        <result property="templateId" column="template_id" />
        <result property="status" column="status" />
        <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <select id="getQcScoringHitRuleItem" resultMap="qcScoringHitRuleItemVoResultMap">
        SELECT
            qshri.id,
            qshri.scoring_hit_rule_group_id,
            qshri.rule_id,
            qshri.rule_classification,
            qshri.bus_sort,
            qshri.rule_name,
            qshri.template_id,
            qshri.status,
            qshri.is_deleted
        FROM
            qc_scoring_hit_rule_item qshri
        WHERE
            qshri.scoring_hit_rule_group_id = #{scoringHitRuleGroupId}
            AND qshri.is_deleted = '0'
        ORDER BY
            qshri.bus_sort ASC
    </select>

    <select id="getAllQcScoringTemplate" resultMap="qcScoringTemplateResultMap">
        SELECT
            qct.id,
            qct.scoring_template_name,
            qct.scoring_template_type,
            qct.basic_score,
            qct.highest_score,
            qct.lowest_score,
            qct.create_by,
            qct.create_time,
            qct.update_by,
            qct.update_time,
            qct.remark,
            qct.`status`,
            qct.is_deleted
        FROM
            qc_scoring_template qct
        WHERE
            qct.is_deleted = '0'
          AND qct.`status` = '1'
    </select>
</mapper>