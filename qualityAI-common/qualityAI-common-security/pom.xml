<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ideal</groupId>
        <artifactId>qualityAI-common</artifactId>
        <version>3.6.5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>qualityAI-common-security</artifactId>

    <description>
        qualityAI-common-security安全模块
    </description>

    <dependencies>

        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <!--  Api System -->
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>qualityAI-api-system</artifactId>
        </dependency>

        <!--  Common Redis-->
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>qualityAI-common-redis</artifactId>
        </dependency>

    </dependencies>

</project>
