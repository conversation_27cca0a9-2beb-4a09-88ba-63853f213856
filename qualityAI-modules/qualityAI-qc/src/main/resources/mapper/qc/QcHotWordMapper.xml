<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.QcHotWordMapper">
    
    <resultMap type="QcHotWord" id="QcHotWordResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="wordName"    column="word_name"    />
        <result property="wordType"    column="word_type"    />
        <result property="status"    column="status"    />
        <result property="orderNum"    column="order_num"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectQcHotWordVo">
        select id, parent_id, word_name, word_type, status, order_num, tenant_id, create_by, create_time, update_by, update_time, remark from qc_hot_word
    </sql>

    <select id="selectQcHotWordList" parameterType="QcHotWord" resultMap="QcHotWordResult">
        <include refid="selectQcHotWordVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="wordName != null  and wordName != ''"> and word_name like concat('%', #{wordName}, '%')</if>
            <if test="wordType != null  and wordType != ''"> and word_type = #{wordType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
        </where>
        order by order_num asc, create_time asc
    </select>

    <select id="selectQcHotWordTree" parameterType="QcHotWord" resultMap="QcHotWordResult">
        <include refid="selectQcHotWordVo"/>
        <where>  
            <if test="wordName != null  and wordName != ''"> and word_name like concat('%', #{wordName}, '%')</if>
            <if test="wordType != null  and wordType != ''"> and word_type = #{wordType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
        </where>
        order by parent_id asc, order_num asc, create_time asc
    </select>
    
    <select id="selectQcHotWordById" parameterType="Long" resultMap="QcHotWordResult">
        <include refid="selectQcHotWordVo"/>
        where id = #{id}
    </select>

    <select id="selectEnabledHotWords" resultType="String">
        select word_name from qc_hot_word 
        where word_type = 'word' and status = 1
        order by order_num asc, create_time asc
    </select>

    <select id="selectChildrenCountByParentId" parameterType="Long" resultType="int">
        select count(1) from qc_hot_word where parent_id = #{parentId}
    </select>

    <select id="checkWordNameUnique" resultType="int">
        select count(1) from qc_hot_word 
        where word_name = #{wordName} and parent_id = #{parentId}
        <if test="id != null and id != 0">
            and id != #{id}
        </if>
    </select>

    <insert id="insertQcHotWord" parameterType="QcHotWord" useGeneratedKeys="true" keyProperty="id">
        insert into qc_hot_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="wordName != null and wordName != ''">word_name,</if>
            <if test="wordType != null and wordType != ''">word_type,</if>
            <if test="status != null">status,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="wordName != null and wordName != ''">#{wordName},</if>
            <if test="wordType != null and wordType != ''">#{wordType},</if>
            <if test="status != null">#{status},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateQcHotWord" parameterType="QcHotWord">
        update qc_hot_word
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="wordName != null and wordName != ''">word_name = #{wordName},</if>
            <if test="wordType != null and wordType != ''">word_type = #{wordType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcHotWordById" parameterType="Long">
        delete from qc_hot_word where id = #{id}
    </delete>

    <delete id="deleteQcHotWordByIds" parameterType="String">
        delete from qc_hot_word where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>