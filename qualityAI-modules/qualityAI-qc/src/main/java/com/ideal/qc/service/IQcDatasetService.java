package com.ideal.qc.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.qc.domain.QcDataset;

/**
 * 数据集Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IQcDatasetService 
{
    /**
     * 查询数据集
     * 
     * @param id 数据集主键
     * @return 数据集
     */
    public QcDataset selectQcDatasetById(Long id);

    /**
     * 查询数据集列表
     * 
     * @param qcDataset 数据集
     * @return 数据集集合
     */
    public List<QcDataset> selectQcDatasetList(QcDataset qcDataset);

    /**
     * 新增数据集
     * 
     * @param qcDataset 数据集
     * @return 结果
     */
    public int insertQcDataset(QcDataset qcDataset);

    /**
     * 修改数据集
     * 
     * @param qcDataset 数据集
     * @return 结果
     */
    public int updateQcDataset(QcDataset qcDataset);

    /**
     * 批量删除数据集
     * 
     * @param ids 需要删除的数据集主键集合
     * @return 结果
     */
    public int deleteQcDatasetByIds(Long[] ids);

    /**
     * 删除数据集信息
     * 
     * @param id 数据集主键
     * @return 结果
     */
    public int deleteQcDatasetById(Long id);

	/**
	 * 查询数据集列表
	 * 
	 * @param qcDataset 数据集
	 * @return 数据集
	 */
	List<QcDataset> selectQcDatasetListByMap(JSONObject param);
}
