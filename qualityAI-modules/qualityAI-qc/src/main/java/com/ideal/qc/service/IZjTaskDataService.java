package com.ideal.qc.service;


import com.ideal.qc.domain.ZjTaskData;

import java.util.List;

/**
 * 质检源数据Service接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface IZjTaskDataService
{
    /**
     * 查询质检源数据
     *
     * @param id 质检源数据主键
     * @return 质检源数据
     */
    public ZjTaskData selectZjTaskDataById(Long id);
    /**
     * 查询质检源数据列表
     *
     * @param zjData 质检源数据
     * @return 质检源数据集合
     */
    public List<ZjTaskData> selectZjTaskDataList(ZjTaskData zjTaskData);


    String getVideoText(String onlyNo);

    List<String> getVideoTextMatchKey(String onlyNo);
}
