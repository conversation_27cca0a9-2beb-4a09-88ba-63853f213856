package com.ideal.qc.service;

import java.util.List;
import com.ideal.qc.domain.ScoreItem;

/**
 * 评分规则模板Service接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface IScoreItemService
{
    /**
     * 查询评分规则模板
     *
     * @param id 评分规则模板主键
     * @return 评分规则模板
     */
    public ScoreItem selectScoreItemById(Long id);

    /**
     * 查询评分规则模板列表
     *
     * @param scoreItem 评分规则模板
     * @return 评分规则模板集合
     */
    public List<ScoreItem> selectScoreItemList(ScoreItem scoreItem);

    /**
     * 获取评分项分类节点
     * @param scoreItem
     * @return
     */
    public List<ScoreItem> selectScoreItemPnode(ScoreItem scoreItem);

    /**
     * 查询评分规则模板列表 By 模板Id
     *
     * @param scoreItem 评分规则模板
     * @return 评分规则模板集合
     */
    public List<ScoreItem> selectScoreItemByTplId(ScoreItem scoreItem);
    /**
     * 新增评分规则模板
     *
     * @param scoreItem 评分规则模板
     * @return 结果
     */
    public int insertScoreItem(ScoreItem scoreItem);

    /**
     * 修改评分规则模板
     *
     * @param scoreItem 评分规则模板
     * @return 结果
     */
    public int updateScoreItem(ScoreItem scoreItem);

    /**
     * 批量删除评分规则模板
     *
     * @param ids 需要删除的评分规则模板主键集合
     * @return 结果
     */
    public int deleteScoreItemByIds(Long[] ids);

    /**
     * 删除评分规则模板信息
     *
     * @param id 评分规则模板主键
     * @return 结果
     */
    public int deleteScoreItemById(Long id);
}
