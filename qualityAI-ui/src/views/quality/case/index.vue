<template>
  <div class="app-container">
    <el-row :gutter="40">
      <el-col :span="4" style="border-right:1px solid #F2F6FC;padding:0;">
        <div class="sortItem row spaceBet" @click="handleCaseClassification()">
          <div>全部分类</div>
          <i class="el-icon-plus" @click="handleAdd"></i>
        </div>
        <div class="sortChild">
          <div v-for="(item, index) in caseClassificationList" :key="index" class="sortItem row spaceBet childItem"
            :class="{ 'active-item': activeCaseClassification === item }" @click="handleCaseClassification(item)">
            <div>{{ item.caseClassificationName }}</div>
            <el-dropdown size="mini" @command="(command) => handleCommand(command, item)">
              <i class="el-icon-more"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="handleReset" icon="el-icon-refresh-right">重命名</el-dropdown-item>
                <el-dropdown-item command="handleDelete" icon="el-icon-delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-col>

      <el-col :span="20">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px"
          class="row spaceBet">
          <div style="display: flex;flex-direction: row;align-items: center;">
            <el-form-item prop="caseStatus">
              <el-radio-group v-model="queryParams.caseStatus"
                @change="changeCaseStatus">
                <el-radio-button label="1">待审核（{{ eachCaseStatusQuantity.pendingReviewQuantity }}）</el-radio-button>
                <el-radio-button label="2">已通过（{{ eachCaseStatusQuantity.passedQuantity }}）</el-radio-button>
                <el-radio-button label="3">已驳回（{{ eachCaseStatusQuantity.rejectedQuantity }}）</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="workNo">
              <el-input v-model="queryParams.workNo" placeholder="坐席工号"
                clearable style="width: 240px">
                <el-button slot="append" icon="el-icon-search"
                  @click="getCaseList"></el-button>
              </el-input>
            </el-form-item>
          </div>
        </el-form>
        <el-table v-loading="loading" :data="caseList">
          <el-table-column label="归属分类" align="center" prop="caseClassificationName" />
          <el-table-column label="机检完成时间" align="center" prop="macInspectTime">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.macInspectTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="人工完成时间" align="center" prop="manualInspectTime">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.manualInspectTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="坐席工号" align="center" prop="workNo">
            <template slot-scope="scope">
              {{ getWorkNo(scope.row.workNo) }}
            </template>
          </el-table-column>
          <el-table-column label="坐席姓名" align="center" prop="workName">
            <template slot-scope="scope">
              {{ getName(scope.row.workName) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-popconfirm v-if="scope.row.caseStatus === '1'" v-hasRole="['admin', 'manager']" confirm-button-text='通过' cancel-button-text='驳回' @confirm="handleConfirm(scope.row)"
                @cancel="handleCancel(scope.row)" icon="el-icon-info" icon-color="red" title="请选择审核意见">
                <el-button size="mini" type="text" slot="reference"><i class="el-icon-edit">审核</i></el-button>
              </el-popconfirm>
              <el-button style="margin-left: 10px;" size="mini" type="text"><i class="el-icon-tickets"
                  @click="openCaseDetail(scope.row)">详情</i></el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getCaseList" />
      </el-col>
    </el-row>

    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="caseClassificationAddOrUpdateForm" :inline="true" label-position="top"
        :model="caseClassificationAddOrUpdateForm" :rules="CaseClassificationAddOrUpdateFormRules" label-width="160px"
        class="caseClassificationAddOrUpdateForm">
        <el-form-item label="案例分类名称" prop="caseClassificationName">
          <el-input v-model="caseClassificationAddOrUpdateForm.caseClassificationName" placeholder="请输入案例分类名称"
            maxlength="30" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCaseClassificationAddOrUpdateForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getQcCaseClassificationList, addQcCaseClassification, updateQcCaseClassification, removeQcCaseClassification } from "@/api/quality/caseClassification"
import { getQcCaseList, getQcCaseEachCaseStatusQuantity, updateQcCase } from "@/api/quality/case"
import { Utils } from "@/api/util/common";

export default {
  name: "Case",
  data() {
    return {
      loading: true,
      total: 0,
      title: "",
      open: false,
      form: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        workNo: '',
        caseStatus: '1'
      },
      caseClassificationList: [],
      CaseClassificationAddOrUpdateFormRules: {
        caseClassificationName: [
          { required: true, message: "案例分类名称不能为空", trigger: "blur" }
        ],
      },
      caseClassificationAddOrUpdateForm: {
        caseClassificationName: ''
      },
      caseList: [],
      activeCaseClassification: {},
      eachCaseStatusQuantity: {
        pendingReviewQuantity: 0,
        passedQuantity: 0,
        rejectedQuantity: 0
      }
    };
  },
  created() {
    this.getCaseList();
    this.getQcCaseClassificationList();
  },
  methods: {
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleReset":
          this.handleUpdate(row);
          break;
        case "handleDelete":
          this.handleDelete(row);
          break;
        default:
          break;
      }
    },
    getQcCaseClassificationList() {
      getQcCaseClassificationList({}).then(response => {
        this.caseClassificationList = response.data;
      });
    },
    submitCaseClassificationAddOrUpdateForm: function () {
      this.$refs["caseClassificationAddOrUpdateForm"].validate(valid => {
        if (valid) {
          if (this.caseClassificationAddOrUpdateForm.id != undefined) {
            updateQcCaseClassification(this.caseClassificationAddOrUpdateForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getQcCaseClassificationList();
              this.getCaseList();
            });
          } else {
            addQcCaseClassification(this.caseClassificationAddOrUpdateForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getQcCaseClassificationList();
              this.getCaseList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      const caseClassificationId = row.id;
      this.$modal.confirm('确定删除该分类吗').then(function () {
        return removeQcCaseClassification(caseClassificationId);
      }).then(() => {
        this.getQcCaseClassificationList();
        this.getCaseList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加案例分类";
    },
    handleUpdate(row) {
      this.reset();
      let caseClassificationAddOrUpdateForm = JSON.parse(JSON.stringify(row))
      this.caseClassificationAddOrUpdateForm = caseClassificationAddOrUpdateForm
      this.open = true;
      this.title = "修改案例分类";
    },
    getCaseList() {
      this.loading = true;
      const queryParams = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize
      };
      const payload = {
        workNo: this.queryParams.workNo,
        caseClassificationId: this.activeCaseClassification.id,
        caseStatus: this.queryParams.caseStatus
      };
      getQcCaseList(queryParams, payload).then(response => {
        this.caseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      this.getQcCaseEachCaseStatusQuantity();
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.caseClassificationAddOrUpdateForm = {};
      this.resetForm("caseClassificationAddOrUpdateForm");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getCaseList();
    },
    handleCaseClassification(row) {
      if (row) {
        this.activeCaseClassification = row;
      } else {
        this.activeCaseClassification = {};
      }
      this.getCaseList();
    },
    changeCaseStatus(item) {
      this.getCaseList();
    },
    getQcCaseEachCaseStatusQuantity() {
      const payload = {
        workNo: this.queryParams.workNo,
        caseClassificationId: this.activeCaseClassification.id
      };
      getQcCaseEachCaseStatusQuantity(payload).then(response => {
        this.eachCaseStatusQuantity = response.data;
      });
    },
    openCaseDetail(row) {
      this.$router.push({ path: '/quality-detail/index', query: {bizNo:row.bizNo,type:"3"}})
    },
    handleConfirm(row) {
      row.caseStatus = '2';
      updateQcCase(row).then(response => {
        this.$modal.msgSuccess("案例通过");
        this.getCaseList();
      });
    },
    handleCancel(row) {
      row.caseStatus = '3';
      updateQcCase(row).then(response => {
        this.$modal.msgSuccess("案例驳回");
        this.getCaseList();
      });
    },
    getWorkNo(v){
      return Utils.getWorkNo(v)
    },
    getName(v){
      return Utils.getName(v)
    },
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.sortItem {
  border-left: 2px solid white;
  font-size: 13px;
  padding: 8px 14px;
  cursor: pointer;
}

.sortItem:hover {
  border-left: 2px solid #409EFF;
  background: #ecf5ff;
}

div.sortItem.row.spaceBet.childItem.active-item {
  border-left: 2px solid #409EFF;
  background: #ecf5ff;
}

.childItem {
  padding-left: 30px;
}
</style>

<style scoped>
.caseClassificationAddOrUpdateForm .el-form-item {
  width: 100%;
}

::v-deep .caseClassificationAddOrUpdateForm .el-form-item .el-form-item__label {
  padding: 0px;
}

.caseClassificationAddOrUpdateForm .el-select {
  width: 100%;
}

.caseForm .el-form-item {
  width: 100%;
}

::v-deep .caseForm .el-form-item .el-form-item__label {
  padding: 0px;
}

.caseForm .scoringSelect .el-select {
  width: 100%;
}
</style>