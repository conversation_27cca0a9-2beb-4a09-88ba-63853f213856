import request from '@/utils/request'

// 查询业务标签列表
export function getQcBusinessLabelList(params, data) {
  return request({
    url: '/qc/business/label/list',
    method: 'post',
    params,
    data
  })
}

// 新增业务标签
export function addQcBusinessLabel(data) {
  return request({
    url: '/qc/business/label/add',
    method: 'post',
    data: data
  })
}

// 修改业务标签
export function updateQcBusinessLabel(data) {
  return request({
    url: '/qc/business/label/update',
    method: 'post',
    data: data
  })
}

// 删除业务标签
export function removeQcBusinessLabel(id) {
  return request({
    url: '/qc/business/label/remove/' + id,
    method: 'post'
  })
}
