package com.ideal.qc.service.impl;

import java.io.File;
import java.util.List;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ideal.qc.mapper.QcDatasetMapper;
import com.ideal.qc.domain.QcDataset;
import com.ideal.qc.service.IQcDatasetService;

import cn.hutool.core.io.FileUtil;

import javax.annotation.Resource;

/**
 * 数据集Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
public class QcDatasetServiceImpl implements IQcDatasetService 
{
    @Resource
    private QcDatasetMapper qcDatasetMapper;

    /**
     * 查询数据集
     * 
     * @param id 数据集主键
     * @return 数据集
     */
    @Override
    public QcDataset selectQcDatasetById(Long id)
    {
        return qcDatasetMapper.selectQcDatasetById(id);
    }

    /**
     * 查询数据集列表
     * 
     * @param qcDataset 数据集
     * @return 数据集
     */
    @Override
    public List<QcDataset> selectQcDatasetList(QcDataset qcDataset)
    {
        return qcDatasetMapper.selectQcDatasetList(qcDataset);
    }
    /**
     * 查询数据集列表
     * 
     * @param qcDataset 数据集
     * @return 数据集
     */
    @Override
    public List<QcDataset> selectQcDatasetListByMap(JSONObject param)
    {
        return qcDatasetMapper.selectQcDatasetListByMap(param);
    }
    /**
     * 新增数据集
     * 
     * @param qcDataset 数据集
     * @return 结果
     */
    @Override
    public int insertQcDataset(QcDataset qcDataset)
    {
        qcDataset.setCreateTime(DateUtils.getNowDate());
        return qcDatasetMapper.insertQcDataset(qcDataset);
    }

    /**
     * 修改数据集
     * 
     * @param qcDataset 数据集
     * @return 结果
     */
    @Override
    public int updateQcDataset(QcDataset qcDataset)
    {
        qcDataset.setUpdateTime(DateUtils.getNowDate());
        return qcDatasetMapper.updateQcDataset(qcDataset);
    }

    /**
     * 批量删除数据集
     * 
     * @param ids 需要删除的数据集主键
     * @return 结果
     */
    @Override
    public int deleteQcDatasetByIds(Long[] ids)
    {
        return qcDatasetMapper.deleteQcDatasetByIds(ids);
    }

    /**
     * 删除数据集信息
     * 
     * @param id 数据集主键
     * @return 结果
     */
    @Override
    public int deleteQcDatasetById(Long id)
    {
        return qcDatasetMapper.deleteQcDatasetById(id);
    }


    
}
