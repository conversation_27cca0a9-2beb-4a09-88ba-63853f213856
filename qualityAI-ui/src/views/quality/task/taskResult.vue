<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="所属平台" prop="platform">
        <el-input
          v-model="queryParams.platform"
          placeholder="请输入所属平台"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属项目" prop="project">
        <el-input
          v-model="queryParams.project"
          placeholder="请输入所属项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="坐席工号" prop="workNo">
        <el-input
          v-model="queryParams.workNo"
          placeholder="请输入坐席工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="坐席姓名" prop="workName">
        <el-input
          v-model="queryParams.workName"
          placeholder="请输入坐席姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-input
          v-model="queryParams.startTime"
          placeholder="请输入开始时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-input
          v-model="queryParams.endTime"
          placeholder="请输入结束时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务结果" prop="taskResult">
        <el-input
          v-model="queryParams.taskResult"
          placeholder="请输入任务结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:taskData:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:taskData:edit']"
          >查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="所属平台" align="center" prop="platform"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="所属项目" align="center" prop="project"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="坐席工号" align="center" prop="workNo"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="坐席姓名" align="center" prop="workName"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="业务类型" align="center" prop="businessType"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="产品类型" align="center" prop="productType"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="服务类型" align="center" prop="serviceType"  show-overflow-tooltip min-width="90"/>
      <el-table-column label="智能质检状态" align="center" prop="zjStatus" />
      <el-table-column label="智能质检时间" align="center" prop="incomingTime" show-overflow-tooltip min-width="90" />
      <el-table-column label="人工复核" align="center" prop="taskResult"  show-overflow-tooltip min-width="90"/>

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
  import { listData, getData } from "@/api/quality/taskData";

  export default {
    name: "Data",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 质检源数据表格数据
        dataList: [],
        // 质检评测表格数据
        evaluationList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          platform: null,
          project: null,
          targetName: null,
          targetSubName: null,
          serviceCenter: null,
          workNo: null,
          workName: null,
          startTime: null,
          endTime: null,
          longTime: null,
          dataDate: null,
          qsNo: null,
          telephone: null,
          businessType: null,
          serviceProvider: null,
          onlyNo: null,
          province: null,
          city: null,
          contactPhone: null,
          area: null,
          productType: null,
          serviceOrderNo: null,
          serviceType: null,
          completionTime: null,
          productName: null,
          saleName: null,
          seqNo: null,
          testMonth: null,
          callResult: null,
          taskResult: null,
          audioUrl: null,
          incomingTime: null,
          zjStatus: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
        }
      };
    },
    created() {
      this.getList();
    },
    methods: {
      /** 查询质检源数据列表 */
      getList() {
        this.loading = true;
        const zjTaskId = this.$route.query.taskId || this.$route.params.taskId;
        if (zjTaskId && !/^\d+$/.test(zjTaskId)) {
          this.$message.warning('任务ID格式不正确');
          return;
        }
        const requestParams = {
          ...this.queryParams,  // 保留原有的查询参数
          ...(zjTaskId && { zjTaskId }) // 如果taskId存在则加入参数
        };

        listData(requestParams).then(response => {
          this.dataList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      handleDetail(row){
        this.$router.push({ path: '/quality-task-check/index', query: { id: row.id,isEdit:0}})
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          platform: null,
          project: null,
          targetName: null,
          targetSubName: null,
          serviceCenter: null,
          workNo: null,
          workName: null,
          startTime: null,
          endTime: null,
          businessType: null,
          serviceProvider: null,
          onlyNo: null,
          contactPhone: null,
          productType: null,
          serviceOrderNo: null,
          serviceType: null,
          completionTime: null,
          productName: null,
          callResult: null,
          taskResult: null,
          audioUrl: null,
          incomingTime: null,
          zjStatus: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length!==1
        this.multiple = !selection.length
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download('system/data/export', {
          ...this.queryParams
        }, `data_${new Date().getTime()}.xlsx`)
      }
    }
  };
</script>
