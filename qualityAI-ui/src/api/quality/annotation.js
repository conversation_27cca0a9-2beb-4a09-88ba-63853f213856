import request from '@/utils/request'

// 查询质检标注列表
export function listAnnotation(query) {
  return request({
    url: '/qc/annotation/list',
    method: 'get',
    params: query
  })
}
// 查询质检标注列表
export function listAllAnnotation(query) {
  return request({
    url: '/qc/annotation/listAll',
    method: 'get',
    params: query
  })
}

// 查询质检标注详细
export function getAnnotation(id) {
  return request({
    url: '/qc/annotation/' + id,
    method: 'get'
  })
}

// 新增质检标注
export function addAnnotation(data) {
  return request({
    url: '/qc/annotation',
    method: 'post',
    data: data
  })
}

// 修改质检标注
export function updateAnnotation(data) {
  return request({
    url: '/qc/annotation',
    method: 'put',
    data: data
  })
}

// 删除质检标注
export function delAnnotation(id) {
  return request({
    url: '/qc/annotation/' + id,
    method: 'delete'
  })
}
