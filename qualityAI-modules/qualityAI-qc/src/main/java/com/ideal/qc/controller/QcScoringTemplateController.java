package com.ideal.qc.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.web.page.TableDataInfo;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcScoringTemplate;
import com.ideal.qc.dto.QcScoringTemplateDTO;
import com.ideal.qc.service.QcScoringTemplateService;
import com.ideal.qc.vo.QcScoringTemplateVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 评分模板表Controller接口
 *
 * <AUTHOR>
 * @date 2025/5/13 17:20
 */
@RestController
@RequestMapping("/scoring/template")
public class QcScoringTemplateController extends BaseController {

    @Resource
    private QcScoringTemplateService qcScoringTemplateService;

    /**
     * 查询评分模板列表
     */
    @RequiresPermissions("quality:scoringTemplate:list")
    @PostMapping("/list")
    public TableDataInfo getQcScoringTemplateList(@RequestBody JSONObject payload) {
        startPage();
        List<QcScoringTemplate> list = qcScoringTemplateService.getQcScoringTemplateList(payload);
        return getDataTable(list);
    }

    /**
     * 新增评分模板
     */
    @RequiresPermissions("quality:scoringTemplate:add")
    @PostMapping("/add")
    public AjaxResult addQcScoringTemplate(@RequestBody JSONObject payload) {
        qcScoringTemplateService.addQcScoringTemplate(payload);
        return AjaxResult.success();
    }

    /**
     * 修改评分模板
     */
    @RequiresPermissions("quality:scoringTemplate:edit")
    @PostMapping("/update")
    public AjaxResult updateQcScoringTemplate(@RequestBody JSONObject payload) {
        qcScoringTemplateService.updateQcScoringTemplate(payload);
        return AjaxResult.success();
    }

    /**
     * 查询模板详情根据模板ID
     */
    @PostMapping("/{id}")
    public AjaxResult getQcScoringTemplate(@PathVariable Long id) {
        QcScoringTemplateVo result = qcScoringTemplateService.getQcScoringTemplate(id);
        return AjaxResult.success(result);
    }

    /**
     * 删除评分模板
     */
    @RequiresPermissions("quality:scoringTemplate:remove")
    @PostMapping("/remove/{ids}")
    public AjaxResult removeQcScoringTemplate(@PathVariable Long[] ids) {
        qcScoringTemplateService.removeQcScoringTemplate(ids);
        return AjaxResult.success();
    }

    /**
     * 查询所有状态正常的评分模板
     */
    @PostMapping("/all")
    public AjaxResult getAllQcScoringTemplate() {
        return AjaxResult.success(qcScoringTemplateService.getAllQcScoringTemplate());
    }
}
