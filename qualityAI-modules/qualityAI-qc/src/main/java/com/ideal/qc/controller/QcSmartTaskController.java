package com.ideal.qc.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.common.security.utils.SecurityUtils;
import com.ideal.qc.domain.QcSmartPlan;
import com.ideal.qc.domain.QcSmartTask;
import com.ideal.qc.domain.QcSmartTaskDetail;
import com.ideal.qc.domain.QcSmartTaskResult;
import com.ideal.qc.service.IQcSmartTaskService;
import com.ideal.system.api.DictDataService;
import com.ideal.system.api.domain.SysDictData;
import com.ideal.system.api.domain.SysUser;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 智能质检任务Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequestMapping("/smart/task")
public class QcSmartTaskController extends BaseController
{
	@Autowired
    private DictDataService dictDataService;
    @Autowired
    private IQcSmartTaskService qstService;

    /**
     * 查询质检规则列表
     */
    @RequiresPermissions("quality:task:list")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody JSONObject qst)
    {
		startPage();
        List<QcSmartTask> list = qstService.selectQcSmartTask(qst);
        return getDataTable(list);
    }
    @RequiresPermissions("quality:task:list")
    @PostMapping("/detail/list")
    public TableDataInfo detailList(@RequestBody JSONObject qstd)
    {
		startPage();
        List<QcSmartTaskDetail> list = qstService.selectQcSmartTaskDetail(qstd);
        return getDataTable(list);
    }
    @RequiresPermissions("quality:detail:export")
    @PostMapping("/detail/export")
    public void export(HttpServletRequest request, HttpServletResponse response, QcSmartTaskDetail qstd)
    {
    	List<QcSmartTaskDetail> list = qstService.selectQcSmartTaskDetail(JSONObject.from(qstd));
        Map data = dictDataService.queryListByType(new HashMap());
        
        ExcelUtil<QcSmartTaskDetail> util = new ExcelUtil<QcSmartTaskDetail>(QcSmartTaskDetail.class,JSONObject.from(data.get("data")));
        util.exportExcel(response, list, "任务明细数据");
    }
    @RequiresPermissions("quality:task:list")
    @PostMapping("/result/list")
    public TableDataInfo resultList(@RequestBody QcSmartTaskResult qstr)
    {
    	if(qstr.isPg()) {
    		startPage();
    	}
        List<QcSmartTaskResult> list = qstService.selectQcSmartTaskResult(qstr);
        return getDataTable(list);
    }
    @RequiresPermissions("quality:task:list")
    @PostMapping("/plan/list")
    public TableDataInfo planList(@RequestBody JSONObject qsp)
    {
    	if(qsp.getBooleanValue("isPg")) {
    		startPage();
    	}
        List<QcSmartPlan> list = qstService.selectQcSmartPlanList(qsp);
        return getDataTable(list);
    }

    /**
     * 删除质检规则
     */
    @RequiresPermissions("quality:plan:exec")
    @Log(title = "执行智能计划", businessType = BusinessType.DOING)
    @PostMapping("/plan/exec")
    public AjaxResult doPlan(@RequestBody JSONObject param)
    {
    	qstService.doSmartPlan(param);
        return AjaxResult.success();
    }
    /**
     * 删除质检规则
     */
    @RequiresPermissions("quality:task:update")
    @Log(title = "更新智能明细", businessType = BusinessType.UPDATE)
    @PostMapping("/detail/update")
    public AjaxResult updSmartDetail(@RequestBody JSONObject param)
    {
    	param.put("updateBy",SecurityUtils.getUsername());
    	param.put("createBy",SecurityUtils.getUsername());
    	qstService.updateSmartDetail(param);
        return AjaxResult.success();
    }
    /**
     * 删除质检规则
     */
    @RequiresPermissions("quality:task:update")
    @Log(title = "更新智能计划", businessType = BusinessType.UPDATE)
    @PostMapping("/plan/update")
    public AjaxResult updSmartPlan(@RequestBody JSONObject param)
    {
    	param.put("updateBy",SecurityUtils.getUsername());
    	param.put("createBy",SecurityUtils.getUsername());
    	qstService.updateSmartPlan(param);
        return AjaxResult.success();
    }
    /**
     * 删除质检规则
     */
    @RequiresPermissions("quality:task:create")
    @Log(title = "创建智能任务", businessType = BusinessType.CREATE)
    @PostMapping("/create")
    public AjaxResult createSmartTask(@RequestBody JSONObject param)
    {
    	qstService.createSmartTask(param);
        return AjaxResult.success();
    }
    /**
     * 删除质检规则
     */
    @RequiresPermissions("quality:task:do")
    @Log(title = "执行智能明细", businessType = BusinessType.DOING)
    @PostMapping("/detail/exec")
    public AjaxResult execSmartTaskDetail(@RequestBody JSONObject param)
    {
    	qstService.doSmartTaskDetail(param);
        return AjaxResult.success();
    }
    /**
     * 大模型执行指定的质检任务
     */
    @RequiresPermissions("quality:task:do")
    @Log(title = "执行单条明细", businessType = BusinessType.DOING)
    @GetMapping("/do/{id}")
    public AjaxResult execSmartTaskDetail(@PathVariable Long id)
    {	
    	JSONObject param = new JSONObject();
    	param.put("id", id);
    	param.put("status", 3);
    	qstService.doSmartTaskDetail(param);
        return AjaxResult.success();
    }
    /**
     * 大模型执行指定的质检任务
     */
    @RequiresPermissions("quality:task:stop")
    @Log(title = "终止智能任务", businessType = BusinessType.STOP)
    @PostMapping("/stop")
    public AjaxResult stopTask(@RequestBody JSONObject param)
    {	
    	param.put("updateBy",SecurityUtils.getUsername());
    	qstService.stopQcSmartTask(param);
        return AjaxResult.success();
    }
}
