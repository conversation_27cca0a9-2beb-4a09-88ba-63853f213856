package com.ideal.qc.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ideal.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.common.log.annotation.Log;
import com.ideal.common.log.enums.BusinessType;
import com.ideal.common.security.annotation.RequiresPermissions;
import com.ideal.qc.domain.QcListenLog;
import com.ideal.qc.service.IQcListenLogService;
import com.ideal.common.core.web.controller.BaseController;
import com.ideal.common.core.web.domain.AjaxResult;
import com.ideal.common.core.utils.poi.ExcelUtil;
import com.ideal.common.core.web.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@RequestMapping("/listenLog")
public class QcListenLogController extends BaseController
{
    @Autowired
    private IQcListenLogService qcListenLogService;

    /**
     * 查询【请填写功能名称】列表
     */
    @RequiresPermissions("system:log:list")
    @GetMapping("/list")
    public TableDataInfo list(QcListenLog qcListenLog)
    {
        startPage();
        List<QcListenLog> list = qcListenLogService.selectQcListenLogList(qcListenLog);
        return getDataTable(list);
    }
    @GetMapping("/allList")
    public AjaxResult allList(QcListenLog qcListenLog)
    {
        List<QcListenLog> list = qcListenLogService.selectQcListenLogList(qcListenLog);
        return success(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @RequiresPermissions("system:log:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QcListenLog qcListenLog)
    {
        List<QcListenLog> list = qcListenLogService.selectQcListenLogList(qcListenLog);
        ExcelUtil<QcListenLog> util = new ExcelUtil<QcListenLog>(QcListenLog.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @RequiresPermissions("system:log:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(qcListenLogService.selectQcListenLogById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @RequiresPermissions("system:log:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QcListenLog qcListenLog)
    {
        qcListenLog.setCreateBy(SecurityUtils.getUsername());
        return toAjax(qcListenLogService.insertQcListenLog(qcListenLog));
    }

    /**
     * 修改【请填写功能名称】
     */
    @RequiresPermissions("system:log:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QcListenLog qcListenLog)
    {
        return toAjax(qcListenLogService.updateQcListenLog(qcListenLog));
    }

    /**
     * 删除【请填写功能名称】
     */
    @RequiresPermissions("system:log:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(qcListenLogService.deleteQcListenLogByIds(ids));
    }
}
