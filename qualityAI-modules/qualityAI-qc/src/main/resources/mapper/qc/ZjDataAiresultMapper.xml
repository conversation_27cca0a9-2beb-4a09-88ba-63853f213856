<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.qc.mapper.ZjDataAiresultMapper">

    <resultMap type="com.ideal.qc.domain.ZjDataAiresult" id="ZjDataAiresultResult">
        <result property="id"    column="id"    />
        <result property="dataNo"    column="data_no"    />
        <result property="templateId"    column="template_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="aiResult"    column="ai_result"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectZjDataAiresultVo">
        select id, data_no, template_id, item_id,ai_result, create_by, create_time from zj_data_airesult
    </sql>

    <select id="selectZjDataAiresultList" parameterType="ZjDataAiresult" resultMap="ZjDataAiresultResult">
        <include refid="selectZjDataAiresultVo"/>
        <where>
            <if test="dataNo != null  and dataNo != ''"> and data_no = #{dataNo}</if>
            <if test="templateId != null "> and template_id = #{templateId}</if>
            <if test="itemId != null">item_id= #{itemId},</if>
            <if test="aiResult != null  and aiResult != ''"> and ai_result = #{aiResult}</if>
        </where>
    </select>

    <select id="selectZjDataAiresultById" parameterType="Long" resultMap="ZjDataAiresultResult">
        <include refid="selectZjDataAiresultVo"/>
        where id = #{id}
    </select>

    <insert id="insertZjDataAiresult" parameterType="ZjDataAiresult" useGeneratedKeys="true" keyProperty="id">
        insert into zj_data_airesult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataNo != null">data_no,</if>
            <if test="templateId != null">template_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="aiResult != null">ai_result,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataNo != null">#{dataNo},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="aiResult != null">#{aiResult},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateZjDataAiresult" parameterType="ZjDataAiresult">
        update zj_data_airesult
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataNo != null">data_no = #{dataNo},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="itemId != null">item_id= #{itemId},</if>
            <if test="aiResult != null">ai_result = #{aiResult},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZjDataAiresultById" parameterType="Long">
        delete from zj_data_airesult where id = #{id}
    </delete>

    <delete id="deleteZjDataAiresultByIds" parameterType="String">
        delete from zj_data_airesult where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
