<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="模板类型" prop="templateType">
        <el-select v-model="queryParams.templateType" placeholder="请选择模板类型" clearable :style="{width: '100%'}">
          <el-option v-for="(item, index) in modelTypeList" :key="index" :label="item.label"
                     :value="item.value" :disabled="item.disabled"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" prop="taskStatus">
        <el-select v-model="queryParams.taskStatus" placeholder="任务状态" clearable>
          <el-option
            v-for="dict in dict.type.quality_task_execute_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['quality:task:add']"
        >新增</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['quality:task:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="taskId" />
      <el-table-column label="任务名称" align="left" prop="taskName" />
      <el-table-column label="质检模板" align="left" prop="templateName"/>
      <el-table-column label="创建时间" align="center" prop="createTime" >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="优先级"  align="center" prop="priority">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.quality_task_priority" :value="scope.row.priority"/>
        </template>
      </el-table-column>

      <el-table-column label="执行状态" align="center" prop="executeStatus" width="150">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.quality_task_execute_status" :value="scope.row.executeStatus"/>
        </template>
      </el-table-column>

      <el-table-column label="完成度"  align="center" prop="taskCompletion" />

     <!-- <el-table-column label="任务状态" align="center" prop="taskStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.quality_task_status" :value="scope.row.taskStatus"/>
        </template>
      </el-table-column>-->
      <!--<el-table-column label="租户"  align="center" prop="tenantCode" />-->
      <el-table-column label="操作"  align="center" class-name="small-padding fixed-width"  width="250">

        <template slot-scope="scope">
           <!-- <el-switch v-model="scope.row.taskStatus" :active-value="1" :inactive-value="0" :active-text="开启"
                       :inactive-text="禁用" @change="handleSwitchClick(scope.row)"></el-switch>-->

          <el-button
            size="mini"
            type="text"
            @click="handleSwitchClick(scope.row)"
            :loading="scope.row.switchLoading"
            :disabled="scope.row.executeStatus == 2"
            :type="scope.row.executeStatus == 2 ? 'info' : 'text'"
            :plain="scope.row.executeStatus == 2"
          >
            {{ scope.row.executeStatus == 0 ? '启动' :
            scope.row.executeStatus == 1 ? '停止' : '已完成'}}
          </el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['quality:task:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['quality:task:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="handleQualityCheck(scope.row.taskId)"
          >质检列表</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改 -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col>
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="form.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="业务类型" prop="businessType">
              <el-select v-model="form.businessType" placeholder="业务类型" clearable>
                <el-option
                  v-for="dict in dict.type.quality_task_business_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品类型" prop="productType">
              <el-select v-model="form.productType" placeholder="请选择产品类型" clearable>
                <el-option
                  v-for="dict in dict.type.quality_task_product_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="坐席工号" prop="workNo">
              <el-input v-model="form.workNo" placeholder="请输入坐席工号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="质检模板" prop="templateId">
              <el-select v-model="form.templateId" placeholder="请选择质检模板" clearable>
                <el-option
                  v-for="dict in templateOptions"
                  :key="dict.id"
                  :label="dict.tplName"
                  :value="dict.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级" clearable>
                <el-option
                  v-for="dict in dict.type.quality_task_priority"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="筛选时间">
              <el-date-picker
                v-model="dateRange"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTask, getTask, delTask, addTask, updateTask,startTask } from "@/api/quality/task";
import { Utils } from '@/api/util/common';
import { listScoretpl} from "@/api/quality/scoreTemplate";
export default {
  name: "QualityTask",
  dicts: ['quality_task_execute_status','quality_task_business_type','quality_task_product_type','quality_task_priority','quality_task_status'],
  data() {
    return {
      currentExecution: null,  // 当前执行任务的Promise
      rejectExecution: null,    // 用于撤回任务的reject方法
      // 遮罩层
      loading: false,
      // 选中数组
      taskIds: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 题目表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示得分
      dfshow: false,
      // 是否显示单选或多选
      selectshow: false,
      modelTypeList: [{"label":'所有',"value":''},{"label":'规则模板',"value":'规则模板'},{"label":'大模型',"value":'大模型'}],
      // 日期范围
      dateRange: [],
      //质检模板
      templateOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateType: undefined,
        taskName: undefined,
        taskStatus: undefined
      },
      // 查询参数
      queryTemplateParams: {
        pageNum: 1,
        pageSize: 10,
        tplName: null,
        itemCnt: null,
        status: null,
      },
      // 表单参数
      form: {
        taskName:""
      },
      // 表单校验
      rules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],

      },
      beforeDestroy() {
        // 组件销毁时清理
        if (this.rejectExecution) {
          this.rejectExecution({ isCanceled: true });
        }
      }
    };
  },
  created() {
    this.getList();
    this.getTemplateList();
  },
  methods: {

    /** 查询模板列表 */
    getTemplateList() {
      this.loading = true;
      listScoretpl(this.queryTemplateParams).then(response => {
        let scoretplList = [];
          scoretplList = response.rows;
        this.templateOptions = scoretplList;
      });
    },

    /** 查询任务列表 */
    getList() {
      this.loading = false;
      listTask(this.queryParams).then(response => {
        this.taskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        taskName: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.dfshow =  false;
      this.selectshow =  false;
      this.title = "添加任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const taskId = row.taskId || this.taskIdIds
      getTask(taskId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改任务";
        let taskName = this.form.taskName;
        this.dfshow =  false;
        this.selectshow =  false;
      });
    },
    handleStatusChange(row){
    // 添加加载状态
      this.$set(row, 'switchLoading', true);
      // 调用API接口，假设你的接口是 updateTaskStatus
      startTask({
        taskStatus: row.taskStatus,
        taskId: row.taskId  // 假设row中有id字段
      }).then(response => {
        this.$modal.msgSuccess("状态更新成功");
        // 这里可以根据需要刷新数据或做其他处理
      }).catch(error => {
        // 出错时恢复原来的状态
        row.status = row.status === 1 ? 0 : 1;
        this.$modal.msgError("状态更新失败");
      }).finally(() => {
        // 移除加载状态
        this.$set(row, 'switchLoading', false);
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateTask(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTask(this.addDateRange(this.form, this.dateRange)).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.taskId || this.taskIds;
      console.log(ids)
      this.$modal.confirm('是否确认删除任务编号为"' + ids + '"的数据项？').then(function() {
        return delTask(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('qc/task/export', {
        ...this.queryParams
      }, `task_${new Date().getTime()}.xlsx`)
    },
    handleRadioChange(value) {
      console.log('Radio value changed:', value);
      this.dfshow =  false;
      this.selectshow =  false;
    },
    addOption(){
      this.form['options'].push({answer:''})
      console.log(this.form.options,'9999999999')
    },
    cutOption(index){
      this.form['options'].splice(index,1);
    },
    handleQualityCheck(taskId){
      this.$router.push({ path: '/taskResult', query: { taskId: taskId}})
    /*  this.$router.push({
        path: '/taskResult',
        query: { taskId }
      });*/
    },
    async handleSwitchClick(row) {
      try {
        if (row.executeStatus === 2) return;
        // 防止重复点击
        if (row.switchLoading) return;

        this.$set(row, 'switchLoading', true);
        // 先确认用户操作
        const confirmMessage = row.executeStatus == 0
          ? '确定要启用并执行该任务吗？'
          : '确定要禁用该任务吗？';
        await this.$confirm(confirmMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        // 保存原始状态以便撤回
        const originalStatus = row.executeStatus;
        // 先更新状态
        await this.updateTaskStatus(row);

        // 如果是启用操作，则执行任务
        if (row.executeStatus == 0) {
          // 这里返回执行任务的Promise，以便可以撤回
          this.currentExecution = this.executeTask(row);
          try {
            await this.currentExecution;
          } catch (e) {
            if (e.isCanceled) {
              // 任务被撤回，恢复状态
              await this.revertTaskStatus(row, originalStatus);
              return;
            }
            throw e;
          }
        }

        this.$message.success(
          row.executeStatus == 0
            ? '任务已启用并开始执行'
            : '任务已禁用'
        );
      } catch (error) {
        if (error !== 'cancel') {
          console.log(error)
          // 恢复开关状态
         /* row.executeStatus = row.executeStatus == 1 ? 0 : 1;
          this.$message.error(
            row.executeStatus == 1
              ? '任务启用失败: ' + error.message
              : '任务禁用失败: ' + error.message
          );*/
        }
      } finally {
        this.$set(row, 'switchLoading', false);
      }
      this.getList();
    },

    // 更新任务状态API
    async updateTaskStatus(row) {
      debugger;
      try {
        const newStatus = row.executeStatus == 0 ? 1 : 0; // 切换 0 ↔ 1
        const response = await updateTask({
          taskId: row.taskId,
          executeStatus:newStatus
        });
        // 可以根据API返回结果做进一步处理
        return response;
      } catch (error) {
        console.log(error)
        throw new Error('状态更新失败');
      }
    },

    // 执行任务API（返回Promise以便可以撤回）
    executeTask(row) {
      return new Promise(async (resolve, reject) => {
        // 存储reject方法以便撤回
        this.rejectExecution = reject;

        try {
          const result = await startTask({
            taskId: row.taskId
          });

          // 清除reject引用
          this.rejectExecution = null;
          resolve(result);
        } catch (error) {
          // 清除reject引用
          this.rejectExecution = null;

          if (error.isCanceled) {
            // 任务被主动撤回
            reject({ ...error, isCanceled: true });
          } else {
            reject(error);
          }
        }
      });
    },

    // 撤回当前执行的任务
    async cancelCurrentExecution() {
      if (this.rejectExecution) {
        // 调用reject方法中断Promise
        this.rejectExecution({
          message: '任务执行已撤回',
          isCanceled: true
        });
        this.rejectExecution = null;

        // 可以在这里添加撤回后的处理逻辑
        this.$message.warning('任务执行已撤回');
        return true;
      }
      return false;
    },

    // 恢复任务状态
    async revertTaskStatus(row, originalStatus) {
      try {
        row.executeStatus = originalStatus;
        await updateTask({
          taskId: row.taskId,
          executeStatus: originalStatus
        });
        this.$message.warning('已恢复任务状态');
      } catch (error) {
        this.$message.error('恢复任务状态失败');
      }
    }
  },
};
</script>
