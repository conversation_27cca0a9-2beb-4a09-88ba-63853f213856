<template>
	<div class="app-container">
		<div >
				<el-button v-if="op=='edit'" class="btn" type="primary" @click="submitForm" :loading="submiting">保 存</el-button>
				<el-input v-if="test" type="textarea" v-model="input" />
				<el-input v-if="test" type="textarea" v-model="result" />
				<el-button v-if="test" class="btn" type="primary" @click="getTemplate" :loading="submiting">获取数据</el-button>
				<el-button v-if="test" class="btn" type="primary" @click="formatSubmitResult" :loading="submiting">结果</el-button>
				<el-button v-if="test" class="btn" type="primary" @click="submitForm" :loading="submiting">保存测试</el-button>
		    </div>
		<div ref="treePanel" style="width: 1600px; height: 800px;overflow:auto;"></div>
	<!-- 添加或修改分类对话框 -->
<!--	    <el-dialog :title="title" :visible.sync="currRule.open" width="500px" append-to-body>-->
<!--	      <el-form ref="sortForm" :model="currRule" label-width="80px">-->
<!--	        <el-form-item label="智能结果" prop="dictLabel">-->
<!--	          <el-input v-model="currRule.dictLabel" placeholder="请输入分类名称" maxlength="30" />-->
<!--	        </el-form-item>-->
<!--			<el-form-item label="大模型结果" prop="dictLabel">-->
<!--	          <el-input v-model="currRule.dictLabel" placeholder="请输入分类名称" maxlength="30" />-->
<!--	        </el-form-item>-->
<!--			<el-form-item label="人工复检结果" prop="dictLabel">-->
<!--	          <el-input v-model="currRule.dictLabel" placeholder="请输入分类名称" maxlength="30" />-->
<!--	        </el-form-item>-->
<!--			<el-form-item label="被指" prop="dictLabel">-->
<!--	          <el-input v-model="currRule.remark" placeholder="请输入分类名称" maxlength="30" />-->
<!--	        </el-form-item>-->
<!--	      </el-form>-->
<!--	      <div slot="footer" class="dialog-footer">-->
<!--	        <el-button type="primary" @click="">保 存</el-button>-->
<!--	        <el-button @click="cancel">取 消</el-button>-->
<!--	      </div>-->
<!--	    </el-dialog>-->
	</div>
</template>

<script>
import { getTemplate} from "@/api/quality/scoringTemplate.js";
import { detailList,resultList,updateSmart,addManualResult} from "@/api/quality/smart.js";
import { getDetail,manualResultList} from "@/api/quality/manualTask.js";
import * as echarts from '@/api/system/echarts.min.js'
var myChart
var option

export default {
  name: "Review",
  props:{
  	viewParam:Object
    },
  data() {
    return {
		data:[],
		level:[
			{lkey:"qcScoringItemDTOList",nkey:"scoringTemplateName"},
			{lkey:"qcScoringHitRuleGroupDTOList",nkey:"scoringItemName"},
			{lkey:"qcScoringHitRuleItemList",nkey:""}
		],
		smartList:[],
		updObj:[],
		smartData:{},
		op:"view",
		submiting:false,
		currRule:{
			open:false
		},show:false,input:"",title:"1111111",result:"",test:false
    };
  },
  created() {
	if(!this._props || !this._props.viewParam){
		this.test = true;
		return ;
	}
	this.getTreeData();
	this.op = this._props.viewParam.op;
	
  },
  methods: {
	getTemplate(){
		this["_props"]["viewParam"]=JSON.parse(this.input);
		this.getTreeData();
		this.op = this._props.viewParam.op;
	},
	formatSubmitResult(){
		if(Object.keys(this.updObj).length === 0){
					this.$message.warning('模板信息未变更');
					return;
				}
				var score = this.data.basicScore;
				var hitItemCount = 0;
				var hitRuleCount = 0;
				var hitPRuleCount = 0;
				var hitMRuleCount = 0;
				var tmp = this.data.children;
				for(var index in tmp){
					var tmp1 = tmp[index];//评分项
					if(tmp1.isHit){
						score += tmp1.hitRuleScore;
						hitItemCount++;
					}
					for(var index2 in tmp1.children){
						var tmp2 = tmp1.children[index2];//规则组
						for(var index3 in tmp2.children){
							var tmp3 = tmp2.children[index3];//规则
							for(var tmp4 in this.updObj){
								if(this.updObj[tmp4].ruleItemId == tmp3.id){
									this.updObj[tmp4]['itemResult'] = tmp1.isHit?'1':"2";
									break;
								}
							}
							if(tmp1.isHit && tmp3.isHit){
								hitRuleCount++;
								if(tmp1.hitRuleScore < 0){
									hitMRuleCount++;
								}else{
									hitPRuleCount++;
								}
							}
						}
					}
				}
				if(score<this.data.lowestScore){
					score = this.data.lowestScore;
				}else if(score > this.data.highestScore){
					score = this.data.highestScore;
				}
				var ret = {
					id:this._props.viewParam.taskDetailId,
					taskDetailId:this._props.viewParam.taskDetailId,
					smartTaskId:this.smartTaskId,
					hitItemCount:hitItemCount,
					hitRuleCount:hitRuleCount,
					hitPlusRuleCount:hitPRuleCount,
					hitMinusRuleCount:hitMRuleCount,
					checkInspectScore:score,
					templateHitRule:JSON.stringify(this.data),
					detailList:[]
				}
				for(var tmp in this.updObj){
					ret.detailList.push(this.updObj[tmp]);
				}
				console.log(ret);
				this.result = JSON.stringify(ret);
				return ret;
	},
    getTreeData(){
		if(this._props.viewParam.taskDetailId != undefined && this._props.viewParam.taskDetailId !=''){
			this.getResult();
		}else if(this._props.viewParam && this._props.viewParam.tempId && this._props.viewParam.tempId !=''){
			getTemplate(this._props.viewParam.tempId).then(response => {
				if(response.data ==undefined){
					this.$message.error('模板【'+this._props.viewParam.tempId+'】编号不存在');
					return;
				}
				this.data = response.data;
				this.init();
				
		      });
		}else{
			this.$message.error('缺失模板编号');
			return;
		}
		 
	},
	getResult(){
		if(this._props.viewParam && this._props.viewParam.qcType == 2 || !this._props.viewParam.qcType){
			detailList({},{isPg:false,id:this._props.viewParam.taskDetailId}).then(response => {
				this.smartTaskId = response.rows[0].smartTaskId;
				this.data = JSON.parse(response.rows[0].templateHitRule);
				resultList({},{isPg:false,taskDetailId:this._props.viewParam.taskDetailId}).then(response => {
					this.smartList = response.rows;
					if(this.data == undefined){
						return;
					}
					this.formatData(this.data,0,0);
					this.init();
		      	}).finally(v=>{});
	      	}).finally(v=>{});
		}else if(this._props.viewParam && this._props.viewParam.qcType == 1){
			getDetail(this._props.viewParam.taskDetailId).then(response => {
				this.smartTaskId = response.rows[0].smartTaskId;
				this.data = JSON.parse(response.data.templateHitRule);
				manualResultList({taskDetailId:this._props.viewParam.taskDetailId}).then(response => {
					this.smartList = response.rows;
					if(this.data == undefined){
						return;
					}
					this.formatData(this.data,0,0);
					this.init();
		      	}).finally(v=>{});
	      	}).finally(v=>{});
		}  
		
			 
	},
		init () {
		      myChart = echarts.init(this.$refs.treePanel)
		      option = {
		        tooltip: { // 提示框浮层设置
		          trigger: 'item',
		          triggerOn: 'mousemove', // 提示框触发条件
		          enterable: true, // 鼠标是否可进入提示框浮层中，默认false
		          confine: true, // 是否将tooltip框限制在图表的区域内
		          formatter: (params) => // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
		            this.formatTip(params)
		          ,
		          // valueFormatter: function (value) { // tooltip 中数值显示部分的格式化回调函数
		          //   return '$' + value.toFixed(2)
		          // },
		          backgroundColor: '#FFF', // 提示框浮层的背景颜色
		          borderColor: '#1890FF', // 提示框浮层的边框颜色
		          borderWidth: 1, // 提示框浮层的边框宽
		          borderRadius: 8, // 提示框浮层圆角
		          padding: [6, 8], // 提示框浮层的内边距
		          textStyle: { // 提示框浮层的文本样式
		            color: '#333', // 文字颜色
		            fontWeight: 400, // 字体粗细
		            fontSize: 16, // 字体大小
		            lineHeight: 20, // 行高
		            width: 60, // 文本显示宽度
		            // 文字超出宽度是否截断或者换行；只有配置width时有效
		            overflow: 'breakAll', // truncate截断，并在末尾显示ellipsis配置的文本，默认为...;break换行;breakAll换行，并强制单词内换行
		            ellipsis: '...'
		          },
		          extraCssText: 'box-shadow: 0 0 9px rgba(0, 0, 0, 0.3);text-align: right;' // 额外添加到浮层的css样式
		        },
		        series: [
		          {
		            type: 'tree',
		            data: [this.data],
		            name: '树图',
		            top: '1%', // 组件离容器上侧的距离，像素值20，或相对容器的百分比20%
		            left: '7%', // 组件离容器左侧的距离
		            bottom: '1%', // 组件离容器下侧的距离
		            right: '20%', // 组件离容器右侧的距离
		            layout: 'orthogonal', // 树图的布局，正交orthogonal和径向radial两种
		            orient: 'LR', // 树图中正交布局的方向，'LR','RL','TB','BT'，只有布局是正交时才生效
		            edgeShape: 'curve', // 树图边的形状，有曲线curve和折线polyline两种，只有正交布局下生效
					width:'1000',
					height:'800',
		            roam: false, // 是否开启鼠标缩放或平移，默认false
		            initialTreeDepth: 5, // 树图初始的展开层级（深度），根节点是0，不设置时全部展开
		            // symbol: 'arrow', // 标记的图形，默认是emptyCircle;circle,rect,roundRect,triangle,diamond,pin,arrow,none
		            // symbolRotate: 270, // 配合arrow图形使用效果较好
		            symbolSize: 16, // 大于0时是圆圈，等于0时不展示，标记的大小
		            itemStyle: { // 树图中每个节点的样式
		              color: '#1890FF', // 节点未展开时的填充色
		              borderColor: 'rgba(255, 144, 0, 1)', // 图形的描边颜色
		              borderWidth: 1, // 描边线宽，为0时无描边
		              borderType: 'dotted', // 描边类型
		              borderCap: 'square', // 指定线段末端的绘制方式butt方形结束，round圆形结束，square
		              shadowColor: 'rgba(0,121,221,0.3)', // 阴影颜色
		              shadowBlur: 16, // 图形阴影的模糊大小
		              opacity: 1 // 图形透明度
		            },
		            label: { // 每个节点对应的文本标签样式
		              show: true, // 是否显示标签
		              distance: 8, // 文本距离图形元素的距离
		              position: 'left', // 标签位置
		              verticalAlign: 'middle', // 文字垂直对齐方式，默认自动，top，middle，bottom
		              align: 'center', // 文字水平对齐方式，默认自动，left，right，center
		              fontSize: 16, // 字体大小
		              color: '#333', // 字体颜色
		              backgroundColor: '#F0F5FA', // 文字块的背景颜色
		              borderColor: '#1890FF', // 文字块边框颜色
		              borderWidth: 1, // 文字块边框宽度
		              borderType: 'solid', // 文字块边框描边类型 solid dashed dotted
		              borderRadius: 4, // 文字块的圆角
		              padding: [6, 12], // 文字块内边距
		              shadowColor: 'rgba(0,121,221,0.3)', // 文字块的背景阴影颜色
		              shadowBlur: 6, // 文字块的背景阴影长度
		              width: 100,
		              // 文字超出宽度是否截断或者换行；只有配置width时有效
		              overflow: 'truncate', // truncate截断，并在末尾显示ellipsis配置的文本，默认为...;break换行;breakAll换行，并强制单词内换行
		              ellipsis: '...'
		            },
		            lineStyle: { // 树图边的样式
		              color: 'rgba(0,0,0,.35)', // 树图边的颜色
		              width: 2, // 树图边的宽度
		              curveness: 0.5, // 树图边的曲度
		              shadowColor: 'rgba(0, 0, 0, 0.5)', // 阴影颜色
		              shadowBlur: 10 // 图形阴影的模糊大小
		            },
		            emphasis: { // 树图中图形和标签高亮的样式
		              disabled: false, // 是否关闭高亮状态，默认false
		              // 在高亮图形时，是否淡出其它数据的图形已达到聚焦的效果
		              focus: 'self', // none不淡出其他图形（默认）；self只聚焦当前高亮的数据图形；series聚焦当前高亮的数据所在系列的所有图形；ancestor聚焦所有祖先节点；descendant聚焦所有子孙节点；relative聚焦所有子孙和祖先节点
		              blurScope: 'coordinateSystem', // 开启focus时，配置淡出的范围，coordinateSystem淡出范围为坐标系（默认）；series淡出范围为系列；global淡出范围为全局
		              itemStyle: { // 该节点的样式
		                color: '#1890FF', // 图形的颜色
		                // borderColor: 'rgba(255, 144, 0, 1)', // 图形的描边颜色
		                borderWidth: 1, // 描边线宽，为0时无描边
		                borderType: 'solid', // 描边类型 solid dashed dotted
		                borderCap: 'square', // 指定线段末端的绘制方式butt方形结束，round圆形结束，square
		                shadowColor: 'rgba(0,121,221,0.3)', // 阴影颜色
		                shadowBlur: 12, // 图形阴影的模糊大小
		                opacity: 1 // 图形透明度
		              },
		              lineStyle: { // 树图边的样式
		                color: '#4bcb78', // 树图边的颜色
		                width: 2, // 树图边的宽度
		                curveness: 0.5, // 树图边的曲度
		                shadowColor: 'rgba(0, 0, 0, 0.5)', // 阴影颜色
		                shadowBlur: 6 // 图形阴影的模糊大小
		              },
		              label: { // 高亮标签的文本样式
		                color: '#333',
		                fontWeight: 600
		              }
		            },
		            blur: { // 淡出状态的相关配置，开启emphasis.focus后有效
		              itemStyle: {color:"gray"}, // 节点的样式
		              lineStyle: {}, // 树图边的样式
		              label: {} // 淡出标签的文本样式
		            },
		            leaves: { // 叶子节点的特殊配置
		              label: { // 叶子节点的文本标签样式
		                distance: 8,
		                // color: '#1890FF',
		                position: 'right',
		                verticalAlign: 'middle',
		                align: 'left',
						formatter: function (params) {
						                    // 你可以在这里返回你想要显示的文字
						                    return params.data.name!=null?params.data.name:""; // 例如，显示节点名称
						                }
		              },
		              itemStyle: {color:"gray"}, // 叶子节点的样式
		              emphasis: {}, // 叶子节点高亮状态的配置
		              blur: {}, // 叶子节点淡出状态的配置
		              select: {} // 叶子节点选中状态的配置
		            },
		            animation: true, // 是否开启动画
		            expandAndCollapse: true, // 子树折叠和展开的交互，默认打开
		            animationDuration: 550, // 初始动画的时长
		            animationEasing: 'linear', // 初始动画的缓动效果
		            animationDelay: 0, // 初始动画的延迟
		            animationDurationUpdate: 750, // 数据更新动画的时长
		            animationEasingUpdate: 'cubicInOut', // 数据更新动画的缓动效果
		            animationDelayUpdate: 0 ,// 数据更新动画的延迟
					layout: { // 调整布局参数以优化显示。
					                orientation: 'vertical', // 或'horizontal'，根据需要选择。
					                nodePadding: 30, // 增加节点之间的间距。
					                layerPadding: 100 // 增加层级之间的间距。
					            }
		          }
		        ]
		      }
		      myChart.setOption(option)
			  if(this.op != "view"){
				  myChart.on("click", this.handleNodeClick)
			  }
		    },
			handleNodeClick(params){
				// params 是一个包含点击节点信息的对象

			      // 在这里可以添加自定义的处理逻辑，比如改变节点样式、弹出提示框等
			      //alert('你点击了节点: ' + params.name);
				  if(params.data.children == undefined){
						//params.data["isHit"] = params.data["isHit"]?false:true;
						var pids = params.data.parentId.split(",");
						var tmp = {
							ruleItemId:params.data.id,
							itemId:pids[2],
							ruleId:params.data.ruleId,
							taskDetailId:this._props.viewParam.taskDetailId,
							//recheckResult:params.data["isHit"],
							id:params.data["qstrId"]?params.data["qstrId"]:null,
							isHit:params.data["isHit"]?false:true,
							recheckResult:params.data["isHit"]?2:1,
							finalResult:params.data["isHit"]?2:1,
							ruleName:params.data.name,
							//updateBy:'CS1868',
							indexs:params.data.indexs
						};
						
						this.updObj[tmp.ruleItemId] = tmp;
						this.updLastTreeNode();
						this.formatData(this.data,0,0);
						this.init();
				  }
			},
	updLastTreeNode(indexs,isHit){
		var tmpL = this.updObj;
		for(var ii in tmpL){
			var tmp = tmpL[ii]["indexs"].split("-");
			var parent = this.data.children[tmp[1]]//评分项
								  .children[tmp[2]];//规则组
			var isAll = parent.ruleGroupMode;
			var final = tmpL[ii].isHit;
			//更新叶子节点
			this.data.children[tmp[1]].children[tmp[2]].children[tmp[3]].isHit = final;
			//更新规则组
			if(isAll == '1'){//全部
				if(final == true){
					for(var j=0;j<parent.children.length;j++){
						if(j==tmp[3]){
							continue;
						}
						if(parent.children[j]["isHit"] ==undefined || parent.children[j]["isHit"] == false){
							final = false;
							break;
						}
					}
				}
			}else{//任一
				if(final == false){
					for(var j=0;j<parent.children.length;j++){
						if(j==tmp[3]){
							continue;
						}
						if(parent.children[j]["isHit"] && parent.children[j]["isHit"] == true){
							final = true;
							break;
						}
					}
				}
			}
			parent["isHit"] = final;
			//更新评分项
			if(final == false){
				var score = this.data.children[tmp[1]].children;
				for(var i=0;i<score.length;i++){
					if(i==tmp[2]){
						continue;
					}
					if(score[i].isHit){
						final = true;
						break;
					}
				}
				
			}
			this.data.children[tmp[1]].isHit = final;
		}
	},
	formatData(data,index,index2){
		if(this.level.length==index){//底层,叶子节点，规则项 
			var status = 1;
			var tmpList = this.smartList;
			if(tmpList.length==0){
//				tmpList = this.updList;
			}
			for(var tmp in tmpList){
				if(data.id == tmpList[tmp].ruleItemId && data.ruleId == tmpList[tmp].ruleId){
					data["sRet"] = tmpList[tmp];
					data["itemStyle"] = {color : '#8AC007'};
					data["lineStyle"] = {color:"red"};
					data["qstrId"] = tmpList[tmp].id;
					if(tmpList[tmp]["isHit"] != undefined){
						data["isHit"] = tmpList[tmp]["isHit"];
					}
					data["indexs"] = index2+"-"+tmp;
					status = 2;
					break;
				}
				data["lineStyle"] = {color:"green"};
			}
			data["indexs"] = index2
			if(data.status == 1 ){
				data["lineStyle"] = {color:"green"};
			}else{
				data["lineStyle"] = {color:"gray"};
			}
			if(data.isHit){
				data["itemStyle"] = {color : '#8AC007'};
				data["lineStyle"] = {color:"red"};
				status = 2;
			}
			//data["name"] = data["ruleName"];
			var label = {};
			if(data.ruleClassification=='1'){
				label["backgroundColor"] = "#5ceb9c";
			}else if(data.ruleClassification=='2'){
				label["backgroundColor"] = "#409EFF";
			}else if(data.ruleClassification=='3'){
				label["backgroundColor"] = "#e2f594";
			}
			data["label"] = label;
			return status;
		}
		if(data.children == undefined){
			return ;
		}
		var tmp = data.children;
		if(!tmp || tmp.length ==0){
			data["children"] = [];
		}
		if(this.level[index].nkey == ''){
			data["name"] = "规则组"+"-" +(data["ruleGroupMode"]==1?"全部":"任一");
		}else{
			//data["name"] = data[this.level[index].nkey];
		}
		for(var obj in tmp){
			var loac = data.parentId?data.parentId:"";
			tmp[obj]["parentId"] = loac +","+data.id;
			var restlt = this.formatData(tmp[obj],index+1,index2+"-"+obj);
			//下层命中，添加样式
			if(restlt==2){
				data["lineStyle"] = {color:"red"};		
			}else if(restlt==1){
				data["lineStyle"] = {color:"green"};
			}
			data["children"] = tmp;
		}
		if(data.status == 1){
			data["lineStyle"] = {color:"green"};
		}
		if(data.isHit){
			//data["itemStyle"] = {color : '#8AC007'};
			data["lineStyle"] = {color:"red"};
			//status = 2;
		}
	 },
	 formatTip(params){
		if(params.data.scoringTemplateType){
			var str = "<div style='text-align: left;'><span>"+params.data.name+"</span>";
			if(params.data.basicScore){
				str += "</br><span>基础得分:"+params.data.basicScore+"</span>";
			}
			if(params.data.highestScore){
				str += "</br><span>最高得分:"+params.data.highestScore+"</span>";
			}
			str += "</br><span>最低得分:"+(params.data.lowestScore?params.data.lowestScore:0)+"</span>";
			if(params.data.macInspectScore){
				str += "</br><span>机检得分:"+params.data.macInspectScore+"</span>";
			}
			return str+"</div>";
		}else if(params.data.children){//非叶子节点
			var str = "<div style='text-align: left;'><span>"+params.data.name+"</span>";
			if(params.data.hitRuleScore){
				str += "</br><span>命中评分项得分:"+params.data.hitRuleScore+"</span>";
			}
			return str+"</div>";
		}else{
			var str = "<div style='text-align: left;'><span>"+params.data.name+"</span>";
			if(params.data.sRet){
				if(params.data.sRet.smartResult){
					str += "</br><span>机检结果:"+this.formatHitRet(params.data.sRet.smartResult)+"  机检时间:"+params.data.sRet.createTime+"</span>";
					if(params.data.sRet.smartResultDetail && params.data.sRet.smartResultDetail !='' && params.data.sRet.smartResultDetail != '{}'){
						str += "</br><span>智能规则命中语句</span>";
						var tmp = JSON.parse(params.data.sRet.smartResultDetail);
						for(var i in tmp){
							str += "</br><span>"+tmp[i]+"</span>";
						}
					}
					if(params.data.sRet.semanticsResult && params.data.sRet.semanticsResult !='' && params.data.sRet.semanticsResult != '{}'){
						str += "</br><span>大模型结果：";
						var tmp = JSON.parse(params.data.sRet.semanticsResult);
						for(var i in tmp){
							str += tmp[i];
						}
						str += "</span>";
					}
				}else{
				}
				str += "</br><span>首次复检时间:"+params.data.sRet.firstRecheckTime+"</span>";
				str += "</br><span>复检结果:"+this.formatHitRet(params.data.sRet.recheckResult)+"</span>";
				str += "</br><span>最终结果:"+this.formatHitRet(params.data.sRet.finalResult)+"</span>";
			}
			return str+"</div>";
		}
	 },
	 formatHitRet(val){
		if(val == null || val == ''){
			return "无结果";
		}else if(val == 1 ){
			return "命中";
		}else if(val == 2 ){
			return "未命中";
		}
	 }	,
	  submitForm(){
		var ret = this.formatSubmitResult();
		if(!ret){
			return;
		}
		if(this._props.viewParam && this._props.viewParam.qcType == 1){
			this.subimting = true;
			addManualResult(ret).then(response => {
				this.$message({message: "保存成功",type: 'success'});
				this.getTreeData();
	      	}).finally(v=>{
				this.submiting = false;
				this.updObj = [];
			});
		}else if(this._props.viewParam && this._props.viewParam.qcType == 2 || !this._props.viewParam.qcType){
			this.subimting = true;
			updateSmart(ret).then(response => {
				this.$message({message: "保存成功",type: 'success'});
				this.getTreeData();
	      	}).finally(v=>{
				this.submiting = false;
				this.updObj = [];
			});
		}
	  }
  }
};
</script>
